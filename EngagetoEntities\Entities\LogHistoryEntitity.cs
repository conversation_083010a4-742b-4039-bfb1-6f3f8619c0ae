﻿using EngagetoEntities.Enums;
using System.ComponentModel.DataAnnotations.Schema;

namespace EngagetoEntities.Entities
{
    [Table("LogHistoryEntities")]
    public class LogHistoryEntitity
    {
        public Guid Id { get; set; }
        public string? ApiName { get; set; }
        public string? RequestData { get; set; }
        public string? ResponseData { get; set; }
        public string? Notes { get; set; }
        public LogType LogType { get; set; }
        public string? ErrorMessage { get; set; }
        public string? StackTrace { get; set; }
        public DateTime CreatedAt { get; set; }
        public string? WhatsappBusinessAccountId { get; set; }
        public string? Field {  get; set; } 

        public LogHistoryEntitity() { }

        public LogHistoryEntitity(string? apiName,
            string? requestData,
            string? responseData,
            string? notes,
            LogType logType,
            string? errorMessage,
            string? stackTrace)
        {
            Id = Guid.NewGuid();
            ApiName = apiName;
            RequestData = requestData;
            ResponseData = responseData;
            Notes = notes;
            LogType = logType;
            ErrorMessage = errorMessage;
            StackTrace = stackTrace;
            CreatedAt = DateTime.UtcNow;       
        }
    }
}
