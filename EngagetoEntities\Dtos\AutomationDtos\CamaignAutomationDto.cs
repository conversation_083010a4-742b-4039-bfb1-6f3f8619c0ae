﻿using EngagetoEntities.Enums;

namespace EngagetoEntities.Dtos.AutomationDtos
{
    public record CampaignAutomationDto(
        string Name,
        AutoCustomAutomationDto? AutoCustomAutomation,
        WorkflowAutomationDto? WorkflowAutomation
    );

    public record AutoCustomAutomationDto(
        Guid? Id,
        string Input,
        ResponseType? ResponseType,
        CustomerResponse CustomerResponse,
        string BodyMessage
    );

    public record WorkflowAutomationDto(
        string? Input,
        ResponseType? ResponseType,
        CustomerResponse CustomerResponse,
        string? WorkflowName,
        List<Guid> ContactIds
    );
}
