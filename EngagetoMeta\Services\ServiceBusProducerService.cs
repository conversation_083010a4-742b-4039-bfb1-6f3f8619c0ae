using Azure.Messaging.ServiceBus;
using EngagetoMeta.Dtos;
using System.Text.Json;

namespace EngagetoMeta.Services
{
    public class ServiceBusProducerService : IServiceBusProducerService, IDisposable
    {
        private readonly ServiceBusClient _client;
        private readonly ServiceBusSender _sender;
        private readonly ILogger<ServiceBusProducerService> _logger;
        private readonly string _queueName;

        public ServiceBusProducerService(IConfiguration configuration, ILogger<ServiceBusProducerService> logger)
        {
            _logger = logger;
            
            var connectionString = configuration.GetConnectionString("ServiceBus") 
                ?? throw new InvalidOperationException("ServiceBus connection string not found");
            
            _queueName = configuration["ServiceBus:QueueName"] 
                ?? throw new InvalidOperationException("ServiceBus QueueName not found");

            _client = new ServiceBusClient(connectionString);
            _sender = _client.CreateSender(_queueName);

            _logger.LogInformation("🚀 ServiceBusProducerService initialized | Queue: {QueueName}", _queueName);
        }

        public async Task SendMessageAsync<T>(T message, CancellationToken cancellationToken = default)
        {
            var correlationId = Guid.NewGuid().ToString();
            
            try
            {
                _logger.LogInformation("📤 Preparing to send message to Service Bus | Type: {MessageType} | Queue: {QueueName} | CorrelationId: {CorrelationId}", 
                    typeof(T).Name, _queueName, correlationId);

                var messageJson = JsonSerializer.Serialize(message);

                var serviceBusMessage = new ServiceBusMessage(messageJson)
                {
                    CorrelationId = correlationId,
                    ContentType = "application/json"
                };

                // Add custom properties for better tracking
                serviceBusMessage.ApplicationProperties["MessageType"] = typeof(T).Name;
                serviceBusMessage.ApplicationProperties["Timestamp"] = DateTimeOffset.UtcNow.ToString();

                _logger.LogInformation("📡 Sending message to Service Bus | Queue: {QueueName} | CorrelationId: {CorrelationId} | Size: {Size} bytes", 
                    _queueName, correlationId, messageJson.Length);

                await _sender.SendMessageAsync(serviceBusMessage, cancellationToken);

                _logger.LogInformation("✅ Message sent successfully to Service Bus | Queue: {QueueName} | CorrelationId: {CorrelationId}", 
                    _queueName, correlationId);
            }
            catch (ServiceBusException sbEx)
            {
                _logger.LogError(sbEx, "❌ ServiceBus error sending message | Queue: {QueueName} | CorrelationId: {CorrelationId} | Error: {Error}", 
                    _queueName, correlationId, sbEx.Message);
                throw;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "❌ Unexpected error sending message to Service Bus | Queue: {QueueName} | CorrelationId: {CorrelationId} | Error: {Error}", 
                    _queueName, correlationId, ex.Message);
                throw;
            }
        }

        public async Task SendWebhookMessageAsync(WebhookDto webhookMessage, dynamic message, CancellationToken cancellationToken = default)
        {
            var correlationId = Guid.NewGuid().ToString();
            var businessId = webhookMessage?.Entry?.FirstOrDefault()?.Id ?? "unknown";
            var field = webhookMessage?.Entry?.FirstOrDefault()?.Changes?.FirstOrDefault()?.Field ?? "unknown";

            try
            {
                _logger.LogInformation("📤 Sending webhook message to Service Bus | Field: {Field} | BusinessId: {BusinessId} | Queue: {QueueName} | CorrelationId: {CorrelationId}", 
                    field, businessId, _queueName, correlationId);

                await SendMessageAsync(message, cancellationToken);

                _logger.LogInformation("✅ Webhook message sent successfully | Field: {Field} | BusinessId: {BusinessId} | CorrelationId: {CorrelationId}", 
                    field, businessId, correlationId);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "❌ Error sending webhook message | Field: {Field} | BusinessId: {BusinessId} | CorrelationId: {CorrelationId} | Error: {Error}", 
                    field, businessId, correlationId, ex.Message);
                throw;
            }
        }

        public void Dispose()
        {
            try
            {
                _sender?.DisposeAsync().AsTask().Wait(TimeSpan.FromSeconds(5));
                _client?.DisposeAsync().AsTask().Wait(TimeSpan.FromSeconds(5));
                _logger.LogInformation("🔄 ServiceBusProducerService disposed successfully");
            }
            catch (Exception ex)
            {
                _logger.LogWarning(ex, "⚠️  Warning during ServiceBusProducerService disposal: {Error}", ex.Message);
            }
        }
    }
}