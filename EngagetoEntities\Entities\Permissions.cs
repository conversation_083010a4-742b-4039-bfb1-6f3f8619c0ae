﻿using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace EngagetoEntities.Entities
{
    [Table("Permissions")]
    public class Permissions
    {
        [Key]
        [DatabaseGenerated(DatabaseGeneratedOption.Identity)]
        public Guid Id { get; set; }
        [Required]
        public string Name { get; set; }
        public string Section { get; set; }

    }
    public class PermissionDto
    {
        public string Name { get; set; }
    }
    public class ViewPermissionDto
    {
        public Guid Id { get; set; }
        public string Name { get; set; }
        public string Section { get; set; }
    }
}
