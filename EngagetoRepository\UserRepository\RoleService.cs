﻿using Microsoft.EntityFrameworkCore;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using EngagetoContracts.UserContracts;
using EngagetoEntities.UserEntities.Models;
using EngagetoEntities.Entities;

namespace EngagetoRepository.UserRepository
{
    public class RoleService : IRoleService
    {
        private readonly ApplicationDBContext _context;

        public RoleService(ApplicationDBContext context)
        {
            _context = context;
        }

        public async Task<List<Role>> GetAllRolesAsync(string companyId, string searchQuery, string sortBy, bool isSortAscending)
        {
            var query = _context.Roles.Where(role => role.CompanyId == companyId.ToString()).AsQueryable();

            if (!string.IsNullOrEmpty(searchQuery))
            {
                query = query.Where(role => role.Name.Contains(searchQuery) || role.Id.ToString().Contains(searchQuery));
            }

            if (!string.IsNullOrEmpty(sortBy))
            {
                switch (sortBy.ToLower())
                {
                    case "name":
                        query = isSortAscending ? query.OrderBy(role => role.Name) : query.OrderByDescending(role => role.Name);
                        break;
                    case "level":
                        query = isSortAscending ? query.OrderBy(role => role.Level) : query.OrderByDescending(role => role.Level);
                        break;
                    case "id":
                        query = isSortAscending ? query.OrderBy(role => role.Id) : query.OrderByDescending(role => role.Id);
                        break;
                    default:
                        // Default sorting by Name if no valid sortBy is provided
                        query = isSortAscending ? query.OrderBy(role => role.Name) : query.OrderByDescending(role => role.Name);
                        break;
                }
            }

            return await query.ToListAsync();
        }

        public async Task<Role> GetRoleByIdAsync(Guid roleId)
        {
            return await _context.Roles.FindAsync(roleId);
        }

        public async Task<bool> AddRoleAsync(RoleDto roleDto, Guid currentUserId)
        {
            try
            {

                var currentUser = await _context.Ahex_CRM_Users
                                       .Where(u => u.Id == currentUserId)
                                       .Select(u => new { u.CompanyId, u.RoleId })
                                       .FirstOrDefaultAsync();

                if (currentUser == null || string.IsNullOrEmpty(currentUser.CompanyId))
                {
                    throw new InvalidOperationException("Current user does not have a valid companyId.");
                }

                var highestLevel = await _context.Roles
                                                 .Where(r => r.CompanyId == roleDto.CompanyId)
                                                 .OrderByDescending(r => r.Level)
                                                 .Select(r => r.Level)
                                                 .FirstOrDefaultAsync();
                
                var role = new Role
                {
                    Id = Guid.NewGuid(),
                    Name = roleDto.Name,
                    Level = highestLevel + 1,
                    CompanyId = roleDto.CompanyId
                };

                _context.Roles.Add(role);
                await _context.SaveChangesAsync();


                var menuid = await GetMenuIdsAsync();


                foreach (var id in menuid)
                {
                    var menupermissions = new MenuwithRoleRelationDetails
                    {
                        MenuId = id,
                        RoleId = role.Id.ToString(),
                        CompanyId = roleDto.CompanyId,
                        Status = true,

                    };

                    _context.MenuwithRoleRelationDetails.Add(menupermissions);
                    await _context.SaveChangesAsync();
                }

                var roleIds = await GetAllRoleIdsAsync(roleDto.CompanyId);
                roleIds.Remove(role.Id);
                var accessToRoleId = role.Id.ToString();
                foreach (var roleId in roleIds)
                {
                    await AddRolePermissionAsync(roleId.ToString(), accessToRoleId, roleDto.CompanyId);
                }

                await _context.SaveChangesAsync();

                return true;
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Error adding role: {ex.Message}");
                return false;
            }
        }


        private async Task AddRolePermissionAsync(string roleId, string accessToRoleId, string currentUserCompanyId)
        {
            var rolePermission = new RolePermissions
            {
                RoleId = roleId,
                AccessToRoleId = accessToRoleId,
                CompanyId = currentUserCompanyId,
            };

            _context.RolePermissions.Add(rolePermission);
        }


        private async Task<Guid?> GetRoleIdByNameAsync(string roleName)
        {
            return await _context.Roles
                .Where(r => r.Name == roleName)
                .Select(r => r.Id)
                .FirstOrDefaultAsync();
        }

        public async Task<List<Guid>> GetAllRoleIdsAsync(string companyId)
        {

            var roleIds = await _context.Roles
                                    .Where(role => role.CompanyId == companyId)
                                    .Select(role => role.Id)
                                    .ToListAsync();

            return roleIds;
        }

        public async Task<IEnumerable<Guid>> GetPermissionIdsAsync()
        {
            try
            {

                var permissions = await _context.Permissions.ToListAsync();


                var permissionIds = permissions.Select(p => p.Id).ToList();

                return permissionIds;
            }
            catch (Exception ex)
            {

                Console.WriteLine($"Error fetching permission IDs: {ex.Message}");
                return Enumerable.Empty<Guid>();
            }
        }

        public async Task<IEnumerable<int>> GetMenuIdsAsync()
        {
            try
            {

                var permissions = await _context.MenuDetails.ToListAsync();


                var permissionIds = permissions.Select(p => p.MenuId).ToList();

                return permissionIds;
            }
            catch (Exception ex)
            {

                Console.WriteLine($"Error fetching permission IDs: {ex.Message}");
                return Enumerable.Empty<int>();
            }
        }

        public async Task<bool> UpdateRoleAsync(Guid roleId, RoleDto updatedRoleDto, Guid currentUserId)
        {


            var existingRole = await _context.Roles.FindAsync(roleId);
            if (existingRole == null)
            {
                return false;
            }
            if (!string.IsNullOrEmpty(updatedRoleDto.Name))
            {
                existingRole.Name = updatedRoleDto.Name;
            }

            await _context.SaveChangesAsync();

            return true;
        }



        public async Task<bool> DeleteRoleAsync(Guid roleId)
        {
            try
            {

                var role = await _context.Roles.FindAsync(roleId);
                if (role == null)
                {

                    return false;
                }

                int level = role.Level;

                var rolePermissions = _context.RolePermissions.Where(rp => rp.AccessToRoleId == roleId.ToString());
                _context.RolePermissions.RemoveRange(rolePermissions);


                var assignPermissions = _context.AssignPermissionsToRoleIds.Where(ap => ap.RoleId == roleId);
                _context.AssignPermissionsToRoleIds.RemoveRange(assignPermissions);


                _context.Roles.Remove(role);

                var remainingRoles = _context.Roles.Where(r => r.Level > level);
                foreach (var remainingRole in remainingRoles)
                {
                    remainingRole.Level--;
                }


                await _context.SaveChangesAsync();

                return true;
            }
            catch (Exception ex)
            {

                Console.WriteLine($"Error deleting role: {ex.Message}");
                return false;
            }
        }
    }
}
