﻿using EngagetoContracts.Services;
using EngagetoEntities.Dtos.SubscriptionDtos;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;

namespace Engageto.Controllers.UserControllers
{
    [Route("api/[controller]")]
    [ApiController]
    public class SubscriptionController : BaseController
    {
        private readonly ISubscriptionService _subscriptionService;
        public SubscriptionController(ISubscriptionService subscriptionService) 
        { 
            _subscriptionService = subscriptionService;
        }

        [AllowAnonymous]
        [HttpGet]
        public async Task<IActionResult> GetTenantSubscription(string tenantId)
        {
            try
            {
                var result = await _subscriptionService.GetTenantSubscriptionAsync(tenantId);
                return Ok(CreateSuccessResponse<TenantSubscriptionDto?>(result, "Subscription Details"));
            }
            catch (Exception ex) 
            { 
                return BadRequest(CreateErrorResponse<string>(ex.Message,ex.StackTrace));
            }
        }
    }
}
