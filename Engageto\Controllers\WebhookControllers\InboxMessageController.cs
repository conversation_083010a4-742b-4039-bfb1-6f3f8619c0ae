﻿using Engageto.Attributes;
using Engageto.Hubs;
using EngagetoDapper.Data.Interfaces;
using EngagetoDapper.Data.Interfaces.IUserInterfaces.IUserServices;
using EngagetoDatabase.WhatsAppBusinessDatabase.Models;
using EngagetoEntities.Dtos;
using EngagetoEntities.Dtos.MetaDto;
using EngagetoEntities.Response;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.SignalR;
using Newtonsoft.Json;
using System.ComponentModel.DataAnnotations;
using System.Security.Claims;

namespace Engageto.Controllers.WebhookControllers
{
    [Route("api")]
    [ApiController]
    public class InboxMessageController : ControllerBase
    {
        private readonly IInboxMessageService _inboxMessageService;
        private readonly DbAa80b1WhatsappbusinessContext _DbAa80b1WhatsappbusinessContext;
        private IHubContext<MessageHub, IMessageHubClient> messageHub;
        public IInboxService _inboxService;
        public InboxMessageController(
                IHubContext<MessageHub, IMessageHubClient> Hub,
                DbAa80b1WhatsappbusinessContext DbAa80b1WhatsappbusinessContext,
                IInboxService inboxService,
                IInboxMessageService inboxMessageService)
        {
            _DbAa80b1WhatsappbusinessContext = DbAa80b1WhatsappbusinessContext;
            messageHub = Hub;
            _inboxService = inboxService;
            _inboxMessageService = inboxMessageService;
        }
        [HttpPost("send-text-or-emoji-message")]
        [Authorize]
        public async Task<IActionResult> SendTextOrEmojiMessage([FromBody, Required] TextData data, [FromQuery, Required(ErrorMessage = "Should Not be empty.")] Guid BusinessId)
        {
            try
            {
                var result = await _inboxMessageService.TextOrEmojiMessageAsync(data, BusinessId);
                return Ok(result);
            }
            catch (InvalidOperationException ex)
            {
                return BadRequest(new { Message = ex.Message });
            }
            catch (Exception ex)
            {
                return BadRequest(new { Message = $"Internal Server Error: {ex.Message}" });
            }
        }
        [HttpPost("send-media-message")]
        [Authorize]
        public async Task<IActionResult> SendMediaMessage([FromBody, Required] MediaData data, [FromQuery, Required(ErrorMessage = "Should Not be empty.")] Guid BusinessId)
        {
            try
            {
                var result = await _inboxMessageService.SendMediaMessageAsync(data, BusinessId);
                return Ok(result);
            }
            catch (InvalidOperationException ex)
            {
                return BadRequest(new { Message = ex.Message });
            }
            catch (Exception ex)
            {
                return BadRequest(new { Message = $"Internal Server Error: {ex.Message}" });
            }
        }

        [HttpPost("SendWATextMediaMessage")]
        [Header("Api-Key")]
        [ApiKeyAuthentication]
        public async Task<IActionResult> SendTextEmojiMediaMessage(TextMediaMassages messages)
        {
            try
            {
                _ = HttpContext.Items.TryGetValue("CompanyId", out var companyId);
                var result = await _inboxMessageService.SendTextEmojiMediaMessageAsync(messages, companyId ?? new());
                if (result.Item2 != null)
                {
                    return BadRequest(
                            new ApiResponse<WhatsAppErrorResponse>
                            {
                                Success = true,
                                Message = result.Item2.Error.Message,
                                Errors = result.Item2
                            });
                }
                return Ok(new ApiResponse<object> { Data = result.Item1, Success = true, Message = "Message sent successfully.", Errors = null });
            }
            catch (Exception ex)
            {
                return BadRequest(
                    new ApiResponse<string>
                    {
                        Success = false,
                        Message = "Unable sent message.",
                        Errors = ex.Message
                    });
            }
        }

        [HttpPost("SendWhatsappText")]
        [Header("Api-Key")]
        [ApiKeyAuthentication]
        public async Task<IActionResult> SendWhatsappText([FromBody, Required] TextData data)
        {
            try
            {
                if (!HttpContext.Items.TryGetValue("CompanyId", out var companyId))
                    throw new Exception("Api key is not valid");

                var result = await _inboxMessageService.SendWhatsappTextAsync(data, companyId ?? new());
                return Ok(new ApiResponse<object>
                {
                    Data = result,
                    Success = true,
                    Message = "Text Message sent successfully.",
                    Errors = null
                }); ;
            }
            catch (InvalidOperationException ex)
            {
                return BadRequest(new ApiResponse<string>
                {
                    Success = false,
                    Message = "Unable sent text message.",
                    Errors = ex.Message
                });
            }
            catch (Exception ex)
            {
                return BadRequest(new ApiResponse<string>
                {

                    Success = false,
                    Message = "Unable sent text message.",
                    Errors = ex.Message
                });
            }
        }

        [HttpPost("SendWhatsappMedia")]
        [Header("Api-Key")]
        [ApiKeyAuthentication]
        public async Task<IActionResult> SendWhatsappMedia([FromBody, Required] MediaData data)
        {
            try
            {
                if (!HttpContext.Items.TryGetValue("CompanyId", out var companyId))
                    throw new Exception("Api key is not valid");
                var result = await _inboxMessageService.SendWhatsappMediaAsync(data, companyId ?? new());
                return Ok(new ApiResponse<object>
                {
                    Data = result,
                    Success = true,
                    Message = "Media sent successfully.",
                    Errors = null
                });
            }
            catch (InvalidOperationException ex)
            {
                return BadRequest(new ApiResponse<object>
                {

                    Success = false,
                    Message = "Unable to send the Media message.",
                    Errors = ex.Message
                });
            }
            catch (Exception ex)
            {
                return BadRequest(new ApiResponse<object>
                {

                    Success = false,
                    Message = "Unable to send the Media message.",
                    Errors = ex.Message
                });
            }
        }

        [HttpGet("user-details")]
        //[AuthorizeMenu("inboxUserDetails")]
        [Authorize]
        public async Task<ActionResult> UserDetails([FromQuery, Required] Guid BusinessId, [FromQuery, Required] string Contact)
        {
            try
            {
                var result = await _inboxMessageService.GetUserDetailsAsync(BusinessId, Contact);
                return Ok(result);
            }
            catch (InvalidOperationException ex)
            {
                return BadRequest(new { Message = ex.Message });
            }
            catch (Exception ex)
            {
                return BadRequest(new { Message = $" {ex.Message}" });
            }
        }

        [HttpPost("assign-chat")]
        // [AuthorizeMenu("assignTo")]
        [Authorize]
        public async Task<IActionResult> AssignChat([FromQuery, Required] Guid BusinessId, [FromQuery, Required] Guid? AssignUserId, [FromBody, Required] ContactNumbers Contacts)
        {
            try
            {
                var currentUserIdClaim = User.Claims.FirstOrDefault(c => c.Type == ClaimTypes.Name);
                if (currentUserIdClaim == null || !Guid.TryParse(currentUserIdClaim.Value, out Guid currentUserId))
                {
                    throw new Exception("Invalid current user.");
                }
                bool IsChatAssigned = await _inboxMessageService.AssignChatAsync(BusinessId, AssignUserId, Contacts, currentUserId);
                return Ok(new { Message = "Successfully assigning is done." });
            }
            catch (InvalidOperationException ex)
            {
                return BadRequest(new { Message = ex.Message });
            }
            catch (Exception ex)
            {
                return BadRequest(new { Message = $"Internal Server Error: {ex.Message}" });
            }
        }
        [HttpPost("close-chat")]
        //[AuthorizeMenu("closeChat")]
        [Authorize]
        public async Task<IActionResult> CloseChat([FromQuery, Required] Guid BusinessId, [FromQuery, Required] Guid UserId, [FromBody, Required] ContactNumbers Contacts)
        {
            try
            {
                foreach (var item in Contacts.Contact)
                {
                    var data = _DbAa80b1WhatsappbusinessContext.Contacts.FirstOrDefault(m => ((m.CountryCode + m.Contact1).Replace("+", "") == item) && (m.BusinessId == BusinessId) && (m.IsActive));
                    if (data != null)
                    {
                        await _inboxService.SaveAndUpdateChatStatusAsync(data.ContactId, EngagetoEntities.Enums.ChatStatus.resolved, UserId);
                        data.ChatStatus = ChatStatus.resolved;
                        _DbAa80b1WhatsappbusinessContext.Contacts.Update(data);
                        _DbAa80b1WhatsappbusinessContext.SaveChanges();
                    }
                    var data1 = _DbAa80b1WhatsappbusinessContext.Users.Where(m => m.CompanyId.ToLower() == BusinessId.ToString().ToLower());
                    if (data1 != null)
                    {
                        var UserIds = data1.Select(m => m.Id).ToList();
                        foreach (var User in UserIds)
                        {
                            await messageHub.Clients.Groups(BusinessId.ToString().ToLower() + User.ToString().ToLower()).RenderContacts();
                        }
                    }
                }
                var result = new
                {
                    Success = true,
                    Message = "Successfully chats are closed."
                };
                return Ok(result);

            }
            catch (InvalidOperationException ex)
            {
                return BadRequest(new { Message = ex.Message });
            }
            catch (Exception ex)
            {
                return BadRequest(new { Message = $"Internal Server Error: {ex.Message}" });
            }
        }

        [HttpPost("mark-as-spam")]
        [Authorize]
        public IActionResult MarkAsSpam([FromQuery, Required] Guid BusinessId, [FromQuery, Required] Guid UserId, [FromBody, Required] ContactNumbers Contacts)
        {
            try
            {
                foreach (var contact in Contacts.Contact)
                {
                    var data = _DbAa80b1WhatsappbusinessContext.Contacts
                        .FirstOrDefault(m => (m.CountryCode + m.Contact1).Replace("+", "") == contact
                                             && m.BusinessId == BusinessId
                                             && m.IsActive);
                    if (data != null)
                    {
                        data.IsSpam = !(data.IsSpam ?? false);
                        _DbAa80b1WhatsappbusinessContext.Contacts.Update(data);
                        _DbAa80b1WhatsappbusinessContext.SaveChanges();

                        var data1 = _DbAa80b1WhatsappbusinessContext.Users.Where(m => m.CompanyId.ToLower() == BusinessId.ToString().ToLower());
                        if (data1 != null)
                        {
                            var UserIds = data1.Select(m => m.Id).ToList();
                            foreach (var User in UserIds)
                            {
                                messageHub.Clients.Groups(BusinessId.ToString().ToLower() + User.ToString().ToLower()).RenderContacts();
                            }

                        }
                        var message = data.IsSpam == true ? "Successfully marked contact as spam." : "Successfully unmarked contact as spam.";
                        return Ok(new { Message = message });
                    }
                    else
                    {
                        return BadRequest(new { Message = "Failed to mark contact as spam: contact not found." });
                    }
                }

                return BadRequest(new { Message = "No contacts were processed." });
            }
            catch (InvalidOperationException ex)
            {
                return BadRequest(new { Message = ex.Message });
            }
            catch (Exception ex)
            {
                return BadRequest(new { Message = $"Internal Server Error: {ex.Message}" });
            }
        }
    }
}
