﻿using EngagetoEntities.Enums;
using EngagetoEntities.UserEntities.Dtos;
using EngagetoEntities.Utilities;
using Newtonsoft.Json;
using System.ComponentModel.DataAnnotations.Schema;

namespace EngagetoEntities.Entities
{
    [Table("ResourcePermissionEntities")]
    public class ResourcePermissionEntity : BaseEntity
    {
        public Guid Id { get; set; }
        public string? CompanyId { get; set; }
        public int Year { get; set; }
        public int? PlanId { get; set; }
        public int AgentLimit { get; set; } = -1;
        public string? FreeAnalyticsConversationCountsJson { get; set; }
        public PlanType PlanType { get; set; }

        [NotMapped]
        public List<FreeConversationCountDetail>? FreeAnalyticsConversationCounts
        {
            get => string.IsNullOrEmpty(FreeAnalyticsConversationCountsJson)
                ? new List<FreeConversationCountDetail>()
                : JsonConvert.DeserializeObject<List<FreeConversationCountDetail>>(FreeAnalyticsConversationCountsJson);

            set => FreeAnalyticsConversationCountsJson = JsonConvert.SerializeObject(value);
        }
        public ResourcePermissionEntity()
        {
            FreeAnalyticsConversationCounts = new List<FreeConversationCountDetail>();
            UpdatedAt = StringHelper.GetIndianDateTime();
        }

        public void AddFreeConversationCount(ConversationCategoriesEnum category, int count)
        {
            FreeAnalyticsConversationCounts.Add(new FreeConversationCountDetail
            {
                AnalyticsCategoriesEnum = category,
                Count = count
            });
        }
    }
}
