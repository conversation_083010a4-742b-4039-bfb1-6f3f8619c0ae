﻿using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Configuration;

namespace EngagetoDatabase.AutomationDataBase.Models;

public partial class DbAa80b1WhatsappbusinessContext : DbContext
{
    private readonly IConfiguration _configuration;

    public DbAa80b1WhatsappbusinessContext(IConfiguration configuration)
    {
        _configuration = configuration;
    }
    public DbAa80b1WhatsappbusinessContext(DbContextOptions<DbAa80b1WhatsappbusinessContext> options)
        : base(options)
    {
    }

    public virtual DbSet<Contact> Contacts { get; set; }

    public virtual DbSet<Template> Templates { get; set; }

    public virtual DbSet<User> Users { get; set; }

    protected override void OnConfiguring(DbContextOptionsBuilder optionsBuilder)
    {
        if (!optionsBuilder.IsConfigured)
        {
            var connectionString = _configuration.GetConnectionString("ConnStr");
            optionsBuilder.UseSqlServer(connectionString);
        }
    }

    protected override void OnModelCreating(ModelBuilder modelBuilder)
    {
        modelBuilder.Entity<Contact>(entity =>
        {
            entity.Property(e => e.ContactId).ValueGeneratedNever();
            entity.Property(e => e.Contact1).HasColumnName("Contact");
        });

        modelBuilder.Entity<Template>(entity =>
        {
            entity.Property(e => e.TemplateId).ValueGeneratedNever();
        });

        modelBuilder.Entity<User>(entity =>
        {
            entity.Property(e => e.Id).ValueGeneratedNever();
        });

        OnModelCreatingPartial(modelBuilder);
    }

    partial void OnModelCreatingPartial(ModelBuilder modelBuilder);
}
