﻿using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace EngagetoEntities.Entities
{
    public class ForgotPassword
    {
        public Guid Id { get; set; }
        public string? Email { get; set; }
        public string OTPToken { get; set; }
        public DateTime OTPTokenExpiration { get; set; }

    }
    public class ForgotPasswordRequest
    {

        [Required(ErrorMessage = "Email is required.")]
        [EmailAddress(ErrorMessage = "Invalid email address.")]
        [RegularExpression(@"^\S+@\S+\.\S+$", ErrorMessage = "Email must be in a valid format.")]
        public string Email { get; set; }
    }

    public class OTPVerificationRequest
    {
        [Required(ErrorMessage = "Email is required.")]
        [EmailAddress(ErrorMessage = "Invalid email address.")]
        [RegularExpression(@"^\S+@\S+\.\S+$", ErrorMessage = "Email must be in a valid format.")]
        public string Email { get; set; }

        [Required]
        public string OTP { get; set; }

        [Required]
        [MinLength(6)]
        public string NewPassword { get; set; }

        [Required]
        [MinLength(6)]
        public string ConfirmPassword { get; set; }
    }
    public class VerifyOTPAndUpdatePasswordRequest
    {
        public string Email { get; set; }
        [Required]
        [MinLength(6)]
        public string NewPassword { get; set; }
        [Required]
        [MinLength(6)]
        public string ConfirmPassword { get; set; }
    }
    public class VerifyOTPRequest
    {
        public string email { get; set; }
        public string OTP { get; set; }
    }
}
