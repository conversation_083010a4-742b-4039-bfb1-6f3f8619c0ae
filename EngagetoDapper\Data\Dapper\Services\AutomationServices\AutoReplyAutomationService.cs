﻿using EngagetoDapper.Data.Interfaces.AutomationInterfaces;
using EngagetoEntities.Dtos.AutomationDtos;
using EngagetoEntities.Entities;

namespace EngagetoDapper.Data.Dapper.Services.AutomationServices
{
    public class AutoReplyAutomationService : IAutoReplyAutomationService
    {
        private readonly IAutoReplyAutomationRepostry _autoReplyAutomationRepostry;
         
        public AutoReplyAutomationService(IAutoReplyAutomationRepostry autoReplyAutomationRepostry) 
        { 
            _autoReplyAutomationRepostry = autoReplyAutomationRepostry;
        }
        public async Task<AutoReplyAutomationEntity?> GetAutoReplyAutomationByInput(Guid companyId, string input, string? inputVariations = null)
        {
            try
            {
                var results = await _autoReplyAutomationRepostry
                    .GetAutoReplyMessageByInputAsync<AutoReplyAutomationEntity>(companyId,input,inputVariations);
                return results.LastOrDefault();
            }catch (Exception ex)
            {
                return null;
            }
        }

        public async Task<IEnumerable<WorkflowCustomerResponseDto>?> GetWorkflowCustomerResponseAsync(Guid? contactId = null, string? workflowName = null, Guid? companyId = null, long? workflowStartId = null)
        {
            try
            {
                var result = await _autoReplyAutomationRepostry.GetWorkflowCustomerResponseAsync(contactId,workflowName,companyId,workflowStartId);
                return result;
            }
            catch (Exception ex) 
            {
                return null;
            }
        }
    }
}
