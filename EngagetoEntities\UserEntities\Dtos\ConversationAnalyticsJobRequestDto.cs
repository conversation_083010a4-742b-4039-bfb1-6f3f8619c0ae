﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace EngagetoEntities.UserEntities.Dtos
{
    public class ConversationAnalyticsJobRequestDto
    {
        public string CompanyId { get; set; }
        public bool IsCompleted { get; set; }
        public string? ErrorMessage { get; set; }
        public DateTime StartDate { get; set; }
        public DateTime EndDate { get; set; }
        public string ResponseMessage { get; set; }
    }
}
