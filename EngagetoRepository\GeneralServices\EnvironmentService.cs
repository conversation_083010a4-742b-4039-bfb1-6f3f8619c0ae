﻿using EngagetoContracts.GeneralContracts;
using Microsoft.AspNetCore.Http;
using Microsoft.Extensions.Configuration;

namespace EngagetoRepository.GeneralServices
{
    public class EnvironmentService : IEnvironmentService
    {
        private readonly IConfiguration _configuration;
        public EnvironmentService(IConfiguration configuration)
        {
            _configuration = configuration;
        }
        public bool IsDevelopment { get; set; }
        public HostString RequestHost { get; set; }
        public string? RequestScheme { get; set; } = string.Empty;
        public string GetDevWebsiteLink()
        {
            return _configuration["Origins:DevWebsiteMain"];
        }
        public string GetProdWebsiteLink()
        {
            return _configuration["Origins:ProdWebsiteMain"];
        }
    }
}
