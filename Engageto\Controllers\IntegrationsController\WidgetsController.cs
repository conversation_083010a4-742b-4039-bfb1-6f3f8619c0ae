﻿using EngagetoContracts.IntegrationsContracts;
using EngagetoContracts.Services;
using EngagetoEntities.Dtos.IntegrationDtos;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Cors;
using Microsoft.AspNetCore.Mvc;
using System.Security.Claims;

namespace Engageto.Controllers.Integrations
{
    [Route("api/[controller]")]
    [ApiController]
    public class WidgetsController : ControllerBase
    {
        private readonly IWidgetRepository _widgetRepository;
        private readonly IUserIdentityService _userIdentityService;

        public WidgetsController(IWidgetRepository widgetRepository,IUserIdentityService userIdentityService)
        {
            _widgetRepository = widgetRepository;
            _userIdentityService = userIdentityService;
        }

        [HttpPost("create")]
        [Authorize]
        public async Task<IActionResult> WidgetCreate(WidgetEntityDto widget)
        {
            try
            {
                var currentUserIdClaim = User.Claims.FirstOrDefault(c => c.Type == ClaimTypes.Name);
                var response = Guid.Empty;

                if (currentUserIdClaim == null || !Guid.TryParse(currentUserIdClaim.Value, out Guid currentUserId))
                {
                    return BadRequest(new { Message = "Invalid current user." });
                }

                if (widget != null)
                {
                    response = await _widgetRepository.WidgetCreate(widget);
                }

                return Ok(new { Message = "Successfully created widget.", Widget_Id = response });
            }
            catch (Exception ex)
            {
                return BadRequest(new { Message = ex.Message });
            }
        }

        // New action to get widget by id
        [HttpGet("get")]
        [EnableCors("CustomCors")]
        public async Task<IActionResult> GetWidgetById(Guid id)
        {
            try
            {
                // Fetch the widget by ID using the repository
                var widget = await _widgetRepository.GetWidgetByIdAsync(id);

                if (widget == null)
                {
                    return NotFound(new { Message = "Widget not found." });
                }

                return Ok(widget);
            }
            catch (Exception ex)
            {
                return BadRequest(new { Message = ex.Message });
            }
        }
    }
}
