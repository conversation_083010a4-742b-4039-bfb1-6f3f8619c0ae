﻿using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using EngagetoEntities.Enums;

namespace EngagetoEntities.Entities
{
    [Table("AutoReplyAutomationEntities")]
    public class AutoReplyAutomationEntity : BaseEntity
    {
        [Key]
        public Guid Id { get; set; }
        public Guid CompanyId { get; set; }
        [ForeignKey("CompanyId")]
        public Ahex_CRM_BusinessDetails BusinessDetails { get; set; }
        public Guid UserId { get; set; }
        [ForeignKey("UserId")]
        public Ahex_CRM_Users? AhexCRMUser { get; set; }
        public string Input { get; set; }
        public string? InputVariation { get; set; }
        public ResponseType AutoReplyType { get; set; }
        public string? WorkflowName { get; set; }
        public virtual ICollection<AutoReplyCustomMessageEntity>? AutoReplyCustomMessageEntities { get; set; }
        public AutoReplyAutomationEntity() { }
        public AutoReplyAutomationEntity(Guid companyId, Guid userId,string input, string? inputVariation, ResponseType autoReplyType, string? workflowName)
        {
            Id = Guid.NewGuid();
            CompanyId = companyId;
            UserId = userId;
            Input = input;
            InputVariation = inputVariation;
            AutoReplyType = autoReplyType;
            WorkflowName = workflowName;
            CreatedAt = DateTime.UtcNow;
            UpdatedAt = DateTime.UtcNow;
            CreatedBy = userId;
            UpdatedBy = userId;
        }
    }
}
