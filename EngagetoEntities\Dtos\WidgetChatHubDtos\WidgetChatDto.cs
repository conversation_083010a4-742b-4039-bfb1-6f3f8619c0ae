﻿using EngagetoEntities.Enums;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using System.Text.Json;

namespace EngagetoEntities.Dtos.WidgetChatHubDtos
{
    public class WidgetChatDto
    {
        public Guid? Id { get; set; }

        [Required(ErrorMessage = "BusinessId is required")]
        public string From { get; set; }
        [Required(ErrorMessage = "Target is required")]
        public string To { get; set; }
        public string? Body { get; set; }

        [NotMapped]
        public BodyMessage? BodyMessage
        {
            get
            {
                return string.IsNullOrEmpty(Body)
                ? null
                : Newtonsoft.Json.JsonConvert.DeserializeObject<BodyMessage>(Body);
            }

            set
            {
                JsonSerializerOptions options = new JsonSerializerOptions
                {
                    PropertyNamingPolicy = JsonNamingPolicy.CamelCase,
                    WriteIndented = true
                };
                Body = value == null
                            ? ""
                            : JsonSerializer.Serialize(value,options);
            }
        }
    }

    public class BodyMessage
    {
        public MediaType Type { get; set; }
        public TextMessage? Text { get; set; }
        public MediaMessage? Media { get; set; }
        public ConvStatus Status { get; set; }
        public string? ErrorMessage { get; set; }
    }
    public class TextMessage
    {
        public string Text { get; set; }
    }
    public class MediaMessage
    {
        public string? Caption { get; set; }
        public string MediaUrl { get; set; }
        public string MimeType  { get; set; }
    }
}
