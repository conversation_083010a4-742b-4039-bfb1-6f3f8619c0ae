﻿using EngagetoDapper.Data.Interfaces.CampaignInterfaces.ICampaignRepositories;
using EngagetoDapper.Data.Interfaces.CampaignInterfaces.ICampaignServices;
using EngagetoDapper.Data.Interfaces.IConversationAnalyticsInterfaces;
using EngagetoDapper.Data.Interfaces.IUserInterfaces.IUserServices;
using EngagetoEntities.Dtos.ConversationDtos;


namespace EngagetoDapper.Data.Dapper.Services.CampaignServices
{
    public class ConversationsService : IConversationsService
    {
        private readonly ICampaignRespository _conversationsRespositry;
        private readonly IConversationAnalyticsService _conversationAnalyticsService;
        private readonly IResourcePermissionService _resourcePermissionService;
        private readonly IUserService _userService;
        public ConversationsService(ICampaignRespository conversationsRespositry,
            IConversationAnalyticsService conversationAnalyticsService,
            IResourcePermissionService resourcePermissionService,
            IUserService userService)
        {
            _conversationsRespositry = conversationsRespositry;
            _conversationAnalyticsService = conversationAnalyticsService;
            _resourcePermissionService = resourcePermissionService;
            _userService = userService;
        }
        public async Task<List<ConverstationDetailsDto>> GetConversationDetailsAsync(string CompanyId)
        {
            try
            {
                var results = await _conversationsRespositry.GetConversationDetailsAsync(CompanyId);
                var resourcePermissions = await _resourcePermissionService.GetResourcePermissionsAsync();
                if (results == null || !results.Any())
                {
                    return new List<ConverstationDetailsDto>();
                }
                else
                {
                    foreach (var conversation in results)
                    {
                        var permissions = resourcePermissions.FirstOrDefault(x => x.PlanId == conversation.PlanId);
                        if (permissions is not null)
                        {
                            var analyticsService = permissions.FreeAnalyticsConversationCounts?
                                .FirstOrDefault(x => x.AnalyticsCategoriesEnum.ToString() == conversation.ConversationCategory);
                            if (analyticsService is not null)
                                conversation.IsFreeConversation = conversation.ConversationCount < analyticsService.Count;
                            else
                                conversation.IsFreeConversation = false;
                        }
                        else
                            conversation.IsFreeConversation = false;
                    }
                    return results.ToList();
                }

            }
            catch (Exception ex)
            {
                throw new Exception($"Error in fetching the conversation analytics count: {ex.Message}", ex);
            }
        }

        public async Task VerifySubscriptionAsync(string companyId, bool isVerifyLowWalletBalance = false, int count = 1)
        {
            try
            {
                var conversationAndSubscription = (await _conversationsRespositry.GetConversationDetailsAsync(companyId))?.FirstOrDefault();
                //var conversationCost = await _conversationAnalyticsService.GetConversationAnalyticsPriceByYear(companyId,DateTime.Now.Year);
                if (conversationAndSubscription == null)
                {
                    throw new Exception("There are no existing subscription plans. Please purchase a new plan.");
                }

                if (!conversationAndSubscription.IsActive)
                {
                    throw new Exception("Your plan is expired.");
                }

                //if (conversationAndSubscription.SubscriptionPlanName == "Intro" && (conversationAndSubscription.IsFreeConversation ?? false))
                //{
                //    throw new Exception("Your free trial period has ended.");
                //}

                if (isVerifyLowWalletBalance)
                {
                    if (conversationAndSubscription.WalletBalance < 1)
                        throw new Exception("There is a low balance in your wallet, so you cannot proceed.");
                    else
                    {
                        int totalCount = count +
                            (conversationAndSubscription.CampaignCount ?? 0) +
                            (conversationAndSubscription.ConversationCount ?? 0);
                        int messagesToSend = (conversationAndSubscription.CampaignCount ?? 0) + (conversationAndSubscription.ConversationCount ?? 0);
                        if (conversationAndSubscription.WalletBalance < totalCount)
                        {
                            string message = "Your current balance is insufficient to send this campaign. Please add more funds to your account and try again.";
                            /*$"You have a low balance in your wallet, so you cannot proceed with sending {count} messages. " +
                             $"You already sent a total of {messagesToSend} messages, including {conversationAndSubscription.CampaignCount ?? 0} campaign messages " +
                             $"and {conversationAndSubscription.ConversationCount ?? 0} conversation messages in the last 30 Minutes. " +
                             $"Due to this, you are not eligible to send the message.";*/
                            throw new Exception(message);
                        }
                    }
                }
            }
            catch (Exception ex)
            {
                throw;
            }
        }

        public async Task VerifySubscriptionAndExpectedWalletBalanceAysc(string companyId, int count = 1)
        {
            try
            {
                var expectedWalletBalance = await _userService.GetExpectedWalletBalanceAsync(companyId)
                    ?? throw new Exception("There are no existing subscription plans. Please purchase a new plan.");

                if (!expectedWalletBalance.IsActive ?? false)
                {
                    throw new Exception("Your plan is expired.");
                }
                if (((expectedWalletBalance?.ExpectedWalletBalance ?? 0) - count) < 0)
                    throw new Exception("Your expected wallet balance is insufficient to send this campaign. Please add more funds to your account and try again.");
            }
            catch (Exception ex)
            {
                throw new Exception(ex.Message);
            }
        }
    }
}
