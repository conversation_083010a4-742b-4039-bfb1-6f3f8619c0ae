﻿using System.ComponentModel;

namespace EngagetoEntities.Dtos.OptInManagementDto
{
    public class AddKeywordRequestDto
    {
        public Guid? Id { get; set; }
        public string BusinessId { get; set; }
        public Guid UserId { get; set; }
        public List<string>? OptInKeyword { get; set; } = new List<string>();

        [DefaultValue(false)]
        public bool? OptInMessageToggle { get; set; } = false;
        public string? TextOptIn { get; set; }
        public string? UrlOptIn { get; set; }
        public List<string>? OptOutKeyword { get; set; } = new List<string>();

        [DefaultValue(false)]
        public bool? OptOutMessageToggle { get; set; } = false;
        public string? TextOptOut { get; set; }
        public string? UrlOptOut { get; set; }
    }
}
