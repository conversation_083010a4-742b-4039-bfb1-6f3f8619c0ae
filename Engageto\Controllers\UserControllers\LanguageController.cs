﻿using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using System.Security.Claims;
using EngagetoContracts.UserContracts;
using EngagetoEntities.UserEntities.Models;

namespace Engageto.Controllers.UserControllers
{
    [Route("api/[controller]")]
    [ApiController]
    public class LanguageController : ControllerBase
    {
        private readonly ILanguageService _languageService;

        public LanguageController(ILanguageService languageService)
        {
            _languageService = languageService;
        }


        [HttpPost("create-language")]
        //[AuthorizePermission("Add/Update/Delete-Languages")]
        public async Task<IActionResult> CreateLanguage([FromBody] LanguagePreferenceRequest languageRequest)
        {
            try
            {
                var languageId = await _languageService.CreateLanguageAsync(languageRequest.LanguageCode, languageRequest.LanguageName);
                return Ok(new { LanguageId = languageId });
            }
            catch (Exception ex)
            {
                return BadRequest(new { Message = $"Error: {ex.Message}" });
            }
        }
        [HttpPut("update-language/{languageId}")]
        //[AuthorizePermission("Add/Update/Delete-Languages")]
        public async Task<IActionResult> UpdateLanguage(Guid languageId, LanguageUpdateDto languageUpdateDto)
        {
            try
            {
                var success = await _languageService.UpdateLanguageAsync(languageId, languageUpdateDto);
                if (success)
                {
                    return Ok("Language Updated Successfully!");
                }
                return NotFound($"Language with ID '{languageId}' not found.");
            }
            catch (Exception ex)
            {
                return StatusCode(500, $"Error: {ex.Message}");
            }
        }

        [HttpDelete("delete-language/{languageId}")]
        //[AuthorizePermission("Add/Update/Delete-Languages")]
        public async Task<IActionResult> DeleteLanguage(Guid languageId)
        {
            try
            {
                var success = await _languageService.DeleteLanguageAsync(languageId);
                if (success)
                {
                    return Ok("Language Deleted Successfully!");
                }
                return NotFound($"Language with ID '{languageId}' not found.");
            }
            catch (Exception ex)
            {
                return StatusCode(500, $"Error: {ex.Message}");
            }
        }
        [HttpGet("DisplayAllLanguages")]
        [Authorize]
        public async Task<IActionResult> GetAllLanguages(string searchKeyword = null)
        {
            try
            {
                var languages = await _languageService.GetAllLanguagesAsync(searchKeyword);
                return Ok(languages);
            }
            catch (Exception ex)
            {
                return StatusCode(500, $"Internal server error: {ex.Message}");
            }
        }
        [HttpPost("set-preferred-language")]
        [Authorize]
        //[AuthorizeMenu("languagePreferences")]
        public async Task<IActionResult> SetUserPreferredLanguage([FromBody] UserLanguageRequest userLanguageRequest)
        {
            try
            {
                var currentUserIdClaim = User.Claims.FirstOrDefault(c => c.Type == ClaimTypes.Name);

                if (currentUserIdClaim == null || !Guid.TryParse(currentUserIdClaim.Value, out Guid currentUserId))
                {
                    return BadRequest(new { Message = "Invalid current user." });
                }

                var userLanguageId = await _languageService.SetUserPreferredLanguageAsync(currentUserId, userLanguageRequest.LanguageName);
                return Ok(new { UserLanguageId = userLanguageId });
            }
            catch (Exception ex)
            {
                return BadRequest(new { Message = $"Error: {ex.Message}" });
            }
        }
    }
}
