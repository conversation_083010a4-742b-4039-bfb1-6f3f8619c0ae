﻿
using AutoMapper;
using EngagetoEntities.UserEntities.Dtos;
using Razorpay.Api;


namespace EngagetoEntities.Mapping
{
    internal class RazorpayOrderProfile : Profile
    {
        public RazorpayOrderProfile()
        {
            CreateMap<Order, OrderDto>()
            .ForMember(dest => dest.Id, opt => opt.MapFrom(src => src["id"]))
            .ForMember(dest => dest.Amount, opt => opt.MapFrom(src => src["amount"]))
            .ForMember(dest => dest.Currency, opt => opt.MapFrom(src => src["currency"]))
            .ForMember(dest => dest.AmountDue, opt => opt.MapFrom(src => src["amount_due"]))
            .ForMember(dest => dest.AmountPaid, opt => opt.MapFrom(src => src["amount_paid"]))
            .ForMember(dest => dest.Attempts, opt => opt.MapFrom(src => src["attempts"]))
            .ForMember(dest => dest.CreatedAt, opt => opt.MapFrom(src => src["created_at"]))
            .ForMember(dest => dest.Entity, opt => opt.MapFrom(src => src["entity"]))
            .ForMember(dest => dest.Id, opt => opt.MapFrom(src => src["id"]))
            .ForMember(dest => dest.Notes, opt => opt.MapFrom(src => src["notes"]))
            .ForMember(dest => dest.OfferId, opt => opt.MapFrom(src => src["offer_id"]))
            .ForMember(dest => dest.Status, opt => opt.MapFrom(src => src["status"]))
            .ForMember(dest => dest.Receipt, opt => opt.MapFrom(src => src["receipt"]))
            // Add other mappings as needed
            .ReverseMap();
        }
    }
}
