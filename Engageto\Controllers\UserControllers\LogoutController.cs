﻿using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using EngagetoContracts.UserContracts;
using EngagetoEntities.UserEntities.Models;

namespace Engageto.Controllers.UserControllers
{
    [Route("api/[controller]")]
    [ApiController]
    public class LogoutController : ControllerBase
    {
        private readonly ILogoutService _logoutService;

        public LogoutController(ILogoutService logoutService)
        {
            _logoutService = logoutService;
        }

        [HttpPost]
        [Authorize]
        public async Task<IActionResult> Logout()
        {
            try
            {
                var token = Request.Headers["Authorization"].ToString().Split(" ").LastOrDefault();

                var result = await _logoutService.LogoutAsync(token);

                if (result.Success)
                {
                    return Ok(new { status = "success", message = result.Message });
                }
                else
                {
                    return BadRequest(new { status = "error", message = result.Message });
                }
            }
            catch
            {
                return StatusCode(500, new { status = "error", message = "Internal Server Error" });
            }
        }
    }
}
