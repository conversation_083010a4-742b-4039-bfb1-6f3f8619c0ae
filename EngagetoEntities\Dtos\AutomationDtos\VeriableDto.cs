﻿using EngagetoEntities.Enums;

namespace EngagetoEntities.Dtos.AutomationDtos
{
    public class VeriableDto: BaseVariableDto
    {
        public Guid? Id { get; set; }
        public Guid? ReferenceId { get; set; }
        public int? Index { get; set; }
        public VariableType? Type { get; set; }
        public ReferenceTableType? ReferenceTableType { get; set; }
    }
    public class BaseVariableDto
    {
        public string Veriable { get; set; }
        public string? Value { get; set; }
        public string? FallbackValue { get; set; }
    }
}
