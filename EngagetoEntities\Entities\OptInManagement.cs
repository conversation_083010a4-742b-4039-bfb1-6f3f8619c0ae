﻿namespace EngagetoEntities.Entities
{
    public class OptInManagement
    {
        public Guid Id { get; set; }
        public string BusinessId { get; set; }
        public Guid UserId { get; set; }
        public string? OptInKeyword { get; set; }
        public bool? OptInMessageToggle { get; set; }
        public string? TextOptIn { get; set; }
        public string? UrlOptIn { get; set; }
        public string? OptInWhatsAppMessageId { get; set; }
        public string? OptOutKeyword { get; set; }
        public bool? OptOutMessageToggle { get; set; }
        public string? TextOptOut { get; set; }
        public string? UrlOptOut { get; set; }
        public string? OptOutWhatsAppMessageId { get; set; }
        public string? CreatedBy { get; set; }
        public DateTime? CreatedDate { get; set; }
    }
}
