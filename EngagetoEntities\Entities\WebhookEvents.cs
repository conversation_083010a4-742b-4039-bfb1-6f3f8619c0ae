﻿namespace EngagetoEntities.Entities
{
    public class WebhookEvents
    {
        public Guid Id { get; set; }
        public Guid WebhookId { get; set; }
        public string? EventType { get; set; }
        public string? Payload { get; set; }
        public DateTime? SentAt { get; set; }
        public int? ResponseStatus { get; set; }
        public string? ResponseBody { get; set; }
        public int RetryCount { get; set; } = 0;
        public WebhookEvents() { }
        public WebhookEvents(Guid webhookId,
            string? eventType,
            string? payload,
            DateTime? sentAt,
            int? responseStatus,
            string? responseBody,
            int retryCount = 1)
        {
            Id = Guid.NewGuid();
            WebhookId = webhookId;
            EventType = eventType;
            Payload = payload;
            SentAt = sentAt;
            ResponseStatus = responseStatus;
            ResponseBody = responseBody;
            RetryCount = retryCount;
        }
    }
}
