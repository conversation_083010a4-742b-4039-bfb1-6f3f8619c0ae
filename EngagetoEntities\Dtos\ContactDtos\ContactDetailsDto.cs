﻿using EngagetoEntities.Enums;

namespace EngagetoEntities.Dtos.ContactDtos
{
    public class ContactDetailsDto
    {
        public Guid ContactId { get; set; }
        public string Name { get; set; } = default!;
        public string CountryCode { get; set; } = default!;
        public string Contact { get; set; } = default!;
        public string? CountryName { get; set; }
        public string? Email { get; set; }
        public List<string>? Tags { get; set; }
        public SourceType? Source { get; set; }
        public Guid?  WorkFlowNodeId { get; set; }
        public string ? BusinessId { get; set; }
    }
}
