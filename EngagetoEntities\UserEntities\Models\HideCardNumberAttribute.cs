﻿using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace EngagetoEntities.UserEntities.Models
{
    [AttributeUsage(AttributeTargets.Property | AttributeTargets.Field | AttributeTargets.Parameter, AllowMultiple = false)]
    public class HideCardNumberAttribute : ValidationAttribute
    {
        public override string FormatErrorMessage(string name)
        {
            return string.Format(ErrorMessageString, name);
        }

        protected override ValidationResult IsValid(object value, ValidationContext validationContext)
        {
            if (value != null)
            {
                string cardNumber = value.ToString();
                if (cardNumber.Length >= 16)
                {
                    // Hide the first 12 digits
                    string hiddenPart = new string('x', cardNumber.Length - 4);
                    string visiblePart = cardNumber.Substring(cardNumber.Length - 4);
                    string maskedNumber = hiddenPart + visiblePart;

                    // Set the masked value to the CardNumber property
                    validationContext.ObjectType.GetProperty(validationContext.MemberName).SetValue(validationContext.ObjectInstance, maskedNumber);
                    return ValidationResult.Success;
                }
            }
            return new ValidationResult(ErrorMessage ?? "Invalid card number.");
        }
    }
}
