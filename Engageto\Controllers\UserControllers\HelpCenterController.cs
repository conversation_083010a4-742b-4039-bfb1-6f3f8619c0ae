﻿using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using System.Security.Claims;
using EngagetoContracts.UserContracts;
using EngagetoEntities.Entities;

namespace Engageto.Controllers.UserControllers
{
    [Route("api/[controller]")]
    [ApiController]
    public class HelpCenterController : ControllerBase
    {
        private readonly IHelpCenterService _helpCenterService;

        public HelpCenterController(IHelpCenterService helpCenterService)
        {
            _helpCenterService = helpCenterService;
        }

        [HttpGet("get-all-faq's")]
        [Authorize]
        public async Task<IActionResult> GetHelpItems([FromQuery] string searchTerm = null)
        {
            try
            {
                IEnumerable<HelpCenterDto> searchResults;

                if (string.IsNullOrEmpty(searchTerm))
                {

                    var allHelpItems = await _helpCenterService.GetAllHelpItemsAsync();


                    searchResults = allHelpItems.Select(item => new HelpCenterDto
                    {
                        Question = item.Question,
                        Answer = item.Answer
                    });
                }
                else
                {

                    searchResults = await _helpCenterService.SearchHelpItemsAsync(searchTerm);
                }

                return Ok(searchResults);
            }
            catch (Exception ex)
            {
                return StatusCode(500, "Internal Server Error");
            }
        }
        [HttpPost("create-helpcenter")]
        [Authorize]
        // [AuthorizeMenu("addNewFaq")]
        public async Task<IActionResult> AddHelpCenter([FromBody] HelpCenterDto helpCenterDto)
        {
            try
            {

                var currentUserIdClaim = User.Claims.FirstOrDefault(c => c.Type == ClaimTypes.Name);

                if (currentUserIdClaim == null || !Guid.TryParse(currentUserIdClaim.Value, out Guid currentUserId))
                {
                    return BadRequest(new { Message = "Invalid current user." });
                }

                if (helpCenterDto == null)
                {
                    return BadRequest(new { Message = "Invalid request body." });
                }


                var helpCenterId = await _helpCenterService.CreateHelpCenterAsync(currentUserId, helpCenterDto);

                if (helpCenterId != Guid.Empty)
                {
                    return Ok(new { HelpCenterId = helpCenterId, Message = "New FAQ added successfully." });
                }
                else
                {
                    return BadRequest(new { Message = "Failed to add New FAQ" });
                }
            }
            catch (InvalidOperationException ex)
            {
                return BadRequest(new { Message = ex.Message });
            }
            catch (Exception ex)
            {
                return StatusCode(500, new { Message = "Internal Server Error" });
            }
        }

        [HttpPut]
        [Route("update-helpcenter/{id}")]
        // [AuthorizePermission("Add/Update/Delete-HelpCenter")]
        public async Task<IActionResult> UpdateHelpCenter(Guid id, [FromBody] UpdateHelpCenterDto updatedHelpCenterDto)
        {
            try
            {
                var success = await _helpCenterService.UpdateHelpCenterAsync(id, updatedHelpCenterDto);
                if (success)
                {
                    return Ok("Help center updated successfull!");
                }
                else
                {
                    return NotFound();
                }
            }
            catch (Exception ex)
            {
                return StatusCode(500, new { Message = "Failed to update help center entry." });
            }
        }
        [HttpDelete]
        [Route("delete/{id}")]
        //[AuthorizePermission("Add/Update/Delete-HelpCenter")]
        public async Task<IActionResult> DeleteHelpCenter(Guid id)
        {
            try
            {
                var success = await _helpCenterService.DeleteHelpCenterAsync(id);
                if (success)
                {
                    return Ok("Help center Deleted successfull!");
                }
                else
                {
                    return NotFound();
                }
            }
            catch (Exception ex)
            {
                return StatusCode(500, new { Message = "Failed to delete help center entry." });
            }
        }
    }
}
