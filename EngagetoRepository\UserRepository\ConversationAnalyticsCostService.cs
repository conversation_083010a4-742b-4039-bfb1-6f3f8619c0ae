﻿using EngagetoContracts.UserContracts;
using EngagetoEntities.DdContext;
using EngagetoEntities.Entities;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Logging;

namespace EngagetoRepository.UserRepository
{
    public class ConversationAnalyticsCostService : IConversationAnalyticsService
    {
        private readonly ILogger<ConversationAnalyticsCostService> _logger;
        private readonly ApplicationDbContext _dbContext;
        private int batchLimit = 50;
        public ConversationAnalyticsCostService(ILogger<ConversationAnalyticsCostService> logger, ApplicationDbContext context)
        {
            _logger = logger;
            _dbContext = context;
        }
        public async Task<List<BusinessDetailsMeta>> GetAllBusinessMetaAccountsAsync()
        {
            return await _dbContext.BusinessDetailsMetas.ToListAsync();
        }

        public Task<List<ConversationAnalyticsEntity>> GetAnalyticsDetailsByCompanyIdAsync(string companyId)
        {
            return _dbContext.ConversationAnalyticsEntities.Where(x => x.Equals(companyId) && !x.IsDeleted).ToListAsync();
        }

        public async Task<List<ConversationAnalyticsEntity>> GetAnalyticsDetailsByStartAndEndDateAsync(string companyId, DateTime startDate, DateTime endDate)
        {
            return await _dbContext.ConversationAnalyticsEntities
                .Where(x => x.CompanyId == companyId && x.StartDate.Date >= startDate.Date && x.EndDate.Date <= endDate.Date.Date)
                .ToListAsync();
        }

        public async Task<List<ConversationAnalyticsEntity>> GetConversationAnalyticsByIdAsync(Guid id)
        {
            var conversationAnalytics = await _dbContext.ConversationAnalyticsEntities.FirstOrDefaultAsync(x => x.Id == id);
            if (conversationAnalytics == null)
            {
                throw new Exception("Id is Invalid");
            }
            return await _dbContext.ConversationAnalyticsEntities
                 .Where(x => x.ConversationCategory == conversationAnalytics.ConversationCategory &&
                             x.StartDate.Date == conversationAnalytics.StartDate.Date)
                 .ToListAsync();

        }

        public async Task<bool> SaveConversationAnalyticsDetailsAsync(List<ConversationAnalyticsEntity> conversationAnalyticsDto)
        {
            int batchCount = 0;
            while (conversationAnalyticsDto.Skip(batchCount * batchLimit).Take(batchLimit).Count() > 0)
            {
                await _dbContext.AddRangeAsync(conversationAnalyticsDto.Skip(batchCount * batchLimit).Take(batchLimit));
                batchCount++;
            }
            _dbContext.SaveChanges();
            return true;
        }
    }
}
