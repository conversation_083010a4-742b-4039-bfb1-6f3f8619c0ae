﻿using System;
using System.Collections.Generic;

namespace EngagetoDatabase.WhatsAppBusinessDatabase.Models;

public partial class UserRole
{
    public Guid Id { get; set; }

    public Guid RoleId { get; set; }

    public Guid? AhexCrmUsersId { get; set; }

    public int? RoleSno { get; set; }

    public virtual User? AhexCrmUsers { get; set; }

    public virtual Role? RoleSnoNavigation { get; set; }
}
