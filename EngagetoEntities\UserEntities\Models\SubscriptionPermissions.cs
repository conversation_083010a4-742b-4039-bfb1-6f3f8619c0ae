﻿using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations.Schema;
using System.ComponentModel.DataAnnotations;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using EngagetoEntities.Entities;

namespace EngagetoEntities.UserEntities.Models
{
    [Table("SubscriptionPermissions")]
    public class SubscriptionPermissions
    {    
            [Key]
            [DatabaseGenerated(DatabaseGeneratedOption.Identity)]
            public int Id { get; set; }
            public int PlanId { get; set; }
            public int MenuId { get; set; }
            public bool isActive { get; set; }

            [ForeignKey("PlanId")]
            public PlanEntities PlanEntities { get; set; }
            [ForeignKey("MenuId")]
            public MenuDetails MenuDetails { get; set; }
        }
        public class SubscriptionPermissionsDto
        {
            public int PlanId { get; set; }
            public int MenuId { get; set; }
            public bool isActive { get; set; }

        }
    }

