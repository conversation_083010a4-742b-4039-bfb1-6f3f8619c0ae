﻿using Dapper;
using EngagetoDapper.Data.Connections;
using EngagetoDapper.Data.Interfaces.IUserInterfaces.IUserRepositories;
using EngagetoEntities.Dtos;
using EngagetoEntities.UserEntities.Dtos;
using PhoneNumbers;
using System.ComponentModel.Design;
using System.Data;

namespace EngagetoDapper.Data.Dapper.Repositories.UserRepositories
{
    public class UserRepository : IUserRepository
    {
        private readonly IUnitOfWork _unitOfWork;
        public UserRepository(IUnitOfWork unitOfWork)
        {
            _unitOfWork = unitOfWork;
        }
        public async Task<IEnumerable<MenuDetailsDto>> GetMenuDetailsAsync(string companyId, string roleId)
        {
            var param = new
            {
                CompanyId = companyId,
                RoleId = roleId
            };
            return await _unitOfWork.Connection.QueryAsync<MenuDetailsDto>("Menu_Get",
                param,
                _unitOfWork.Transaction,
                commandType: CommandType.StoredProcedure);
        }
        public async Task<IEnumerable<MenuPermissionDto>> GetPermissionMenuAsync(string companyId, string roleId)
        {
            var param = new
            {
                CompanyId = companyId,
                RoleId = roleId
            };
            return await _unitOfWork.Connection.QueryAsync<MenuPermissionDto>("PermissionMenuHierarchy_Get",
                param,
                _unitOfWork.Transaction,
                commandType: CommandType.StoredProcedure);
        }
        public async Task<IEnumerable<T>> GetCompanyWalletTransactionsHistoryAsync<T>(string companyId)
        {
            var sqlQuery = @"SELECT t.* 
                            FROM UserWalletEntities u 
                            INNER JOIN TransactionHistoryEntities t ON
	                            t.CompanyId = u.CompanyId
	                            AND t.WalletId = u.WalletId
                            WHERE u.CompanyId = @CompanyId
                            AND [Status] = @Status";

            var param = new
            {
                CompanyId = companyId,
                Status = "paid"
            };
            return await _unitOfWork.Connection.QueryAsync<T>(sqlQuery, param);
        }
        public async Task<IEnumerable<T>> GetMetaAccountAsync<T>(string companyId, Guid? userId = null)
        {
            var sqlQuery = @"SELECT TOP(1) bdm.* 
                            FROM BusinessDetails_Metas bdm 
                            INNER JOIN BusinessDetails b ON 
                                b.Id = bdm.BusinessId 
                            INNER JOIN Users u ON
                                u.CompanyId = b.Id
                            WHERE bdm.BusinessId = @CompanyId
                                AND (@UserId Is NULL OR u.Id = @UserId)";

            var param = new
            {
                CompanyId = companyId,
                UserId = userId
            };
            return await _unitOfWork.Connection.QueryAsync<T>(sqlQuery, param);
        }

        public async Task<IEnumerable<T>> GetInboxContactsAsync<T>(string companyId, Guid userId, string businessPhoneNumber, CancellationToken cancellationToken)
        {
            try
            {
                var param = new
                {
                    BusinessId = companyId,
                    UserId = userId,
                    BusinessPhoneNumber = businessPhoneNumber
                };

                if (cancellationToken.IsCancellationRequested)
                    cancellationToken.ThrowIfCancellationRequested();

                return await _unitOfWork.Connection.QueryAsync<T>(
                    "GetContactDetails",
                    param,
                    _unitOfWork.Transaction,
                    commandType: CommandType.StoredProcedure);
            }
            catch (Exception ex)
            {
                throw new Exception(ex.Message);
            }
        }

        public async Task<IEnumerable<T>> GetExpectedWalletBalanceAsync<T>(string companyId)
        {
            string sql = "SELECT * FROM ExpectedBalanceWalletBalance(@CompanyId)";
            var parameters = new { CompanyId = companyId };
            var result = await _unitOfWork.Connection.QueryAsync<T>(sql, parameters);
            return result;
        }

        public async Task<IEnumerable<T>> GetAgentsAsync<T>(string companyId,bool? status = true)
        {
            var sqlQuery = @"SELECT Id,
	                            CompanyId,
	                            [Name],
	                            Designation,
	                            EmailAddress,
	                            RoleId,
	                            CountryCode,
	                            PhoneNumber,
	                            CountryName
                            FROM Users
                            WHERE LOWER(CompanyId) = @CompanyId
	                            AND (@Status IS NULL OR [Status] = @Status)";

            var param = new
            {
                CompanyId = companyId,
                Status = status
            };
            return await _unitOfWork.Connection.QueryAsync<T>(sqlQuery, param);
        }

        public async Task<T?> GetSubscriptionDetailsAsync<T>(string companyId)
        {
            var sqlQuery = @"SELECT s.Id,
	                            s.userId,
	                            s.CompanyId,
	                            s.PlanId,
	                            s.DurationType,
	                            p.PlanName,
	                            CASE 
		                            WHEN CAST(COALESCE(s.RenewEndDate,PlanEndDate,GETUTCDATE()) AS DATE) >= GETUTCDATE() THEN CAST(1 AS bit)
		                            ELSE 0
	                            END IsActive
                            FROM Subscriptions s 
                            INNER JOIN PlanEntities p ON
	                            p.Id = s.PlanId
                            where CompanyId = @CompanyId";

            var param = new
            {
                CompanyId = companyId,
            };
            return await _unitOfWork.Connection.QueryFirstOrDefaultAsync<T>(sqlQuery, param);
        }

        public async Task<IEnumerable<T>> GetExpiringOrExpiredSubscriptionsAsync<T>()
        {
            var sqlQuery = @"SELECT 
                                b.Id AS BusinessId,
                                b.BusinessName AS BusinessName,
                                COALESCE(b.BusinessEmail, b.GrievanceOfficerEmail, b.CustomerCareEmail) AS Email,
                                b.CountryCode,
                                b.PhoneNumber,
                                s.Id AS SubscriptionId,
                                s.PlanId,
                                p.PlanName,
                                COALESCE(s.RenewEndDate, s.PlanEndDate) AS PlanEndDate,
                                s.DurationType
                            FROM BusinessDetails b 
                            INNER JOIN Subscriptions s ON s.CompanyId = b.Id
                            INNER JOIN PlanEntities p ON p.Id = s.PlanId
                            WHERE 
                                -- Filter subscriptions that will expire in two days or have expired in the last two days
                                CAST(COALESCE(s.RenewEndDate, s.PlanEndDate) AS DATE) BETWEEN CAST(DATEADD(DAY, -2, GETUTCDATE()) AS DATE) AND CAST(DATEADD(DAY, 2, GETUTCDATE()) AS DATE)";

            return await _unitOfWork.Connection.QueryAsync<T>(sqlQuery);
        }
        public async Task<IEnumerable<T>> GetUsersByBusinessIdAync<T>(string? businessId, string? waPhoneNumberId)
        {
            var sqlQuery = @"
                    SELECT u.Id,CAST(b.Id AS NVARCHAR(100)) AS BusinessId, u.[Name], u.EmailAddress, u.CountryCode, u.PhoneNumber, 
                           u.CountryName, u.[Address], u.About, u.Designation, u.[Status], 
                           u.Image, u.CreationDate
                    FROM Users u
                    INNER JOIN BusinessDetails b ON u.CompanyId = b.Id
                    WHERE (@BusinessId IS NULL OR b.Id = @BusinessId)
                    AND (@PhoneNumberId IS NULL OR EXISTS (
                            SELECT 1 FROM BusinessDetails_Metas bm 
                            WHERE bm.BusinessId = b.Id 
                            AND bm.PhoneNumberID = @PhoneNumberId
                        ))";

            var param = new
            {
                @BusinessId = businessId,
                @PhoneNumberId = waPhoneNumberId
            };
            Console.WriteLine($"Executing SQL: {sqlQuery}");
            Console.WriteLine($"Parameters: {Newtonsoft.Json.JsonConvert.SerializeObject(param)}");
            return await _unitOfWork.Connection.QueryAsync<T>(sqlQuery, param);
        }

    }
}


