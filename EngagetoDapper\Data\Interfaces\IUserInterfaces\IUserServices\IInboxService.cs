﻿using EngagetoEntities.Dtos.ContactDtos;
using EngagetoEntities.Entities;
using EngagetoEntities.Enums;


namespace EngagetoDapper.Data.Interfaces.IUserInterfaces.IUserServices
{
    public interface IInboxService
    {
        Task<bool> SaveAndUpdateChatStatusAsync(Guid contactId, ChatStatus chatStatus, Guid? userId);
        Task<List<ChatStatusEntityDto>> GetChatStatusAsync(Guid businessId);
        Task<List<ChatStatusEntity>> GetChatStatusByContactIdAsync(Guid businessId);
        Task<int> UpdateChatStatusAsync(Guid businessId);
        Task<bool> SaveContactAssignmentAsync(string companyId, Guid contactId, Guid userId, Guid assignedToUserId);
        Task<IEnumerable<ContactAssignmentHistoryDto>> GetContactAssignmentsAsync(string? companyId, Guid? contactId);
        Task<IEnumerable<ContactAssignAndReassignHistoryDto>> GetContactAssignmentHistoriesAsync(string? companyId,
            Guid? contactId, DateTime fromDate, DateTime toDate, CancellationToken cancellationToken);
        Task<bool> CheckConversationSatus(string companyId, string phoneNumber, CancellationToken cancellationToken);
        Task UpdateErrorMessageAsync(Guid conversationId, CancellationToken cancellationToken);
        Task<List<EngagetoEntities.Entities.Conversations>?> GetConversationsAsync(Guid conversationId, CancellationToken cancellationToken);
        Task<WATemplateCategory> GetTemplateCategoryAsync(string referenceId);
        Task<bool> SaveConversationCostAsync(ConversationAnalyticsEntity conversation);
    }
}
