﻿using EngagetoDapper.Data.Interfaces.GenericInterfaces;
using EngagetoDapper.Data.Interfaces.IUserInterfaces.IUserServices;
using EngagetoEntities.Entities;
using EngagetoEntities.UserEntities.Dtos;
using EngagetoEntities.Utilities;
using Mapster;

namespace EngagetoDapper.Data.Dapper.Services.UserServices
{
    public class ResourcePermissionService : IResourcePermissionService
    {
        private readonly IGenericRepository _genericRepository;
        public ResourcePermissionService(IGenericRepository genericRepository)
        {
            _genericRepository = genericRepository;
        }

        public async Task<bool> AddResourcePermissionAsync(RequestResourcePermissionDto requestResourcePermissionDto, Guid userId)
        {
            try
            {
                var plans = (await _genericRepository.GetAllAsync<PlanEntities>())?.Where(x => x.Status) ?? new List<PlanEntities>();
                if (plans.Any())
                {
                    var resourcePermission = requestResourcePermissionDto.Adapt<ResourcePermissionEntity>();
                    var planResult = plans.FirstOrDefault(x => x.Id == requestResourcePermissionDto.PlanId)
                        ?? throw new Exception("Plan Id is not valid");

                    resourcePermission.Id = Guid.NewGuid();
                    resourcePermission.CreatedAt = StringHelper.GetIndianDateTime();
                    resourcePermission.CreatedBy = userId;
                    resourcePermission.UpdatedBy = userId;
                    var columns = StringHelper.GetPropertyNames<ResourcePermissionEntity>(false);
                    string tableName = StringHelper.GetTableName<ResourcePermissionEntity>();
                    var result = await _genericRepository
                        .InsertRecordsAsync(tableName, columns, new List<ResourcePermissionEntity> { resourcePermission });
                    return result;
                }
                return false;
            }
            catch (Exception e)
            {
                throw;
            }

        }
        public async Task<List<RequestResourcePermissionDto>> GetResourcePermissionsAsync()
        {
            try
            {
                var result = await _genericRepository
                    .GetByObjectAsync<ResourcePermissionEntity>(new Dictionary<string, object>()
                        {
                            { "Year", DateTime.Now.Year }
                        });
                if (result is not null && result.Any())
                    return result.Adapt<List<RequestResourcePermissionDto>>();

                result = await _genericRepository
                    .GetRecordByRequestFilter<ResourcePermissionEntity>(new List<Dtos.RequestFilterDto>()
                    {
                        new Dtos.RequestFilterDto("Year",DateTime.Now.Year,"<=")
                    });
                return result.Adapt<List<RequestResourcePermissionDto>>();
            }
            catch (Exception e)
            {
                throw;
            }

        }
    }
}
