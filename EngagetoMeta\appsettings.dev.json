{
  "ConnectionStrings": {
    "Connection": "Data Source=engageto.database.windows.net;Initial Catalog=qa-engageto;User Id=dbmasteruser;Password=************`H9hTJK5ojM)C=$C69,2;MultipleActiveResultSets=True;Encrypt=True;TrustServerCertificate=False;Connection Timeout=30;Max Pool Size=100;Min Pool Size=5;",
    "ServiceBus": "Endpoint=sb://lrb-background-jobs.servicebus.windows.net/;SharedAccessKeyName=RootManageSharedAccessKey;SharedAccessKey=ad6gNL9QvFB1n6EHGHp9aSrVqSznCqwEY+ASbDGg8pA="
  },
  "Logging": {
    "LogLevel": {
      "Default": "Information",
      "Microsoft.AspNetCore": "Warning"
    }
  },
  "MetaConfig": {
    "baseUrl": /*"https://localhost:7153/api/"*/ "https://connect.engagetoq.in/api/",
    "statusUpdate": "WAWebhookMessage/sent-message",
    "receiveMessage": "WAWebhookMessage/receive-message",
    "templateUpdate": "WAWebhookMessage/update-template",
    "common": "WAWebhookMessage/receive-WAmessage"
  },
  "WebhookConfig": {
    "VerifyToken": "12345"
  },
  "ServiceBus": {
    "QueueName": "engageto-meta-background-jobs"
  }
}
