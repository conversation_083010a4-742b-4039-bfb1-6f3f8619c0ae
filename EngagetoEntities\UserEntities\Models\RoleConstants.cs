﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace EngagetoEntities.UserEntities.Models
{
    public static class RoleConstants
    {
        public const string PlatformAdmin = "PlatformAdmin";
        public const string PlatformOwner = "PlatformOwner";
        public const string PlatformTeammate = "PlatformTeammate";
        public const string ClientAdmin = "ClientAdmin";
        public const string ClientOwner = "ClientOwner";
        public const string ClientTeamMate = "ClientTeamMate";
        public const string Admin = "Admin";
        public const string Owner = "Owner";
        public const string Teammate = "Teammate";

        public const string CompanyId = "99969011-7b1d-4c2d-92a6-fba9ca31a261";
        public const string link = "https://app.engageto.com";
    }
}
