﻿using EngagetoEntities.Extensions;
using System.ComponentModel.DataAnnotations;

namespace EngagetoEntities.Dtos.WebhookDtos
{
    public class MediaMessageDto
    {

        [Required(ErrorMessage = "Contact should Not be empty.")]


        public List<string> Contact { get; set; }

        [AllowedExtensions(ErrorMessage = "Invalid file."), Required]
        public string? MediaFile { get; set; }
        public string? MediaFileName { get; set; }
        public string? Caption { get; set; }
        public string? MessageId { get; set; }

    }
}
