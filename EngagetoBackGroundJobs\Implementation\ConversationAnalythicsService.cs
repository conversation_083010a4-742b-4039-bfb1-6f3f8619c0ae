﻿using AutoMapper;
using EngagetoBackGroundJobs.Helper;
using EngagetoBackGroundJobs.Interfaces;
using EngagetoContracts.MetaContracts;
using EngagetoContracts.UserContracts;
using EngagetoDapper.Data.Interfaces.CampaignInterfaces.ICampaignServices;
using EngagetoDapper.Utilities;
using EngagetoEntities.Entities;
using EngagetoEntities.Enums;
using EngagetoEntities.UserEntities.Dtos;
using Hangfire.Console;
using Hangfire.Server;
using Microsoft.Extensions.Logging;
using Newtonsoft.Json;

namespace EngagetoBackGroundJobs.Implementation
{
    public class ConversationAnalythicsService : IConversationAnalythicsService
    {
        private readonly ILogger<JobTaskService> _logger;
        private readonly IHttpService _httpService;
        private readonly EngagetoContracts.UserContracts.IConversationAnalyticsService _conversationAnalyticsService;
        private readonly EngagetoDapper.Data.Interfaces.IConversationAnalyticsInterfaces.IConversationAnalyticsService _analyticsService;
        private readonly IWalletService _walletService;
        private readonly IMapper _mapper;
        private readonly IConversationsService _conversationsService;
        private readonly EngagetoDapper.Data.Interfaces.IEmailInterfaces.IEmailService _mailService;
        private readonly EngagetoDapper.Data.Interfaces.IUserInterfaces.IUserServices.IUserService _userServices;
        private readonly IMetaCostInfoService _metaCostInfoService;

        public ConversationAnalythicsService(ILogger<JobTaskService> logger,
            IHttpService httpService,
            EngagetoContracts.UserContracts.IConversationAnalyticsService analyticsService,
            IWalletService walletService,
            IMapper mapper,
            EngagetoDapper.Data.Interfaces.IConversationAnalyticsInterfaces.IConversationAnalyticsService converstationAnalyticsService,
            IConversationsService conversationsService,
            EngagetoDapper.Data.Interfaces.IEmailInterfaces.IEmailService mailService,
            EngagetoDapper.Data.Interfaces.IUserInterfaces.IUserServices.IUserService userServices,
            IMetaCostInfoService metaCostInfoService)
        {
            _logger = logger;
            _httpService = httpService;
            _conversationAnalyticsService = analyticsService;
            _walletService = walletService;
            _mapper = mapper;
            _analyticsService = converstationAnalyticsService;
            _conversationsService = conversationsService;
            _mailService = mailService;
            _userServices = userServices;
            _metaCostInfoService = metaCostInfoService;
        }
        public async Task<bool> ProcessMetaConversationAnalyticsRequestAsync(PerformContext? context,
            BusinessDetailsMeta account,
            DateTime startDate,
            DateTime endDate,
            long startDateUnixtime,
            long endDateUnixtime)
        {
            var bussinessDetails = await _userServices.GetBusinessDetailsAsync(account.BusinessId);
            var jobRequest = await _analyticsService.GetConversationAnalyticsJobRequest(account.BusinessId, startDate);
            var wallet = await EnsureWalletExists(account);
            try
            {
                //List<ConversationAnalyticsPriceEntity> costs = new List<ConversationAnalyticsPriceEntity>();
                //check Missing request
                var beforeRequestDateTime = await _analyticsService.GetCurrentDayConversationAnalyticsJobRequestsAsync(account.BusinessId);
                var result = await SaveJobRequestAsync(context, account, startDate, endDate);
                if (beforeRequestDateTime != null && beforeRequestDateTime < startDate)
                {
                    startDate = beforeRequestDateTime ?? startDate;
                    startDateUnixtime = new DateTimeOffset(startDate, TimeSpan.Zero).ToUnixTimeSeconds();
                }
                //costs = await GetCosts(account.BusinessId);

                var subscriptionAndConversationCountDetails = await _conversationsService.GetConversationDetailsAsync(account.BusinessId);

                var campaignCost = await _userServices.GetCampaignCostAsync(account.BusinessId);
                var conversations = new List<ConversationAnalyticsEntity>();
                string text = $"started the pulling the data for ConversationAnalyticsCost: {account.BusinessId} start date : {startDate}, End Date : {endDate}, JOBID: {context.JobId}";
                HangfireConsoleWrite(context, text, ConsoleTextColor.Yellow);

                var conversationCategories = new List<string>()
                {
                    ConversationCategoriesEnum.MARKETING.ToString(),
                    ConversationCategoriesEnum.SERVICE.ToString(),
                    ConversationCategoriesEnum.UTILITY.ToString(),
                    ConversationCategoriesEnum.AUTHENTICATION.ToString()
                };
                var dimention = new List<string>() { "CONVERSATION_CATEGORY", "CONVERSATION_TYPE", "PHONE", "COUNTRY" };

                var url = $"https://graph.facebook.com/v21.0/{account.WhatsAppBusinessAccountID}/?fields=conversation_analytics.start({startDateUnixtime}).end({endDateUnixtime}).granularity({Enums.GranularityEnum.HALF_HOUR.ToString()}).conversation_categories({JsonConvert.SerializeObject(conversationCategories)}).dimensions({JsonConvert.SerializeObject(dimention)})";
                var headers = new Dictionary<string, string>() { { "Authorization", $"Bearer {account.Token}" } };
                var results = await _httpService.GetAsync<ConversationAnalyticsResponseDto>(url, headers);
                if (results != null && results?.ConversationAnalytics?.Data?.Count > 0
                    && (results?.ConversationAnalytics?.Data?.FirstOrDefault()?.DataPoints?.Any() ?? false))
                {
                    text = $"ConversationAnalytics Cost Found: {account.BusinessId} start date : {startDate}, End Date : {endDate}, JOBID: {context.JobId}: URL: {url}, count: {results?.ConversationAnalytics?.Data?.FirstOrDefault()?.DataPoints?.Sum(x => x.Conversation)}";
                    HangfireConsoleWrite(context, text, ConsoleTextColor.DarkGreen);
                    var conversationAnalytics = _mapper.Map<List<ConversationAnalyticsEntity>>(results?.ConversationAnalytics?.Data?.FirstOrDefault()?.DataPoints);

                    var conversationCategoriesData = conversationAnalytics
                        .OrderByDescending(x => x.StartDate)
                        .GroupBy(x => new { x.StartDate.Date, x.ConversationCategory, x.Country });

                    var l = JsonConvert.SerializeObject(conversationCategoriesData);
                    foreach (var conversation in conversationCategoriesData)
                    {
                        var cost = await _metaCostInfoService.GetMetaCostAsync(account.BusinessId, conversation.Key.Country, conversation.Key.ConversationCategory);
                        var totalCost = (conversation?.Sum(x => x.Conversation) * cost) ?? 0;
                        var currentWalletBalance = wallet.Balance - totalCost;
                        var isFreeCoversation = subscriptionAndConversationCountDetails
                            .FirstOrDefault(x => x.ConversationCategory == conversation.Key.ConversationCategory)?.IsFreeConversation ?? true;
                        if (conversation.Key.ConversationCategory == ConversationCategoriesEnum.SERVICE.ToString())
                        {
                            if (isFreeCoversation)
                            {
                                currentWalletBalance = wallet.Balance;
                                totalCost = 0;
                            }
                            else
                            {
                                wallet.Balance = currentWalletBalance;
                            }
                        }
                        else
                        {
                            wallet.Balance = currentWalletBalance;
                        }

                        wallet.ExpectedWalletBallance = wallet.Balance - campaignCost;

                        var coversationCount = conversation.Sum(x => x.Conversation);
                        conversations.Add(new ConversationAnalyticsEntity
                        {
                            Id = Guid.NewGuid(),
                            MobileNumber = conversation.OrderByDescending(x => x.StartDate)?.FirstOrDefault()?.MobileNumber,
                            ConversationType = conversation.FirstOrDefault()?.ConversationType,
                            Conversation = coversationCount,
                            Cost = totalCost,
                            CompanyId = account.BusinessId,
                            CurrentBalance = currentWalletBalance,
                            Country = conversation.Key.Country,
                            ConversationCategory = conversation.Key.ConversationCategory,
                            StartDate = conversation.OrderByDescending(x => x.StartDate).FirstOrDefault().StartDate,
                            EndDate = conversation.OrderByDescending(x => x.EndDate).FirstOrDefault().EndDate,
                            CreatedAt = DateTime.UtcNow
                        });
                    }
                }
                else
                {
                    wallet.ExpectedWalletBallance = wallet.Balance;
                    await _walletService.UpdateWalletAsync(wallet);
                    text = $"Not Found data for ConversationAnalyticsCost: {account.BusinessId} start date : {startDate}, End Date : {endDate}, JOBID: {context.JobId}: URL: {url}";
                    HangfireConsoleWrite(context, text, ConsoleTextColor.DarkYellow);
                }
                if (conversations.Count > 0)
                {
                    var converstationDetuctionAmount = WalletHelper.CalculateConversationAnalyticsCost(conversations);
                    //wallet.Balance -= converstationDetuctionAmount.Sum(y => y.Value);
                    await _analyticsService.SaveConversationAnalyticsDetailsAsync(conversations);
                    await _walletService.UpdateWalletAsync(wallet);
                    text = $"ConversationAnalytics Cost update successfully: {account.BusinessId} start date : {startDate}, End Date : {endDate}, JOBID: {context.JobId}";
                    HangfireConsoleWrite(context, text, ConsoleTextColor.DarkGreen);
                }
                if (jobRequest != null)
                {
                    jobRequest.IsCompleted = true;
                    jobRequest.ResponseMessage = JsonConvert.SerializeObject(conversations);
                    await _analyticsService.UpdateConversationAnalyticsRequestAsync(jobRequest);
                }
                return true;
            }
            catch (Exception ex)
            {
                wallet.ExpectedWalletBallance = wallet.Balance;
                await _walletService.UpdateWalletAsync(wallet);
                if (jobRequest != null)
                {
                    jobRequest.IsCompleted = false;
                    jobRequest.ErrorMessage = ex.Message;
                    await _analyticsService.UpdateConversationAnalyticsRequestAsync(jobRequest);
                }
                _logger.LogError($"Exception getting to pulling the GetConversationAnalyticsCost: {ex.Message}, {ex.StackTrace} JOBID : {context.JobId}");
                HangfireConsoleWrite(context, $"Exception getting to pulling the GetConversationAnalyticsCost: {ex.Message}, {ex.StackTrace} JOBID : {context.JobId}", ConsoleTextColor.Red);
                await SendErrorEmailAsync(bussinessDetails, context?.JobId.ToString(), startDate, endDate, ex);
                return false;
            }
        }

        #region Helper Method
        private async Task<bool> SaveJobRequestAsync(PerformContext console, BusinessDetailsMeta account, DateTime startDate, DateTime endDate)
        {
            try
            {
                return await _analyticsService.SaveConversationAnalyticsRequestAsync(new ConversationAnalyticsJobRequest
                {
                    CompanyId = account.BusinessId,
                    StartDate = startDate.AddDays(1),
                    EndDate = endDate.AddDays(1)
                });
            }
            catch (Exception ex)
            {
                HangfireConsoleWrite(console, $"Error saving job request for {account.BusinessId}: {ex.Message}", ConsoleTextColor.Red);
                return false;
            }
        }
        private async Task<List<ConversationAnalyticsPriceEntity>> GetCosts(string businessId)
        {
            var costs = await _analyticsService.GetConversationAnalyticsPriceByYear(businessId, DateTime.Now.Year);
            if (!costs.Any())
            {
                costs = await _analyticsService.GetDefaultConversationAnalyticsPrice();
            }
            return costs;
        }
        private async Task<UserWalletEntity> EnsureWalletExists(BusinessDetailsMeta account)
        {
            var wallet = await _walletService.GetWalletAsync(account.BusinessId);
            if (wallet.WalletId == Guid.Empty)
            {
                wallet = await _walletService.CreateWalletAsync(new UserWalletEntity
                {
                    UserId = Guid.Empty,
                    CompanyId = account.BusinessId,
                    CreatedAt = DateTime.Now,
                    UpdatedAt = DateTime.Now,
                    Balance = 0,
                    Currency = Currency.INR.ToString()
                });
            }
            return wallet;
        }
        public static void HangfireConsoleWrite(PerformContext console, string text, ConsoleTextColor consoleTextColor = null)
        {
            if (consoleTextColor != null)
            {
                console.SetTextColor(consoleTextColor);
            }

            console.WriteLine(text);

            if (consoleTextColor != null)
            {
                console.ResetTextColor();
            }
        }

        public async Task SendErrorEmailAsync(Ahex_CRM_BusinessDetails? businessDetails, string? jobId, DateTime? startDate, DateTime? endDate, Exception? ex)
        {
            await _mailService.SendEmailAsync(new EngagetoEntities.ServiceModels.EmailMessage()
            {
                To = new List<string>() { "<EMAIL>", "<EMAIL>" },
                Subject = $"Error: Meta Conversation Cost Deduction Issue for Client {businessDetails?.BusinessName ?? "Unknown"}",
                Body = $@"
                        <html>
                        <body>
                            <p>Dear Team,</p>
                            <p>An issue has been detected while processing the cost deduction for client <strong>{businessDetails?.BusinessName ?? "Unknown"}</strong>.</p>
                            <p>Details:</p>
                            <ul>
                                <li>Job ID: <strong>{jobId ?? "Not specified"}</strong></li>
                                <li>Error Message: {ex?.Message}</li>
                                <li>Stack Trace: {ex?.StackTrace}</li>
                                <li>Start Time: {StringHelpers.GetIndianDateTime(startDate ?? DateTime.MinValue).ToString("yyyy-MM-dd HH:mm:ss") ?? "Not available"}</li>
                                <li>End Time: {StringHelpers.GetIndianDateTime(endDate ?? DateTime.MinValue).ToString("yyyy-MM-dd HH:mm:ss") ?? "Not available"}</li>
                            </ul>
                            <p>Please take appropriate action to resolve this issue promptly.</p>
                            <p>Best regards,<br/>Your Hangfire JOB</p>
                        </body>
                        </html>"
            });
        }
        #endregion
    }
}
