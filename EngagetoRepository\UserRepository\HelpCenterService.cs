﻿using Microsoft.EntityFrameworkCore;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using EngagetoContracts.UserContracts;
using EngagetoEntities.UserEntities.Models;
using EngagetoEntities.Entities;

namespace EngagetoRepository.UserRepository
{
    public class HelpCenterService : IHelpCenterService
    {
        private readonly ApplicationDBContext _dbContext;

        public HelpCenterService(ApplicationDBContext dbContext)
        {
            _dbContext = dbContext;
        }

        public async Task<List<HelpCenterDto>> GetAllHelpItemsAsync()
        {
            try
            {
                var allHelpItems = await _dbContext.HelpCenters
                    .Select(item => new HelpCenterDto
                    {
                        Question = item.Question,
                        Answer = item.Answer
                    })
                    .ToListAsync();

                return allHelpItems;
            }
            catch (Exception ex)
            {
                throw new Exception("Error getting all help items.", ex);
            }
        }

        public async Task<List<HelpCenterDto>> SearchHelpItemsAsync(string searchTerm)
        {
            try
            {
                IQueryable<HelpCenter> query = _dbContext.HelpCenters;

                if (!string.IsNullOrEmpty(searchTerm))
                {
                    query = query.Where(item => item.Question.Contains(searchTerm) || item.Answer.Contains(searchTerm));
                }

                var searchResults = await query
                    .Select(item => new HelpCenterDto
                    {
                        Question = item.Question,
                        Answer = item.Answer
                    })
                    .ToListAsync();

                return searchResults;
            }
            catch (Exception ex)
            {
                throw new Exception("Error searching help items.", ex);
            }
        }
        public async Task<Guid> CreateHelpCenterAsync(Guid currentuserId, HelpCenterDto helpCenterDto)
        {
            try
            {

                string lowercaseQuestion = helpCenterDto.Question.ToLower();


                var existingEntry = await _dbContext.HelpCenters.FirstOrDefaultAsync(h => h.Question.ToLower() == lowercaseQuestion);


                if (existingEntry != null)
                {
                    throw new InvalidOperationException("An entry with the same question already exists.");
                }

                var entry = new HelpCenter
                {
                    Question = helpCenterDto.Question,
                    Answer = helpCenterDto.Answer,
                    CreatedAt = DateTime.Now,
                };

                _dbContext.HelpCenters.Add(entry);
                await _dbContext.SaveChangesAsync();

                return entry.Id;
            }
            catch (Exception ex)
            {

                Console.WriteLine($"An error occurred while creating a help center entry: {ex.Message}");


                throw;
            }
        }

        public async Task<bool> UpdateHelpCenterAsync(Guid entryId, UpdateHelpCenterDto updatedHelpCenterDto)
        {
            var entry =  _dbContext.HelpCenters?.FirstOrDefault(m=>m.Id==entryId);
            if (entry == null)
                return false;




            var existingEntry = _dbContext.HelpCenters?.Select(m => m.Question.ToLower()).ToList();

            existingEntry?.Remove(entry.Question.ToLower());
            if (existingEntry.Contains(updatedHelpCenterDto.Question.ToLower()))
            {
                return false;
            }
            if (!string.IsNullOrEmpty(updatedHelpCenterDto.Question))
            {
                entry.Question = updatedHelpCenterDto.Question;
            }

            if (!string.IsNullOrEmpty(updatedHelpCenterDto.Answer))
            {
                entry.Answer = updatedHelpCenterDto.Answer;
            }

            await _dbContext.SaveChangesAsync();

            return true;
        }
        public async Task<bool> DeleteHelpCenterAsync(Guid id)
        {
            var entry = await _dbContext.HelpCenters.FindAsync(id);
            if (entry == null)
                return false;

            _dbContext.HelpCenters.Remove(entry);
            await _dbContext.SaveChangesAsync();

            return true;
        }
    }
}
