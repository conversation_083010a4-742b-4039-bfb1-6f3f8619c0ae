﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using EngagetoEntities.Dtos.WalletDtos;
using EngagetoEntities.Entities;
using EngagetoEntities.UserEntities.Dtos;

namespace EngagetoContracts.UserContracts
{
    public interface IWalletService
    {
        public Task<UserWalletEntity> CreateWalletAsync(UserWalletEntity wallet);
        public Task<bool> DeleteWalletAsync(string companyId);
        public Task<UserWalletEntity?> GetWalletAsync(string companyId);
        public Task<UserWalletEntity> UpdateWalletAsync(UserWalletEntity walletEntity);
        public Task<WalletAndSubscription?> GetWalletAndSubscriptionPlanAsync(string CompanyId,Guid? userId = null);
        Task<TenantWalletDto?> GetTenantWalletAsync(string tenantId,DateTime fromDate,DateTime toDate);

    }
}
