﻿using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace EngagetoEntities.Entities
{
    [Table("Subscriptions")]
    public class Subscriptions
    {
        [Key]
        [DatabaseGenerated(DatabaseGeneratedOption.Identity)]
        public int Id { get; set; }
        public string? UserId { get; set; }
        public string? CompanyId { get; set; }
        //public string? BusinessId { get; set; }
        public int PlanId { get; set; }
        public DateTime PlanStartDate { get; set; }
        public DateTime PlanEndDate { get; set; }
        public string? DurationType { get; set; }
        public DateTime? RenewStartDate { get; set; }
        public DateTime? RenewEndDate { get; set; }
        public string? Status { get; set; }
        public string? CreatedBy { get; set; }
        public DateTime CreatedAt { get; set; }
        public string? UpdatedBy { get; set; }
        public DateTime? UpdatedAt { get; set; }

        public ICollection<PaymentWalletDetail>? PaymentWalletDetails { get; set; }

    }
    public class SubscriptionDeductionDto
    {
        public int Id { get; set; }
        public string UserId { get; set; }
        public string CompanyId { get; set; }
        public int PlanId { get; set; }
        public DateTime PlanStartDate { get; set; }
        public DateTime PlanEndDate { get; set; }
        public DateTime? RenewStartDate { get; set; }
        public DateTime? RenewEndDate { get; set; }
        public string Status { get; set; }
        public DateTime PaymentDate { get; set; }
        public decimal Cost { get; set; }
        public string PlanName { get; set; }
    }
    public class FileResponse
    {
        public string FileName { get; set; }
        public decimal Amount { get; set; }
        public byte[] FileContent { get; set; }
    }
    public class PaymentWalletDetailDto
    {
        public int Id { get; set; }
        public DateTime? UpdatedAt { get; set; }
        public string? Status { get; set; }
        public decimal? OrderAmount { get; set; }
        public string? Description { get; set; }
        public decimal? IGSTAmount { get; set; }
        public decimal? TotalAmount { get; set; }
    }
    public class OrderRequestModel
    {
        public decimal TotalAmount { get; set; }
        public decimal? IGSTAmount { get; set; }
        [Description("Plan Amount")]
        public decimal Amount { get; set; }
        public string? SubscriptionPlanId { get; set; }
        public string? DurationType { get; set; }
        public Guid? DiscountId { get; set; }
        public bool? IsUpgradePlan { get; set; } = false;
    }
    public class PaymentVerificationModel
    {
        public string? RazorpayOrderId { get; set; }
        public string? RazorpayPaymentId { get; set; }
        public string? RazorpaySignature { get; set; }
        public bool? IsUpgradePlan { get; set; } = false;
    }
}
