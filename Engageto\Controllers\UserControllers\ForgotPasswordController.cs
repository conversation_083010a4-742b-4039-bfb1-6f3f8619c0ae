﻿using EngagetoContracts.UserContracts;
using EngagetoEntities.Entities;
using EngagetoEntities.UserEntities.Models;
using Microsoft.AspNetCore.Mvc;
using Microsoft.EntityFrameworkCore;

namespace Engageto.Controllers.UserControllers
{
    [Route("api/[controller]")]
    [ApiController]
    public class ForgotPasswordController : ControllerBase
    {
        private readonly IForgotPasswordService _forgotPasswordService;
        private readonly ApplicationDBContext _context;

        public ForgotPasswordController(IForgotPasswordService forgotPasswordService, ApplicationDBContext dbcontext)
        {
            _forgotPasswordService = forgotPasswordService;
            _context = dbcontext;
        }
        [HttpPost("send-otp")]
        public async Task<IActionResult> SendOTPToken([FromBody] ForgotPasswordRequest request)
        {
            try
            {
                var result = await _forgotPasswordService.SendOTPTokenAsync(request.Email);

                if (result)
                {
                    return Ok(new { Message = "OTP sent successfully." });
                }
                else
                {
                    var accountUser = await _context.Ahex_CRM_Users.FirstOrDefaultAsync(u => u.EmailAddress == request.Email);
                    if (accountUser == null)
                    {
                        return BadRequest(new { Message = "Email not found in our records." });
                    }

                    return BadRequest(new { Message = "Failed to send OTP." });
                }
            }
            catch (Exception ex)
            {
                return StatusCode(500, new { Message = $"Internal Server Error: {ex.Message}" });
            }
        }


        [HttpPost("verify-otp")]
        public async Task<IActionResult> VerifyOTP([FromBody] VerifyOTPRequest request)
        {
            try
            {
                if (!ModelState.IsValid)
                {
                    return BadRequest("Invalid request data.");
                }

                bool isOTPValid = await _forgotPasswordService.VerifyOTPToken(request.OTP, request.email);

                if (isOTPValid)
                {
                    return Ok("OTP verified successfully.");
                }
                else
                {
                    return BadRequest("Invalid OTP.");
                }
            }
            catch (Exception ex)
            {

                return StatusCode(500, "An error occurred while verifying OTP.");
            }
        }

        [HttpPost("update-password")]
        public async Task<IActionResult> UpdatePassword([FromBody] VerifyOTPAndUpdatePasswordRequest model)
        {
            try
            {

                var httpContext = HttpContext;


                var passwordUpdated = await _forgotPasswordService.UpdatePassword(model.Email, model.NewPassword, model.ConfirmPassword, httpContext);

                if (passwordUpdated)
                {
                    return Ok(new { Message = "Password updated successfully." });
                }
                else
                {
                    return BadRequest(new { Message = "Failed to update password. Passwords don't match or user not found." });
                }
            }
            catch (Exception ex)
            {

                return StatusCode(500, new { Message = $"An error occurred: {ex.Message}" });
            }
        }

    }
}
