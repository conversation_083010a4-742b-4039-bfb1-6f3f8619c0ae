﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace EngagetoEntities.Utilities
{
    public static class LeadratApiUrls
    {
        public readonly static string DevUrl = "https://connect.leadrat.info/api"; // QA
        public readonly static string ProductionUrl = "https://connect.leadrat.com/api";    // Production
        public static void SetEnvironment(bool isDevUrl)
        {
            BaseUrl = isDevUrl ? DevUrl : ProductionUrl;
        }
        public static string BaseUrl { get; private set; } = DevUrl;
        public static string GetLeadApiUrl(string phoneNumber, string countryCode) => $"{BaseUrl}/v1/lead/contactinfo?ContactNo={phoneNumber}&CountryCode={countryCode}";
        public static string GetUserDetailsApiUrl(Guid Id) => $"{BaseUrl}/v1/user/{Id}";
    }
}

