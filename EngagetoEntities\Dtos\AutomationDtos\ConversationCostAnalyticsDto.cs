﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace EngagetoEntities.Dtos.AutomationDtos
{
    public class TenantCCostAnalyticsDto: BaseCostAnalyticsDto
    {
        public string TenantId { get; set; }
    }
    public class BaseCostAnalyticsDto
    {
        public int FreeConversations { get; set; }
        public int PaidConversation { get; set; }
        public int UtilityConversation { get; set; }
        public int MarketingConversation { get; set; }
        public int AuthenticationConversation { get; set; }
        public decimal Cost { get; set; }
    }
}
