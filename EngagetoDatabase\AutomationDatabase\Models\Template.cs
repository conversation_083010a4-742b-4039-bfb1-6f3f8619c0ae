﻿using EngagetoEntities.Enums;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace EngagetoDatabase.AutomationDataBase.Models;

public partial class Template
{
    [Key]
    [DatabaseGenerated(DatabaseGeneratedOption.Identity)]
    public Guid TemplateId { get; set; }

    public Guid? UserId { get; set; }

    public string BusinessId { get; set; } = null!;

    public string? MetaId { get; set; }

    public string TemplateName { get; set; } = null!;

    public int? Category { get; set; }

    public string? SubCategory { get; set; } 

    public int MediaType { get; set; }

    public string? MediaAwsUrl { get; set; }

    public string? Header { get; set; }

    public string LanguageCode { get; set; } = null!;

    public string Body { get; set; } = null!;

    public string? Footer { get; set; }

    public int? Status { get; set; }

    public string? Createdby { get; set; }

    public string? Updatedby { get; set; }

    public DateTime? CreatedDate { get; set; }

    public DateTime? UpdatedDate { get; set; }

    public TimeSpan? Delay { get; set; }

    public bool? Enabled { get; set; }

    public Feature? Feature { get; set; }
}
