﻿using Newtonsoft.Json;


namespace UserManagement.Dtos
{
    public class PaymentsDto
    {
        [JsonProperty("acquirer_data")]
        public AcquirerDataDto? AcquirerData { get; set; } = new();
        public decimal Amount { get; set; }
        [JsonProperty("amount_refunded")]
        public decimal? AmountRefunded { get; set; }
        [JsonProperty("amount_transferred")]
        public decimal? AmountTransferred { get; set; }
        public string? Bank { get; set; }
        public bool? Captured { get; set; }
        public CardDto? Card { get; set; }
        [JsonProperty("card_id")]
        public string? CardId { get; set; }
        public string? Contact { get; set; }
        [JsonProperty("created_at")]
        public int? CreatedAt { get; set; }
        public string? Currency { get; set; }
        public string? Description { get; set; }
        public string? Email { get; set; }
        public string? Entity { get; set; }
        [JsonProperty("error_code ")]
        public string? ErrorCode { get; set; }
        [JsonProperty("error_description")]
        public string? ErrorDescription { get; set; }
        [JsonProperty("error_reason")]
        public string? ErrorReason { get; set; }
        [JsonProperty("error_source")]
        public string? ErrorSource { get; set; }
        [JsonProperty("error_step")]
        public string? ErrorStep { get; set; }
        public string? Fee { get; set; }
        public string? Id { get; set; }
        public bool? International { get; set; }
        [JsonProperty("invoice_id")]
        public string? InvoiceId { get; set; }
        public string? Method { get; set; }
        public List<string>? Notes { get; set; }
        [JsonProperty("order_id")]
        public string? OrderId { get; set; }
        [JsonProperty("RefundStatus")]
        public string? RefundStatus { get; set; }
        public string? Status { get; set; }
        public string? Tax { get; set; }
        [JsonProperty("token_id")]
        public string? TokenId { get; set; }
        public string? Vpa { get; set; }
        public string? Wallet { get; set; }
    }
    public class AcquirerDataDto
    {
        [JsonProperty("auth_code")]
        public string? AuthCode { get; set; }
        public string? Rrn { get; set; }
        [JsonProperty("bank_transaction_id ")]
        public string? BankTransactionId { get; set; }
    }

    public class CardDto
    {
        public bool? Emi { get; set; }
        public string? Entity { get; set; }
        public string? Id { get; set; }
        public string? Iin { get; set; }
        public bool? International { get; set; }
        public string? Issuer { get; set; }
        public string? Last4 { get; set; }
        public string? Name { get; set; }
        public string? Network { get; set; }
        [JsonProperty("sub_type")]
        public string? SubType { get; set; }
        public string? Type { get; set; }
    }
    public class PaymentDto
    {
        public PaymentsDto? Entity { get; set; } = new();
    }

    public class PayloadDto
    {
        public PaymentDto? Payment { get; set; } = new();
    }

    public class EventDto
    {
        public string? Entity { get; set; }
        [JsonProperty("account_id")]
        public string? AccountId { get; set; }
        [JsonProperty("event")]
        public string? Event1 { get; set; }
        public List<string>? Contains { get; set; } = new List<string>();
        public PayloadDto? Payload { get; set; } = new();
        [JsonProperty("created_at")]
        public int? CreatedAt { get; set; }
    }
}
