﻿namespace EngagetoEntities.UserEntities.Dtos
{
    public class HealthStatusDto
    {
        public Guid BusinessId { get; set; }
        public string OverAllStatus { get; set; }
        public List<HealthEntityDto> Entities { get; set; } = new();
    }
    public class HealthEntityDto
    {
        public string MetaEntityType { get; set; }
        public string MetaAccountStatus { get; set; }
        public string? ErrorMessage { get; set; }
        public string? PossibleSolution { get; set; }
        public HealthEntityDto() { }
        public HealthEntityDto(string metaEntityType, string metaAccountStatus, string? errorMessage,string? possibleSolution)
        {
            MetaEntityType = metaEntityType;
            MetaAccountStatus = metaAccountStatus;
            ErrorMessage = errorMessage;
            PossibleSolution = possibleSolution;
        }
    }
}
