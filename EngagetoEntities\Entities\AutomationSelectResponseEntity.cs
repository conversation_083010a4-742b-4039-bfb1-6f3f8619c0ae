﻿using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace EngagetoEntities.Entities
{
    [Table("AutomationSelectResponseEntities")]
    public class AutomationSelectResponseEntity : BaseEntity
    {
        [Key]
        public Guid Id { get; set; }
        public string SelectResponse { get; set; }
        public Guid? CompanyId { get; set; }
    }
}
