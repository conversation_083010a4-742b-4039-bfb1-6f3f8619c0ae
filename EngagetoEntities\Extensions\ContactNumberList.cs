﻿using System.ComponentModel.DataAnnotations;
using System.Text.RegularExpressions;

namespace EngagetoEntities.Extensions
{
    public class ContactNumberList : ValidationAttribute
    {
        public override bool IsValid(object value)
        {
            var contacts = value as List<string>;
            if (contacts == null || contacts.Count == 0)
                return true;

            // Regular expression for validating phone numbers (you can adjust the pattern as needed)
            var regex = new Regex(@"^\+?[1-9]\d{1,14}$");

            return contacts.All(contact =>
            {
                var cleanedContact = CleanPhoneNumber(contact);
                return regex.IsMatch(cleanedContact);
            });
        }

        private string CleanPhoneNumber(string phoneNumber)
        {
            // Remove all non-digit characters
            return new string(phoneNumber.Where(char.IsDigit).ToArray());
        }
    }
}
