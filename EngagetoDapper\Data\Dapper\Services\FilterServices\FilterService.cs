﻿using EngagetoDapper.Data.Dtos;
using EngagetoDapper.Data.Interfaces.GenericInterfaces;
using EngagetoEntities.Dtos.FilterDtos;
using EngagetoEntities.Utilities;

namespace EngagetoDapper.Data.Dapper.Services.FilterServices
{
    public class FilterService : IFilterService
    {
        private readonly IGenericRepository _genericRepository;

        public FilterService(IGenericRepository genericRepository)
        {
            _genericRepository = genericRepository;
        }

        public async Task<IEnumerable<T>> FilterDataAsync<T>(string businessId, Guid? userId, FilterDto filter, string? tableName = null, int? page = null, int? pageSize = null)
        {
            var requestFilters = new List<RequestFilterDto>();
            var columns = StringHelper.GetPropertyNames<T>(false);

            tableName = tableName ?? StringHelper.GetTableName<T>();
            requestFilters.Add(new RequestFilterDto { Key = "BusinessId", Value = businessId });

            if (userId != null && userId != Guid.Empty)
            {
                requestFilters.Add(new RequestFilterDto { Key = "UserId", Value = userId });
            }
            if (filter?.Searching != null && !string.IsNullOrEmpty(filter?.Searching?.Value) && !string.IsNullOrEmpty(filter?.Searching?.Column))
            {
                var name = columns.FirstOrDefault(x => x.ToLowerInvariant().Trim() == filter?.Searching.Column.ToLowerInvariant().Replace(" ", ""));
                requestFilters.Add(new RequestFilterDto { Key = name, Value = filter.Searching.Value, Operator = "like" });
            }

            if (filter?.Sorting != null && !string.IsNullOrEmpty(filter.Sorting?.Column))
            {
                var name = columns.FirstOrDefault(x => x.ToLowerInvariant().Trim() == filter.Sorting.Column.ToLowerInvariant().Replace(" ", ""));
                string opr = filter.Sorting?.Order?.ToLowerInvariant().Replace(" ", "") == "asc" ? "asc" : "desc";
                requestFilters.Add(new RequestFilterDto { Key = name, Value = string.Empty, Operator = opr });
            }

            if (filter?.Filtering != null && !(string.IsNullOrEmpty(filter.Filtering?.FilterType)))
            {
                if (filter.Filtering?.Conditions != null && filter.Filtering.Conditions.Any())
                {
                    foreach (var condition in filter.Filtering.Conditions)
                    {
                        var columnName = columns.FirstOrDefault(x => x.Equals(condition.Column, StringComparison.OrdinalIgnoreCase));
                        if (string.IsNullOrEmpty(columnName) || string.IsNullOrEmpty(condition.Operator))
                            continue;

                        object value = condition.Value ?? string.Empty;
                        DateTime date = DateTime.MinValue;
                        if (DateTime.TryParse(condition.Value, out date))
                        {
                            value = date.ToString("o");
                        }
                        var requestFilter = new RequestFilterDto { Key = columnName, Value = value, LogicalOperator = filter.Filtering.FilterType.ToLowerInvariant() };
                        var baseDate = DateTime.MinValue;
                        switch (condition.Operator.ToLowerInvariant())
                        {
                            case "equal":
                            case "=":
                            case "eq":
                                requestFilter.Operator = "=";
                                break;
                            case "notequal":
                            case "<>":
                            case "!=":
                            case "ne":
                                requestFilter.Operator = "<>";
                                break;

                            case "greaterthan":
                            case ">":
                            case "after":
                                requestFilter.Operator = ">";
                                break;
                            case "lessthan":
                            case "<":
                            case "before":
                                requestFilter.Operator = "<";
                                break;
                            case "contains":
                                requestFilter.Operator = "like";
                                break;
                            case "startswith":
                                requestFilter.Operator = "starts";
                                break;
                            case "endswith":
                                requestFilter.Operator = "ends";
                                break;
                            case "last7days":
                                requestFilter.Operator = ">";
                                baseDate = date != DateTime.MinValue ? date : DateTime.UtcNow;
                                requestFilter.Value = baseDate.AddDays(-7).ToString("yyyy-MM-ddTHH:mm:ss");
                                break;
                            case "last14days":
                                requestFilter.Operator = ">";
                                 baseDate = date != DateTime.MinValue ? date : DateTime.UtcNow;
                                requestFilter.Value = baseDate.AddDays(-14).ToString("yyyy-MM-ddTHH:mm:ss");
                                break;
                            case "last30days":
                                requestFilter.Operator = ">";
                                 baseDate = date != DateTime.MinValue ? date : DateTime.UtcNow;
                                requestFilter.Value = baseDate.AddDays(-30).ToString("yyyy-MM-ddTHH:mm:ss");
                                break;
                            case "next7days":
                                requestFilter.Operator = "<";
                                baseDate = date != DateTime.MinValue ? date : DateTime.UtcNow;
                                requestFilter.Value = baseDate.AddDays(7).ToString("yyyy-MM-ddTHH:mm:ss");
                                break;
                            case "next14days":
                                requestFilter.Operator = "<";
                                baseDate = date != DateTime.MinValue ? date : DateTime.UtcNow;
                                requestFilter.Value = baseDate.AddDays(14).ToString("yyyy-MM-ddTHH:mm:ss");
                                break;
                            case "next30days":
                                requestFilter.Operator = "<";
                                baseDate = date != DateTime.MinValue ? date : DateTime.UtcNow;
                                requestFilter.Value = baseDate.AddDays(30).ToString("yyyy-MM-ddTHH:mm:ss");
                                break;
                            case "allschedule":
                                requestFilter.Operator = ">";
                                baseDate = date != DateTime.MinValue ? date : DateTime.UtcNow;
                                requestFilter.Value = baseDate.ToString("yyyy-MM-ddTHH:mm:ss");
                                break;
                            case "all":
                                requestFilter.Operator = "<";
                                baseDate = date != DateTime.MinValue ? date : DateTime.UtcNow;
                                requestFilter.Value = baseDate.AddDays(1).ToString("yyyy-MM-ddTHH:mm:ss");
                                break;
                        }
                        requestFilters.Add(requestFilter);
                    }
                }
            }
            return await _genericRepository.GetRecordByRequestFilter<T>(requestFilters, tableName, page, pageSize);
        }
    }
}
