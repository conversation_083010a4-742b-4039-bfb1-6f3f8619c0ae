﻿using AutoMapper;
using EngagetoContracts.UserContracts;
using EngagetoEntities.Response;
using EngagetoEntities.Settings;
using EngagetoEntities.UserEntities.Dtos;
using EngagetoEntities.Utilities;
using iTextSharp.text.pdf;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using Microsoft.CodeAnalysis;
using Microsoft.Extensions.Options;
using System.Data;
using System.Security.Claims;

namespace Engageto.Controllers.UserControllers
{
    [Route("api/[controller]")]
    [ApiController]
    public class ConversationAnalyticsController : ControllerBase
    {
        private readonly IConversationAnalyticsService _analyticsService;
        private readonly IAccountDetailsService _accountDetailsService;
        private readonly IWalletService _walletService;
        private readonly ICompanyDetailsService _companyDetailsService;
        private readonly EngagetoDapper.Data.Interfaces.IConversationAnalyticsInterfaces.IConversationAnalyticsService _conversationAnalyticsService;
        private readonly ILogger<ConversationAnalyticsController> _logger;
        private readonly IMapper _mapper;
        private readonly EngagetoDapper.Data.Interfaces.IUserInterfaces.IUserServices.IUserService _userService;
        private readonly TemplateFiles _templateFiles;
        public ConversationAnalyticsController(IConversationAnalyticsService analyticsService,
            IAccountDetailsService accountDetailsService,
            IWalletService walletService,
            ICompanyDetailsService companyDetailsService,
            EngagetoDapper.Data.Interfaces.IConversationAnalyticsInterfaces.IConversationAnalyticsService conversationAnalyticsService,
            ILogger<ConversationAnalyticsController> logger,
            IMapper mapper,
            EngagetoDapper.Data.Interfaces.IUserInterfaces.IUserServices.IUserService userService,
            IOptions<TemplateFiles> templateFiles
            )
        {
            _analyticsService = analyticsService;
            _accountDetailsService = accountDetailsService;
            _walletService = walletService;
            _companyDetailsService = companyDetailsService;
            _conversationAnalyticsService = conversationAnalyticsService;
            _logger = logger;
            _mapper = mapper;
            _userService = userService;
            _templateFiles = templateFiles.Value;
        }

        [HttpGet]
        [Route("NotificationCost")]
        [Authorize]
        public async Task<IActionResult> GetConverstationAnalyticsNotificationCost(DateTime startDate, DateTime endDate)
        {
            try
            {
                ConversationAnalyticsCostNotificationDto conversationAnalyticsNotificationCost = new ConversationAnalyticsCostNotificationDto();

                conversationAnalyticsNotificationCost.TotalCount = 0;
                conversationAnalyticsNotificationCost.Transactions = new List<WalletTransactionDto>();

                var currentUserIdClaim = User.Claims.FirstOrDefault(c => c.Type == ClaimTypes.Name);

                if (currentUserIdClaim == null || !Guid.TryParse(currentUserIdClaim.Value, out Guid currentUserId))
                {
                    return BadRequest(new { Message = "Invalid current user." });
                }

                var userDetails = await _accountDetailsService.GetAccountDetails(currentUserId);
                Guid companyid = Guid.Empty;
                Guid.TryParse(userDetails.CompanyId, out companyid);
                var companyDetails = await _companyDetailsService.GetCompanyByIdAsync(companyid);
                //company wallet transactions
                var companyWallettransactions = await _userService.GetCompanyWalletTransactionsHistoryAsync(userDetails.CompanyId);

                if (endDate != StringHelper.ConvertIntoIndianDateTime(DateTime.UtcNow).Date)
                    endDate = endDate.AddDays(1);

                var conversationAnalyticCosts = await _conversationAnalyticsService.GetAnalyticsDetailsByStartAndEndDateAsync(userDetails.CompanyId, startDate, endDate);
                var groupedData = conversationAnalyticCosts
                .OrderBy(x => x.StartDate)
                .GroupBy(x => new { Date = x.StartDate.Date, x.ConversationCategory })
                .ToList();

                var transactions = new List<WalletTransactionDto>();

                if (groupedData.Count > 0)
                {
                    // Process each group and create transactions
                    for (int i = 0; i < groupedData.Count; i++)
                    {
                        var currentGroup = groupedData[i];
                        //var nextGroupStartDate = i + 1 < groupedData.Count ? groupedData[i + 1].Key.Date : (DateTime?)null;
                        var currentGroupStartDate = currentGroup.Key.Date;
                        var currentGroupEndDate = currentGroup.Max(x => x.EndDate);

                        var totalCost = currentGroup.Sum(x => x.Cost);
                        var totalConversations = currentGroup.Sum(x => x.Conversation);
                        var currentBalance = currentGroup.OrderByDescending(y => y.StartDate)?.FirstOrDefault()?.CurrentBalance ?? 0; // currentGroup.Min(x => x.CurrentBalance ?? 0); 

                        var wallteTrasactionDto = new WalletTransactionDto()
                        {
                            Id = currentGroup.Min(x => x.Id),
                            ConversationCount = totalConversations,
                            Cost = totalCost,
                            CurrentBalance = currentBalance,
                            Description = $"Balance Deducted for {currentGroup.Key.ConversationCategory} Conversations from {currentGroupStartDate:dd MMMM yyyy} to {currentGroupEndDate:dd MMMM yyyy}",
                            StartDate = currentGroupStartDate,
                            EndDate = currentGroupEndDate,
                            ConversationCategory = currentGroup.Key.ConversationCategory,
                            ConversationType = currentGroup.FirstOrDefault()?.ConversationType,
                        };
                        transactions.Add(wallteTrasactionDto);

                    }
                }
                return Ok(new ApiResponse<ConversationAnalyticsCostNotificationDto>()
                {
                    Success = true,
                    Message = "wallet transaction list",
                    Data = new ConversationAnalyticsCostNotificationDto
                    {
                        TotalCount = transactions.Count,
                        Transactions = transactions.OrderByDescending(x => x.StartDate).ToList(),
                        TransactionsHistory = companyWallettransactions?.OrderByDescending(x => x.CreatedAt).ToList()
                    }
                });
            }
            catch (Exception ex)
            {
                return StatusCode(500, new ApiResponse<string?>()
                {
                    Success = false,
                    Message = ex.Message,
                    Errors = ex.StackTrace
                });
            }

        }

        [HttpGet]
        [Route("DownloadWalletTransactionInvoice/{id}")]
        [Authorize]
        public async Task<IActionResult> CreateWalletTrasactionPdf(Guid id)
        {
            try
            {
                var currentUserIdClaim = User.Claims.FirstOrDefault(c => c.Type == ClaimTypes.Name);
                if (currentUserIdClaim == null || !Guid.TryParse(currentUserIdClaim.Value, out Guid currentUserId))
                {
                    throw new Exception("Invalid current user.");
                }
                var userDetails = await _accountDetailsService.GetAccountDetails(currentUserId);
                Guid companyid = Guid.Empty;
                Guid.TryParse(userDetails.CompanyId, out companyid);
                var businessDetails = await _companyDetailsService.GetCompanyByIdAsync(companyid);
                var walletTransactions = await _conversationAnalyticsService.GetConversationAnalyticsByIdAsync(id);
                if (walletTransactions.Count > 0)
                {
                    walletTransactions = await _conversationAnalyticsService
                        .GetAnalyticsDetailsByStartAndEndDateAsync(companyid.ToString(),
                        walletTransactions?.FirstOrDefault()?.StartDate.Date ?? DateTime.Now,
                        walletTransactions?.FirstOrDefault()?.EndDate.Date.AddDays(1) ?? DateTime.Now,
                        walletTransactions?.FirstOrDefault()?.ConversationCategory);

                    var walletTransaction = new WalletTransactionDto()
                    {
                        StartDate = walletTransactions?.FirstOrDefault()?.StartDate,
                        EndDate = walletTransactions?.FirstOrDefault()?.EndDate,
                        ConversationCategory = walletTransactions?.FirstOrDefault()?.ConversationCategory,
                        ConversationType = walletTransactions?.FirstOrDefault()?.ConversationType,
                        ConversationCount = walletTransactions?.Where(x => x.Cost > 0).Sum(x => x.Conversation) ?? 0,
                        Cost = walletTransactions?.Where(x => x.Cost > 0).Sum((x) => x.Cost) ?? 0
                    };
                    var filePath = _templateFiles.Wallet;
                    string mimeType = CommonHelper.GetMimeFileType(filePath);
                    using (MemoryStream memoryStream = new MemoryStream())
                    {
                        try
                        {
                            PdfReader pdfReader = new PdfReader(filePath);
                            using (PdfStamper pdfStamper = new PdfStamper(pdfReader, memoryStream))
                            {
                                AcroFields pdfFormFields = pdfStamper.AcroFields;
                                // Billing details
                                pdfFormFields.SetField("Company", businessDetails.CompanyLegalName);
                                pdfFormFields.SetField("Name", businessDetails.BusinessName);
                                pdfFormFields.SetField("A" +
                                    "" +
                                    "ddress", businessDetails.CompanyAddress);
                                pdfFormFields.SetField("Email", businessDetails.BusinessEmail);
                                // Wallet transaction details
                                pdfFormFields.SetField("SlNoRow1", "1");
                                pdfFormFields.SetField("CategoryRow1", walletTransaction.ConversationCategory);
                                pdfFormFields.SetField("TypeRow1", walletTransaction?.ConversationType?.ToString());
                                pdfFormFields.SetField("Start DateRow1", $"{walletTransaction?.StartDate: dd MMMM yyyy}");
                                pdfFormFields.SetField("End DateRow1", $"{walletTransaction?.EndDate: dd MMMM yyyy}");
                                pdfFormFields.SetField("CountRow1", walletTransaction?.ConversationCount.ToString());
                                pdfFormFields.SetField("CostRow1", walletTransaction?.Cost.ToString());
                                pdfStamper.FormFlattening = true;
                            }
                            pdfReader.Close();
                            byte[] buffer = CommonHelper.CompressPdf(memoryStream.ToArray());
                            return Ok(new ApiResponse<byte[]>()
                            {
                                Success = true,
                                Message = "stream data form invoice",
                                Data = buffer,
                                Errors = null
                            });
                        }
                        catch (Exception ex)
                        {
                            throw;
                        }
                    }
                }
                else
                {
                    throw new Exception("Wallet transaction details are not found");
                }
            }
            catch (Exception ex)
            {
                return StatusCode(500, new ApiResponse<string?>()
                {
                    Success = false,
                    Message = ex.Message,
                    Errors = ex.StackTrace
                });
            }
        }

        [HttpPost]
        [Route("AddConversationCost")]
        [AllowAnonymous]
        public async Task<IActionResult> AddConversationCost([FromForm] ConversationCostDto conversationCost)
        {
            try
            {
                var result = await _conversationAnalyticsService.SaveConversationCostAsync(conversationCost);
                if (result)
                    return Ok(new ApiResponse<string>()
                    {
                        Success = true,
                        Message = "Conversion cost added successfully.",
                        Data = null,
                        Errors = null
                    });
                else
                    return Ok(new ApiResponse<string>()
                    {
                        Success = false,
                        Message = "something went wrong to add conversion cost.",
                        Data = null,
                        Errors = null
                    });

            }
            catch (Exception ex)
            {
                return StatusCode(500, new ApiResponse<string?>()
                {
                    Success = false,
                    Message = ex.Message,
                    Errors = ex.StackTrace
                });
            }
        }
    }
}

