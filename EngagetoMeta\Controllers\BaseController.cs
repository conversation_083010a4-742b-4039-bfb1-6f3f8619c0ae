﻿using EngagetoMeta.Dtos;
using Microsoft.AspNetCore.Mvc;

namespace EngagetoMeta.Controllers
{
    [Route("api/[controller]")]
    [ApiController]
    public class BaseController : ControllerBase
    {
        protected ApiResponse<T> CreateResponse<T>(bool success, string message, T data)
        {
            return new ApiResponse<T>(success, message, data);
        }

        protected ApiResponse<T> CreateSuccessResponse<T>(T data, string message = null)
        {
            message = string.IsNullOrEmpty(message) ? "Operation was successful." : message;
            return new ApiResponse<T>(true, message, data);
        }
        protected ApiResponse<T> CreateSuccessResponse<T>(string message)
        {
            return new ApiResponse<T>(true, message, default);
        }
        protected ApiResponse<T> CreateErrorResponse<T>(string message, string? error)
        {
            return new ApiResponse<T>(false, message, default, error);
        }
    }
}
