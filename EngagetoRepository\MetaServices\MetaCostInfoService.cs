﻿using EngagetoContracts.MetaContracts;
using EngagetoContracts.Services;
using EngagetoEntities.DdContext;
using EngagetoEntities.Dtos.WhatsAppChargeDtos;
using EngagetoEntities.Entities;
using EngagetoEntities.Enums;
using Mapster;
using Microsoft.AspNetCore.Mvc;
using Microsoft.EntityFrameworkCore;
using System.ComponentModel.DataAnnotations;

namespace EngagetoRepository.MetaServices
{
    public class MetaCostInfoService : IMetaCostInfoService
    {
        private readonly ApplicationDbContext _contextDb;
        private readonly IUserIdentityService _userIdentityService;
        public MetaCostInfoService(ApplicationDbContext applicationDbContext, IUserIdentityService userIdentityService)
        {
            _contextDb = applicationDbContext;
            _userIdentityService = userIdentityService;
        }

        public async Task<bool> UpdateMetaMasterChargeAsync(Guid Id, MasterMetaPriceDtos masterMetaPriceDtos)
        {
            try
            {
                var status = 0;

                var masterMetaPrice = await _contextDb.MasterMetaPrice.FirstOrDefaultAsync(i => i.Id == Id && i.IsDeleted == false);
                if (masterMetaPrice != null)
                {
                    masterMetaPrice.Currency = masterMetaPriceDtos.Currency;
                    masterMetaPrice.Marketing = masterMetaPriceDtos.Marketing;
                    masterMetaPrice.Utility = masterMetaPriceDtos.Utility;
                    masterMetaPrice.Service = masterMetaPriceDtos.Service;
                    masterMetaPrice.Authentication = masterMetaPriceDtos.Authentication;
                    masterMetaPrice.AuthenticationOutSide = masterMetaPriceDtos.AuthenticationOutSide;
                    masterMetaPrice.CountryName = masterMetaPriceDtos.CountryName;
                    masterMetaPrice.CountryCode = masterMetaPriceDtos.CountryCode;
                    masterMetaPrice.UpdatedAt = DateTime.UtcNow;
                    masterMetaPrice.UpdatedBy = _userIdentityService.UserId;
                    _contextDb.MasterMetaPrice.Update(masterMetaPrice);
                    status = await _contextDb.SaveChangesAsync();
                }

                if (status > 0)
                {
                    return true;
                }
                else return false;
            }
            catch (Exception ex)
            {
                return false;
            }
        }

        public async Task<bool> DeleteMetaMasterChargeAsync(Guid Id)
        {

            var masterMetaPrice = await _contextDb.MasterMetaPrice.FirstOrDefaultAsync(m => m.Id == Id);
            if (masterMetaPrice == null)
            {
                return false;
            }
            masterMetaPrice.IsDeleted = true;
            masterMetaPrice.DeletedAt = DateTime.Now;
            masterMetaPrice.DeletedBy = _userIdentityService.UserId;
            await _contextDb.SaveChangesAsync();

            return true;
        }

        public async Task<bool> AddMetaCustomChargeAsync([FromBody] CustomMetapPriceDtos customMetapPriceDtos, [FromQuery, Required] string businessId)
        {
            bool isBusinessIdExists = await _contextDb.CustomMetaApiFeeInfo
                                             .AnyAsync(x => x.BusinessId == businessId);
            if (isBusinessIdExists)
            {
                throw new InvalidOperationException("BusinessId must be unique. This BusinessId already exists.");
            }
            var customMetaPrice = customMetapPriceDtos.Adapt<CustomMetaApiFeeInfo>();
            customMetaPrice.BusinessId = businessId;
            customMetaPrice.CreatedAt = DateTime.Now;
            customMetaPrice.CreatedBy = _userIdentityService.UserId;
            await _contextDb.CustomMetaApiFeeInfo.AddAsync(customMetaPrice);
            await _contextDb.SaveChangesAsync();
            return customMetaPrice != null;
        }

        public async Task<List<CustomMetapPriceDtos>> GetAllMetaCustomChargeAsync()
        {
            var customMetaPrices = await _contextDb.CustomMetaApiFeeInfo.Where(i => i.IsDeleted == false).ToListAsync();
            var customMetaPriceDtos = customMetaPrices.Adapt<List<CustomMetapPriceDtos>>();
            return customMetaPriceDtos;

        }
        public async Task<CustomMetapPriceDtos> GetMetaCustomChargeByIdAsync(Guid id)
        {

            var customMetaPrice = await _contextDb.CustomMetaApiFeeInfo.FirstOrDefaultAsync(i => i.Id == id && i.IsDeleted == false);
            var customMetaPriceDto = customMetaPrice.Adapt<CustomMetapPriceDtos>();
            return customMetaPriceDto;

        }

        public async Task<bool> UpdateMetaCustomChargeAsync(Guid Id, CustomMetapPriceDtos customMetaPriceDtos)
        {
            try
            {
                var status = 0;
                var customMetaPrice = await _contextDb.CustomMetaApiFeeInfo.FirstOrDefaultAsync(i => i.Id == Id && i.IsDeleted == false);

                if (customMetaPrice != null)
                {
                    customMetaPrice.Currency = customMetaPriceDtos.Currency;
                    customMetaPrice.MarketingFee = customMetaPriceDtos.MarketingFee;
                    customMetaPrice.OutSideCountryFee = customMetaPriceDtos.OutSideCountryFee;
                    customMetaPrice.UtilityFee = customMetaPriceDtos.UtilityFee;
                    customMetaPrice.ServiceFee = customMetaPriceDtos.ServiceFee;
                    customMetaPrice.AuthenticationFee = customMetaPriceDtos.AuthenticationFee;
                    customMetaPrice.AuthenticationOutSideFee = customMetaPriceDtos.AuthenticationOutSideFee;
                    customMetaPrice.MasterMetaPriceId = customMetaPriceDtos.MasterMetaPriceId;
                    customMetaPrice.UpdatedAt = DateTime.UtcNow;
                    customMetaPrice.UpdatedBy = _userIdentityService.UserId;
                    _contextDb.CustomMetaApiFeeInfo.Update(customMetaPrice);
                    status = await _contextDb.SaveChangesAsync();
                }
                if (status > 0)
                {
                    return true;
                }
                else return false;
            }
            catch (Exception ex)
            {
                return false;
            }

        }
        public async Task<bool> DeleteMetaCustomChargeAsync(Guid Id)
        {

            var customMetaPrice = await _contextDb.CustomMetaApiFeeInfo.FirstOrDefaultAsync(x => x.Id == Id);
            if (customMetaPrice == null)
            {
                return false;
            }
            customMetaPrice.IsDeleted = true;
            customMetaPrice.DeletedAt = DateTime.UtcNow;
            customMetaPrice.DeletedBy = _userIdentityService.UserId;
            await _contextDb.SaveChangesAsync();

            return true;
        }
        public async Task<decimal> GetCostAsync(string businessId, string phoneNumber, string categoryType)
        {
            bool isValidPhoneNumber = ValidateWithPhoneNumber(phoneNumber);
            if (!isValidPhoneNumber)
            {
                throw new InvalidOperationException("Invalid Mobile Number.");
            }

            var customMetaprice = await _contextDb.CustomMetaApiFeeInfo.FirstOrDefaultAsync(i => i.BusinessId == businessId);
            if (customMetaprice == null)
            {
                throw new InvalidOperationException($"custom meta price not found with this ID:");
            }
            var masterMetaPrice = await _contextDb.MasterMetaPrice.FirstOrDefaultAsync(i => i.Id == customMetaprice.MasterMetaPriceId);
            if (masterMetaPrice == null)
            {
                throw new InvalidOperationException($"Matser meta price not found with this Id: {customMetaprice.MasterMetaPriceId}");
            }

            string countryCode = masterMetaPrice.CountryCode;
            bool isoutSideNumber = IsOutSideNumber(phoneNumber, countryCode);
            if (!isoutSideNumber)
            {
                if (categoryType != null)
                {
                    switch (categoryType.ToLower().Trim())
                    {
                        case "utility":
                            decimal utilityCost = (decimal)(customMetaprice.UtilityFee + masterMetaPrice.Utility);
                            return utilityCost;
                        case "marketing":
                            decimal marketingCost = (decimal)(customMetaprice?.MarketingFee + masterMetaPrice.Marketing);
                            return marketingCost;
                        case "service":
                            decimal serviceCost = (decimal)(customMetaprice?.ServiceFee + masterMetaPrice.Service);
                            return serviceCost;
                        case "authentication":
                            decimal authenticationCost = (decimal)(customMetaprice?.AuthenticationFee + masterMetaPrice.Authentication);
                            return authenticationCost;
                        case "authenticationoutside":
                            decimal authenticationOutsideCost = (decimal)(customMetaprice.AuthenticationOutSideFee + masterMetaPrice.AuthenticationOutSide);
                            return authenticationOutsideCost;
                        default:
                            throw new InvalidOperationException($"Invalid category type: {categoryType}. Please provide a valid category type.");
                    }
                }
            }
            else
            {
                if (categoryType != null)
                {
                    switch (categoryType.ToLower().Trim())
                    {
                        case "utility":
                            decimal utilityCost = (decimal)(masterMetaPrice.Utility + customMetaprice.OutSideCountryFee);
                            return utilityCost;
                        case "marketing":
                            decimal marketingCost = (decimal)(masterMetaPrice.Marketing + customMetaprice.OutSideCountryFee);
                            return marketingCost;
                        case "service":
                            decimal serviceCost = (decimal)(masterMetaPrice.Service + customMetaprice.OutSideCountryFee);
                            return serviceCost;
                        case "authentication":
                            decimal authenticationCost = (decimal)(masterMetaPrice.Authentication + customMetaprice.OutSideCountryFee);
                            return authenticationCost;
                        case "authenticationoutside":
                            decimal authenticationOutsideCost = (decimal)(masterMetaPrice.AuthenticationOutSide + customMetaprice?.OutSideCountryFee);
                            return authenticationOutsideCost;
                        default:
                            throw new InvalidOperationException($"Invalid category type: {categoryType}. Please provide a valid category type.");
                    }
                }

            }
            return 0;
        }
        private bool ValidateWithPhoneNumber(string mobileNumber)
        {
            if (!string.IsNullOrEmpty(mobileNumber) && mobileNumber.StartsWith("+"))
            {
                mobileNumber = mobileNumber.Substring(1);
            }
            try
            {
                var phoneNumber = PhoneNumberHelper.GetCountryCodeAndPhoneNumber(mobileNumber);
                return true;
            }
            catch (ArgumentException ex)
            {
                return false;
            }
        }
        private bool IsOutSideNumber(string phoneNumber, string countryCode)
        {
            var countryCodeAndPhoneNumber = PhoneNumberHelper.GetCountryCodeAndPhoneNumber(phoneNumber, countryCode);
            var formattedCountryCode = $"+{countryCodeAndPhoneNumber.CountryCode.TrimStart('+')}";
            if (formattedCountryCode == countryCode)
            {
                return false;
            }
            return true;
        }
        private async Task<bool> ValidateCountryAsync(string countryName, string countryCode)
        {
            var country = await _contextDb.CountryDetails.FirstOrDefaultAsync(i => i.CountryName == countryName && i.CountryCode == countryCode);
            if (country == null)
            {
                return false;
            }
            return true;
        }

        public async Task<bool> AddMetaMasterChargeAsync(MasterMetaPriceDtos masterMetaPriceDtos)
        {
            if (!masterMetaPriceDtos.CountryCode.StartsWith("+"))
            {
                masterMetaPriceDtos.CountryCode = $"+{masterMetaPriceDtos.CountryCode}";
            }
            //bool isCountryExists = await ValidateCountryAsync(masterMetaPriceDtos.CountryName.ToLower().Trim(), masterMetaPriceDtos.CountryCode.Trim());

            //if (!isCountryExists)
            //{
            //    throw new InvalidOperationException($"A record with CountryName '{masterMetaPriceDtos.CountryName}' with CountryCode '{masterMetaPriceDtos.CountryCode}'  not  exists......");
            //}

            bool countryExists = await _contextDb.MasterMetaPrice.AnyAsync(x => x.CountryName == masterMetaPriceDtos.CountryName && x.CountryCode == masterMetaPriceDtos.CountryCode);
            if (countryExists)
            {
                throw new InvalidOperationException($"A record with CountryName '{masterMetaPriceDtos.CountryName.Trim()}' with CountryCode '{masterMetaPriceDtos.CountryCode.Trim()}' already exists.");
            }
            var masterMetaPrice = masterMetaPriceDtos.Adapt<MasterMetaPrice>();
            masterMetaPrice.CreatedAt = DateTime.UtcNow;
            masterMetaPrice.CreatedBy = _userIdentityService.UserId;
            await _contextDb.MasterMetaPrice.AddAsync(masterMetaPrice);
            await _contextDb.SaveChangesAsync();
            var masterMetaPricee = masterMetaPrice.Adapt<MasterMetaPrice>();
            if (masterMetaPricee != null)
            {

                return true;
            }
            else
            {
                return false;
            }
        }
        public async Task<List<MasterMetaPriceDtos>> GetAllMetaMasterChargeAsync()
        {
            var masterMetaPrices = await _contextDb.MasterMetaPrice.Where(i => i.IsDeleted == false).ToListAsync();
            var masterMetaPriceDtos = masterMetaPrices.Adapt<List<MasterMetaPriceDtos>>();
            return masterMetaPriceDtos;

        }
        public async Task<MasterMetaPriceDtos> GetMetaMasterChargeByIdAsync(Guid id)
        {
            var masterMetaPrice = await _contextDb.MasterMetaPrice.FirstOrDefaultAsync(i => i.Id == id && i.IsDeleted == false);
            var masterMetaPriceDto = masterMetaPrice.Adapt<MasterMetaPriceDtos>();
            return masterMetaPriceDto;
        }

        public async Task<decimal> GetMetaCostAsync(string businessId, string countryId, string category)
        {
            try
            {
                var masterCost = await _contextDb.MasterMetaPrice.FirstOrDefaultAsync(x => x.CountryName == countryId);
                var customMasterCost = await _contextDb.CustomMetaApiFeeInfo.FirstOrDefaultAsync(x => x.BusinessId == businessId);
                if (masterCost != null)
                {
                    if (Enum.TryParse(category, true, out ConversationCategoriesEnum categoryEnum))
                    {
                        switch (categoryEnum)
                        {
                            case ConversationCategoriesEnum.MARKETING:
                                var cost = (masterCost.Marketing + (customMasterCost?.MarketingFee ?? 0.12m));
                                return cost ?? 0.90m;
                            case ConversationCategoriesEnum.UTILITY:
                                cost = (masterCost.Utility + (customMasterCost?.UtilityFee ?? 0.24m));
                                return cost ?? 0.35m;
                            case ConversationCategoriesEnum.AUTHENTICATION:
                                cost = (masterCost.Authentication + (customMasterCost?.AuthenticationFee ?? 0.24m));
                                return cost ?? 0.35m;
                            case ConversationCategoriesEnum.SERVICE:
                                cost = (masterCost.Service + (customMasterCost?.ServiceFee ?? 0.10m));
                                return cost ?? 0.35m;
                        }
                    }
                    return decimal.Parse("0.35");
                }
                return (await _contextDb.MasterMetaPrice.FirstOrDefaultAsync())?.Marketing ?? 0.90m;
            }
            catch (Exception ex)
            {
                throw ex;
            }
        }
    }
}
