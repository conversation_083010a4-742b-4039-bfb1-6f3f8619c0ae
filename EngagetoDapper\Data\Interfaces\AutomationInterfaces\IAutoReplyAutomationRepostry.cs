﻿using EngagetoEntities.Dtos.AutomationDtos;
using EngagetoEntities.Entities;

namespace EngagetoDapper.Data.Interfaces.AutomationInterfaces
{
    public interface IAutoReplyAutomationRepostry
    {
        Task<T?> SaveAutoReplyAutomationAsync<T>(AutoReplyAutomationEntity autoReplyAutomation);
        Task<int> SaveVeriablesAsync(IEnumerable<VeriableEntity> veriables);
        Task<IEnumerable<T>> GetWorkflowAsync<T>(Guid? companyId, Guid? userId = null, Guid? workflowId = null, string? workflowName = null);
        Task<int> SoftDeleteAutoReplyMessageAsync(Guid companyId,Guid userId, Guid autoReplyId);
        Task<int> SoftDeleteAutoReplyWorkflowAsync(Guid companyId, Guid userId, Guid? workflowId, string? workflowName);
        Task<IEnumerable<T>> GetAutoReplyMessageByInputAsync<T>(Guid companyId, string input, string? inputvariations = null);
        Task<IEnumerable<T>> GetWorkflowNamesAsync<T>(Guid companyId);
        Task<bool> UpdateWorkflowNameAsync(WorkflowUpdatePayloadDto workflowUpdatePayload, Guid userId);
        Task<IEnumerable<WorkflowCustomerResponseDto>> GetWorkflowCustomerResponseAsync(Guid? contactId = null, string? workflowName = null, Guid? companyId = null, long? workflowStartId = null);
    }
}
