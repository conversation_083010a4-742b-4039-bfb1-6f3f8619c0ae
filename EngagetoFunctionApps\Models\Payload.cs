﻿namespace EngagetoFunctionApps.Models
{
    public class Payload
    {
        public string Type { get; set; }                // e.g., Campaign, Contact, etc.
        public string Id { get; set; }
        public string BusinessId { get; set; }          // ✅ Added
        public string? JsonPayload { get; set; }
        public DateTime? ScheduleDateTime { get; set; } // ✅ Added
        public int? Page { get; set; } = 1;             // Defaults to 1
        public int? PageSize { get; set; }
        public Payload() { }

        public Payload(
            string type,
            string id,
            string businessId,
            string? jsonPayload = null,
            DateTime? scheduleDateTime = null,
            int? page = null,
            int? pageSize = null)
        {
            Type = type;
            Id = id;
            BusinessId = businessId;
            JsonPayload = jsonPayload;
            ScheduleDateTime = scheduleDateTime;
            Page = page ?? 0;
            PageSize = pageSize;
        }
    }
}
