using System;
using System.Collections.Generic;
using System.Globalization;
using System.Linq;
using Microsoft.Extensions.Logging;
using Newtonsoft.Json.Linq;
using Newtonsoft.Json;
using EngagetoEntities.Dtos.TemplateDtos;
using EngagetoEntities.Entities;
using EngagetoEntities.Constants;

namespace EngagetoEntities.Utilities
{
    public static class LeadRatMapping
    {
        public static string ProcessLeadRatVariables(
            Dictionary<string, object> leadratDataDict,
            Dictionary<string, object> userDataDict,
            List<VariableModel> extractedVariableModels,
            string messageBody,
            ILogger logger = null)
        {
            try
            {
                var replacementValues = new List<string>();
                var processedMessage = messageBody;
                foreach (var variable in extractedVariableModels ?? new List<VariableModel>())
                {
                    var variableKey = variable.Variable;
                    var fallbackValue = variable.FallbackValue ?? variableKey;
                    string replacementValue = fallbackValue;


                    if (!string.IsNullOrEmpty(variableKey) && LeadRatVariableMapper.LeadVariableMapping.ContainsKey(variableKey))
                    {
                        var fieldPath = LeadRatVariableMapper.LeadVariableMapping[variableKey];
                        var value = GetValueFromApiData(leadratDataDict, fieldPath, variableKey, logger);

                        if (!string.IsNullOrEmpty(value))
                        {
                            replacementValue = value;
                        }
                    }
                    else if (!string.IsNullOrEmpty(variableKey) && LeadRatVariableMapper.UserDetailsMapping.ContainsKey(variableKey))
                    {
                        var fieldPath = LeadRatVariableMapper.UserDetailsMapping[variableKey];
                        var value = GetValueFromApiData(userDataDict, fieldPath, variableKey, logger);
                        if (!string.IsNullOrEmpty(value))
                        {
                            replacementValue = value;
                        }
                    }
                    replacementValues.Add(replacementValue);
                    if (!string.IsNullOrEmpty(variableKey))
                    {
                         processedMessage = StringHelper.ReplacePlaceholders(messageBody, replacementValues);
                    }
                }
                return processedMessage;
            }
            catch (Exception ex)
            {
                logger?.LogError(ex, "Error processing LeadRat variables");
                return messageBody; 
            }
        }
        public static string GetValueFromApiData(
            Dictionary<string, object> apiData,
            string keyPath,
            string variableName = null,
            ILogger logger = null)
        {
            try
            {
                if (apiData == null || string.IsNullOrEmpty(keyPath))
                {
                    return null;
                }

                if (keyPath.Contains(","))
                {
                    var fieldPaths = keyPath.Split(',', StringSplitOptions.RemoveEmptyEntries)
                                           .Select(path => path.Trim())
                                           .ToArray();

                    var combinedValues = new List<string>();

                    foreach (var fieldPath in fieldPaths)
                    {
                        var value = GetSingleValueFromApiData(apiData, fieldPath, logger);
                        var stringValue = value?.ToString();
                        if (!string.IsNullOrEmpty(stringValue))
                        {
                            combinedValues.Add(stringValue);
                        }
                    }

                    if (combinedValues.Any())
                    {
                        var result = string.Join(" ", combinedValues);
                        return result;
                    }

                    return null;
                }

                var foundValue = GetSingleValueFromApiData(apiData, keyPath, logger);

                if (foundValue == null)
                {
                    return null;
                }

                return ApplySpecialFormatting(foundValue, variableName, keyPath, logger);
            }
            catch (Exception ex)
            {
                logger?.LogWarning(ex, "Error getting value from API data for keyPath: {KeyPath}", keyPath);
                return null;
            }
        }

        public static object GetSingleValueFromApiData(
            Dictionary<string, object> apiData,
            string keyPath,
            ILogger logger = null)
        {
            try
            {
                if (apiData == null || string.IsNullOrEmpty(keyPath))
                {
                    return null;
                }
                if (apiData.ContainsKey(keyPath))
                {
                    return apiData[keyPath];
                }

                if (!keyPath.StartsWith("data.", StringComparison.OrdinalIgnoreCase))
                {
                    var dataKeyPath = $"data.{keyPath}";
                    if (apiData.ContainsKey(dataKeyPath))
                    {
                        return apiData[dataKeyPath];
                    }
                }
                var exactMatch = apiData.FirstOrDefault(kvp =>
                    string.Equals(kvp.Key, keyPath, StringComparison.OrdinalIgnoreCase));
                if (!exactMatch.Equals(default(KeyValuePair<string, object>)))
                {
                    return exactMatch.Value;
                }

                if (!keyPath.StartsWith("data.", StringComparison.OrdinalIgnoreCase))
                {
                    var dataKeyPath = $"data.{keyPath}";
                    var dataMatch = apiData.FirstOrDefault(kvp =>
                        string.Equals(kvp.Key, dataKeyPath, StringComparison.OrdinalIgnoreCase));
                    if (!dataMatch.Equals(default(KeyValuePair<string, object>)))
                    {
                        return dataMatch.Value;
                    }
                }

                var partialMatch = apiData.FirstOrDefault(kvp =>
                    kvp.Key.EndsWith($".{keyPath}", StringComparison.OrdinalIgnoreCase));
                if (!partialMatch.Equals(default(KeyValuePair<string, object>)))
                {
                    return partialMatch.Value;
                }

                var containsMatch = apiData.FirstOrDefault(kvp =>
                    kvp.Key.Contains(keyPath, StringComparison.OrdinalIgnoreCase));
                if (!containsMatch.Equals(default(KeyValuePair<string, object>)))
                {
                    return containsMatch.Value;
                }

                return null;
            }
            catch (Exception ex)
            {
                logger?.LogWarning(ex, "Error in GetSingleValueFromApiData for keyPath: {KeyPath}", keyPath);
                return null;
            }
        }

        public static string ApplySpecialFormatting(
            object foundValue,
            string variableName,
            string keyPath,
            ILogger logger = null)
        {
            try
            {
                if (foundValue == null)
                {
                    return null;
                }
                if (!string.IsNullOrEmpty(variableName))
                {
                    if (DateTime.TryParse(foundValue.ToString(), out var parsedDateTime))
                    {
                        DateTime localDateTime = ConvertToLocalDateTime(parsedDateTime, foundValue.ToString());

                        // Date formatting
                        if (variableName.Equals("#date#", StringComparison.OrdinalIgnoreCase) ||
                            variableName.Contains("date", StringComparison.OrdinalIgnoreCase))
                        {
                            return localDateTime.ToString("dd-MMM-yyyy", CultureInfo.InvariantCulture);
                        }
                        if (variableName.Equals("#time#", StringComparison.OrdinalIgnoreCase) ||
                            variableName.Contains("time", StringComparison.OrdinalIgnoreCase))
                        {
                            return localDateTime.ToString("hh:mm tt", CultureInfo.InvariantCulture);
                        }

                        if (keyPath.Contains("scheduledDate", StringComparison.OrdinalIgnoreCase))
                        {
                            if (variableName.Equals("#date#", StringComparison.OrdinalIgnoreCase) ||
                                variableName.Contains("date", StringComparison.OrdinalIgnoreCase))
                            {
                                return localDateTime.ToString("dd-MMM-yyyy", CultureInfo.InvariantCulture);
                            }
                            else if (variableName.Equals("#time#", StringComparison.OrdinalIgnoreCase) ||
                                     variableName.Contains("time", StringComparison.OrdinalIgnoreCase))
                            {
                                return localDateTime.ToString("HH:mm tt", CultureInfo.InvariantCulture);
                            }
                            else
                            {
                                return localDateTime.ToString("dd-MMM-yyyy HH:mm tt", CultureInfo.InvariantCulture);
                            }
                        }

                        return localDateTime.ToString("dd-MMM-yyyy HH:mm tt", CultureInfo.InvariantCulture);
                    }
                }

                return foundValue.ToString();
            }
            catch (Exception ex)
            {
                logger?.LogWarning(ex, "Error applying special formatting to value: {Value}", foundValue);
                return foundValue?.ToString();
            }
        }

        private static DateTime ConvertToLocalDateTime(DateTime parsedDateTime, string originalValue)
        {
            if (parsedDateTime.Kind == DateTimeKind.Utc || originalValue.EndsWith("Z", StringComparison.OrdinalIgnoreCase))
            {
                return DateTime.SpecifyKind(parsedDateTime, DateTimeKind.Utc).ToLocalTime();
            }
            else if (parsedDateTime.Kind == DateTimeKind.Unspecified)
            {
                return DateTime.SpecifyKind(parsedDateTime, DateTimeKind.Utc).ToLocalTime();
            }
            else
            {
                return parsedDateTime;
            }
        }

        public static Dictionary<string, object> ConvertApiResponseToDictionary(string jsonResponse)
        {
            try
            {
                if (string.IsNullOrWhiteSpace(jsonResponse))
                {
                    return new Dictionary<string, object>();
                }

                var jsonObject = JsonConvert.DeserializeObject<JObject>(jsonResponse);
                return ObjectHelper.ConvertJObjectToDictionary(jsonObject);
            }
            catch (Exception)
            {
                return new Dictionary<string, object>();
            }
        }
    }
}
