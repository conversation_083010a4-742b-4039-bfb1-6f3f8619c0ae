﻿using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations.Schema;
using System.ComponentModel.DataAnnotations;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using EngagetoEntities.UserEntities.Models;

namespace EngagetoEntities.Entities
{
    public class PaymentWalletDetail
    {
        [Key]
        [DatabaseGenerated(DatabaseGeneratedOption.Identity)]
        public int Id { get; set; }

        [ForeignKey("WalletEntities")]
        public int? WalletEntitiesId { get; set; }
        public WalletEntity? WalletEntities { get; set; }
        [ForeignKey("Subscriptions")]
        public int? SubscriptionId { get; set; }
        public Subscriptions? Subscriptions { get; set; }
        public Guid? UserId { get; set; }
        public string? CompanyId { get; set; }

        public decimal? OrderAmount { get; set; }

        public string? Currency { get; set; }

        public string? OrderId { get; set; }

        public string? PaymentId { get; set; }

        public string? Signature { get; set; }
        public int PlanId { get; set; }
        public DateTime PlanStartDate { get; set; }
        public DateTime PlanEndDate { get; set; }
        public string? DurationType { get; set; }
        public string? PaymentMode { get; set; }
        public decimal? TotalAmount { get; set; }
        public decimal? IGSTAmount { get; set; }
        public string? MarchantTransactionId { get; set; }

        public string? Receipt { get; set; }

        public string? Status { get; set; }

        public Guid? CreatedBy { get; set; }

        public Guid? UpdatedBy { get; set; }

        //[Required]
        public DateTime? CreatedAt { get; set; }

        //[Required]
        public DateTime UpdatedAt { get; set; }
        public Guid? DiscountId { get; set; }
    }
}
