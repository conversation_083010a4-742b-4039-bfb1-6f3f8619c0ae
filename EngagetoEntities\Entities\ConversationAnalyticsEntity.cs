﻿using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace EngagetoEntities.Entities
{
    [Table("ConversationAnalyticsEntities")]
    public class ConversationAnalyticsEntity : BaseEntity
    {
        [Key]
        public Guid Id { get; set; }
        public string? WAMessageId { get; set; }
        public string CompanyId { get; set; }
        public DateTime StartDate { get; set; }
        public DateTime EndDate { get; set; }
        public int Conversation { get; set; }
        public string? ConversationType { get; set; }
        public string? Country { get; set; }
        public string? ConversationCategory { get; set; }
        public decimal Cost { get; set; }
        public decimal? CurrentBalance { get; set; }
        public string? MobileNumber { get; set; }

    }

}
