﻿using Dapper;
using EngagetoDapper.Data.Connections;
using EngagetoDapper.Data.Interfaces.AutomationInterfaces;
using EngagetoEntities.Dtos.AutomationDtos;
using EngagetoEntities.Entities;
using System.Data;

namespace EngagetoDapper.Data.Dapper.Repositories.AutomationRepositories
{
    public class AutoReplyAutomationRepostry : IAutoReplyAutomationRepostry
    {
        private readonly IUnitOfWork _unitOfWork;
        public AutoReplyAutomationRepostry(IUnitOfWork unitOfWork)
        {
            _unitOfWork = unitOfWork;
        }
        public async Task<T?> SaveAutoReplyAutomationAsync<T>(AutoReplyAutomationEntity autoReplyAutomation)
        {
            return await _unitOfWork.Connection.ExecuteScalarAsync<T>("AutoReplyAutomationEntity_Save",
                             new
                             {
                                 autoReplyAutomation.Id,
                                 autoReplyAutomation.CompanyId,
                                 autoReplyAutomation.UserId,
                                 autoReplyAutomation.Input,
                                 autoReplyAutomation.InputVariation,
                                 autoReplyAutomation.AutoReplyType,
                                 autoReplyAutomation.IsDeleted,
                                 autoReplyAutomation.WorkflowName
                             },
                             _unitOfWork.Transaction,
                             commandType: CommandType.StoredProcedure);
        }

        //public async Task<T?> SaveVeriablesAsync<T>(string veriablesXMLData)
        //{
        //    try
        //    {
        //        return await _unitOfWork.Connection.ExecuteScalarAsync<T>(
        //                         "VeriableEntity_SaveBulk",
        //                         new
        //                         {
        //                             @xmlData = veriablesXMLData
        //                         },
        //                         _unitOfWork.Transaction,
        //                         commandType: CommandType.StoredProcedure);
        //    }
        //    catch (Exception ex)
        //    {
        //        return default(T?);
        //    }
        //}

        public async Task<int> SaveVeriablesAsync(IEnumerable<VeriableEntity> entities)
        {
            const string sql = @"
                INSERT INTO VeriableEntities (
                    Id, CompanyId, UserId, ReferenceId,[Type], [Index], Veriable, [Value], FallbackValue, ReferenceTableType,
                    CreatedAt, UpdatedAt, CreatedBy, UpdatedBy, IsDeleted
                ) VALUES (
                    @Id, @CompanyId, @UserId, @ReferenceId,@Type, @Index, @Veriable, @Value, @FallbackValue, @ReferenceTableType,
                    @CreatedAt, @UpdatedAt, @CreatedBy, @UpdatedBy, @IsDeleted
                );";
            try
            {
                var result = await _unitOfWork.Connection.ExecuteAsync(sql, entities);

                return result;
            }
            catch (Exception)
            {
                throw;
            }
        }

        public async Task<IEnumerable<T>> GetWorkflowAsync<T>(Guid? companyId, Guid? userId = null, Guid? workflowId = null, string? workflowName = null)
        {
            var data = await _unitOfWork.Connection.QueryAsync<T>(
                                "WorkflowEntity_Get",
                                 new
                                 {
                                     @CompanyId = companyId,
                                     @UserId = userId,
                                     @WorkflowId = workflowId,
                                     @WorkflowName = workflowName
                                 },
                                 _unitOfWork.Transaction,
                                 commandType: CommandType.StoredProcedure);
            return data;
        }
        public async Task<int> SoftDeleteAutoReplyMessageAsync(Guid companyId, Guid userId, Guid autoReplyId)
        {
            return await _unitOfWork.Connection.ExecuteScalarAsync<int>("AutoReplyMessageEntity_Delete",
                                 new
                                 {
                                     @CompanyId = companyId,
                                     @UserId = userId,
                                     @AutoReplyId = autoReplyId
                                 },
                                 _unitOfWork.Transaction,
                                 commandType: CommandType.StoredProcedure);
        }
        public async Task<int> SoftDeleteAutoReplyWorkflowAsync(Guid companyId, Guid userId, Guid? workflowId, string? workflowName)
        {
            return await _unitOfWork.Connection.ExecuteScalarAsync<int>("AutoReplyWorkflow_Delete",
                                 new
                                 {
                                     @CompanyId = companyId,
                                     @UserId = userId,
                                     @WorkflowId = workflowId,
                                     @WorkflowName = workflowName
                                 },
                                 _unitOfWork.Transaction,
                                 commandType: CommandType.StoredProcedure);
        }



        public async Task<IEnumerable<T>> GetAutoReplyMessageByInputAsync<T>(Guid companyId, string input, string? inputvariations = null)
        {
            return await _unitOfWork.Connection.QueryAsync<T>(
                            "AutoReplyMessageType_GetByInput",
                            new
                            {
                                @Input = input.Trim(),
                                @CompanyId = companyId,
                                @InputVariationJson = inputvariations
                            },
                            _unitOfWork.Transaction,
                            commandType: CommandType.StoredProcedure);
        }

        public async Task<IEnumerable<T>> GetWorkflowNamesAsync<T>(Guid companyId)
        {
            var sqlQuery = @"SELECT CompanyId,
                                   WorkflowName
                            FROM WorkflowEntities 
                            WHERE CompanyId = @CompanyId
                            AND IsDeleted = @IsDeleted
                            GROUP BY CompanyId, WorkflowName
                            ORDER BY MAX(CreatedAt) DESC;";
            var param = new { CompanyId = companyId, IsDeleted = false };

            return await _unitOfWork.Connection.QueryAsync<T>(
                            sqlQuery,
                            param,
                            _unitOfWork.Transaction);
        }
        public async Task<bool> UpdateWorkflowNameAsync(WorkflowUpdatePayloadDto workflowUpdatePayload, Guid userId)
        {
            var sqlQuery = @"UPDATE WorkflowEntities
	                        SET WorkflowName = @OldWorkflowName,
		                        UpdatedAt = GETUTCDATE(),
		                        UpdatedBy = @UserId
                        WHERE CompanyId = @CompanyId
	                        AND WorkflowName = @NewWorkflowName";
            var param = new
            {
                OldWorkflowName = workflowUpdatePayload.OldWorkflowName,
                NewWorkflowName = workflowUpdatePayload.NewWorkflowName,
                CompanyId = workflowUpdatePayload.CompanyId,
                UserId = userId
            };
            return await _unitOfWork.Connection.ExecuteScalarAsync<bool>(
                            sqlQuery,
                            param,
                            _unitOfWork.Transaction);
        }
        public async Task<IEnumerable<WorkflowCustomerResponseDto>> GetWorkflowCustomerResponseAsync(Guid? contactId = null,string? workflowName = null, Guid? companyId = null, long? workflowStartId = null)
        {
            try
            {
                var sqlQuery = @"SELECT COALESCE(vr.VeriableName,'UndefineVeriable') VeriableName,
		                        wfs.Response,
		                        wfs.WorkflowName,
                                wfs.CompanyId,
                                wfs.WorkflowStartId,
		                        MAX(wfs.Step) Step,
		                        MAX(wfs.CreatedAt) CreatedAt
	                        FROM AutomationCustomerResponseEntities acs 
	                        INNER JOIN WorkflowResponseHistoryEntities wfs ON
		                        acs.ReferenceId = wfs.WorkflowId
	                        LEFT JOIN VeriableNameEntities vr On
		                        vr.Id = acs.VeriableNameEntityId
	                         WHERE (@ContactId IS NULL OR wfs.ContactId = @ContactId)
                                AND (@WorkflowName IS NULL OR wfs.WorkflowName = @WorkflowName)
                                AND (@CompanyId IS NULL OR wfs.CompanyId = @CompanyId)
                                AND (@WorkflowStartId IS NULL OR wfs.WorkflowStartId = @WorkflowStartId)
	                         GROUP BY wfs.WorkflowName,
		                        wfs.Response,
		                        vr.VeriableName,
                                wfs.CompanyId,
                                wfs.CreatedAt,
		                        wfs.WorkflowStartId
	                        ORDER BY wfs.CreatedAt";
                var param = new
                {
                    @ContactId = contactId,
                    @WorkflowName = workflowName,
                    @CompanyId = companyId,
                    @WorkflowStartId = workflowStartId
                };
                return await _unitOfWork.Connection.QueryAsync<WorkflowCustomerResponseDto>(
                                sqlQuery,
                                param,
                                _unitOfWork.Transaction);
            }catch(Exception ex)
            {
                return null;
            }
        }
    }
}
