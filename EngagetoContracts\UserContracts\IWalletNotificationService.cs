﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using EngagetoEntities.Entities;

namespace EngagetoContracts.UserContracts
{
    public interface IWalletNotificationService
    {
        public Task<WalletNotification> CreateWalletNotificationAsync(WalletNotification WalletNotification);
        public Task<List<WalletNotification>> GetWalletNotificationAsync(Guid walletId);
    }
}
