﻿namespace EngagetoEntities.Dtos.MetaDto
{
    public class AutoReply
    {
        public class Rootobject
        {
            public string messaging_product { get; set; } = "whatsapp";
            public string recipient_type { get; set; } = "individual";
            public string to { get; set; }
            public string type { get; set; } = "interactive";
            public Interactive interactive { get; set; }
        }

        public class Interactive
        {
            public string? type { get; set; } = "button"; // Default is "button"
            public Body? body { get; set; }
            public Action? action { get; set; }
            public Header? header { get; set; } // For list type
            public Footer? footer { get; set; } // For list type
        }

        public class Body
        {
            public string? text { get; set; }
        }

        public class Action
        {
            // Buttons for "button" type
            public List<Button>? buttons { get; set; }

            // Properties for "list" type
            public string? button { get; set; }
            public List<Section>? sections { get; set; }
        }

        public class Button
        {
            public string? type { get; set; } = "reply";
            public Reply? reply { get; set; }
        }

        public class Reply
        {
            public string? id { get; set; }
            public string? title { get; set; }
        }

        // For list type interactive message
        public class Header
        {
            public string? type { get; set; } = "text";
            public string? text { get; set; }
        }

        public class Footer
        {
            public string? text { get; set; }
        }

        public class Section
        {
            public string? title { get; set; }
            public List<Row>? rows { get; set; }
        }

        public class Row
        {
            public string? id { get; set; }
            public string? title { get; set; }
            public string? description { get; set; }
        }
    }
}
