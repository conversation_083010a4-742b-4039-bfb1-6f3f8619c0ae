﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace EngagetoEntities.Dtos.AttributeNameDtos
{
    public class AttributeNameDtos
    {
        public Guid Id { get; set; }
        public string Name { get; set; }
        public string? BusinessId { get; set; }
        public string? UserId { get; set; }
        public bool IsExist { get; set; }
    }
    public class CreateAttributeNameDtos
    {
        public string Name { get; set; }
        public string? BusinessId { get; set; }
        public string? UserId { get; set; }
    }
    public class UpdateAttributeNameDtos
    {
        public string Name { get; set; }
        public bool IsExist { get; set; }
    }


}
