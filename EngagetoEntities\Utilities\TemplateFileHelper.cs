﻿namespace EngagetoEntities.Utilities
{
    public static class TemplateFileHelper
    {
        private static readonly string TemplateFolderPath = Path.Combine("..", "EngagetoEntities", "TemplatesFiles");

        public static string GetTemplateFilePath(string fileName)
        {
            ValidateFileName(fileName);

            string filePath = Path.Combine(TemplateFolderPath, fileName);

            if (!File.Exists(filePath))
            {
                throw new FileNotFoundException("The specified file does not exist.", filePath);
            }

            return Path.GetFullPath(filePath);
        }
        
        public static byte[] GetTemplateFileAsByteArray(string fileName)
        {
            string filePath = GetTemplateFilePath(fileName);
            return File.ReadAllBytes(filePath);
        }

        private static void ValidateFileName(string fileName)
        {
            if (string.IsNullOrWhiteSpace(fileName))
            {
                throw new ArgumentException("File name cannot be null or empty.", nameof(fileName));
            }
        }
    }
}
