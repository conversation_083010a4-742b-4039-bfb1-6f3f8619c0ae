﻿using EngagetoContracts.IntegrationsContracts;
using EngagetoContracts.Services;
using EngagetoEntities.DdContext;
using EngagetoEntities.Dtos.IntegrationDtos;
using EngagetoEntities.Entities;
using Microsoft.EntityFrameworkCore;

namespace EngagetoRepository.IntegrationsRepository
{
    public class WidgetRepository : IWidgetRepository
    {
        private readonly ApplicationDbContext _context; // Assuming YourDbContext is your EF Core DbContext
        private readonly IUserIdentityService _userIdentityService;

        public WidgetRepository(ApplicationDbContext context, IUserIdentityService userIdentityService)
        {
            _context = context;
            _userIdentityService = userIdentityService;
        }

        public async Task<Guid> WidgetCreate(WidgetEntityDto widgetDto)
        {
            if (widgetDto == null)
            {
                throw new ArgumentNullException(nameof(widgetDto), "Widget data is null.");
            }

            try
            {
                // Map WidgetEntityDto to WidgetEntity
                var widgetEntity = new WidgetEntity
                {
                    Id = Guid.NewGuid(), // Generate a new unique ID for the widget
                    BusinessId = _userIdentityService.BusinessId,
                    UserId = _userIdentityService.UserId,
                    PhoneNumber = widgetDto.PhoneNumber,
                    CtaText = widgetDto.CtaText,
                    ButtonBackground = widgetDto.ButtonBackground,
                    MarginBottom = widgetDto.MarginBottom,
                    MarginLeft = widgetDto.MarginLeft,
                    MarginRight = widgetDto.MarginRight,
                    BorderRadius = widgetDto.BorderRadius,
                    DefaultMessage = widgetDto.DefaultMessage,
                    Position = widgetDto.Position,
                    BrandName = widgetDto.BrandName,
                    BrandSubtitle = widgetDto.BrandSubtitle,
                    BrandColor = widgetDto.BrandColor,
                    WidgetCtaText = widgetDto.WidgetCtaText,
                    BrandImageUrl = widgetDto.BrandImageUrl,
                    DefaultOnScreenMessage = widgetDto.DefaultOnScreenMessage,
                    OpenWidgetOnMobileScreen = widgetDto.OpenWidgetOnMobileScreen,
                    OpenWidgetByDefault = widgetDto.OpenWidgetByDefault,
                    StartChat = widgetDto.StartChat,
                    UrlFields = widgetDto.UrlFields?.Select(urlFieldDto => new WidgetUrlFieldEntity
                    {
                        Id = Guid.NewGuid(), // Generate a new unique ID for each URL field
                        SourceUrl = urlFieldDto.SourceUrl,
                        PreFilledMessage = urlFieldDto.PreFilledMessage,
                        OnScreenMessage = urlFieldDto.OnScreenMessage,
                        RemoveChecked = urlFieldDto.RemoveChecked,
                        CapitalizeChecked = urlFieldDto.CapitalizeChecked,
                        BrandImageUrl = urlFieldDto.BrandImageUrl,
                    }).ToList()
                };

                // Add widget entity to the DbContext
                await _context.WidgetEntities.AddAsync(widgetEntity);

                // Save the changes to the database
                var result = await _context.SaveChangesAsync();

                // Return the widget's Id if the save was successful
                if (result > 0)
                {
                    return widgetEntity.Id;
                }
                else
                {
                    throw new InvalidOperationException("Failed to save the widget.");
                }
            }
            catch (Exception ex)
            {
                // Log the exception (logging logic not included in this example)
                throw new InvalidOperationException("An error occurred while saving the widget.", ex);
            }
        }
        public async Task<WidgetEntity> GetWidgetByIdAsync(Guid id)
        {
            try
            {
                var widgetEntity = await _context.WidgetEntities
                    .Include(w => w.UrlFields) // Include related entities if necessary
                    .FirstOrDefaultAsync(w => w.Id == id);

                if (widgetEntity == null)
                {
                    throw new KeyNotFoundException($"Widget with ID {id} not found.");
                }

                return widgetEntity;
            }
            catch (Exception ex)
            {
                // Log the exception (logging logic not included in this example)
                throw new InvalidOperationException("An error occurred while retrieving the widget.", ex);
            }
        }
    }
}
