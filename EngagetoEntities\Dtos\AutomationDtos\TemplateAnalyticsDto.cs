﻿namespace EngagetoEntities.Dtos.AutomationDtos
{
    public class TemplateAnalyticsDto: BaseTemplateAnlayticsDto
    {
        public string TenantId { get; set; }
    }
    public class BaseTemplateAnlayticsDto
    {
        public int Total { get; set; }
        public int ApprovedTemplate {  get; set; }
        public int PendingTemplate { get; set; }
        public int RejectedTemplate { get; set; }
        public int MarketingTemplate { get; set; }
        public int UtlityTemplae { get; set; }
        public int AuthenticationTemplate { get; set; }
        public int SentTemplate { get; set; }
    }
}
