﻿using EngagetoEntities.Dtos.AutomationDtos;
using EngagetoEntities.Entities;

namespace EngagetoDapper.Data.Interfaces.CommonInterfaces
{
    public interface ICommonService
    {
        Task<List<WorkflowVariableResponseDto>> WorkflowVariableResponse(Guid contactId);
        Task<MemoryStream> GenerateWorkflowResponseListByName(string WorkflowName, Guid CompanyId);
        Task<VeriableNameEntity> SaveVeriableNameAsync(VeriableNameEntity entity);
        Task<List<VeriableNameEntity>> GetVeriableNameAsync(Guid companyId, Guid? userId);
        Task<WorkflowListDto> SaveInputListAsync(WorkflowListDto entity);
        Task<List<WorkflowListDto>> GetInputListAsync(Guid companyId, Guid? userId);
        Task<WorkflowListDto> UpdateInputListAsync(WorkflowListDto entity);
        Task<AutomationCustomerResponseEntity> SaveAutomationCustomerResponseAsync(AutoReplyWorkflowCustomerResponseDto customerResponseDto);
        Task<List<AutomationCustomerResponseEntity>> GetAutomationCustomerResponseAsync(Guid companyId, Guid? userId);
        Task<AutomationCustomerResponseEntity> UpdateAutomationCustomerResponseAsync(AutomationCustomerResponseEntity entity);
        Task<List<PlanDiscountEntity>> GetPlanDiscountsAsync();
    }
}
