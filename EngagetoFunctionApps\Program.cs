using EngagetoBackGroundJobs.Implementation;
using EngagetoBackGroundJobs.Interfaces;
using EngagetoContracts.GeneralContracts;
using EngagetoContracts.MetaContracts;
using EngagetoContracts.Services;
using EngagetoContracts.Workflow;
using EngagetoDapper.Data.Connections;
using EngagetoDapper.Data.Dapper.Repositories.GenericRepositories;
using EngagetoDapper.Data.Dapper.Repositories.InboxRepositories;
using EngagetoDapper.Data.Dapper.Services.LogHistoryServices;
using EngagetoDapper.Data.Interfaces.GenericInterfaces;
using EngagetoDapper.Data.Interfaces.ILogInterfaces;
using EngagetoRepository.GeneralServices;
using EngagetoRepository.MetaServices;
using EngagetoRepository.Services;
using Microsoft.Azure.Functions.Worker;
using Microsoft.Data.SqlClient;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Hosting;
using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Options;
using EngagetoDapper.Data.Interfaces.CampaignInterfaces;
using EngagetoDapper.Data.Dapper.Repositories.CampaignRespositories;
using System.Data;
using EngagetoDapper.Data.Interfaces.CampaignInterfaces.ICampaignRepositories;
using EngagetoContracts.AttributeName;
using EngagetoContracts.WorkflowRepository;
using EngagetoContracts.ContactContracts;
using EngagetoContracts.TemplateContracts;
using EngagetoContracts.UserContracts;
using EngagetoRepository.Repostiories;
using EngagetoRepository.ContactsRepository;
using EngagetoRepository.TemplateRepository;
using EngagetoRepository.UserRepository;
using EngagetoEntities.Validations.TemplateValidation;

using EngagetoDapper;
using EngagetoDapper.Data.Interfaces.IEmailInterfaces;
using EngagetoDapper.Data.Dapper.Services.EmailServices;
using EngagetoContracts.WebhookContracts.DownloadMedia;
using EngagetoContracts.WebhookContracts.SentMessage;
using EngagetoRepository.WebhookRepository.DownloadMediaRepo;
using EngagetoRepository.WebhookRepository.SentMessageRepo;
using EngagetoDatabase.WhatsAppBusinessDatabase.Models;


var host = new HostBuilder()
    .ConfigureFunctionsWebApplication()
       .ConfigureAppConfiguration((context, config) =>
       {
           config.SetBasePath(Directory.GetCurrentDirectory())
               .AddJsonFile("local.settings.json", optional: true, reloadOnChange: true)
               .AddEnvironmentVariables();
       })
    .ConfigureServices((context, services) =>
    {
        // Add Application Insights
        services.AddApplicationInsightsTelemetryWorkerService();
        services.ConfigureFunctionsApplicationInsights();

        // Add Configuration
        services.AddSingleton<IConfiguration>(context.Configuration);

        var connectionString = context.Configuration["ConnStr"];

        // Configure ConnectionString options for ApplicationDbContext
        services.Configure<EngagetoEntities.Settings.ConnectionString>(options =>
        {
            options.ConnStr = connectionString;
        });

        // Configure SmtpSettings for EmailService
        services.Configure<EngagetoEntities.ServiceModels.SmtpSettings>(context.Configuration.GetSection("SmtpSettings"));

        // Add LoggerFactory
        services.AddLogging();

        services.AddDbContext<EngagetoEntities.DdContext.ApplicationDbContext>(options =>
               options.UseSqlServer(connectionString));

        services.AddDbContext<DbAa80b1WhatsappbusinessContext>(options =>
            options.UseSqlServer(connectionString));

        services.AddScoped<IDbConnection>(sp =>
        {
            var connection = new SqlConnection(connectionString);
            return connection;
        });

        services.AddScoped<IUnitOfWork>(sp =>
        {
            var dbConnection = sp.GetRequiredService<IDbConnection>();
            // 🔧 CRITICAL FIX: Don't open connection here - let UnitOfWork handle it
            // Connection will be opened when first accessed
            return new UnitOfWork(dbConnection);
        });

        // 🗑️ REMOVED: DapperConnectionFactory - unnecessary complexity
        // Services now use IUnitOfWork directly for better transaction handling

        // Register Services
        services.AddSingleton<ISqlConnectionFactory, SqlConnectionFactory>();
        services.AddScoped<IGenericRepository, GenericRepository>();
        services.AddScoped<ICampaignScheduler, CampaignScheduler>();
        services.AddScoped<IContactScheduler, ContactScheduler>();
        services.AddScoped<IEnvironmentService, EnvironmentService>();
        services.AddScoped<IJobService, JobService>();
        services.AddScoped<ILogHistoryService, LogHistoryService>();
        services.AddScoped<IInboxRepository, InboxRepository>();
        services.AddScoped<IMetaApiService, MetaApiService>();
        services.AddScoped<IMetaPayloadService, MetaPayloadService>();
        services.AddScoped<IUserIdentityService, UserIdentityService>();
        services.AddScoped<ICampaignRespository, CampaignRespository>();
        services.AddScoped<IBlobStorageService, BlobStorageService>();
        services.AddScoped<INodeWorkflowEngineService, NodeWorkflowEngineService>();
        services.AddScoped<IWorkflowCustomResponseService, WorkflowCustomResponseService>();
        services.AddScoped<IAttributeNameService, AttributeNameService>();
        services.AddScoped<ICustomerWorkflowTrackerRepository, CustomerWorkflowTrackerRepository>();

        // Add missing template and user services
        services.AddScoped<ITemplate, TemplatesService>();
        services.AddScoped<IAccountDetailsService, AccountDetailsService>();
        services.AddScoped<EngagetoContracts.UserContracts.IEmailService, EngagetoRepository.Repository.EmailService>();
        services.AddScoped<IContactRepositoryBase, ContactRepositoryBase>();
        services.AddScoped<ICompanyDetailsService, EngagetoRepository.Repository.CompanyDetailsService>();
        services.AddScoped<IMediaURL, MediaURL>();
        services.AddScoped<IDownloadMedia, DownloadMeidaService>();
        services.AddScoped<IWhatsAppBusinessNotificarion , SentMessageStatusUpdate>();
        services.AddScoped<TemplateValidation>();
        services.AddScoped<EngagetoDapper.Data.Interfaces.IUserInterfaces.IUserServices.IUserService, EngagetoDapper.Data.Dapper.Services.UserServices.UserService>();
        services.AddScoped<EngagetoDapper.Data.Interfaces.CampaignInterfaces.ICampaignServices.IConversationsService, EngagetoDapper.Data.Dapper.Services.CampaignServices.ConversationsService>();


    
        // Add infrastructure services (includes IEmailService and other common services)
        services.AddInfraStructure(context.Configuration);

        services.AddLogging(loggingBuilder =>
        {
            loggingBuilder.AddConsole();
            loggingBuilder.AddDebug();
        });

        // Add HttpClient for services that need it
        services.AddHttpClient();

        // 🚀 OPTIMIZED HTTP CLIENT CONFIGURATION for high-throughput WhatsApp messaging
        services.AddHttpClient("LongRunning", client =>
        {
            client.Timeout = TimeSpan.FromMinutes(10); // Extended timeout for batch operations
        })
        .ConfigurePrimaryHttpMessageHandler(() => new HttpClientHandler()
        {
            MaxConnectionsPerServer = 100, // Increased connection pool
            UseCookies = false // Disable cookies for better performance
        });


        services.AddCors(options =>
        {
            options.AddPolicy("AllowAllOrigins", builder =>
            {
                builder.AllowAnyOrigin()
                       .AllowAnyMethod()
                       .AllowAnyHeader();
            });
        });
    })
    .Build();

host.Run();