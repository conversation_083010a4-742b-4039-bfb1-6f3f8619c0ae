﻿using EngagetoContracts.GeneralContracts;
using EngagetoContracts.WebhookContracts.ReceivedNotification;
using EngagetoDapper.Data.Interfaces.GenericInterfaces;
using EngagetoDapper.Data.Interfaces.ILogInterfaces;
using EngagetoEntities.Dtos.WebhookDtos;
using EngagetoEntities.Entities;
using EngagetoEntities.Extensions;
using EngagetoEntities.Utilities;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using Newtonsoft.Json;
using System.Collections.Concurrent;
using System.Text;

namespace Engageto.Controllers.WebhookControllers
{
    [Route("api/[controller]")]
    [ApiController]
    public class WAWebhookMessageController : ControllerBase
    {
        private string verifyToken;
        private readonly IConfiguration _configuration;
        private readonly IWAWebhookMessageServiceAsync _waMessageServiceAsync;
        private readonly IServiceScopeFactory _serviceScopeFactory;
        private readonly IEnvironmentService _environmentService;

        public WAWebhookMessageController(IConfiguration configuration,
            IWAWebhookMessageServiceAsync waMessageServiceAsync,
            IServiceScopeFactory serviceScopeFactory,
            IEnvironmentService environmentService)
        {
            _configuration = configuration;
            _waMessageServiceAsync = waMessageServiceAsync;
            _serviceScopeFactory = serviceScopeFactory;
            _environmentService = environmentService;
        }

        [HttpGet("receive-WAmessage")]
        public ActionResult<string> VerifyWAWebhookAsync([FromQuery(Name = "hub.mode")] string hubMode,
                                                                    [FromQuery(Name = "hub.challenge")] int hubChallenge,
                                                                    [FromQuery(Name = "hub.verify_token")] string hubVerifyToken)
        {
            verifyToken = _configuration["Webhooks:VerifyToken"] ?? default!;
            if (!hubVerifyToken.Equals(verifyToken))
            {
                return Forbid("VerifyToken doesn't match");
            }
            return Ok(hubChallenge);
        }

        [HttpPost("receive-WAmessage")]
        public async Task<IActionResult> ReceivedWAWebhookMessageAsync([FromBody] dynamic receivedMessage, CancellationToken token)
        {
            try
            {
                // Deserialize the incoming message to the DTO
                var webhookDto = JsonConvert.DeserializeObject<WAWebhookDto>(receivedMessage.ToString());
                // Perform the operation in the background

                //_ = Task.Run(async () =>
                //{
                // Create a new scope for the background operation
                using (var scope = _serviceScopeFactory.CreateScope())
                {

                    var scopedService = scope.ServiceProvider.GetRequiredService<IWAWebhookMessageServiceAsync>();
                    var historyService = scope.ServiceProvider.GetRequiredService<ILogHistoryService>();
                    var genericRepo = scope.ServiceProvider.GetRequiredService<IGenericRepository>();

                    try
                    {
                        // Process the message with the scoped service
                        await scopedService.ProcessWAWebhookMessageAsync(webhookDto, token);
                        await SendWebhookMessage(webhookDto, historyService, genericRepo);
                    }
                    catch (Exception ex)
                    {
                        Console.WriteLine($"Error processing message: {ex.Message}");
                    }
                }
                //});
                return Ok("Message received and processing started.");
            }
            catch (Exception ex)
            {
                // Log the exception
                Console.WriteLine($"Error: {ex.Message}");
                // Return a 500 status code with error details
                return StatusCode(StatusCodes.Status500InternalServerError, "An error occurred while processing the request.");
            }
        }
        [HttpPost("receive-message")]
        [AllowAnonymous]
        public async Task<IActionResult> ReceiveWAMessageAsync(WAWebhookDto wAWebhook, CancellationToken token)
        {
            try
            {
                _ = Task.Run(async () =>
                {
                    // Create a new scope for the background operation
                    using (var scope = _serviceScopeFactory.CreateScope())
                    {
                        var scopedService = scope.ServiceProvider.GetRequiredService<IWAWebhookMessageServiceAsync>();
                        var historyService = scope.ServiceProvider.GetRequiredService<ILogHistoryService>();
                        var genericRepo = scope.ServiceProvider.GetRequiredService<IGenericRepository>();

                        try
                        {
                            // Process the message with the scoped service
                            await scopedService.HandleReceviedMessageAsync(wAWebhook, token);
                            await SendWebhookMessage(wAWebhook, historyService, genericRepo);
                        }
                        catch (Exception ex)
                        {
                            Console.WriteLine($"Error processing message: {ex.Message}");
                        }
                    }
                });
                return Ok("start the processing for ReceiveWAMessageAsync api");
            }
            catch (Exception ex)
            {
                // Log the exception
                Console.WriteLine($"Error: {ex.Message}");

                // Return a 500 status code with error details
                return StatusCode(StatusCodes.Status500InternalServerError, "An error occurred while processing the request.");
            }
        }

        [HttpPost("sent-message")]
        [AllowAnonymous]
        public async Task<IActionResult> SentWAMessageAsync(WAWebhookDto wAWebhook, CancellationToken token)
        {
            try
            {
                _ = Task.Run(async () =>
                {
                    // Create a new scope for the background operation
                    using (var scope = _serviceScopeFactory.CreateScope())
                    {
                        var scopedService = scope.ServiceProvider.GetRequiredService<IWAWebhookMessageServiceAsync>();
                        var historyService = scope.ServiceProvider.GetRequiredService<ILogHistoryService>();
                        var genericRepo = scope.ServiceProvider.GetRequiredService<IGenericRepository>();

                        try
                        {
                            // Process the message with the scoped service
                            await scopedService.ProcessSentMessageAsync(wAWebhook, token);
                            await SendWebhookMessage(wAWebhook, historyService, genericRepo);
                        }
                        catch (Exception ex)
                        {
                            Console.WriteLine($"Error processing message: {ex.Message}");
                        }
                    }
                });
                return Ok("start the processing for SentWAMessageAsync api");
            }
            catch (Exception ex)
            {
                // Log the exception
                Console.WriteLine($"Error: {ex.Message}");

                // Return a 500 status code with error details
                return StatusCode(StatusCodes.Status500InternalServerError, "An error occurred while processing the request.");
            }
        }
        [HttpPost("update-template")]
        [AllowAnonymous]
        public async Task<IActionResult> UpdateTemplateAsync(WAWebhookDto wAWebhook, CancellationToken token)
        {
            try
            {
                _ = Task.Run(async () =>
                {
                    // Create a new scope for the background operation
                    using (var scope = _serviceScopeFactory.CreateScope())
                    {
                        var scopedService = scope.ServiceProvider.GetRequiredService<IWAWebhookMessageServiceAsync>();
                        var historyService = scope.ServiceProvider.GetRequiredService<ILogHistoryService>();
                        var genericRepo = scope.ServiceProvider.GetRequiredService<IGenericRepository>();
                        try
                        {
                            // Process the message with the scoped service
                            await scopedService.HandleReceivedTemplateStatus(wAWebhook, null, token);
                            await SendWebhookMessage(wAWebhook, historyService, genericRepo);
                        }
                        catch (Exception ex)
                        {
                            Console.WriteLine($"Error processing message: {ex.Message}");
                        }
                    }
                });
                return Ok("start the processing for UpdateTemplateAsync api");
            }
            catch (Exception ex)
            {
                // Log the exception
                Console.WriteLine($"Error: {ex.Message}");

                // Return a 500 status code with error details
                return StatusCode(StatusCodes.Status500InternalServerError, "An error occurred while processing the request.");
            }
        }

        #region Send the webhook WhatsApp message to the other environment instead of production.
        private async Task SendWebhookMessage(WAWebhookDto message, ILogHistoryService _historyService, IGenericRepository _genericRepository)
        {
            try
            {
                List<string> developmentBusinessIds = new() { "297817036758193", "537494702780895", "470271766163991", "273756975824166" };
                string env = _environmentService.IsDevelopment ? "dev" : "prd";
                var waPhoneNumber = message?.Entry?[0]?.Changes?[0]?.Value?.Metadata?.PhoneNumberId ?? string.Empty;
                var isDevelopment = _environmentService.IsDevelopment;
                //await _historyService.SaveInformationLogHistoryAsyn($"Pre:WebhookMessage WAWebhooksend", JsonConvert.SerializeObject(message), null, env);
                if (!isDevelopment && (developmentBusinessIds.Contains(waPhoneNumber) || message?.Entry?[0]?.Changes?[0]?.Field == "message_template_status_update"))
                {
                    var environments = await _genericRepository.GetByObjectAsync<EnvironmentUrlEntity>(new Dictionary<string, object> { { "Environment", "dev" } });
                    foreach (var environment in environments.Where(x => x.IsActive))
                    {
                        var httpClient = new HttpClient();
                        var content = new StringContent(JsonConvert.SerializeObject(message), Encoding.UTF8, "application/json");

                        // Call the POST API
                        var response = await httpClient.PostAsync(environment.Url, content);
                        if (response.IsSuccessStatusCode)
                        {
                            var responseString = await response.Content.ReadAsStringAsync();
                            await _historyService.SaveSuccessLogHistoryAsyn($"WebhookMessage WAWebhooksend", JsonConvert.SerializeObject(message), responseString, "Success");
                        }
                        else
                        {
                            var responseString = await response.Content.ReadAsStringAsync();
                            await _historyService.SaveInformationLogHistoryAsyn($"WebhookMessage WAWebhooksend", JsonConvert.SerializeObject(message), responseString, "Information");
                        }
                    }
                }
            }
            catch (Exception ex)
            {
                Console.WriteLine(ex.Message);
                await _historyService.SaveErrorLogHistoryAsyn($"WebhookMessage WAWebhooksend", JsonConvert.SerializeObject(message), "Error", ex.Message, ex.StackTrace);
            }
        }
        #endregion

    }
}