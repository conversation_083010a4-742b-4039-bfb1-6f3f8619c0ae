﻿using System.ComponentModel.DataAnnotations.Schema;

namespace EngagetoEntities.Entities
{
    [Table("ContactAssignmentHistoryEntities")]
    public class ContactAssignmentHistoryEntity
    {
        public Guid Id { get; set; }
        public Guid ContactId { get; set; }
        public Guid UserId { get; set; }
        public Guid CompanyId { get; set; }
        public Guid AssignedToUserId { get; set; }
        public DateTime AssignDate { get; set; }

        public ContactAssignmentHistoryEntity() { }

        public ContactAssignmentHistoryEntity(Guid contactId, Guid userId, Guid companyId, Guid assignedToUserId)
        {
            Id = Guid.NewGuid();
            ContactId = contactId;
            UserId = userId;
            CompanyId = companyId;
            AssignedToUserId = assignedToUserId;
            AssignDate = DateTime.UtcNow;
        }
    }
}
