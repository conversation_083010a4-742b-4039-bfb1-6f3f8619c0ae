﻿using EngagetoMeta.DatabaseContext;
using EngagetoMeta.Entities;
using EngagetoMeta.Enums;
using Newtonsoft.Json;
using System.Text.Json;

namespace EngagetoMeta.Repositories
{
    public class LogHistoryRepository : ILogHistoryRepository
    {
        private readonly ApplicationDbContext _applicationDbContext;
        public LogHistoryRepository(ApplicationDbContext applicationDbContext)
        {
            _applicationDbContext = applicationDbContext;
        }

        public async Task<bool> SaveErrorLogHistoryAsyn(string? apiName, object? requestBody, string? notes, string? errorMessage, object? stackTrace, string? whatsaapBusinessAccId, string? field)
        {
            try
            {
                var logEntity = new LogHistoryEntitity
                {
                    Id = Guid.NewGuid(),
                    ApiName = apiName,
                    RequestData = requestBody is JsonElement ?
                   ((JsonElement)requestBody).GetRawText() :
                   JsonConvert.SerializeObject(requestBody),
                    ResponseData = $@"{field}",
                    Notes = notes,
                    LogType = LogType.Error,
                    ErrorMessage = errorMessage,
                    StackTrace = stackTrace is JsonElement ?
                   ((JsonElement)stackTrace).GetRawText() :
                   JsonConvert.SerializeObject(stackTrace),
                    CreatedAt = DateTime.UtcNow,
                    WhatsappBusinessAccountId = whatsaapBusinessAccId,
                    Field = field
                };

                await _applicationDbContext.LogHistoryEntities.AddAsync(logEntity);
                var result = await _applicationDbContext.SaveChangesAsync();
                return result > 0;
            }
            catch (Exception ex)
            {
                return false;
            }
        }

        public async Task<bool> SaveSuccessLogHistoryAsyn(string? apiName, object? requestBody, object? response, string? notes, string? whatsaapBusinessAccId, string? field)
        {
            try
            {

                var logEntity = new LogHistoryEntitity
                {
                    Id = Guid.NewGuid(),
                    ApiName = apiName,
                    RequestData = requestBody is JsonElement ?
                    ((JsonElement)requestBody).GetRawText() :
                    JsonConvert.SerializeObject(requestBody),
                    ResponseData = response != null ?
                    (response is JsonElement ?
                        ((JsonElement)response).GetRawText() :
                        JsonConvert.SerializeObject(response)) :
                    $@"{field}",
                    Notes = notes,
                    LogType = LogType.Success,
                    ErrorMessage = null,
                    StackTrace = null,
                    CreatedAt = DateTime.UtcNow,
                    WhatsappBusinessAccountId = whatsaapBusinessAccId,
                    Field = field
                };

                await _applicationDbContext.LogHistoryEntities.AddAsync(logEntity);
                var result = await _applicationDbContext.SaveChangesAsync();
                return result > 0;
            }
            catch (Exception ex)
            {
                return false;
            }
        }
    }
}
