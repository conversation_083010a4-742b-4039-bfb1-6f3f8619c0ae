﻿using AngleSharp.Common;
using Engageto.Hubs;
using EngagetoBackGroundJobs.Implementation;
using EngagetoBackGroundJobs.Interfaces;
using EngagetoContracts.AutomationContracts;
using EngagetoContracts.ContactContracts;
using EngagetoContracts.Services;
using EngagetoContracts.WebhookContracts.Client;
using EngagetoContracts.WebhookContracts.DownloadMedia;
using EngagetoContracts.WebhookContracts.ReceivedNotification;
using EngagetoDapper.Data.Interfaces.AutomationInterfaces;
using EngagetoDapper.Data.Interfaces.ILogInterfaces;
using EngagetoDapper.Data.Interfaces.IUserInterfaces.IUserServices;
using EngagetoEntities.DdContext;
using EngagetoEntities.Dtos.AutomationDtos;
using EngagetoEntities.Dtos.IntegrationDtos;
using EngagetoEntities.Dtos.MetaDto;
using EngagetoEntities.Dtos.WebhookReceiveNotification;
using EngagetoEntities.Entities;
using EngagetoEntities.Enums;
using EngagetoEntities.Utilities;
using Microsoft.AspNetCore.SignalR;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Configuration;
using MimeKit;
using PhoneNumbers;
using System.Reflection;
using System.Text;
using System.Text.Json;
using System.Text.RegularExpressions;


namespace EngagetoRepository.WebhookRepository.ReceivedMessageRepo
{
    public class ReceiveMessageService : IWhatsAppReceiveNotification
    {
        // private readonly IWhatsAppBusinessClient _whatsAppBusinessClient;
        public IInboxService _inboxService;
        private readonly ApplicationDbContext _appDbContext;
        private readonly IMediaURL mediaURL;
        //public DbAa80b1WhatsappbusinessContext apiContext;
        private IHubContext<MessageHub, IMessageHubClient> messageHub;
        private readonly IHttpClientFactory _httpClientFactory;
        private IWhatsAppBusinessClient whatsAppReceive;

        private IConfiguration configuration;
        private IAutomationWorkflowService _automationWorkflowService;
        public IWebhookService _webhookService;
        private readonly EngagetoDapper.Data.Interfaces.AutomationInterfaces.IAutoReplyAutomationService _replyAutomationService;
        private readonly EngagetoDapper.Data.Interfaces.AutomationInterfaces.IAutoReplyMessageService _autoReplyCustomMessageService;
        private readonly IWhatsAppBusinessClient _sentMessage;
        private readonly IContactRepositoryBase _contactRepositoryBase;

        private ApplicationDbContext _dbContext;
        private readonly IRepositoryBase _repositoryBase;
        private readonly ILogHistoryService _logHistoryService;
        private readonly LeadProcessingService _leadProcessingService;
        private readonly IAutomationSettingService _automationSettingService;

        private IJobService _jobService { get; set; }
        public ReceiveMessageService(IAutomationWorkflowService automationWorkflowService, IConfiguration Config, IHttpClientFactory ClientFactory,
                                      IWhatsAppBusinessClient whatsAppReceiveNotification, ApplicationDbContext appDbContext, IMediaURL media
                                      /*DbAa80b1WhatsappbusinessContext context*/, IHubContext<MessageHub, IMessageHubClient> _messageHub,
                                      IWebhookService webhookService, EngagetoDapper.Data.Interfaces.AutomationInterfaces.IAutoReplyAutomationService replyAutomationService,
                                      EngagetoDapper.Data.Interfaces.AutomationInterfaces.IAutoReplyMessageService autoReplyCustomMessageService,
                                      IWhatsAppBusinessClient sentMessage, IContactRepositoryBase contactRepositoryBase, IJobService IJobService,
                                      IInboxService inboxService,
                                      ApplicationDbContext dbContext,
                                      IRepositoryBase repositoryBase,
                                      ILogHistoryService logHistoryService,
                                      LeadProcessingService leadProcessingService,
                                      IAutomationSettingService automationSettingService)
        {
            _dbContext = dbContext;
            this._appDbContext = appDbContext; mediaURL = media;
            _httpClientFactory = ClientFactory; /*apiContext = context*/;
            messageHub = _messageHub; whatsAppReceive = whatsAppReceiveNotification;
            configuration = Config; _replyAutomationService = replyAutomationService;
            _jobService = IJobService; _webhookService = webhookService;
            _autoReplyCustomMessageService = autoReplyCustomMessageService;
            _sentMessage = sentMessage; _contactRepositoryBase = contactRepositoryBase;
            _inboxService = inboxService;
            _automationWorkflowService = automationWorkflowService;
            _repositoryBase = repositoryBase;
            _logHistoryService = logHistoryService;
            _leadProcessingService = leadProcessingService;
            _automationSettingService = automationSettingService;
        }
        /// <summary>
        /// Handles the reception and processing of text messages.
        /// </summary>
        /// <param name="textMessage">The text message received.</param>
        /// <param name="textMessageMetadata">Metadata associated with the text message.</param>
        /// <returns>A task that represents the asynchronous operation.</returns>
        public async Task ReceiveNotification(TextMessage textMessage, TextMessageMetadata textMessageMetadata)
        {
            if (textMessage != null && textMessageMetadata != null)
            {
                try
                {
                    EngagetoEntities.Entities.Conversations receivedMessage = new EngagetoEntities.Entities.Conversations();
                    receivedMessage.WhatsAppMessageId = textMessage.Id;
                   // if (!_appDbContext.Conversations.Any(x => x.WhatsAppMessageId == receivedMessage.WhatsAppMessageId))
                   if(true)
                    {
                        receivedMessage.From = textMessage.From;
                        /*receivedMessage.To = textMessageMetadata.PhoneNumberId;*/

                        var BusinessId = _dbContext.BusinessDetailsMetas.FirstOrDefault(m => m.PhoneNumberID == textMessageMetadata.PhoneNumberId)?.BusinessId;
                        if (BusinessId == null)
                        {
                            BusinessId = textMessageMetadata.PhoneNumberId;
                        }
                        // Retrieve the second item or null if not found
                        Guid businessId = Guid.TryParse(BusinessId, out var tempGuid) ? tempGuid : Guid.Empty;                       
                        receivedMessage.To = BusinessId;
                        receivedMessage.BusinessId = businessId;                     
                        if (textMessage.Context != null)
                            receivedMessage.ReplyId = textMessage.Context.Id;
                        receivedMessage.Status = ConvStatus.sent;
                        var EnableAutomation = false;
                        if (textMessage.Type == "button")
                        {
                            receivedMessage.TextMessage = textMessage.Button.text;
                        }

                        else if (textMessage.Type == "interactive")
                        {
                            if (textMessage.Interactive.Type == "button_reply")
                            {
                                receivedMessage.TextMessage = textMessage.Interactive.ButtonReply.Title;

                            }
                            else if (textMessage.Interactive.Type == "list_reply")
                            {
                                var List = textMessage.Interactive.ListReply;
                                receivedMessage.TextMessage = List.Title + "\n" + List.Description;

                            }
                        }
                        else
                        {
                            EnableAutomation = true;
                            receivedMessage.TextMessage = textMessage.Text.Body;
                        }

                        receivedMessage.CreatedAt = DateTime.UtcNow;
                        string dateTimeString = receivedMessage.CreatedAt.ToString("yyyy-MM-ddTHH:mm:ss.fffffff");

                        // Parse the string back to a DateTime object
                        DateTime parsedDateTime = DateTime.ParseExact(dateTimeString, "yyyy-MM-ddTHH:mm:ss.fffffff", System.Globalization.CultureInfo.InvariantCulture);
                        receivedMessage.MessageType = MessageType.Normal;

                        // Assign parsed DateTime back to sentMessage.CreatedAt
                        receivedMessage.CreatedAt = parsedDateTime;

                        var Contact = _dbContext.Contacts.FirstOrDefault(m => ((m.CountryCode + m.Contact).Replace("+", "") ==
                        textMessage.From.Replace("+","")) && (m.BusinessId.ToString().ToLower() == BusinessId.ToLower()) && (m.IsActive == true));
                        receivedMessage.ContactId = Contact?.ContactId ?? Guid.Empty;
                        var Obj = _dbContext.Conversations.Add(receivedMessage);
                        _appDbContext.SaveChanges();

                        List<EngagetoEntities.Entities.Conversations> query = new();
                        if (Obj.Entity.ReplyId != null)
                        {
                            query = await _dbContext.Conversations.Where(m => m.WhatsAppMessageId == Obj.Entity.ReplyId).ToListAsync();
                        }

                        // company id , event, conversation 
                        List<EngagetoEntities.Dtos.ConversationDtos.ConversationDto> conversations = new();
                        EngagetoEntities.Dtos.WebhookDtos.Messages messages = new();
                        var obj = messages.Message(Obj.Entity, query);                     
                        if (Contact != null)
                        {
                            obj.Name = Contact.Name;
                            obj.ChatStatus = Contact.ChatStatus.ToString();
                            conversations.Add(obj);
                            var data = _dbContext.Users.Where(m => m.CompanyId == BusinessId);
                            if (data != null)
                            {
                                var UserIds = data.Select(m => m.Id).ToList();
                                foreach (var UserId in UserIds)
                                {
                                    await messageHub.Clients.Groups(businessId.ToString().ToLower() + UserId.ToString().ToLower()).RenderContacts();
                                    await messageHub.Clients.Groups(businessId.ToString().ToLower() + UserId.ToString().ToLower()).ReceiveMessageFromServer(conversations);

                                }
                            }
                            bool Optresult = false;
                            if (textMessage.Type == "button")
                            {
                                Optresult = await ReceiveNotificationOptKeyword(textMessage, textMessageMetadata, BusinessId);

                                if (!string.IsNullOrEmpty(textMessage?.Context?.Id))
                                {
                                    string oldMessaageId = textMessage.Context?.Id ?? string.Empty;
                                    var oldConversation = await _appDbContext.Conversations.FirstOrDefaultAsync(x => x.WhatsAppMessageId == oldMessaageId);
                                    if (oldConversation?.MessageType == MessageType.Template)
                                    {
                                        var template = await _appDbContext.Templates.FirstOrDefaultAsync(x => x.TemplateId.ToString() == oldConversation.ReferenceId);
                                        if (!string.IsNullOrEmpty(template?.SubCategory))
                                        {
                                            Contact.SubCategory = template.SubCategory;
                                        }
                                    }
                                }
                            }
                            if (textMessage.Type == "text")
                            {
                                Optresult = await ReceiveNotificationOptKeywordTextMedia(textMessage, textMessageMetadata, BusinessId);
                            }
                            if (Contact.DelayResponseJobID != null)
                            {
                                _jobService.Delete(Contact.DelayResponseJobID);
                                Contact.DelayResponseJobID = null;
                                _dbContext.Contacts.Update(Contact);
                                _dbContext.SaveChanges();
                            }
                            var conversation = Obj.Entity.Clone() as EngagetoEntities.Entities.Conversations;
                            conversation.Status = ConvStatus.received;
                            await _webhookService.SaveWebhookEventsAsync(BusinessId, "text", conversation);

                            //Auto Reply Automation message integration
                            var response = await SendAutoReplyMessageAsync(conversation, BusinessId);
                            var Data = _dbContext.Templates.Where(m => m.BusinessId.ToLower() == businessId.ToString().ToLower());
                            //lead gen processing
                            await _leadProcessingService.SetLeadDataAsync(new IntegrationAccountRecord(businessId.ToString(), contact: Contact, conversation: conversation, @events: new List<IntegrationEvent> { IntegrationEvent.OneTimeLeadGen,IntegrationEvent.Received,IntegrationEvent.OneTimeReceived }), CancellationToken.None);
                            await _automationSettingService.SendAutomationMessageAsync(BusinessId,Contact,conversation,Guid.Empty);
                            //If normal message then only automation inboxsettings will send
                            if (EnableAutomation && !Optresult)
                            {
                                if ((Data != null || Data.Count() > 0) && response != true)
                                {
                                    await InboxSetting(Data, Contact, textMessage.From, BusinessId);
                                }
                            }
                            if (Contact.ChatStatus != EngagetoEntities.Enums.ChatStatus.open)
                            {
                                await _inboxService.SaveAndUpdateChatStatusAsync(Contact.ContactId,
                                    EngagetoEntities.Enums.ChatStatus.open, Contact.UserId ?? null);
                                Contact.ChatStatus = EngagetoEntities.Enums.ChatStatus.open;
                                _dbContext.Contacts.Update(Contact);
                                _dbContext.SaveChanges();
                            }
                        }
                    }
                }
                catch (Exception ex)
                {
                    throw ex;
                }
            }
        }
        //Receive Notification OptKeyword through webhook in quick reply button of template
        public async Task<bool> ReceiveNotificationOptKeyword(TextMessage textMessage, TextMessageMetadata textMessageMetadata, string BusinessId)
        {
            bool Optresult = false;
            if (textMessage != null && textMessageMetadata != null)
            {
                try
                {
                    var optKeywords = await _dbContext.OptInManagement.Where(x => x.BusinessId == BusinessId && (x.OptOutMessageToggle ?? false) == true).ToListAsync();
                    var contacts = await _dbContext.Contacts.ToListAsync();
                    List<WhatsAppResponse> results = new List<EngagetoEntities.Dtos.MetaDto.WhatsAppResponse>();

                    // Find the contact that matches the criteria
                    var contact = contacts.FirstOrDefault(m => (m.CountryCode + m.Contact) == ("+" + textMessage.From) && m.BusinessId.ToString().ToLower() == BusinessId.ToLower() && m.IsActive);

                    if (contact != null)
                    {
                        // Process opt-out keywords
                        if (optKeywords != null && optKeywords.Any())
                        {
                            var optOutKeywords = JsonSerializer.Deserialize<List<string>>(optKeywords.FirstOrDefault()?.OptOutKeyword ?? "[]")?.Select(keyword => keyword.ToLower()).ToList();

                            if (optOutKeywords != null && optOutKeywords.Contains(textMessage.Button.text.ToLower()))
                            {

                                if (!string.IsNullOrEmpty(optKeywords?.FirstOrDefault()?.TextOptOut) && string.IsNullOrEmpty(optKeywords?.FirstOrDefault()?.UrlOptOut)
                                                                                                     && optKeywords?.FirstOrDefault()?.OptOutMessageToggle == true)
                                {
                                    results = await SendTextOrEmojiMessagess(optKeywords?.FirstOrDefault().TextOptOut,
                                                                             Guid.Parse(optKeywords?.FirstOrDefault().BusinessId.ToString()),
                                                                             textMessage.From);
                                }
                                else if (!string.IsNullOrEmpty(optKeywords?.FirstOrDefault()?.TextOptOut) && !string.IsNullOrEmpty(optKeywords?.FirstOrDefault()?.UrlOptOut)
                                                                                                          && optKeywords?.FirstOrDefault()?.OptOutMessageToggle == true)
                                {

                                    results = await SendMediaMessages(optKeywords?.FirstOrDefault().TextOptOut,
                                                                      optKeywords?.FirstOrDefault()?.UrlOptOut,
                                                                      Guid.Parse(optKeywords?.FirstOrDefault().BusinessId.ToString()),
                                                                      textMessage.From);
                                }
                                if (results.Count() > 0)
                                {
                                    var optInManagement = optKeywords.FirstOrDefault();
                                    optInManagement.OptOutWhatsAppMessageId = results.FirstOrDefault().Messages.FirstOrDefault().Id;
                                    await _dbContext.SaveChangesAsync();
                                }
                                contact.IsOptIn = EngagetoEntities.Enums.Is_OptIn.optout;
                                await _dbContext.SaveChangesAsync();

                                Optresult = true;
                                return Optresult;

                            }
                            // Process opt-in keywords
                            if (optKeywords != null && optKeywords.Any())
                            {
                                var optInKeywords = JsonSerializer.Deserialize<List<string>>(optKeywords.FirstOrDefault()?.OptInKeyword ?? "[]")?.Select(keyword => keyword.ToLower()).ToList();

                                if (optInKeywords != null && optInKeywords.Contains(textMessage.Button.text.ToLower()))
                                {
                                    if (!string.IsNullOrEmpty(optKeywords?.FirstOrDefault()?.TextOptIn)
                                                             && string.IsNullOrEmpty(optKeywords?.FirstOrDefault()?.UrlOptIn)
                                                             && optKeywords?.FirstOrDefault()?.OptInMessageToggle == true)
                                    {
                                        results = await SendTextOrEmojiMessagess(optKeywords?.FirstOrDefault().TextOptIn,
                                                                                Guid.Parse(optKeywords?.FirstOrDefault().BusinessId.ToString()),
                                                                                textMessage.From);
                                    }
                                    else if (!string.IsNullOrEmpty(optKeywords?.FirstOrDefault()?.TextOptIn)
                                                                  && !string.IsNullOrEmpty(optKeywords?.FirstOrDefault()?.UrlOptIn)
                                                                  && optKeywords?.FirstOrDefault()?.OptInMessageToggle == true)
                                    {


                                        results = await SendMediaMessages(optKeywords?.FirstOrDefault().TextOptIn,
                                                                         optKeywords?.FirstOrDefault()?.UrlOptIn,
                                                                         Guid.Parse(optKeywords?.FirstOrDefault().BusinessId.ToString()),
                                                                         textMessage.From);
                                    }
                                    if (results.Count() > 0)
                                    {
                                        var optInManagement = optKeywords.FirstOrDefault();
                                        optInManagement.OptInWhatsAppMessageId = results.FirstOrDefault().Messages.FirstOrDefault().Id;
                                        await _dbContext.SaveChangesAsync();
                                    }

                                    contact.IsOptIn = EngagetoEntities.Enums.Is_OptIn.optin;
                                    await _dbContext.SaveChangesAsync();

                                    Optresult = true;
                                    return Optresult;
                                }
                            }
                        }
                    }
                }
                catch (Exception ex)
                {
                    throw new Exception($"An error occurred: {ex.Message}");
                }
            }
            Optresult = true;
            return Optresult;
        }
        public async Task<bool> ReceiveNotificationOptKeywordTextMedia(TextMessage textMessage, TextMessageMetadata textMessageMetadata, string BusinessId)
        {
            bool Optresult = false;
            if (textMessage != null && textMessageMetadata != null)
            {
                try
                {
                    var optKeywords = await _dbContext.OptInManagement.Where(x => x.BusinessId == BusinessId && (x.OptOutMessageToggle ?? false) == true).ToListAsync();
                    var contacts = await _dbContext.Contacts.ToListAsync();
                    List<WhatsAppResponse> results = new List<WhatsAppResponse>();

                    // Find the contact that matches the criteria
                    var contact = contacts.FirstOrDefault(m => (m.CountryCode + m.Contact) == ("+" + textMessage.From) && m.BusinessId.ToString().ToLower() == BusinessId.ToLower() && m.IsActive);

                    if (contact != null)
                    {
                        // Process opt-out keywords
                        if (optKeywords != null && optKeywords.Any())
                        {
                            var optOutKeywords = JsonSerializer.Deserialize<List<string>>(optKeywords.FirstOrDefault()?.OptOutKeyword ?? "[]")?.Select(keyword => keyword.ToLower()).ToList();
                            var BussinessId = Guid.Parse(optKeywords?.FirstOrDefault().BusinessId.ToString());
                            var optKeyword = optKeywords?.FirstOrDefault()?.TextOptOut;
                            var UrlOptoutnull = string.IsNullOrEmpty(optKeywords?.FirstOrDefault()?.UrlOptOut);
                            var UrlOptOutnotnull = !string.IsNullOrEmpty(optKeywords?.FirstOrDefault()?.UrlOptOut);
                            var Urloptout = optKeywords?.FirstOrDefault()?.UrlOptOut;
                            var OptOutMessageToggles = optKeywords?.FirstOrDefault()?.OptOutMessageToggle == true;

                            if (optOutKeywords != null && optOutKeywords.Contains(textMessage.Text.Body.ToLower()))
                            {
                                if (!string.IsNullOrEmpty(optKeyword) && UrlOptoutnull && OptOutMessageToggles)
                                {
                                    results = await SendTextOrEmojiMessagess(optKeyword, BussinessId, textMessage.From);
                                }
                                else if (!string.IsNullOrEmpty(optKeyword) && UrlOptOutnotnull && OptOutMessageToggles)
                                {
                                    results = await SendMediaMessages(optKeyword, Urloptout, BussinessId, textMessage.From);
                                }
                                if (results.Count() > 0)
                                {
                                    var optInManagement = optKeywords.FirstOrDefault();
                                    optInManagement.OptOutWhatsAppMessageId = results.FirstOrDefault().Messages.FirstOrDefault().Id;
                                    await _dbContext.SaveChangesAsync();
                                }

                                contact.IsOptIn = EngagetoEntities.Enums.Is_OptIn.optout;
                                await _dbContext.SaveChangesAsync();

                                Optresult = true;
                                return Optresult;
                            }
                        }
                        // Process opt-in keywords
                        if (optKeywords != null && optKeywords.Any())
                        {
                            var optInKeywords = JsonSerializer.Deserialize<List<string>>(optKeywords.FirstOrDefault()?.OptInKeyword ?? "[]")?.Select(keyword => keyword.ToLower()).ToList();

                            var BussinessId = Guid.Parse(optKeywords?.FirstOrDefault().BusinessId.ToString());
                            var optKeyword = optKeywords?.FirstOrDefault()?.TextOptIn;
                            var UrlOptInnull = string.IsNullOrEmpty(optKeywords?.FirstOrDefault()?.UrlOptIn);
                            var UrlOptInnotnull = !string.IsNullOrEmpty(optKeywords?.FirstOrDefault()?.UrlOptIn);
                            var UrloptIn = optKeywords?.FirstOrDefault()?.UrlOptIn;
                            var OptInMessageToggles = optKeywords?.FirstOrDefault()?.OptInMessageToggle == true;

                            if (optInKeywords != null && optInKeywords.Contains(textMessage.Text.Body.ToLower()))
                            {
                                if (!string.IsNullOrEmpty(optKeyword) && UrlOptInnull && OptInMessageToggles)
                                {
                                    results = await SendTextOrEmojiMessagess(optKeyword, BussinessId, textMessage.From);
                                }
                                else if (!string.IsNullOrEmpty(optKeyword) && UrlOptInnotnull && OptInMessageToggles)
                                {
                                    results = await SendMediaMessages(optKeyword, UrloptIn, BussinessId, textMessage.From);
                                }
                                if (results.Count() > 0)
                                {
                                    var optInManagement = optKeywords.FirstOrDefault();
                                    optInManagement.OptInWhatsAppMessageId = results.FirstOrDefault()?.Messages?.FirstOrDefault()?.Id;
                                    await _dbContext.SaveChangesAsync();
                                }
                                contact.IsOptIn = EngagetoEntities.Enums.Is_OptIn.optin;
                                await _dbContext.SaveChangesAsync();

                                Optresult = true;
                                return Optresult;
                            }
                        }
                    }
                }
                catch (Exception ex)
                {
                    throw new Exception($"An error occurred: {ex.Message}");
                }
            }
            return Optresult;
        }
        // send OPt massage response
        public async Task<List<WhatsAppResponse>> SendTextOrEmojiMessagess(string text, Guid BusinessId, string phoneNumberId)
        {
            var results = new List<WhatsAppResponse>();

            if (!string.IsNullOrEmpty(phoneNumberId) && !string.IsNullOrEmpty(text))
            {
                TextMessageRequest textMessageRequest = new TextMessageRequest
                {
                    To = phoneNumberId,
                    Text = new WhatsAppText
                    {
                        Body = text,
                        PreviewUrl = false
                    }
                };
                var result = await whatsAppReceive.SendMessageAsync(textMessageRequest, BusinessId);

                results.Add(result);
            }
            return results;
        }
        // send OPt massage response with image
        public async Task<List<WhatsAppResponse>> SendMediaMessages(string Text, string MediaUrl, Guid BusinessId, string phoneNumberId)
        {
            var results = new List<WhatsAppResponse>();
            var result = new WhatsAppResponse();
            var regex = new Regex(@"^\+?[1-9]\d{1,14}$");

            if (!string.IsNullOrEmpty(phoneNumberId) && !string.IsNullOrEmpty(MediaUrl))
            {
                var client = _httpClientFactory.CreateClient();
                var response = await client.GetAsync(MediaUrl);
                if (response.IsSuccessStatusCode)
                {
                    var contentType = response.Content.Headers.ContentType?.MediaType;
                    Uri uri = new Uri(MediaUrl);
                    System.IO.Path.GetFileName(uri.LocalPath);
                    var fileName = System.IO.Path.GetFileName(uri.LocalPath);

                    if (contentType != null)
                    {
                        // Check media type based on content type
                        if (contentType.StartsWith("image"))
                        {
                            ImageMessageRequest imageMessageRequest = new ImageMessageRequest();
                            imageMessageRequest.To = phoneNumberId;
                            imageMessageRequest.Image = new WhatsAppImage();
                            imageMessageRequest.Image.Link = MediaUrl;
                            imageMessageRequest.Image.Caption = Text ?? null;
                            result = await whatsAppReceive.SendMessageAsync(imageMessageRequest, BusinessId);
                        }

                    }
                }
                results.Add(result);
            }
            return results;
        }

        /// <summary>
        /// Handles the reception and processing of image messages.
        /// </summary>
        /// <param name="imageMessage">The image message received.</param>
        /// <param name="image">The image associated with the message.</param>
        /// <param name="imageMessageMetadata">Metadata associated with the image message.</param>
        /// <returns>A task that represents the asynchronous operation.</returns>
        public async Task ReceivedImageMessage(ImageMessage imageMessage, Image image, ImageMessageMetadata imageMessageMetadata)
        {
            if (imageMessage != null && image != null)
            {
                try
                {
                    EngagetoEntities.Entities.Conversations receivedMessage = new();
                    receivedMessage.WhatsAppMessageId = imageMessage.Id;
                    if (!_appDbContext.Conversations.Any(x => x.WhatsAppMessageId == receivedMessage.WhatsAppMessageId))
                    {
                        receivedMessage.From = imageMessage.From;
                        //receivedMessage.To = imageMessageMetadata.PhoneNumberId;
                        if (imageMessage.Context != null)
                            receivedMessage.ReplyId = imageMessage.Context.Id;
                        receivedMessage.Status = ConvStatus.sent;
                        receivedMessage.MediaUrl = await mediaURL.GetByMediaId(image.Id, imageMessageMetadata.PhoneNumberId);

                        var BusinessId = _dbContext.BusinessDetailsMetas.FirstOrDefault(m => m.PhoneNumberID == imageMessageMetadata.PhoneNumberId)?.BusinessId;
                        if (BusinessId == null)
                        {
                            BusinessId = imageMessageMetadata.PhoneNumberId;
                        }
                        // Retrieve the second item or null if not found
                        var Contact = _dbContext.Contacts.FirstOrDefault(m => ((m.CountryCode + m.Contact) == ("+" + imageMessage.From)) && (m.BusinessId.ToString().ToLower() == BusinessId.ToLower()) && (m.IsActive == true));
                        Guid businessId = Guid.TryParse(BusinessId, out var tempGuid) ? tempGuid : Guid.Empty;
                        ;
                        receivedMessage.To = BusinessId;
                        receivedMessage.BusinessId = businessId;
                        receivedMessage.ContactId = Contact?.ContactId ?? Guid.Empty;
                        receivedMessage.MediaMimeType = image.MimeType;
                        receivedMessage.MediaCaption = image.Caption;
                        receivedMessage.CreatedAt = DateTime.UtcNow;
                        string dateTimeString = receivedMessage.CreatedAt.ToString("yyyy-MM-ddTHH:mm:ss.fffffff");

                        // Parse the string back to a DateTime object
                        DateTime parsedDateTime = DateTime.ParseExact(dateTimeString, "yyyy-MM-ddTHH:mm:ss.fffffff", System.Globalization.CultureInfo.InvariantCulture);
                        receivedMessage.MessageType = MessageType.Normal;

                        // Assign parsed DateTime back to sentMessage.CreatedAt
                        receivedMessage.CreatedAt = parsedDateTime;

                        var Obj = _dbContext.Conversations.Add(receivedMessage);
                        _dbContext.SaveChanges();
                        List<EngagetoEntities.Entities.Conversations> query = new();
                        if (Obj.Entity.ReplyId != null)
                        {
                            query = await _dbContext.Conversations.Where(m => m.WhatsAppMessageId == Obj.Entity.ReplyId).ToListAsync();
                        }
                        List<EngagetoEntities.Dtos.ConversationDtos.ConversationDto> conversations = new();
                        EngagetoEntities.Dtos.WebhookDtos.Messages messages = new();
                        var obj = messages.Message(Obj.Entity, query);

                        if (Contact != null)
                        {

                            obj.Name = Contact.Name;
                            obj.ChatStatus = Contact.ChatStatus.ToString();
                            conversations.Add(obj);

                            var data = await _dbContext.Users.Where(m => m.CompanyId == BusinessId).ToListAsync();

                            if (data != null)
                            {
                                var UserIds = data.Select(m => m.Id).ToList();
                                foreach (var UserId in UserIds)
                                {
                                    await messageHub.Clients.Groups(businessId.ToString().ToLower() + UserId.ToString().ToLower()).RenderContacts();
                                    await messageHub.Clients.Groups(businessId.ToString().ToLower() + UserId.ToString().ToLower()).ReceiveMessageFromServer(conversations);

                                }
                            }

                            if (Contact.DelayResponseJobID != null)
                            {
                                _jobService.Delete(Contact.DelayResponseJobID);
                                Contact.DelayResponseJobID = null;
                                _dbContext.Contacts.Update(Contact);
                                _dbContext.SaveChanges();
                            }
                            var conversation = Obj.Entity.Clone() as EngagetoEntities.Entities.Conversations;
                            conversation.Status = ConvStatus.received;
                            await _webhookService.SaveWebhookEventsAsync(BusinessId, "image", conversation);
                            var response = await SendAutoReplyMessageAsync(conversation, BusinessId);
                            var Data = _dbContext.Templates.Where(m => m.BusinessId.ToLower() == businessId.ToString().ToLower());
                            if (Data != null && Data.Count() > 0 && response != true)
                            {
                                await InboxSetting(Data, Contact, imageMessage.From, BusinessId);

                            }
                            if (Contact.ChatStatus != EngagetoEntities.Enums.ChatStatus.open)
                            {
                                await _inboxService.SaveAndUpdateChatStatusAsync(Contact.ContactId,
                                  EngagetoEntities.Enums.ChatStatus.open, Contact.UserId ?? null);

                                Contact.ChatStatus = EngagetoEntities.Enums.ChatStatus.open;
                                _dbContext.Contacts.Update(Contact);
                                _dbContext.SaveChanges();
                            }

                        }
                    }
                }
                catch (Exception ex)
                {
                    throw ex;
                }
            }
        }
        /// <summary>
        /// Handles the reception and processing of document messages.
        /// </summary>
        /// <param name="documentMessage">The document message received.</param>
        /// <param name="document">The document associated with the message.</param>
        /// <param name="documentMessageMetadata">Metadata associated with the document message.</param>
        /// <returns>A task that represents the asynchronous operation.</returns>
        public async Task ReceivedAudioMessage(AudioMessage audioMessage, Audio audio, AudioMessageMetadata audioMessageMetadata)
        {
            if (audioMessage != null && audio != null)
            {
                try
                {
                    EngagetoEntities.Entities.Conversations receivedMessage = new EngagetoEntities.Entities.Conversations();
                    receivedMessage.WhatsAppMessageId = audioMessage.Id;
                    receivedMessage.From = audioMessage.From;
                    //receivedMessage.To = audioMessageMetadata.PhoneNumberId;
                    if (audioMessage.Context != null)
                        receivedMessage.ReplyId = audioMessage.Context.Id;
                    receivedMessage.Status = ConvStatus.sent;
                    receivedMessage.MediaUrl = await mediaURL.GetByMediaId(audio.Id, audioMessageMetadata.PhoneNumberId);
                    receivedMessage.MediaMimeType = audio.MimeType;

                    var BusinessId = _dbContext.BusinessDetailsMetas.FirstOrDefault(m => m.PhoneNumberID == audioMessageMetadata.PhoneNumberId)?.BusinessId;
                    var Contact = _dbContext.Contacts.FirstOrDefault(m => ((m.CountryCode + m.Contact) == ("+" + audioMessage.From)) && (m.BusinessId.ToString().ToLower() == BusinessId.ToLower()) && (m.IsActive == true));
                    if (BusinessId == null)
                    {
                        BusinessId = audioMessageMetadata.PhoneNumberId;
                    }
                    // Retrieve the second item or null if not found
                    Guid businessId = Guid.TryParse(BusinessId, out var tempGuid) ? tempGuid : Guid.Empty;
                    ;
                    receivedMessage.BusinessId = businessId;
                    receivedMessage.ContactId = Contact?.ContactId ?? Guid.Empty;
                    receivedMessage.To = BusinessId;

                    receivedMessage.CreatedAt = DateTime.UtcNow;
                    string dateTimeString = receivedMessage.CreatedAt.ToString("yyyy-MM-ddTHH:mm:ss.fffffff");
                    receivedMessage.MessageType = MessageType.Normal;

                    // Parse the string back to a DateTime object
                    DateTime parsedDateTime = DateTime.ParseExact(dateTimeString, "yyyy-MM-ddTHH:mm:ss.fffffff", System.Globalization.CultureInfo.InvariantCulture);

                    // Assign parsed DateTime back to sentMessage.CreatedAt
                    receivedMessage.CreatedAt = parsedDateTime;
                    var Obj = _dbContext.Conversations.Add(receivedMessage);
                    _appDbContext.SaveChanges();
                    List<EngagetoEntities.Entities.Conversations> query = new();
                    if (Obj.Entity.ReplyId != null)
                    {
                        query = await _dbContext.Conversations.Where(m => m.WhatsAppMessageId == Obj.Entity.ReplyId).ToListAsync();
                    }

                    List<EngagetoEntities.Dtos.ConversationDtos.ConversationDto> conversations = new();
                    EngagetoEntities.Dtos.WebhookDtos.Messages messages = new();
                    var obj = messages.Message(Obj.Entity, query);

                    if (Contact != null)
                    {
                        obj.Name = Contact.Name;
                        obj.ChatStatus = Contact.ChatStatus.ToString();
                        conversations.Add(obj);
                        var data = _dbContext.Users.Where(m => m.CompanyId == BusinessId);


                        if (data != null)
                        {
                            var UserIds = data.Select(m => m.Id).ToList();
                            foreach (var UserId in UserIds)
                            {
                                await messageHub.Clients.Groups(businessId.ToString().ToLower() + UserId.ToString().ToLower()).RenderContacts();
                                await messageHub.Clients.Groups(businessId.ToString().ToLower() + UserId.ToString().ToLower()).ReceiveMessageFromServer(conversations);

                            }
                        }
                        if (Contact.DelayResponseJobID != null)
                        {
                            _jobService.Delete(Contact.DelayResponseJobID);
                            Contact.DelayResponseJobID = null;
                            _dbContext.Contacts.Update(Contact);
                            _dbContext.SaveChanges();
                        }
                        var conversation = Obj.Entity.Clone() as EngagetoEntities.Entities.Conversations;
                        conversation.Status = ConvStatus.received;
                        var response = await SendAutoReplyMessageAsync(conversation, BusinessId);
                        await _webhookService.SaveWebhookEventsAsync(BusinessId, "audio", conversation);

                        var Data = _dbContext.Templates.Where(m => m.BusinessId.ToLower() == businessId.ToString().ToLower());
                        if (Data != null && Data.Count() > 0 && response != true)
                        {
                            await InboxSetting(Data, Contact, audioMessage.From, BusinessId);
                        }


                        if (Contact.ChatStatus != EngagetoEntities.Enums.ChatStatus.open)
                        {
                            await _inboxService.SaveAndUpdateChatStatusAsync(Contact.ContactId,
                              EngagetoEntities.Enums.ChatStatus.open, Contact.UserId ?? null);

                            Contact.ChatStatus = EngagetoEntities.Enums.ChatStatus.open;
                            _dbContext.Contacts.Update(Contact);
                            _dbContext.SaveChanges();
                        }
                    }
                }
                catch (Exception ex)
                {
                    throw ex;
                }

            }
        }

        /// <summary>
        /// Handles the reception and processing of video messages.
        /// </summary>
        /// <param name="videoMessage">The video message received.</param>
        /// <param name="video">The video associated with the message.</param>
        /// <param name="videoMessageMetadata">Metadata associated with the video message.</param>
        /// <returns>A task that represents the asynchronous operation.</returns>
        public async Task ReceivedVideoMessage(VideoMessage videoMessage, Video video, VideoMessageMetadata videoMessageMetadata)
        {
            if (videoMessage != null && video != null)
            {
                try
                {
                    EngagetoEntities.Entities.Conversations receivedMessage = new();
                    receivedMessage.WhatsAppMessageId = videoMessage.Id;
                    if (!_appDbContext.Conversations.Any(x => x.WhatsAppMessageId == receivedMessage.WhatsAppMessageId))
                    {
                        receivedMessage.From = videoMessage.From;
                        //receivedMessage.To = videoMessageMetadata.PhoneNumberId;

                        if (videoMessage.Context != null)
                            receivedMessage.ReplyId = videoMessage.Context.Id;
                        receivedMessage.Status = ConvStatus.sent;
                        receivedMessage.MediaUrl = await mediaURL.GetByMediaId(video.Id, videoMessageMetadata.PhoneNumberId);
                        receivedMessage.MediaMimeType = video.MimeType;
                        receivedMessage.MediaCaption = video.Caption;

                        receivedMessage.MessageType = MessageType.Normal;

                        var BusinessId = _dbContext.BusinessDetailsMetas.FirstOrDefault(m => m.PhoneNumberID == videoMessageMetadata.PhoneNumberId)?.BusinessId;
                        if (BusinessId == null)
                        {
                            BusinessId = videoMessageMetadata.PhoneNumberId;
                        }
                        // Retrieve the second item or null if not found
                        Guid businessId = Guid.TryParse(BusinessId, out var tempGuid) ? tempGuid : Guid.Empty;
                        ;
                        var Contact = _dbContext.Contacts.FirstOrDefault(m => ((m.CountryCode + m.Contact) == ("+" + videoMessage.From)) && (m.BusinessId.ToString().ToLower() == BusinessId.ToLower()) && (m.IsActive == true));

                        receivedMessage.To = BusinessId;
                        receivedMessage.BusinessId = businessId;
                        receivedMessage.ContactId = Contact?.ContactId ?? Guid.Empty;
                        receivedMessage.CreatedAt = DateTime.UtcNow;
                        string dateTimeString = receivedMessage.CreatedAt.ToString("yyyy-MM-ddTHH:mm:ss.fffffff");

                        // Parse the string back to a DateTime object
                        DateTime parsedDateTime = DateTime.ParseExact(dateTimeString, "yyyy-MM-ddTHH:mm:ss.fffffff", System.Globalization.CultureInfo.InvariantCulture);

                        // Assign parsed DateTime back to sentMessage.CreatedAt
                        receivedMessage.CreatedAt = parsedDateTime;
                        var Obj = _dbContext.Conversations.Add(receivedMessage);
                        _appDbContext.SaveChanges();
                        List<EngagetoEntities.Entities.Conversations> query = new();
                        if (Obj.Entity.ReplyId != null)
                        {
                            query = await _dbContext.Conversations.Where(m => m.WhatsAppMessageId == Obj.Entity.ReplyId).ToListAsync();
                        }
                        List<EngagetoEntities.Dtos.ConversationDtos.ConversationDto> conversations = new();
                        EngagetoEntities.Dtos.WebhookDtos.Messages messages = new();
                        var obj = messages.Message(Obj.Entity, query);

                        if (Contact != null)
                        {
                            obj.Name = Contact.Name;
                            obj.ChatStatus = Contact.ChatStatus.ToString();
                            conversations.Add(obj);
                            var data = _dbContext.Users.Where(m => m.CompanyId == BusinessId);
                            if (data != null)
                            {
                                var UserIds = data.Select(m => m.Id).ToList();
                                foreach (var UserId in UserIds)
                                {
                                    await messageHub.Clients.Groups(businessId.ToString().ToLower() + UserId.ToString().ToLower()).RenderContacts();
                                    await messageHub.Clients.Groups(businessId.ToString().ToLower() + UserId.ToString().ToLower()).ReceiveMessageFromServer(conversations);

                                }
                            }
                            if (Contact.DelayResponseJobID != null)
                            {
                                _jobService.Delete(Contact.DelayResponseJobID);
                                Contact.DelayResponseJobID = null;
                                _dbContext.Contacts.Update(Contact);
                                _dbContext.SaveChanges();
                            }

                            var conversation = Obj.Entity.Clone() as EngagetoEntities.Entities.Conversations;
                            conversation.Status = ConvStatus.received;
                            var response = await SendAutoReplyMessageAsync(conversation, BusinessId);
                            await _webhookService.SaveWebhookEventsAsync(BusinessId, "video", conversation);
                            var Data = _dbContext.Templates.Where(m => m.BusinessId.ToLower() == businessId.ToString().ToLower());
                            if (Data != null && Data.Count() > 0 && response != true)
                            {
                                await InboxSetting(Data, Contact, videoMessage.From, BusinessId);
                            }
                            obj.Name = Contact.Name;
                            obj.ChatStatus = Contact.ChatStatus.ToString();
                            conversations.Add(obj);

                            if (Contact.ChatStatus != EngagetoEntities.Enums.ChatStatus.open)
                            {
                                await _inboxService.SaveAndUpdateChatStatusAsync(Contact.ContactId,
                                  EngagetoEntities.Enums.ChatStatus.open, Contact.UserId ?? null);

                                Contact.ChatStatus = EngagetoEntities.Enums.ChatStatus.open;
                                _dbContext.Contacts.Update(Contact);
                                _dbContext.SaveChanges();
                            }

                        }
                    }
                }
                catch (Exception ex)
                {
                    throw ex;
                }


            }
        }
        /// <summary>
        /// Handles the reception and processing of document messages.
        /// </summary>
        /// <param name="documentMessage">The document message received.</param>
        /// <param name="document">The document associated with the message.</param>
        /// <param name="documentMessageMetadata">Metadata associated with the document message.</param>
        /// <returns>A task that represents the asynchronous operation.</returns>
        public async Task ReceivedDocumentMessage(DocumentMessage documentMessage, Document document, DocumentMessageMetadata documentMessageMetadata)
        {
            if (documentMessage != null && document != null)
            {
                try
                {
                    EngagetoEntities.Entities.Conversations receivedMessage = new();
                    receivedMessage.WhatsAppMessageId = documentMessage.Id;
                    if (!_appDbContext.Conversations.Any(x => x.WhatsAppMessageId == receivedMessage.WhatsAppMessageId))
                    {
                        receivedMessage.From = documentMessage.From;
                        // receivedMessage.To = documentMessageMetadata.PhoneNumberId;
                        if (documentMessage.Context != null)
                            receivedMessage.ReplyId = documentMessage.Context.Id;
                        receivedMessage.Status = ConvStatus.sent;
                        receivedMessage.MediaUrl = await mediaURL.GetByMediaId(document.Id, documentMessageMetadata.PhoneNumberId);
                        receivedMessage.MediaMimeType = document.MimeType;
                        receivedMessage.MediaCaption = document.Caption;
                        receivedMessage.MediaFileName = document.Filename;
                        receivedMessage.CreatedAt = DateTime.UtcNow;
                        receivedMessage.MessageType = MessageType.Normal;
                        string dateTimeString = receivedMessage.CreatedAt.ToString("yyyy-MM-ddTHH:mm:ss.fffffff");

                        // Parse the string back to a DateTime object
                        DateTime parsedDateTime = DateTime.ParseExact(dateTimeString, "yyyy-MM-ddTHH:mm:ss.fffffff", System.Globalization.CultureInfo.InvariantCulture);

                        // Assign parsed DateTime back to sentMessage.CreatedAt
                        receivedMessage.CreatedAt = parsedDateTime;

                        var BusinessId = _dbContext.BusinessDetailsMetas.FirstOrDefault(m => m.PhoneNumberID == documentMessageMetadata.PhoneNumberId)?.BusinessId;
                        if (BusinessId == null)
                        {
                            BusinessId = documentMessageMetadata.PhoneNumberId;
                        }
                        // Retrieve the second item or null if not found
                        Guid businessId = Guid.TryParse(BusinessId, out var tempGuid) ? tempGuid : Guid.Empty;
                        
                        receivedMessage.To = BusinessId;
                        // Try parsing the string to Guid
                        var Contact = _dbContext.Contacts.FirstOrDefault(m => documentMessage.From.Contains(m.Contact) && (m.BusinessId.ToString().ToLower() == BusinessId.ToLower()) && m.IsActive == true);
                        receivedMessage.To = BusinessId;
                        receivedMessage.BusinessId = businessId;
                        receivedMessage.ContactId = Contact?.ContactId ?? Guid.Empty;
                        var Obj = _dbContext.Conversations.Add(receivedMessage);
                        _appDbContext.SaveChanges();
                        List<EngagetoEntities.Entities.Conversations>? query = new List<EngagetoEntities.Entities.Conversations>();
                        if (Obj.Entity.ReplyId != null)
                        {
                            query = _dbContext.Conversations?.Where(m => m.WhatsAppMessageId == Obj.Entity.ReplyId)?.ToList();
                        }

                        List<EngagetoEntities.Dtos.ConversationDtos.ConversationDto> conversations = new();
                        EngagetoEntities.Dtos.WebhookDtos.Messages messages = new();
                        var obj = messages.Message(Obj.Entity, query);

                        if (Contact != null)
                        {
                            obj.Name = Contact.Name;
                            obj.ChatStatus = Contact.ChatStatus.ToString();
                            conversations.Add(obj);
                            var data = _dbContext.Users.Where(m => m.CompanyId == BusinessId);
                            if (data != null)
                            {
                                var UserIds = data.Select(m => m.Id).ToList();
                                foreach (var UserId in UserIds)
                                {
                                    await messageHub.Clients.Groups(businessId.ToString().ToLower() + UserId.ToString().ToLower()).RenderContacts();
                                    await messageHub.Clients.Groups(businessId.ToString().ToLower() + UserId.ToString().ToLower()).ReceiveMessageFromServer(conversations);

                                }
                            }
                            if (Contact.DelayResponseJobID != null)
                            {
                                _jobService.Delete(Contact.DelayResponseJobID);
                                Contact.DelayResponseJobID = null;
                                _dbContext.Contacts.Update(Contact);
                                _dbContext.SaveChanges();
                            }
                            obj.Name = Contact.Name;
                            obj.ChatStatus = Contact.ChatStatus.ToString();
                            conversations.Add(obj);
                            if (Contact.ChatStatus != EngagetoEntities.Enums.ChatStatus.open)
                            {
                                await _inboxService.SaveAndUpdateChatStatusAsync(Contact.ContactId,
                                  EngagetoEntities.Enums.ChatStatus.open, Contact.UserId ?? null);

                                Contact.ChatStatus = EngagetoEntities.Enums.ChatStatus.open;
                                _dbContext.Contacts.Update(Contact);
                                _dbContext.SaveChanges();

                            }

                            var conversation = Obj.Entity.Clone() as EngagetoEntities.Entities.Conversations;
                            conversation.Status = ConvStatus.received;
                            await _webhookService.SaveWebhookEventsAsync(BusinessId, "document", conversation);
                            var response = await SendAutoReplyMessageAsync(conversation, BusinessId);
                            var Data = _dbContext.Templates.Where(m => m.BusinessId.ToLower() == businessId.ToString().ToLower());

                            if (Data != null && Data.Count() > 0 && response != true)
                            {
                                await InboxSetting(Data, Contact, documentMessage.From, BusinessId);
                            }
                        }
                    }
                }
                catch (Exception ex)
                {
                    throw ex;
                }
            }
        }

        #region Send InboxSetting Messagee
        public async Task InboxSetting(IQueryable<Template> Data, EngagetoEntities.Entities.Contacts Contact, string PhoneNumber, string BusinessId)
        {
            string extractCountryCode = PhoneNumber.StartsWith("+") ? PhoneNumber.Substring(1) : PhoneNumber;
            bool IsConversationHistory = _appDbContext.Conversations.Where(c => c.From == extractCountryCode && c.To == BusinessId).Take(2).Count() < 2 ? true : false;
            if (Contact.ChatStatus == EngagetoEntities.Enums.ChatStatus.New || IsConversationHistory)
            {
                if ((Data.Where(m => (m.Enabled == true) && (m.Feature == EngagetoEntities.Enums.Feature.WelcomeMessage))).Any())
                {
                    List<EngagetoEntities.Entities.InboxSettingsVariable>? Variables = new();
                    var obj = Data.FirstOrDefault(m => m.Feature == EngagetoEntities.Enums.Feature.WelcomeMessage);
                    if (obj != null)
                    {
                        Variables = _dbContext.InboxSettingsVariables.Where(m => m.TemplateId == obj.TemplateId)?.ToList();

                    }
                    await Message(obj.Body, obj.MediaAwsUrl, Variables.ToList(), Contact, PhoneNumber, BusinessId);
                }
            }

            else if (IsOutOffOffice(BusinessId))
            {

                if ((Data.Where(m => (m.Enabled == true) && (m.Feature == EngagetoEntities.Enums.Feature.OutofOfficeMessage))).Any())
                {
                    List<EngagetoEntities.Entities.InboxSettingsVariable>? Variables = new();
                    var obj = Data.FirstOrDefault(m => m.Feature == Feature.OutofOfficeMessage);
                    if (obj != null)
                    {
                        Variables = _dbContext.InboxSettingsVariables.Where(m => m.TemplateId == obj.TemplateId)?.ToList();

                    }

                    await Message(obj.Body, obj.MediaAwsUrl, Variables.ToList(), Contact, PhoneNumber, BusinessId);
                }
            }
            else
            {
                if ((Data.Where(m => (m.Enabled == true) && (m.Feature == Feature.WelcomeMessage))).Any() && Contact.ChatStatus != EngagetoEntities.Enums.ChatStatus.open)
                {
                    List<EngagetoEntities.Entities.InboxSettingsVariable>? Variables = new();
                    var obj = Data.FirstOrDefault(m => m.Feature == Feature.WelcomeMessage);
                    if (obj != null)
                    {
                        Variables = _dbContext.InboxSettingsVariables.Where(m => m.TemplateId == obj.TemplateId)?.ToList();

                    }

                    await Message(obj.Body, obj.MediaAwsUrl, Variables.ToList(), Contact, PhoneNumber, BusinessId);
                }

                else if ((Data.Where(m => (m.Enabled == true) && (m.Feature == Feature.DelayedResponseMessage))).Any())
                {

                    await DelayResponse(Data, Contact, PhoneNumber, BusinessId);

                }
            }

        }
        public async Task Message(string Message, string? MediaFile, List<EngagetoEntities.Entities.InboxSettingsVariable>? Variables,
            EngagetoEntities.Entities.Contacts Contact, string PhoneNumber, string BusinessId)
        {

            TextMessageRequest textMessageRequest = new TextMessageRequest();
            textMessageRequest.To = PhoneNumber;
            textMessageRequest.Text = new WhatsAppText();
            ImageMessageRequest imageMessageRequest = new ImageMessageRequest();
            imageMessageRequest.To = PhoneNumber;
            VideoMessageRequest videoMessageRequest = new VideoMessageRequest();
            videoMessageRequest.To = PhoneNumber;
            videoMessageRequest.Video = new WhatsAppVideo();
            imageMessageRequest.Image = new WhatsAppImage();

            Regex regex1 = new Regex(@"\{\{\d+\}\}");
            bool ContainsVariable = regex1.IsMatch(Message);

            if (ContainsVariable)
            {
                if (Variables != null)
                {
                    foreach (var Variable in Variables)
                    {
                        object value = new object();
                        PropertyInfo propertyInfo = typeof(Contacts).GetProperty(Variable.Value);
                        if (propertyInfo != null)
                        {
                            value = propertyInfo.GetValue(Contact);
                            Console.WriteLine(value); // Outputs: <EMAIL>
                        }
                        Message = Message.Replace(Variable.Variable, System.String.IsNullOrEmpty(value.ToString()) ? Variable.FallBackValue : value.ToString());
                    }
                }
            }

            if (MediaFile != null && MediaFile != "")
            {

                var client = _httpClientFactory.CreateClient();
                var response = await client.GetAsync(MediaFile);
                var contentType = response.Content.Headers.ContentType?.MediaType;
                if (contentType != null)
                {
                    // Check media type based on content type
                    if (contentType.StartsWith("image"))
                    {
                        imageMessageRequest.Image.Link = MediaFile;
                        imageMessageRequest.Image.Caption = Message;
                        var result = await whatsAppReceive.SendMessageAsync(imageMessageRequest, Guid.Parse(BusinessId),
                            null, MessageType.InboxSettings);
                        // Image file
                        // Handle image processing or save to local storage
                    }
                    else if (contentType.StartsWith("video"))
                    {
                        videoMessageRequest.Video.Link = MediaFile;
                        videoMessageRequest.Video.Caption = Message;
                        var result = await whatsAppReceive.SendMessageAsync(videoMessageRequest, Guid.Parse(BusinessId),
                            null, MessageType.InboxSettings);
                        // Video file
                        // Handle video processing or save to local storage
                    }
                }
            }
            else
            {
                textMessageRequest.Text.Body = Message;
                textMessageRequest.Text.PreviewUrl = false;
                var Send = await whatsAppReceive.SendMessageAsync(textMessageRequest, Guid.Parse(BusinessId),
                    null, MessageType.InboxSettings);

            }
            if (Contact.DelayResponseJobID != null)
            {
                Contact.DelayResponseJobID = null;
                _dbContext.Contacts.Update(Contact);
                _dbContext.SaveChanges();
            }
        }


        public bool IsOutOffOffice(string BusinessId)
        {
            string DOW = DateTime.UtcNow.DayOfWeek.ToString().ToLower();
            var WorkingHours = _dbContext.WorkingHours.FirstOrDefault(m => m.BusinessId.ToString().ToLower() == BusinessId.ToLower());
            TimeSpan? To = null;
            TimeSpan? From = null;
            if (WorkingHours != null)
            {
                switch (DOW.ToLower()) // Use ToLower() to make the comparison case-insensitive
                {
                    case "sunday":
                        if (WorkingHours.SundayTo.HasValue)
                            To = (TimeSpan)WorkingHours?.SundayTo;
                        if (WorkingHours.SundayFrom.HasValue)
                            From = (TimeSpan)WorkingHours?.SundayFrom;
                        break;
                    case "monday":
                        if (WorkingHours.MondayTo.HasValue)
                            To = (TimeSpan)WorkingHours?.MondayTo;
                        if (WorkingHours.MondayFrom.HasValue)
                            From = (TimeSpan)WorkingHours?.MondayFrom;
                        break;
                    case "tuesday":
                        if (WorkingHours.TuesdayTo.HasValue)
                            To = (TimeSpan)WorkingHours?.TuesdayTo;
                        if (WorkingHours.TuesdayFrom.HasValue)
                            From = (TimeSpan)WorkingHours?.TuesdayFrom;
                        break;
                    case "wednesday":
                        if (WorkingHours.WednesdayTo.HasValue)
                            To = (TimeSpan)WorkingHours?.WednesdayTo;
                        if (WorkingHours.WednesdayFrom.HasValue)
                            From = (TimeSpan)WorkingHours?.WednesdayFrom;
                        break;
                    case "thursday":
                        if (WorkingHours.ThursdayTo.HasValue)
                            To = (TimeSpan)WorkingHours?.ThursdayTo;
                        if (WorkingHours.ThursdayFrom.HasValue)
                            From = (TimeSpan)WorkingHours?.ThursdayFrom;
                        break;
                    case "friday":
                        if (WorkingHours.FridayTo.HasValue)
                            To = (TimeSpan)WorkingHours?.FridayTo;
                        if (WorkingHours.FridayFrom.HasValue)
                            From = (TimeSpan)WorkingHours?.FridayFrom;
                        break;
                    case "saturday":
                        if (WorkingHours.SaturdayTo.HasValue)
                            To = (TimeSpan)WorkingHours?.SaturdayTo;
                        if (WorkingHours.SaturdayFrom.HasValue)
                            From = (TimeSpan)WorkingHours?.SaturdayFrom;
                        break;
                    default:
                        throw new ArgumentException("Invalid day of the week");
                }
            }
            if (WorkingHours?.TimeZone != null)
            {
                DateTime currentTimeUtc = DateTime.UtcNow;
                TimeSpan currentTimeIst = TimeZoneInfo.ConvertTimeFromUtc(currentTimeUtc, TimeZoneInfo.FindSystemTimeZoneById(WorkingHours.TimeZone)).TimeOfDay;

                if ((!(From <= currentTimeIst && To >= currentTimeIst)) || (To == null && From == null))
                {
                    return true;
                }
            }

            return false;
        }
        public async Task DelayResponse(IQueryable<EngagetoEntities.Entities.Template> Data, EngagetoEntities.Entities.Contacts Contact, string PhoneNumber, string BusinessId)
        {


            if ((Data.Where(m => (m.Feature == Feature.DelayedResponseMessage) && m.Enabled == true).Any()))
            {
                TimeSpan Delay = Data.FirstOrDefault(m => (m.Feature == Feature.DelayedResponseMessage))?.Delay ?? TimeSpan.Zero;
                var DelayMessage = Data.FirstOrDefault(m => m.Feature == Feature.DelayedResponseMessage);
                var Variables = _dbContext.InboxSettingsVariables.Where(m => m.TemplateId == DelayMessage.TemplateId);
                int milliseconds = 0;
                var Dev = Variables.ToList();

                var dateTimeOffset = DateTime.UtcNow.Add(Delay);
                var jobId = _jobService.Schedule(() => Message(DelayMessage.Body, DelayMessage.MediaAwsUrl, Variables.ToList(), Contact, PhoneNumber, BusinessId), dateTimeOffset);
                Contact.DelayResponseJobID = jobId;
                _dbContext.Contacts.Update(Contact);
                _dbContext.SaveChanges();
            }
        }

        #endregion

        #region Send Auto Reply message
        public async Task<bool> SendAutoReplyMessageAsync(EngagetoEntities.Entities.Conversations conversation, string businessId)
        {
            if (!Guid.TryParse(businessId, out Guid companyId))
                return false;
            var Status = false;
            var contactDetails = (await _contactRepositoryBase.GetContactByContactNumber(conversation.From, businessId)).FirstOrDefault();
            if (contactDetails?.WorkflowName != null && contactDetails?.WorkflowStep != null)
            {
                var workflowName = contactDetails?.WorkflowName;
                var workflowStep = contactDetails?.WorkflowStep;
                if (workflowStep == 0 && contactDetails != null)
                {
                    DateTime unixEpoch = new DateTime(1970, 1, 1, 0, 0, 0, DateTimeKind.Utc);
                    long unixTimeMilliseconds = (long)(DateTime.UtcNow - unixEpoch).TotalMilliseconds;
                    contactDetails.WorkflowStartId = unixTimeMilliseconds;
                }
                var data = await _automationWorkflowService.GetWorkflowAsync(companyId, null, null, workflowName);
                if (data.Count > 0)
                {
                    var value = data.Count > 0 ? data?[$"{workflowName}"]?.FirstOrDefault(m => m.Step == (contactDetails.WorkflowStep + 1)) : null;
                    var previousStep = data[$"{workflowName}"]?.FirstOrDefault(m => m.Step == (workflowStep));
                    if (string.IsNullOrEmpty(value?.WebhookTriggerUrl) && contactDetails?.WorkflowStep > 0)
                    {
                        WorkflowResponseHistoryEntity selectResponseEntity = new WorkflowResponseHistoryEntity
                        {
                            WorkflowName = previousStep.WorkflowName ?? "",
                            Step = previousStep.Step,
                            CreatedBy = contactDetails?.UserId,
                            CreatedAt = DateTime.UtcNow,
                            WorkflowId = previousStep.Id,
                            ContactId = contactDetails?.ContactId,
                            CompanyId = contactDetails.BusinessId,
                            UpdatedBy = contactDetails?.UserId,
                            WhatsappMessageId = conversation.WhatsAppMessageId,
                            Response = conversation.TextMessage ?? conversation.MediaCaption,
                            WorkflowStartId = contactDetails?.WorkflowStartId
                        };
                        await _repositoryBase.AddWorkflowResponse(selectResponseEntity);
                    }
                    if (value != null)
                    {
                        var response = await WorkflowStep(value, contactDetails, workflowName, conversation);
                        Status = response;
                    }
                    else
                    {
                        contactDetails.WorkflowName = null;
                        contactDetails.WorkflowStep = null;
                        contactDetails.WorkflowStartId = null;
                        _contactRepositoryBase.ContactEdit(contactDetails);
                    }
                }
                else
                {
                    contactDetails.WorkflowName = null;
                    contactDetails.WorkflowStep = null;
                    contactDetails.WorkflowStartId = null;
                    _contactRepositoryBase.ContactEdit(contactDetails);
                }
            }

            if (contactDetails?.WorkflowStep == null && !string.IsNullOrEmpty(conversation?.TextMessage))
            {

                var autoReplyAutomation = await _replyAutomationService
                    .GetAutoReplyAutomationByInput(companyId, conversation.TextMessage ?? string.Empty);


                if (autoReplyAutomation is null)
                    return false;

                switch (autoReplyAutomation.AutoReplyType)
                {
                    case ResponseType.CustomMessage:
                        var customMessages = (await _autoReplyCustomMessageService
                            .GetAutoReplyMessageAsync(companyId, autoReplyAutomation.Id))?.FirstOrDefault();

                        if (customMessages is not null)
                        {
                            string sendTextMessage = ReplacePlaceholders(customMessages.BodyMessage,
                                (GetContactValues(contactDetails, customMessages.Veriables)));

                            #region Autoreply operation
                            var account = _appDbContext.IntegrationAccounts.Where(i => i.BusinessId == businessId).FirstOrDefault();
                            var stopKeywords = account?.StopAutoReplyKeywords ?? new List<string>();
                            string messages = conversation?.TextMessage?.ToLower() ?? string.Empty;
                            bool response = false;
                            if (!stopKeywords.Any(k => messages.StartsWith(k.ToLower())))
                            {
                                 response = await SendAutoReplyAutomationTextMessageToContactAsync
                                    (conversation, sendTextMessage, customMessages.ButtonValue, companyId);
                            }
                            #endregion

                            await _leadProcessingService.SetLeadDataAsync(new IntegrationAccountRecord(businessId, contactDetails, conversation, @events: new List<IntegrationEvent> { IntegrationEvent.AutoReply }),CancellationToken.None);
                            return response;
                        }
                        break;

                    case ResponseType.Workflow:
                        // Implement workflow handling if needed
                        var data = await _automationWorkflowService.GetWorkflowAsync(companyId, null, null, autoReplyAutomation.WorkflowName);

                        if (data != null)
                        {
                            DateTime unixEpoch = new DateTime(1970, 1, 1, 0, 0, 0, DateTimeKind.Utc);
                            long unixTimeMilliseconds = (long)(DateTime.UtcNow - unixEpoch).TotalMilliseconds;
                            contactDetails.WorkflowStartId = unixTimeMilliseconds;
                            var value = data[$"{autoReplyAutomation.WorkflowName}"];
                            var response = await WorkflowStep(value[0], contactDetails, autoReplyAutomation?.WorkflowName, conversation);
                            return response;
                        }
                        break;
                }
            }

            return Status;
        }

        private async Task<bool> WorkflowStep(AutomationWorkflowResponseDto value, Contacts contact, string workflowName, EngagetoEntities.Entities.Conversations conversations)
        {

            string sendTextMessage = ReplacePlaceholders(value.Title, (GetContactValues(contact, value.Veriables)));

            WorkflowResponseHistoryEntity selectResponseEntity = new WorkflowResponseHistoryEntity
            {
                WorkflowName = value.WorkflowName ?? "",
                Step = value.Step,
                CreatedBy = contact?.UserId,
                CreatedAt = DateTime.UtcNow,
                WorkflowId = value.Id,
                ContactId = contact?.ContactId,
                CompanyId = contact.BusinessId,
                UpdatedBy = contact?.UserId,
                WhatsappMessageId = conversations.WhatsAppMessageId,
                WorkflowStartId = contact.WorkflowStartId,
                Response = conversations.TextMessage ?? conversations.MediaCaption
            };
            await _repositoryBase.AddWorkflowResponse(selectResponseEntity);
            await _leadProcessingService.SetLeadDataAsync(new IntegrationAccountRecord(contact.BusinessId.ToString(), contact, conversations, @events: new List<IntegrationEvent> { IntegrationEvent.AutoReply }), CancellationToken.None);
            var buttonValues = value.AutoReplyWorkflowButtons?.Select((btn, index) => new ButtonValueDto
            {
                Key = $"Key{index + 1}", // Dummy key, you can customize this
                Value = btn
            }).ToList();
            contact.WorkflowName = workflowName;
            contact.WorkflowStep = (value?.Step);
            _contactRepositoryBase.ContactEdit(contact);
            var response = await SendAutoReplyAutomationTextMessageToContactAsync(
                conversations, sendTextMessage,
                buttonValues, contact.BusinessId,
                value.AutoReplyWorkflowList,
                value, selectResponseEntity, contact);

            return response;
        }
        public async Task<bool> ExecuteWebhookAsync(string url, string body, List<WebhookHeader> webhookHeaders, HttpMethod method, WorkflowResponseHistoryEntity? selectResponseEntity)
        {
            using (HttpClient httpClient = new HttpClient())
            {
                var request = new HttpRequestMessage(method, url);
                if (method == HttpMethod.Post || method == HttpMethod.Put || method == HttpMethod.Patch)
                {
                    if (!string.IsNullOrEmpty(body))
                    {
                        request.Content = new StringContent(body, Encoding.UTF8, "application/json");
                    }
                }
                if (webhookHeaders != null && webhookHeaders.Count > 0)
                {
                    foreach (var header in webhookHeaders)
                    {
                        if (!string.IsNullOrEmpty(header.Key) && !string.IsNullOrEmpty(header.Value))
                        {
                            try
                            {
                                request.Headers.Add(header.Key, header.Value);
                            }
                            catch (InvalidOperationException)
                            {
                                if (header.Key.Equals("Content-Type", StringComparison.OrdinalIgnoreCase) && request.Content != null)
                                {
                                    request.Content.Headers.ContentType = new System.Net.Http.Headers.MediaTypeHeaderValue(header.Value);
                                }
                            }
                        }
                    }
                }
                try
                {
                    HttpResponseMessage response = await httpClient.SendAsync(request);
                    string responseContent = await response.Content.ReadAsStringAsync();
                    //if (selectResponseEntity != null)
                    //{
                    //    selectResponseEntity.Response = responseContent;

                    await _logHistoryService.SaveErrorLogHistoryAsyn("ExecuteWebhookAsync3", body, "response", responseContent, null);

                    //await _repositoryBase.UpdateWorkflowResponseEntity(selectResponseEntity);
                    //}
                    return response.IsSuccessStatusCode;
                }
                catch (HttpRequestException ex)
                {

                    Console.WriteLine($"Request error: {ex.Message}");
                    await _logHistoryService.SaveErrorLogHistoryAsyn("ExecuteWebhookAsync", body, "Getting an error calling the API", ex.Message, ex.StackTrace);
                    return false; // Return false on failure
                }
                catch (Exception ex)
                {
                    await _logHistoryService.SaveErrorLogHistoryAsyn("ExecuteWebhookAsync1", body, "Getting an error calling the API", ex.Message, ex.StackTrace);
                    Console.WriteLine($"Unexpected error: {ex.Message}");
                    return false;
                }
            }
        }



        private string ReplacePlaceholders(string format, Dictionary<string, string> values)
        {
            StringBuilder sb = new StringBuilder(format);
            foreach (var kvp in values)
            {
                sb.Replace(kvp.Key, kvp.Value);
            }
            return sb.ToString();
        }

        private async Task<bool> SendAutoReplyAutomationTextMessageToContactAsync
            (EngagetoEntities.Entities.Conversations conversation, string replyTextMessage, List<ButtonValueDto>? buttonValues, Guid companyId,
            WorkflowListDto? workflowListDto = null, AutomationWorkflowResponseDto? value = null,
            WorkflowResponseHistoryEntity responseHistoryEntity = null, Contacts contact = null)
        {
            var isSucess = false;
            if (!string.IsNullOrEmpty(value?.WebhookTriggerUrl))
            {
                string url = ReplacePlaceholders(value.WebhookTriggerUrl, GetContactValues(contact,
                value?.Veriables?.Where(m => m.Type == VariableType.WebhookTriggerUrl)?.ToList()));

                string body = value?.WebhookTriggerBody;
                var workflowResponse = await _replyAutomationService.GetWorkflowCustomerResponseAsync(contact.ContactId, value.WorkflowName, companyId, contact.WorkflowStartId);
                if (workflowResponse?.Any() == true)
                {
                    var wResponse = value?.Veriables?.Join(
                                workflowResponse,
                                v => v.Value?.ToLowerInvariant().Trim(),
                                w => w.VeriableName?.ToLowerInvariant().Trim(),
                                (v, w) => new
                                {
                                    Variable = v.Veriable,
                                    Response = StringHelper.FormateEscapeSequences(w.Response ?? string.Empty),
                                    w.WorkflowName
                                }
                            ).GroupBy(x => x.Variable)
                            .Select(x => x.Last());

                    //var keyAndValues = wResponse?.Where(x => x.WorkflowName == value.WorkflowName)?.ToDictionary(dict => dict.Variable,dict => (object)$"\"{dict.Response}\"");
                    var keyAndValues = wResponse?.Where(x => x.WorkflowName == value.WorkflowName)?.ToDictionary(dict => dict.Variable, dict => dict.Response);
                    body = ReplacePlaceholders(value?.WebhookTriggerBody ?? string.Empty, values: keyAndValues ?? new());
                    body = ReplacePlaceholders(body ?? "", GetContactValues(contact,
                        value?.Veriables?.Where(m => m.Type == VariableType.WebhookTriggerBody)?.ToList()));
                }
                else
                {
                    body = ReplacePlaceholders(body ?? "", GetContactValues(contact,
                    value?.Veriables?.Where(m => m.Type == VariableType.WebhookTriggerBody)?.ToList()));
                }
                string? headerJson = value?.WebhookTriggerHeader == null ? null : ReplacePlaceholders(JsonSerializer.Serialize(value?.WebhookTriggerHeader),
                    GetContactValues(contact, value?.Veriables?.Where(m => m.Type == VariableType.WebhookTriggerHeader)?.ToList()));
                List<WebhookHeader> webhookHeader = new List<WebhookHeader>();
                if (headerJson != null)
                {
                    webhookHeader = JsonSerializer.Deserialize<List<WebhookHeader>>(headerJson);

                }

                // Determine the correct HttpMethod
                HttpMethod method = value?.WebhookTriggerHttpMethod?.ToUpper() switch
                {
                    "POST" => HttpMethod.Post,
                    "GET" => HttpMethod.Get,
                    "PUT" => HttpMethod.Put,
                    "DELETE" => HttpMethod.Delete,
                    "PATCH" => HttpMethod.Patch,
                    _ => HttpMethod.Get
                };

                // Initialize HttpClient (you might want to inject this via Dependency Injection in real projects)
                if (method != null && body != null && webhookHeader != null && url != null)
                {
                    try
                    {
                        var response = await ExecuteWebhookAsync(url, body, webhookHeader, method, responseHistoryEntity);
                        if (!response && !string.IsNullOrEmpty(value?.DefaultErrorResponse))
                        {
                            await SendAutoReplyAutomationTextMessageToContactAsync
                                (conversation, value?.DefaultErrorResponse ?? string.Empty, buttonValues, contact.BusinessId, value.AutoReplyWorkflowList);
                        }
                    }
                    catch (Exception ex)
                    {

                    }
                }
                var data = await _automationWorkflowService.GetWorkflowAsync(contact.BusinessId, null, null, value.WorkflowName);
                value = data[$"{value.WorkflowName}"]?.FirstOrDefault(m => m.Step == (value.Step + 1));
                if (value != null)
                {
                    await WorkflowStep(value, contact, value.WorkflowName, conversation);
                }
                else
                {
                    contact.WorkflowStartId = null;
                    contact.WorkflowStep = null;
                    contact.WorkflowName = null;
                    _contactRepositoryBase.ContactEdit(contact);
                }
                isSucess = true;
            }
            if (workflowListDto != null)
            {
                if (workflowListDto?.Inputs != null && workflowListDto.Inputs.Any() && !string.IsNullOrEmpty(replyTextMessage))
                {
                    // Create the list of rows from Inputs
                    var rows = new List<EngagetoEntities.Dtos.MetaDto.AutoReply.Row>();
                    for (int i = 0; i < workflowListDto.Inputs.Count; i++)
                    {
                        rows.Add(new EngagetoEntities.Dtos.MetaDto.AutoReply.Row
                        {
                            id = i.ToString(), // Generate ID based on index
                            title = workflowListDto.Inputs[i].Title, // Use title from Inputs
                            description = workflowListDto.Inputs[i].Description // Use description from Inputs
                        });
                    }

                    // Create the section with ListName as the title and the generated rows
                    var sections = new List<EngagetoEntities.Dtos.MetaDto.AutoReply.Section>
                    {
                        new EngagetoEntities.Dtos.MetaDto.AutoReply.Section
                        {
                            title = workflowListDto.ListName, // ListName as the section title
                            rows = rows // Add the rows generated from Inputs
                        }
                    };

                    // Construct the AutoReply message for the list
                    var listMessage = new EngagetoEntities.Dtos.MetaDto.AutoReply.Rootobject
                    {
                        to = conversation.From,
                        interactive = new EngagetoEntities.Dtos.MetaDto.AutoReply.Interactive
                        {
                            type = "list",
                            body = new EngagetoEntities.Dtos.MetaDto.AutoReply.Body
                            {
                                text = replyTextMessage ?? string.Empty // Set body text, default to empty if null
                            },
                            action = new EngagetoEntities.Dtos.MetaDto.AutoReply.Action
                            {
                                button = workflowListDto.ButtonName, // Set button text from ButtonName
                                sections = sections // Add sections with rows
                            }
                        }
                    };

                    // Send the message via WhatsApp Business API
                    await _sentMessage.WhatsAppBusinessPostAsync(listMessage, companyId, null, MessageType.AutoReply);
                    isSucess = true; // Indicate success
                }
            }
            else if (buttonValues != null && buttonValues.Count > 0)
            {
                if (buttonValues.Count > 0 && !string.IsNullOrEmpty(replyTextMessage))
                {

                    var data = buttonValues?.Select(m => m.Value).ToList(); // Extracting values from buttonValues

                    if (data != null && data.Any())
                    {
                        var buttons = new List<EngagetoEntities.Dtos.MetaDto.AutoReply.Button>();

                        // Iterate through the data and generate buttons
                        for (int i = 0; i < data.Count; i++)
                        {
                            buttons.Add(new EngagetoEntities.Dtos.MetaDto.AutoReply.Button
                            {
                                reply = new EngagetoEntities.Dtos.MetaDto.AutoReply.Reply
                                {
                                    title = data[i], // Set the button title from data
                                    id = i.ToString() // Use the index as the ID (ensure this is acceptable)
                                }
                            });
                        }

                        if (buttons.Any()) // Proceed only if buttons are generated
                        {
                            // Create the AutoReply message
                            var textMessage = new EngagetoEntities.Dtos.MetaDto.AutoReply.Rootobject
                            {
                                to = conversation.From, // The recipient phone number
                                interactive = new EngagetoEntities.Dtos.MetaDto.AutoReply.Interactive
                                {
                                    body = new EngagetoEntities.Dtos.MetaDto.AutoReply.Body
                                    {
                                        text = replyTextMessage ?? string.Empty // Set body text, default to empty if null
                                    },
                                    action = new EngagetoEntities.Dtos.MetaDto.AutoReply.Action
                                    {
                                        buttons = buttons // Assign the generated buttons to the message
                                    }
                                }
                            };

                            // Send the message via WhatsApp Business API
                            await _sentMessage.WhatsAppBusinessPostAsync(textMessage, companyId, null, MessageType.AutoReply);
                            isSucess = true; // Indicate successful sending
                        }
                    }
                }
            }
            else if (!string.IsNullOrEmpty(replyTextMessage))
            {
                TextMessageRequest textMessageRequest = new TextMessageRequest();
                textMessageRequest.To = conversation.From;
                textMessageRequest.Text = new WhatsAppText();
                textMessageRequest.Text.Body = replyTextMessage;
                textMessageRequest.Text.PreviewUrl = false;
                await whatsAppReceive.SendMessageAsync(textMessageRequest, companyId, null, MessageType.AutoReply);
                isSucess = true;
            }
            else if (value != null)
            {
                var data = await _automationWorkflowService.GetWorkflowAsync(contact.BusinessId, null, null, value.WorkflowName);

                value = data[$"{value.WorkflowName}"]?.FirstOrDefault(m => m.Step == (value.Step + 1));
                if (value != null)
                {
                    await WorkflowStep(value, contact, value.WorkflowName, conversation);
                }
                else
                {
                    contact.WorkflowStartId = null;
                    contact.WorkflowStep = null;
                    contact.WorkflowName = null;
                    _contactRepositoryBase.ContactEdit(contact);
                }
                isSucess = true;
            }
            return isSucess;
        }

        public Dictionary<string, string> GetContactValues(Contacts contact, List<VeriableDto>? variables)
        {
            var values = new Dictionary<string, string>();

            if (contact == null)
            {
                foreach (var variable in variables)
                {
                    values[variable.Veriable] = variable.FallbackValue ?? string.Empty;
                }
                return values;
            }

            if (variables == null)
                return values;

            var contactDict = StringHelper.GetPropertyNamesAndValues(contact);

            foreach (var variable in variables)
            {
                if (contactDict.TryGetValue(variable.Value, out var value))
                {

                    if (value == null)
                    {
                        values[variable.Veriable] = variable.FallbackValue ?? string.Empty;
                    }
                    else if (value.ToString() == string.Empty)
                    {
                        values[variable.Veriable] = variable.FallbackValue ?? string.Empty;
                    }
                    else
                    {
                        values[variable.Veriable] = value?.ToString() ?? string.Empty;
                    }
                }

                else
                    values[variable.Veriable] = variable.FallbackValue ?? string.Empty;
            }

            return values;
        }
        #endregion
    }
}
