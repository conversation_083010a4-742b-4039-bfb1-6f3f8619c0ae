﻿using Newtonsoft.Json;
using EngagetoEntities.Enums;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using EngagetoEntities.Dtos.TemplateDtos;

namespace EngagetoEntities.Entities
{
    public class Template
    {
        [Key]
        public Guid TemplateId { get; set; }
        public Guid? UserId { get; set; }
        public string BusinessId { get; set; }
        [JsonProperty("id")]
        public string? MetaId { get; set; }
        public string TemplateName { get; set; }
        [JsonProperty("category")]
        public WATemplateCategory? Category { get; set; }
        public string? SubCategory { get; set; }
        public MediaType MediaType { get; set; }
        public string? MediaAwsUrl { get; set; }
        public string? Header { get; set; }
        public string LanguageCode { get; set; }
        public string Body { get; set; }
        public string? Footer { get; set; }

        [JsonProperty("status")]
        public WATemplateStatus? Status { get; set; }
        public string? Createdby { get; set; }
        public string? Updatedby { get; set; }
        public DateTime? CreatedDate { get; set; }
        public DateTime? UpdatedDate { get; set; }
        public Feature? Feature { get; set; }
        public bool? Enabled { get; set; }
        public TimeSpan? Delay { get; set; }
        public string? SendTemplateJsonDto { get; set; }
        public string? AuthTemplatePreviewJson { get; set; }
        public List<Button>? Buttons { get; set; }
        public bool IsDeleted { get; set; }
        public string? StatusReason { get; set; }
        public string? Rating { get; set; }
        [NotMapped]
        public List<QualityScoreDtos>? QuailityRating
        {
            get => string.IsNullOrEmpty(Rating) ? new List<QualityScoreDtos>() : JsonConvert.DeserializeObject<List<QualityScoreDtos>>(Rating);
            set => Rating = value != null ? JsonConvert.SerializeObject(value) : null;
        }

        [NotMapped]
        public List<string>? CarouselCards
        {
            get => string.IsNullOrEmpty(CarouselCardsJson) ? new List<string>() : JsonConvert.DeserializeObject<List<string>>(CarouselCardsJson);
            set => CarouselCardsJson = JsonConvert.SerializeObject(value);
        }
        public string? CarouselCardsJson { get; set; }
    }
    
}
