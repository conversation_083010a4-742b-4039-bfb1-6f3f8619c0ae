﻿using Newtonsoft.Json.Linq;

namespace EngagetoEntities.Dtos.MetaDto
{
    public static class CreateAuthenticationMetaPayloadJsonDto
    {
        // Base payload template
        private static readonly string BasePayloadTemplate = @"
        {{
            ""name"": ""{0}"",
            ""languages"": [""{1}""],
            ""category"": ""{2}"",
            ""message_send_ttl_seconds"": ""{3}"" 
            ""components"": []
        }}";

        // Component templates
        private static readonly string BodyTemplate = @"
        {{
            ""type"": ""BODY"",
            ""add_security_recommendation"": {0}
        }}";

        private static readonly string FooterTemplate = @"
        {{
            ""type"": ""FOOTER"",
            ""code_expiration_minutes"": {0}
        }}";

        private static readonly string ButtonPayloadTemplate = @"
        {{
            ""type"": ""BUTTONS"",
            ""buttons"": [{0}]
        }}";

        private static readonly string OTPButtonTemplate_One_Tap = @"
        {{
            ""type"": ""OTP"",
            ""otp_type"": ""{0}"",
            ""supported_apps"": [{1}]
        }}";

        private static readonly string OtpButtonTemplate_Zero_tap = @"
        {{
            ""type"": ""otp"",
            ""otp_type"": ""{0}"",
            ""text"": ""Copy Code"",
            ""autofill_text"": ""Autofill"",
            ""zero_tap_terms_accepted"": ""{1}"",
            ""supported_apps"": [{2}]
        }}";

        private static readonly string SupportedAppTemplate = @"
        {{
            ""package_name"": ""{0}"",
            ""signature_hash"": ""{1}""
        }}";

        // Method to create the base payload
        public static string CreateBasePayload(string templateName, string language, string category)
        {
            return string.Format(BasePayloadTemplate, templateName, language, category);
        }

        // Method to create the body payload
        public static string CreateBodyPayload(bool? add_security_recommendation)
        {
            return string.Format(BodyTemplate, add_security_recommendation.ToString().ToLower());
        }

        // Method to create the footer payload
        public static string CreateFooterPayload(string? code_expiration_minutes)
        {
            return string.Format(FooterTemplate, code_expiration_minutes);
        }

        // Method to create the button payload
        public static string CreateButtonPayload(JArray buttons)
        {
            return string.Format(ButtonPayloadTemplate, buttons.ToString());
        }

        // Method to create an OTP button for One-Tap
        public static string CreateOtpButtonPayload_One_Tap(string otpType, JArray supportedApps)
        {
            return string.Format(OTPButtonTemplate_One_Tap, otpType, supportedApps.ToString());
        }

        // Method to create an OTP button for Zero-Tap
        public static string CreateOtpButtonPayload_Zero_Tap(string otpType, bool zeroTapTermsAccepted, JArray supportedApps)
        {
            return string.Format(OtpButtonTemplate_Zero_tap, otpType, zeroTapTermsAccepted.ToString().ToLower(), supportedApps.ToString());
        }

        // Method to create a supported app payload
        public static string CreateSupportedAppPayload(string package_name, string signature_hash)
        {
            return string.Format(SupportedAppTemplate, package_name, signature_hash);
        }

        // Method to add a button to the buttons array
        public static void AddButtonToButtonsArray(string buttonPayload, ref JArray buttons)
        {
            var button = JObject.Parse(buttonPayload);
            buttons.Add(button);
        }
    }
}













