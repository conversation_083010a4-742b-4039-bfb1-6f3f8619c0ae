﻿using Microsoft.AspNetCore.Http;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace EngagetoContracts.GeneralContracts
{
    public interface IEnvironmentService
    {
        bool IsDevelopment { get; set; }
        HostString RequestHost { get; set; }
        string RequestScheme { get; set; }
        string GetProdWebsiteLink();
        string GetDevWebsiteLink();
    }
}
