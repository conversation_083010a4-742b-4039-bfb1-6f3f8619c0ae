﻿using EngagetoEntities.Settings;
using Newtonsoft.Json;

namespace Engageto.DependencyInjection
{
    public static class ServiceRegistry
    {
        public static IServiceCollection AddTransientServices(this IServiceCollection services, Type interfaceType, ServiceLifetime lifetime)
        {
            try
            {
                var interfaceTypes =
                AppDomain.CurrentDomain.GetAssemblies()
                    .SelectMany(s => s.GetTypes())
                    .Where(t => interfaceType.IsAssignableFrom(t)
                                && t.IsClass && !t.IsAbstract)
                    .Select(t => new
                    {
                        Service = t.GetInterfaces().FirstOrDefault(),
                        Implementation = t
                    })
                    .Where(t => t.Service is not null
                                && interfaceType.IsAssignableFrom(t.Service));

                foreach (var type in interfaceTypes)
                {
                    services.AddService(type.Service!, type.Implementation, lifetime);
                }
            }
            catch (Exception ex)
            {
                Console.WriteLine("error occurred in AddServices: " + JsonConvert.SerializeObject(ex, new JsonSerializerSettings() { ReferenceLoopHandling = ReferenceLoopHandling.Ignore, Formatting = Formatting.Indented }));
                throw;
            }
            return services;
        }
        public static IServiceCollection AddConfigureService(this IServiceCollection services, IConfiguration configuration)
        {
            string env = Environment.GetEnvironmentVariable("ASPNETCORE_ENVIRONMENT");
            services.Configure<PaymentGatewayOptions>(configuration.GetSection("PaymentGateway"));
            services.Configure<AdminEmails>(configuration.GetSection("AdminEmails"));
            services.Configure<TemplateFiles>(configuration.GetSection("TemplateFiles"));
            services.Configure<ConnectionString>(configuration.GetSection("ConnectionStrings"));
            return services;

        }
        internal static IServiceCollection AddService(this IServiceCollection services, Type serviceType, Type implementationType, ServiceLifetime lifetime) =>
           lifetime switch
           {
               ServiceLifetime.Transient => services.AddTransient(serviceType, implementationType),
               ServiceLifetime.Scoped => services.AddScoped(serviceType, implementationType),
               ServiceLifetime.Singleton => services.AddSingleton(serviceType, implementationType),
               _ => throw new ArgumentException("Invalid lifeTime", nameof(lifetime))
           };
    }
}
