﻿using EngagetoEntities.Dtos.MetaDto;
using EngagetoEntities.Enums;

namespace EngagetoContracts.WebhookContracts.Client
{
    public interface IWhatsAppBusinessClient
    {
        /// <summary>
        /// this inteface is use for send message, mark message as read
        /// </summary>
        Task<WhatsAppResponse> SendMessageAsync(TextMessageRequest textMessage, Guid BusinessId, string? SentBy = null, MessageType messageType = MessageType.Normal);
        Task<WhatsAppResponse> SendMessageAsync(ImageMessageRequest Message, Guid BusinessId, string? SentBy = null,
             MessageType messageType = MessageType.Normal);
        Task<WhatsAppResponse> SendMessageAsync(VideoMessageRequest Message, Guid BusinessId, string? SentBy = null,
            MessageType messageType = MessageType.Normal);
        Task<WhatsAppResponse> SendMessageAsync(AudioMessageRequest Message, Guid BusinessId, string? SentBy = null,
            MessageType messageType = MessageType.Normal);
        Task<WhatsAppResponse> SendMessageAsync(DocumentMessageRequest Message, Guid BusinessId, string? SentBy = null,
            MessageType messageType = MessageType.Normal);
        Task MarkMessageAsReadAsync(MarkMessageRequest markMessage, Guid BusinessId);
        Task<WhatsAppResponse> WhatsAppBusinessPostAsync(AutoReply.Rootobject whatsAppDto, Guid BusinessId, string? SentBy = null,
            MessageType messageType = MessageType.Normal);
    }
}

