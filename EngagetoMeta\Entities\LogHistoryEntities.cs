﻿using EngagetoMeta.Enums;
using System.ComponentModel.DataAnnotations.Schema;

namespace EngagetoMeta.Entities
{
    [Table("LogHistoryEntities")]
    public class LogHistoryEntitity
    {
        public Guid Id { get; set; }
        public string? ApiName { get; set; }
        public string? RequestData { get; set; }
        public string? ResponseData { get; set; }
        public string? Notes { get; set; }
        public LogType LogType { get; set; }
        public string? ErrorMessage { get; set; }
        public string? StackTrace { get; set; }
        public DateTime CreatedAt { get; set; }
        public string? WhatsappBusinessAccountId { get; set; }
        public string? Field { get; set; }

    }
}
