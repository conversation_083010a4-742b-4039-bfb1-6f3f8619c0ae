﻿using Microsoft.AspNetCore.Http;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using System.Runtime.Serialization;
using System.Text.Json.Serialization;

namespace EngagetoEntities.Entities
{
    [Table("BusinessDetails")]
    public class Ahex_CRM_BusinessDetails
    {
        [Key]
        [DatabaseGenerated(DatabaseGeneratedOption.Identity)]
        public Guid Id { get; set; }
        public string? BusinessName { get; set; }

        public string? BusinessCategory { get; set; }
        public string? BusinessWebsite { get; set; }
        public string? BusinessEmail { get; set; }
        public string? CountryCode { get; set; }
        public string? PhoneNumber { get; set; }
        public string? CountryName { get; set; }
        public string? GSTNumber { get; set; }
        public string? CompanyAddress { get; set; }
        public string? Description { get; set; }
        public string? CompanyLegalName { get; set; }

        public string? CompanyType { get; set; }
        public bool? CompanyRegistered { get; set; }
        public string? CustomerCareEmail { get; set; }
        public string? CustomerCareCountryCode { get; set; }
        public string? CustomerCarePhone { get; set; }
        public string? GrievanceOfficerName { get; set; }
        public string? GrievanceOfficerEmail { get; set; }
        public string? GrievanceOfficerCountryCode { get; set; }
        public string? GrievanceOfficerPhone { get; set; }
        public string? CompanyLogoLink { get; set; }
        public DateTime? CreatedAt { get; set; }
        public string? TenantId { get; set; }
        public bool Status { get; set; }
        public bool? AutoAssignProjectTag { get; set; }
        [NotMapped]
        public List<Ahex_CRM_Users>? users { get; set; }
    }
    [JsonConverter(typeof(JsonStringEnumConverter))]
    public enum BusinessCategory
    {

        [EnumMember(Value = "retail")]
        retail,
        [EnumMember(Value = "health")]
        health,
        [EnumMember(Value = "finance")]
        finance,
        [EnumMember(Value = "edu")]
        edu,
        [EnumMember(Value = "event_Plan")]
        event_Plan,
        [EnumMember(Value = "beauty")]
        beauty,
        [EnumMember(Value = "apparel")]
        apparel,
        [EnumMember(Value = "auto")]
        auto,
        [EnumMember(Value = "grocery")]
        grocery,
        [EnumMember(Value = "govt")]
        govt,
        [EnumMember(Value = "entertain")]
        entertain,
        [EnumMember(Value = "travel")]
        travel,
        [EnumMember(Value = "nonProfit")]
        nonProfit,
        [EnumMember(Value = "hotel")]
        hotel,
        [EnumMember(Value = "prof_Services")]
        prof_Services,
        [EnumMember(Value = "restaurant")]
        restaurant,
        [EnumMember(Value = "other")]
        other
    }
    [JsonConverter(typeof(JsonStringEnumConverter))]
    public enum CompanyType
    {
        [EnumMember(Value = "corporation")]
        corporation,
        [EnumMember(Value = "partnership")]
        partnership,
        [EnumMember(Value = "SoleProprietorship")]
        soleProprietorship,
        [EnumMember(Value = "limitedLiabilityCompany")]
        limitedLiabilityCompany,
        [EnumMember(Value = "nonProfitOrganization")]
        nonProfitOrganization,
        [EnumMember(Value = "cooperative")]
        cooperative,
        [EnumMember(Value = "franchise")]
        franchise,
        [EnumMember(Value = "public")]
        Public,
        [EnumMember(Value = "private")]
        Private,
        [EnumMember(Value = "Other")]
        other
    }


    public class Ahex_CRM_BusinessDetailsDto
    {

        public string? BusinessName { get; set; }
        public BusinessCategory? BusinessCategory { get; set; }

        public string? BusinessWebsite { get; set; }

        [EmailAddress(ErrorMessage = "Invalid email address.")]
        [RegularExpression(@"^\S+@\S+\.\S+$", ErrorMessage = "Email must be in a valid format.")]
        public string? BusinessEmail { get; set; }
        public string? CountryCode { get; set; }

        public string? PhoneNumber { get; set; }

        public string? CountryName { get; set; }

        public string? GSTNumber { get; set; }

        public string? CompanyAddress { get; set; }

        public string? Description { get; set; }

        public string? CompanyLegalName { get; set; }


        public CompanyType? CompanyType { get; set; }
        public bool? CompanyRegistered { get; set; }

        [EmailAddress(ErrorMessage = "Invalid email address.")]
        [RegularExpression(@"^\S+@\S+\.\S+$", ErrorMessage = "Email must be in a valid format.")]
        public string? CustomerCareEmail { get; set; }
        public string? CustomerCareCountryCode { get; set; }

        public string? CustomerCarePhone { get; set; }
        public string? GrievanceOfficerName { get; set; }
        [EmailAddress(ErrorMessage = "Invalid email address.")]
        [RegularExpression(@"^\S+@\S+\.\S+$", ErrorMessage = "Email must be in a valid format.")]
        public string? GrievanceOfficerEmail { get; set; }
        public string? GrievanceOfficerCountryCode { get; set; }

        public string? GrievanceOfficerPhone { get; set; }

        public IFormFile? Image { get; set; }
    }
    public class Ahex_CRMClientDetailsDto
    {
        [Required(ErrorMessage = "Company name is required")]
        public string CompanyName { get; set; }

        [Required(ErrorMessage = "Client name is required")]
        public string ClientName { get; set; }
        [Required]
        public string OfficialWebsite { get; set; }

        [Required(ErrorMessage = "Email is required.")]
        [EmailAddress(ErrorMessage = "Invalid email address.")]
        [RegularExpression(@"^\S+@\S+\.\S+$", ErrorMessage = "Email must be in a valid format.")]
        public string? Email { get; set; }


        public string? BusinessEmail { get; set; }

        [Required(ErrorMessage = "Country name is required")]
        public string CountryName { get; set; }
        [Required]
        public string Address { get; set; }
        public string? TenantId { get; set; }

    }
    public class Ahex_CRMClientDetailsUpdateDto
    {

        [StringLength(100, ErrorMessage = "Company name must be between 1 and 100 characters", MinimumLength = 1)]
        public string? CompanyName { get; set; }
        public string? OfficialWebsite { get; set; }
        public string? BusinessEmail { get; set; }
        public string? CountryName { get; set; }
        public string? Address { get; set; }
        public string? TenantId { get; set; }
        public BusinessCategory? BusinessCategory { get; set; }
        public IFormFile? CompanyLogoLink { get; set; }

    }
    public class AddClientResult
    {
        public string? ErrorMessage { get; set; }
        public Guid ClientId { get; set; }
    }
}
