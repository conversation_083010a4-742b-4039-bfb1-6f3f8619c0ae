using Microsoft.Extensions.Options;

namespace EngagetoMeta.Settings
{
    public static class Configuration
    {
        public static IConfiguration AppSetting { get; }

        static Configuration()
        {
            AppSetting = new ConfigurationBuilder()
                .SetBasePath(Directory.GetCurrentDirectory())
                .AddJsonFile("appsettings.json", optional: false, reloadOnChange: true)
                .AddJsonFile($"appsettings.{Environment.GetEnvironmentVariable("ASPNETCORE_ENVIRONMENT") ?? "dev"}.json", optional: true)
                .AddEnvironmentVariables()
                .Build();
        }
        public static void AddConfig<T>(this IServiceCollection services, IConfiguration configuration, string sectionName) where T : class
        {
            services.Configure<T>(configuration.GetSection(sectionName));
            services.AddSingleton<T>(sp => sp.GetRequiredService<IOptions<T>>().Value);
        }
    }
}