﻿using EngagetoEntities.Dtos.MetaDto;
using EngagetoEntities.Enums;

namespace EngagetoEntities.Dtos.ConversationDtos
{
    public class ConversationDto
    {
        public Guid Id { get; set; }
        public string WhatsAppMessageId { set; get; }
        public string From { set; get; }
        public string To { set; get; }
        public object? Reply { set; get; }
        public string? ReplyId { set; get; }
        public string Status { set; get; }
        public DateTime CreatedAt { set; get; }
        public string? TextMessage { get; set; }
        public string? MediaFileName { get; set; }
        public string? MediaMimeType { get; set; }
        public string? MediaUrl { get; set; }
        public string? MediaCaption { set; get; }
        public MediaType? TemplateMediaType { set; get; }
        public string? TemplateMediaFile { get; set; }
        public string? TemplateHeader { get; set; }
        public string? TemplateBody { get; set; }
        public string? TemplateFooter { get; set; }
        public string? CallButtonName { get; set; }
        public string? PhoneNumber { get; set; }
        public string[]? UrlButtonNames { get; set; }
        public string[]? RedirectUrls { get; set; }
        public string[]? QuickReplies { get; set; }
        public string? Name { get; set; }
        public string? ChatStatus { get; set; }
        public string? ErrorMessage { get; set; }
        public AutoReply.Action? Action { get; set; }
        public string? CarouselCards { get; set; }
        public Guid? BusinessId { get; set; }
        public Guid? ContactId { get; set; }
    }
}
