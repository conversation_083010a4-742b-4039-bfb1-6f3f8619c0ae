﻿namespace EngagetoEntities.Entities
{
    public class ConversationCostDetuctionHistory : BaseEntity
    {
        public Guid Id { get; set; }
        public string CompanyId { get; set; }
        public Guid? UserId { get; set; }
        public int Conversation { get; set; }
        public string? ConversationType { get; set; }
        public string? ConversationCategory { get; set; }
        public decimal Cost { get; set; }
        public decimal? CurrentBalance { get; set; }
        public string? DestinationMobileNumber { get; set; }

        public ConversationCostDetuctionHistory() { }
        public ConversationCostDetuctionHistory(string companyId,
            Guid? userId,
            int conversation,
            string? conversationType,
            string? conversationCategory,
            decimal cost,
            decimal? currentBalance,
            string? destinationMobileNumber)
        {
            Id = Guid.NewGuid();
            CompanyId = companyId;
            UserId = userId;
            Conversation = conversation;
            ConversationType = conversationType;
            ConversationCategory = conversationCategory;
            Cost = cost;
            CurrentBalance = currentBalance;
            DestinationMobileNumber = destinationMobileNumber;
            CreatedAt = DateTime.UtcNow;
            UpdatedAt = DateTime.UtcNow;
            CreatedBy = userId ?? Guid.Empty;
            UpdatedBy = userId ?? Guid.Empty;
        }
    }
}
