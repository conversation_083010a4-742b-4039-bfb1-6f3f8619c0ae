﻿using EngagetoEntities.Entities;

namespace EngagetoContracts.UserContracts
{
    public interface ITokenService
    {
        string GenerateToken(Ahex_CRM_Users user);
        Task<string> GenerateTokenForTenant(string tenantId);
        /* string GenerateToken1(AccountDetails user);
         string GenerateToken2(ClientsAccountDetails user);*/
        Task<object?> RefreshTokenAsync(string token, string refreshToken);
        string GenerateRefreshToken(Ahex_CRM_Users user, string secretKey);
        DateTime? GetExpiryFromToken(string token);
    }
}
