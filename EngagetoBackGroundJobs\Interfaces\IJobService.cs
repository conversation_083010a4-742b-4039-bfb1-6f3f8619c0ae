﻿using System.Linq.Expressions;

namespace EngagetoBackGroundJobs.Interfaces
{
    public interface IJobService
    {
        string Enqueue(Expression<Func<Task>> methodCall);
        string Schedule(Expression<Func<Task>> methodCall, DateTimeOffset enqueueAt);
        string Schedule(Expression<Func<Task>> methodCall, TimeSpan delay);
        bool Requeue(string jobId);
        bool Delete(string jobId);
        bool Reschedule(string jobId, DateTimeOffset enqueueAt);
        string JobState(string? jobId);
    }

}
