﻿namespace EngagetoEntities.Dtos.ConversationDtos
{
    public class ConverstationDetailsDto
    {
        public bool? IsFreeConversation { get; set; }
        public string? SubscriptionPlanName { get; set; }
        public int? PlanId { get; set; }
        public string CompanyId { get; set; }
        public int? ConversationCount { get; set; }
        public int? MetaConversationCount { get; set; }
        public decimal? WalletBalance { get; set; }
        public bool IsActive { get; set; }
        public string? ConversationCategory { get; set; }
        public int? CampaignCount { get; set; }
    }

}
