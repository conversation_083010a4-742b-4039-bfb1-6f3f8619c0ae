﻿using EngagetoDapper.Data.Interfaces.IUserInterfaces.IUserServices;
using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Mvc.Filters;
using Microsoft.AspNetCore.Mvc;
using EngagetoDapper.Data.Interfaces.IUserInterfaces.IUserServices;
using System.Text;

namespace Engageto.Attributes
{
    public class ApiKeyAuthenticationAttribute : Attribute, IAsyncActionFilter
    {
        private const string ApiKeyHeaderName = "Api-Key";
        private const string CompanyIdKey = "CompanyId";
        private IApiKeyService _apiKeyService;

        public async Task OnActionExecutionAsync(ActionExecutingContext context, ActionExecutionDelegate next)
        {
            _apiKeyService = context.HttpContext.RequestServices.GetService(typeof(IApiKeyService)) as IApiKeyService;

            if (!context.HttpContext.Request.Headers.TryGetValue(ApiKeyHeaderName, out var extractedApiKey))
            {
                SetUnauthorizedResult(context, "Api Key was not provided.");
                return;
            }

            string decodedKey;
            try
            {
                decodedKey = Base64Decode(extractedApiKey);
            }
            catch (FormatException)
            {
                SetUnauthorizedResult(context, "Invalid Api Key format.");
                return;
            }

            var splitDecodedKey = decodedKey.Split(",");
            if (splitDecodedKey.Length != 2)
            {
                SetUnauthorizedResult(context, "Api Key is not valid.");
                return;
            }

            var companyId = splitDecodedKey[0];
            var secretKey = splitDecodedKey[1];

            if (!await IsValidApiKeyAsync(companyId, secretKey))
            {
                SetUnauthorizedResult(context, "Api Key is not valid.");
                return;
            }

            // Store the company ID in HttpContext.Items
            context.HttpContext.Items[CompanyIdKey] = companyId;
            await next();
        }

        private async Task<bool> IsValidApiKeyAsync(string companyId, string secretKey)
        {
            var apiDetails = await _apiKeyService.GetApiDetailsAsync(companyId, secretKey);
            return apiDetails != null;
        }

        private string Base64Decode(string base64)
        {
            byte[] bytes = Convert.FromBase64String(base64);
            return Encoding.UTF8.GetString(bytes);
        }

        private void SetUnauthorizedResult(ActionExecutingContext context, string message)
        {
            context.Result = new ContentResult
            {
                StatusCode = 401,
                Content = message
            };
        }
    }
}
