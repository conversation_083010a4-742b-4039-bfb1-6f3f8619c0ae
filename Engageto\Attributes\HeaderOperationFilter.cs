﻿using Microsoft.OpenApi.Models;
using Swashbuckle.AspNetCore.SwaggerGen;

namespace Engageto.Attributes
{
    public class HeaderOperationFilter : IOperationFilter
    {
        public void Apply(OpenApiOperation operation, OperationFilterContext context)
        {
            var headerAttributes = context.MethodInfo.GetCustomAttributes(true)
                .OfType<HeaderAttribute>();

            foreach (var headerAttribute in headerAttributes)
            {
                operation.Parameters.Add(new OpenApiParameter
                {
                    Name = headerAttribute.HeaderName,
                    In = ParameterLocation.Header,
                    Required = true,
                    Schema = new OpenApiSchema
                    {
                        Type = "string"
                    }
                });
            }
        }
    }
}
