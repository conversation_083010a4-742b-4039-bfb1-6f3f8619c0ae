﻿using System.ComponentModel.DataAnnotations;

namespace EngagetoEntities.Dtos.TemplateDtos
{
    public class DeleteTemplateDto
    {
        [Required(ErrorMessage = "BusinessId is required")]
        public string BusinessId { get; set; }

        [Required(ErrorMessage = "UserId is required")]
        public Guid UserId { get; set; }

        [Required(ErrorMessage = "TemplateId is required")]
        public Guid TemplateId { get; set; }

    }
    public class RestoredTemplateDto
    {
        [Required(ErrorMessage = "BusinessId is required")]
        public string BusinessId { get; set; }

        [Required(ErrorMessage = "UserId is required")]
        public Guid UserId { get; set; }

        [Required(ErrorMessage = "TemplateId is required")]
        public Guid TemplateId { get; set; }

    }


}
