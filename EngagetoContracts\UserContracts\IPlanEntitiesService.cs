﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using EngagetoEntities.Entities;

namespace EngagetoContracts.UserContracts
{
    public interface IPlanEntitiesService
    {
        Task<IEnumerable<PlanEntities>> GetAllPlansAsync(Guid currentUserId, string searchQuery, string sortBy, bool isSortAscending);
        Task<PlanEntities> GetPlanById(int planId);
        Task<bool> CreatePlan(Guid currentUserId, PlanEntitiesDto planEntitiesDto);
        Task<bool> UpdatePlan(Guid currentUserId, int planId, UpdatePlanEntitiesDto updatePlanEntitiesDto);
        Task<bool> DeletePlan(Guid currentUserId, int planId);
        Task<IEnumerable<MenuWithPlanDto>> GetActiveMenusByPlanAsync(int? planId, string? planName);
        Task<List<PlanEntities>> GetAllPlansAsync();
        Task<Subscriptions?> GetSubscriptionsAsync(string companyid);

    }
}
