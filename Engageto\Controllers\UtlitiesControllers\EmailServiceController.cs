﻿using Azure;
using EngagetoContracts.UserContracts;
using EngagetoEntities.Dtos.MailsDtos;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using EngagetoEntities.Dtos.ApiResponseDtos;

namespace Engageto.Controllers.UtlitiesControllers
{
    [Route("api/[controller]")]
    [ApiController]
    public class EmailServiceController  :  BaseController
    {
       private  readonly IEmailService _emailService;
        public EmailServiceController(IEmailService  emailService )
        {
            _emailService = emailService;   
        }
        [HttpPost("sendEmail")]
        [AllowAnonymous]
        public async Task<IActionResult> SendEmail([FromForm] EmailMessageDtos request)
        {
     
            try
            {
                bool result = await _emailService.SendEmailViaUtility(request.Subject, request.Body, request.Attachments, request.To, request.Cc, request.Bcc);

                if (result)
                {
                    return Ok(new ApiResponse<bool>
                    {
                        Success = true,
                        Message = "Email sent successfully.",
                        Data = result,
                        Error = null
                    });
                }
                else
                {
                    return StatusCode(StatusCodes.Status500InternalServerError, new ApiResponse<bool>
                    {
                        Success = false,
                        Message = "Failed to send email.",
                        Data = false,
                        Error = "The email service returned false, indicating a failure."
                    });

                }

            }
            catch (Exception ex)
            {
                var errorResponse = new ApiResponse<bool>
                {
                    Success = false,
                    Data = false,
                    Message = $"An error occurred while sending the email: {ex.Message}"
                };

                return StatusCode(StatusCodes.Status500InternalServerError, errorResponse);
            }
        }

    }
}
