﻿using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Configuration;
using Microsoft.IdentityModel.Tokens;
using System;
using System.Collections.Generic;
using System.IdentityModel.Tokens.Jwt;
using System.Linq;
using System.Security.Claims;
using System.Text;
using System.Threading.Tasks;
using EngagetoContracts.UserContracts;
using EngagetoEntities.UserEntities.Models;

namespace EngagetoRepository.UserRepository
{
    public class LogoutService : ILogoutService
    {
        /* private readonly IConfiguration _configuration;
         private readonly TokenValidationParameters _tokenValidationParameters;
         public LogoutService(IConfiguration configuration)
         {
             _configuration = configuration;
         }

         public async Task<LogoutResult> LogoutAsync(string token)
         {
             try
             {
                 var validationParameters = new TokenValidationParameters
                 {
                     ValidateIssuer = true,
                     ValidateAudience = true,
                     ValidateIssuerSigningKey = true,
                     *//* ValidIssuer = _configuration["Jwt:Issuer"],
                      ValidAudience = _configuration["Jwt:Audience"],*//*
                     ValidAudience = "https://loginAPI/api",
                     ValidIssuer = "https://dev-jvgzpy2caz4fvqso.us.auth0.com/",
                     IssuerSigningKey = new SymmetricSecurityKey(Encoding.UTF8.GetBytes("Im0BochrmocQD992BKCFQvfcUsVdaJZBcTDb9TQ8wt-o9qIAzGH69rubwf5RAGGV"))
                 };

                 var tokenHandler = new JwtSecurityTokenHandler();
                 var principal = tokenHandler.ValidateToken(token, validationParameters, out _);



                 bool success = true;
                 string message = success ? "Logged out successfully!" : "Failed to logout.";

                 return new LogoutResult { Success = success, Message = message };
             }
             catch (SecurityTokenMalformedException ex)
             {

                 return new LogoutResult { Success = false, Message = $"Invalid token format: {ex.Message}" };
             }
             catch (Exception ex)
             {

                 return new LogoutResult { Success = false, Message = "Failed to logout." };
             }
         }*/
        private readonly TokenValidationParameters _validationParameters;

        public LogoutService()
        {
            _validationParameters = new TokenValidationParameters
            {
                ValidateIssuer = true,
                ValidateAudience = true,
                ValidateIssuerSigningKey = true,
                ValidAudience = "https://loginAPI/api",
                ValidIssuer = "https://dev-jvgzpy2caz4fvqso.us.auth0.com/",
                IssuerSigningKey = new SymmetricSecurityKey(Encoding.UTF8.GetBytes("Im0BochrmocQD992BKCFQvfcUsVdaJZBcTDb9TQ8wt-o9qIAzGH69rubwf5RAGGV"))
            };
        }

        public async Task<LogoutResult> LogoutAsync(string token)
        {
            try
            {
                var tokenHandler = new JwtSecurityTokenHandler();
                var principal = tokenHandler.ValidateToken(token, _validationParameters, out _);

                // TODO: Check if the user or token needs to be invalidated (e.g., add to a blacklist)

                bool success = true;
                string message = success ? "Logged out successfully!" : "Failed to logout.";

                return new LogoutResult { Success = success, Message = message };
            }
            catch (SecurityTokenMalformedException ex)
            {
                return new LogoutResult { Success = false, Message = $"Invalid token format: {ex.Message}" };
            }
            catch (Exception ex)
            {
                return new LogoutResult { Success = false, Message = "Failed to logout." };
            }
        }
    }
}
