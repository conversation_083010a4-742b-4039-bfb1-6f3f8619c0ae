﻿using Newtonsoft.Json;

namespace EngagetoEntities.Dtos.MetaDto;

public class CreateTempateErrorResponseDto
{

    [JsonProperty("error")]
    public ErrorDetail ErrrorMessage { get; set; }
}

public class ErrorDetail
{
    [JsonProperty("message")]
    public string Message { get; set; }

    [JsonProperty("error_user_title")]
    public string ErrorUserTitle { get; set; }

    [JsonProperty("error_user_msg")]
    public string ErrorUserMsg { get; set; }
}

public class CreateTemplateResponse
{
    [JsonProperty("id")]
    public string Id { get; set; } = default!;
    [JsonProperty("status")]
    public string Status { get; set; } = default!;
    [JsonProperty("category")]
    public string Category { get; set; } = default!;

    [JsonProperty("error")]
    public string Error { get; set; } = default!;
}


