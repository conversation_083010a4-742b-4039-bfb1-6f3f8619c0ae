﻿namespace EngagetoEntities.UserEntities.Dtos
{
    public class WalletNotificationDto
    {
        public Guid Id { get; set; }
        public string Category { get; set; }
        public Guid WalletId { get; set; }
        public Guid TransactionId { get; set; }
        public decimal? Amount { get; set; }
        public string Title { get; set; }
        public string Description { get; set; }
        public bool IsRead { get; set; } = false;
        public string Type { get; set; }
    }
}
