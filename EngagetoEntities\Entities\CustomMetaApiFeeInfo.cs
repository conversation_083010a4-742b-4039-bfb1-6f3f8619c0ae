﻿using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace EngagetoEntities.Entities
{
    public class CustomMetaApiFeeInfo :  BaseEntity
    {
        [Key]
        public Guid Id { get; set; } = Guid.NewGuid();
        public string? BusinessId { get; set; }
        public string? Currency {get;set;}
        public decimal? MarketingFee { get;set;}
        public decimal? OutSideCountryFee { get; set;}
        public decimal? UtilityFee { get; set; }
        public decimal? ServiceFee { get; set; }
        public decimal? AuthenticationFee { get; set; }
        public decimal? AuthenticationOutSideFee { get; set; }
        public  Guid? MasterMetaPriceId { get; set;}

    }
}
