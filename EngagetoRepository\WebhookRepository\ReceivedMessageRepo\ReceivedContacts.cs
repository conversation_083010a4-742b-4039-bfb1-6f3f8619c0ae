﻿using EngagetoBackGroundJobs.Implementation;
using EngagetoContracts.WebhookContracts.ReceivedNotification;
using EngagetoEntities.DdContext;
using EngagetoEntities.Dtos.IntegrationDtos;
using EngagetoEntities.Dtos.MetaDto;
using EngagetoEntities.Entities;
using EngagetoEntities.Enums;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Configuration;


namespace EngagetoRepository.WebhookRepository.ReceivedMessageRepo
{
    public class ReceivedContacts : IReceivedContacts
    {

        public IConfiguration _configuration;
        private readonly ApplicationDbContext _applicationDbContext;
        private readonly EngagetoDapper.Data.Interfaces.IUserInterfaces.IUserServices.IInboxService _inboxService;
        private readonly LeadProcessingService _leadProcessingService;

        public ReceivedContacts(ApplicationDbContext applicationDbContext, IConfiguration configuration,
            EngagetoDapper.Data.Interfaces.IUserInterfaces.IUserServices.IInboxService inboxService,
            LeadProcessingService leadProcessingService)
        {
            _configuration = configuration;
            _inboxService = inboxService;
            _applicationDbContext = applicationDbContext;
            _leadProcessingService = leadProcessingService;
        }

        public async Task ContactAdd(dynamic value)
        {
            try
            {
                string phoneNumberId = value["metadata"]?["phone_number_id"]?.ToString() ?? string.Empty;
                if (string.IsNullOrEmpty(phoneNumberId)) return;


                var businessMeta = await _applicationDbContext.BusinessDetailsMetas
                    .FirstOrDefaultAsync(m => m.PhoneNumberID == phoneNumberId);
                if (businessMeta == null) return;

                string businessIdString = businessMeta.BusinessId ?? string.Empty;
                var waContacts = value["contacts"];
                if (waContacts == null || waContacts?.Count == 0) return;

                var waContact = waContacts?[0];
                if (waContact == null) return;

                ResponseContacts responseContacts = waContact.ToObject<ResponseContacts>();

                _ = Guid.TryParse(businessIdString, out Guid businessId);

                var existingContact = await _applicationDbContext.Contacts
                    .FirstOrDefaultAsync(m => m.BusinessId == businessId && string.Concat(m.CountryCode.Replace("+", ""), m.Contact) == responseContacts.wa_id);
                if (existingContact == null)
                {
                    var contactWithCountryCode = PhoneNumberHelper.GetCountryCodeAndPhoneNumber(responseContacts.wa_id);
                    // Ensure country code and national number are correct
                    CountryDetails? country = null;
                    if (contactWithCountryCode.NationalNumber == responseContacts.wa_id)
                    {
                        country = await _applicationDbContext.CountryDetails
                             .FirstOrDefaultAsync(m => ($"+{responseContacts.wa_id}").StartsWith(m.CountryCode));
                        contactWithCountryCode.CountryCode = country?.CountryCode ?? "+91";
                        contactWithCountryCode.NationalNumber = responseContacts.wa_id.Replace((country?.CountryCode ?? "91").Replace("+", ""), "");
                    }
                    else
                    {
                        country = await _applicationDbContext.CountryDetails.FirstOrDefaultAsync(m => m.CountryCode.Contains(contactWithCountryCode.CountryCode));
                    }
                    var newContact = new Contacts
                    {
                        Name = responseContacts.profile.name,
                        Source = SourceType.WhatsApp,
                        IsActive = true,
                        Contact = contactWithCountryCode.NationalNumber,
                        CountryCode = contactWithCountryCode.CountryCode.StartsWith("+") ? contactWithCountryCode.CountryCode : $"+{contactWithCountryCode.CountryCode}",
                        BusinessId = businessId,
                        CreatedDate = DateTime.UtcNow,
                        CountryName = country?.CountryName,
                        ContactId = Guid.NewGuid(),
                        LastMessageAt = DateTime.UtcNow,
                        IsOptIn = Is_OptIn.optin
                    };
                    await _applicationDbContext.AddAsync(newContact);
                    await _applicationDbContext.SaveChangesAsync();
                    //Run background
                    await _leadProcessingService.SetLeadDataAsync(new IntegrationAccountRecord(businessIdString, contact: newContact, @events: new List<IntegrationEvent> { IntegrationEvent.LeadGen }), CancellationToken.None);
                }
                if (existingContact?.Name == "Unknown" || existingContact?.IsActive == false)
                {
                    existingContact.Name = responseContacts.profile.name;
                    existingContact.Source = SourceType.WhatsApp;
                    existingContact.IsActive = true;
                    existingContact.LastMessageAt = DateTime.UtcNow;
                    _applicationDbContext.Update(existingContact);
                    await _applicationDbContext.SaveChangesAsync();
                }
                else if (existingContact != null && existingContact?.IsActive == true)
                {
                    existingContact.LastMessageAt = DateTime.UtcNow;
                    await _applicationDbContext.SaveChangesAsync();
                }
            }
            catch (Exception ex)
            {

            }
        }

        public async Task ContactSave(ResponseContacts responseContacts, string businessIdString)
        {
            var CountryCodes = _applicationDbContext.CountryDetails.Select(m => m.CountryCode.Replace("+", "")).ToList();
            var matchedCountryCode = string.Empty;
            var remainingWaId = string.Empty;
            var Contact = new Contacts();
            foreach (var countryCode in CountryCodes)
            {
                if (responseContacts.wa_id.StartsWith(countryCode))
                {
                    matchedCountryCode = countryCode;
                    remainingWaId = responseContacts.wa_id.Substring(countryCode.Length);
                    break;
                }
            }
            Contact.Contact = remainingWaId;
            string remainingString = matchedCountryCode;
            Contact.Name = responseContacts.profile.name;
            // Assuming the country code is the remaining part of the string
            Contact.CountryCode = "+" + remainingString;
            Contact.ChatStatus = ChatStatus.New;
            Contact.UserId = null;
            Contact.IsOptIn = Is_OptIn.optin;
            Contact.Source = EngagetoEntities.Enums.SourceType.WhatsApp;

            try
            {
                Contact.BusinessId = Guid.Parse(businessIdString);
            }
            catch (FormatException)
            {
                // Handle the case where the string cannot be parsed as a Guid
            }
            catch (ArgumentNullException)
            {
                // Handle the case where businessIdString is null
            }
            Contact.IsActive = true;
            Contact.IsSpam = false;
            Contact.ContactId = Guid.NewGuid();
            //Contact.CreatedDate = DateTime.UtcNow;
            Contact.CreatedDate = DateTime.UtcNow;

            // Set the CreatedAt property of sentMessage to the current Indian Standard Time

            var data = _applicationDbContext.Contacts.Add(Contact);
            _applicationDbContext.SaveChanges();
            await _inboxService.SaveAndUpdateChatStatusAsync(data.Entity.ContactId,
                ChatStatus.resolved, data.Entity.UserId ?? null);
        }
        public async Task DemoContacts(dynamic value, Guid BusinessId)
        {
            var Contacts = _applicationDbContext.Contacts.Where(m => m.BusinessId == BusinessId).Select(m => (m.CountryCode + m.Contact).Replace("+", ""));

            var d1 = value["contacts"];
            if (d1 != null)
            {

                d1 = d1[0];
                ResponseContacts responseContacts = new ResponseContacts();
                if (d1 != null)
                {
                    responseContacts = d1.ToObject<ResponseContacts>();
                }
                if (d1 != null)
                {
                    if ((!Contacts.Contains(responseContacts.wa_id)))
                    {
                        await ContactSave(responseContacts, BusinessId.ToString());
                    }
                }
            }
        }
    }
}
