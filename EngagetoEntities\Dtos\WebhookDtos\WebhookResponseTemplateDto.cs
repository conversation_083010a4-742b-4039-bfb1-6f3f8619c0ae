﻿using EngagetoEntities.Entities;
using EngagetoEntities.Enums;
using Newtonsoft.Json;

namespace EngagetoEntities.Dtos.WebhookDtos
{
    public class WebhookResponseTemplateDto : FieldDto
    {
        public string? MetaId { get; set; }
        public Guid TemplateId { get; set; }
        public string TemplateName { get; set; }
        public WATemplateCategory? Category { get; set; }
        public MediaType MediaType { get; set; }
        public string? MediaFile { get; set; }
        public string? Header { get; set; }
        public string LanguageCode { get; set; }
        public string Body { get; set; }
        public string? Footer { get; set; }
        public WATemplateStatus? Status { get; set; }
        public List<Button>? Buttons { get; set; }
    }
}
