﻿using EngagetoEntities.Enums;

namespace EngagetoEntities.Dtos.ContactDtos
{
    public class ContactResponseDto
    {
        public Guid ContactId { get; set; }
        public string Name { get; set; } = default!;
        public Guid BusinessId { get; set; }
        public Guid? UserId { get; set; }
        public string CountryCode { get; set; } = default!;
        public string Contact { get; set; } = default!;
        public string? Email { get; set; }
        public string? CountryName { get; set; }
        public List<TagDto>? Tags { get; set; }
        public SourceType? Source { get; set; }
        public bool IsActive { get; set; }
        public ChatStatus ChatStatus { get; set; }
        public DateTime? CreatedDate { get; set; }
    }
    public class TagDto
    {
        public Guid Id { get; set; }
        public string? TagName { get; set; }
    }
}
