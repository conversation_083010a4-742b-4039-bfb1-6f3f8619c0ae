﻿using EngagetoDapper.InfraStructure;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.DependencyInjection;


namespace EngagetoDapper
{
    public static class Startup
    {
        public static IServiceCollection AddInfraStructure(this IServiceCollection services, IConfiguration configuration)
        {
            return services.AddConnection(configuration)
                .AddAutomation()
                .AddCommonStartup();
        }
    }
}
