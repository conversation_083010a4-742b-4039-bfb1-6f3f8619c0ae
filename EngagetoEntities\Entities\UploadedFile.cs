﻿using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace EngagetoEntities.Entities
{
    [Table("UploadedFiles")]
    public class UploadedFile: BaseEntity
    {
        [Key]
        public int Id { get; set; }
        public string FileName { get; set; }
        public string UploadedFileName { get; set; }
        public string? FilePath { get; set; }
        public string? BucketName { get; set; }
        public string? ContentType { get; set; }
        public bool IsPublic { get; set; }
        public int DownloadCount { get; set; }
        public string? BusinessId { get; set; }
        public Guid? UserId { get; set; }
    }
}
