﻿using EngagetoEntities.UserEntities.Models;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace EngagetoEntities.UserEntities.Dtos
{
    public class WalletAndSubscription
    {
        public decimal WalletBalance { get; set; }
        public SubscriptionPlanDto subscriptionPlan { get; set; } = new();

    }

    public class SubscriptionPlanDto
    {
        public int PlanId { get; set; }
        public string PlanName { get; set; }
        public DateTime? PlanEndDate { get; set; }
        public bool IsActive { get; set; }
        public string? PlanType { get; set; }
        public decimal? CurrentSubscriptionPlanAmount { get; set; }
        public int? TotalClients { get; set; }
        public int? SamePlanWithClients { get; set; }
        public List<PaymentDetailsDto>? PaymentDetails { get; set; }
    }

    public class PaymentDetailsDto
    {
        public int Id { get; set; }
        public int? SubscriptionId { get; set; }
        public int PlanId { get; set; }
        public string? DurationType { get; set; }
        public string? CompanyId { get; set; }
        public decimal? OrderAmount { get; set; }
        public string? Currency { get; set; }
        public DateTime PlanStartDate { get; set; }
        public DateTime PlanEndDate { get; set; } 
    }
}
