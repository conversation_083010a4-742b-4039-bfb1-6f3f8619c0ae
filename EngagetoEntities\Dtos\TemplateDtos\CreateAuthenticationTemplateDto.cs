﻿

using EngagetoEntities.Enums;
using System.ComponentModel.DataAnnotations;

namespace EngagetoEntities.Dtos.TemplateDtos
{
    public class CreateAuthenticationTemplateDto
    {
        public string BusinessId { get; set; }
        public Guid UserId { get; set; }
        public Guid? TemplateId { get; set; } // this is for recreating template from draft
        public string TemplateName { get; set; }
        [Required(ErrorMessage = "Language can't be empty")]
        public Language Language { get; set; }

        [Required(ErrorMessage = "Category can't be empty")]
        public WATemplateCategory Category { get; set; }
        public string? SubCategory { get; set; } 
        public string? message_send_ttl_seconds { get; set; } // "message_send_ttl_seconds": 60,
        public bool? add_security_recommendation { get; set; }
        public string? code_expiration_minutes { get; set; }
        public OtpType? otp_type { get; set; }
        public string package_name { get; set; }
        public string signature_hash { get; set; }

    }
}
