﻿using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations.Schema;
using System.ComponentModel.DataAnnotations;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using static EngagetoEntities.UserEntities.Models.ExpiryDateAttribute;
using EngagetoEntities.Entities;

namespace EngagetoEntities.UserEntities.Models
{
    public class PaymentCardDetails
    {
        private string _cardNumber;

        [Key]
        public Guid CardId { get; set; }

        [ForeignKey(nameof(Ahex_CRM_Users))]
        public Guid UserId { get; set; }

        [HideCardNumber]
        public string CardNumber { get; set; }

      
        public string ExpiryDate { get; set; }

       
        public string CVV { get; set; }

        
        public string CardholderName { get; set; }

       
       public Ahex_CRM_Users Ahex_CRM_Users { get; set; }
    }

    public class ExpiryDateAttribute : ValidationAttribute
    {
        protected override ValidationResult IsValid(object value, ValidationContext validationContext)
        {
            if (value != null && DateTime.TryParseExact((string)value, "MM/yy", null, System.Globalization.DateTimeStyles.None, out DateTime expiryDate))
            {
               
                if (expiryDate < DateTime.UtcNow)
                {
                    return new ValidationResult(ErrorMessage);
                }
                return ValidationResult.Success;
            }
            return new ValidationResult("Expiry Date is required");
        }

        public class MaskedCardNumberAttribute : ValidationAttribute
        {
            public override bool IsValid(object value)
            {
                if (value is string cardNumber && cardNumber.Length == 16)
                {
                    ErrorMessage = "Invalid Card Number";
                    return true; 
                }
                return false; 
            }

            public override string FormatErrorMessage(string name)
            {
                return string.Format(ErrorMessage, name);
            }
        }
        public class PaymentCardDetailsDto
        {
            [Required(ErrorMessage = "Card Number is required")]
            [StringLength(16, MinimumLength = 16, ErrorMessage = "Card Number must be 16 characters long")]
            [RegularExpression(@"^\d{16}$", ErrorMessage = "Invalid Card Number")]
            [HideCardNumber]
            public string CardNumber { get; set; }

            [Required(ErrorMessage = "Expiry Date is required")]
            [RegularExpression(@"^(0[1-9]|1[0-2])\/\d{2}$", ErrorMessage = "Invalid Expiry Date. Format: MM/YY")]
            [ExpiryDate(ErrorMessage = "Card has expired")]
            public string ExpiryDate { get; set; }

            [Required(ErrorMessage = "CVV is required")]
            [RegularExpression(@"^\d{3,4}$", ErrorMessage = "Invalid CVV")]
            public string CVV { get; set; }

            [Required(ErrorMessage = "Cardholder Name is required")]
            public string CardholderName { get; set; }

            
        }
    }
    }

