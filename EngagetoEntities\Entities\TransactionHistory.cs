﻿using EngagetoEntities.Enums;
using Newtonsoft.Json;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace EngagetoEntities.Entities
{
    [Table("TransactionHistoryEntities")]
    public class TransactionHistoryEntity : BaseEntity
    {

        [Key]
        [DatabaseGenerated(DatabaseGeneratedOption.Identity)]
        public Guid Transactionid { get; set; }
        public Guid UserID { get; set; }
        public string? CompanyId { get; set; }
        public Guid? DiscountId { get; set; }
        public string OrderId { get; set; }
        public string? PaymentId { get; set; }
        public Guid? WalletId { get; set; }
        public long? TransactionDate { get; set; }
        public string? TransactionType { get; set; }
        public decimal Amount { get; set; }
        public string? Entity { get; set; }
        public decimal? AmountPaid { get; set; }
        public decimal? AmountDue { get; set; }
        public string? Receipt { get; set; }
        public string? Currency
        {
            get => _currency.ToString();
            set
            {
                if (Enum.TryParse(value, true, out Currency parsedCurrency))
                {
                    _currency = parsedCurrency;
                }
                else
                {
                    throw new ArgumentException($"Invalid currency value: {value}");
                }
            }
        }
        public string? OfferId { get; set; }
        public string? Status { get; set; }
        public int Attempts { get; set; }
        public decimal? DiscountAmount { get; set; }
        public string? Notes
        {
            get => _notes != null ? JsonConvert.SerializeObject(_notes) : null;
            set
            {
                _notes = string.IsNullOrEmpty(value) ? null : JsonConvert.DeserializeObject<Dictionary<string, string>>(value);
            }
        }
        public string? PaymentMethod { get; set; }
        public string? CardNetwork { get; set; }
        public string? CardType { get; set; }
        public string? AccountId { get; set; }
        public string? Email { get; set; }
        public string? Contact { get; set; }
        public string? CardId { get; set; }
        public bool? IsInternationalCard { get; set; } = false;
        public string? CardLast4Digit { get; set; }
        public string? BankTransactionId { get; set; }
        public string? PayerAccountType { get; set; }
        public string? Vpa { get; set; }
        public string? PaymentWallet { get; set; }
        public int? Invoice { get; set; }

        [NotMapped]
        public Currency _currency;
        [NotMapped]
        public Dictionary<string, string>? _notes;

        // Optional constructor to initialize the currency
        public TransactionHistoryEntity(Currency currency)
        {
            _currency = currency;
        }

        // Default constructor
        public TransactionHistoryEntity() { }

    }
}
