﻿using AutoMapper;
using EngagetoEntities.Entities;
using Newtonsoft.Json;

namespace EngagetoEntities.Mapping
{
    public class OrderToTransactionHistoryProfile : Profile
    {
        public OrderToTransactionHistoryProfile()
        {
            CreateMap<UserEntities.Dtos.OrderDto, TransactionHistoryEntity>()
                .ForMember(dest => dest.OrderId, opt => opt.MapFrom(src => src.Id))
                .ForMember(dest => dest.TransactionDate, opt => opt.MapFrom(src => src.CreatedAt))
                .ForMember(dest => dest.Amount, opt => opt.MapFrom(src => src.Amount == 0 ? src.Amount : src.Amount / 100))
                .ForMember(dest => dest.Entity, opt => opt.MapFrom(src => src.Entity))
                .ForMember(dest => dest.AmountPaid, opt => opt.MapFrom(src => src.AmountPaid == 0 ? src.AmountPaid : src.AmountPaid / 100))
                .ForMember(dest => dest.AmountDue, opt => opt.MapFrom(src => src.AmountDue == 0 ? src.AmountDue : src.AmountDue / 100))
                .ForMember(dest => dest.Currency, opt => opt.MapFrom(src => src.Currency))
                .ForMember(dest => dest.OfferId, opt => opt.MapFrom(src => src.OfferId))
                .ForMember(dest => dest.Status, opt => opt.MapFrom(src => src.Status))
                .ForMember(dest => dest.Attempts, opt => opt.MapFrom(src => src.Attempts))
                .ForMember(dest => dest.Notes, opt => opt.MapFrom(src => JsonConvert.SerializeObject(src.Notes)))
                .ForMember(dest => dest.Receipt, opt => opt.MapFrom(src => src.Receipt))
                .ForMember(dest => dest.UserID, opt => opt.Ignore())
                .ForMember(dest => dest.DiscountAmount, opt => opt.Ignore())
                .ForMember(dest => dest.CreatedBy, opt => opt.Ignore())
                .ForMember(dest => dest.UpdatedAt, opt => opt.Ignore())
                .ForMember(dest => dest.CreatedAt, opt => opt.Ignore())
                .ForMember(dest => dest.UpdatedBy, opt => opt.Ignore())
                .ForMember(dest => dest.DeletedAt, opt => opt.Ignore())
                .ForMember(dest => dest.Transactionid, opt => opt.Ignore())
                .ForMember(dest => dest.DiscountId, opt => opt.Ignore())
                .ForMember(dest => dest.TransactionType, opt => opt.Ignore())
                .ForMember(dest => dest._currency, opt => opt.Ignore())
                .ForMember(dest => dest._notes, opt => opt.Ignore())
                .ForMember(dest => dest.DeletedBy, opt => opt.Ignore())
                .ForMember(dest => dest.WalletId, opt => opt.Ignore())
                .ForMember(dest => dest.IsDeleted, opt => opt.Ignore())
                .ForMember(dest => dest.PaymentId, opt => opt.Ignore())
                .ForMember(dest => dest.CompanyId, opt => opt.Ignore())
                .ForMember(dest => dest.CardType, opt => opt.Ignore())
                .ForMember(dest => dest.CardNetwork, opt => opt.Ignore())
                .ForMember(dest => dest.AccountId, opt => opt.Ignore())
                .ForMember(dest => dest.Email, opt => opt.Ignore())
                .ForMember(dest => dest.Contact, opt => opt.Ignore())
                .ForMember(dest => dest.CardId, opt => opt.Ignore())
                .ForMember(dest => dest.IsInternationalCard, opt => opt.Ignore())
                .ForMember(dest => dest.CardLast4Digit, opt => opt.Ignore())
                .ForMember(dest => dest.BankTransactionId, opt => opt.Ignore())
                .ForMember(dest => dest.PayerAccountType, opt => opt.Ignore())
                .ForMember(dest => dest.Vpa, opt => opt.Ignore())
                .ForMember(dest => dest.Invoice, opt => opt.Ignore())
                .ForMember(dest => dest.PaymentWallet, opt => opt.Ignore())
                .ForMember(dest => dest.PaymentMethod, opt => opt.Ignore());
        }

    }
}
