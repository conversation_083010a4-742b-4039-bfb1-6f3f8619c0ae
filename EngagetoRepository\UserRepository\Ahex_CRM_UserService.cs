﻿using Amazon.S3;
using Amazon.S3.Transfer;
using EngagetoContracts.GeneralContracts;
using EngagetoContracts.UserContracts;
using EngagetoEntities.Entities;
using EngagetoEntities.UserEntities.Models;
using Microsoft.AspNetCore.Http;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Configuration;

namespace EngagetoRepository.UserRepository
{
    public class Ahex_CRM_UserService : IAhex_CRM_UserService
    {
        private readonly ApplicationDBContext _context;
        private readonly IEmailService _emailService;
        private readonly IConfiguration _configuration;
        private readonly IEnvironmentService _environmentService;
        private readonly string _requestedLink;

        public Ahex_CRM_UserService(IConfiguration config, ApplicationDBContext context, IEmailService emailService, IEnvironmentService environmentService)
        {
            _context = context;
            _emailService = emailService;
            _configuration = config;
            _environmentService = environmentService;

            if (_environmentService.IsDevelopment)
                _requestedLink = _environmentService.GetDevWebsiteLink();
            else
                _requestedLink = _environmentService.GetProdWebsiteLink();
        }
        public async Task<string> UploadLogoAsync(Guid Id, IFormFile file)
        {
            var manageClient = await _context.Ahex_CRM_Users.FindAsync(Id);

            if (manageClient == null)
            {

                return null;
            }

            var fileLink = await HandleFileUploadAsync(file);

            manageClient.Image = fileLink;

            await _context.SaveChangesAsync();

            return fileLink;
        }
        public async Task<string> UploadLogoAsync1(Guid Id, IFormFile file)
        {
            var manageClient = await _context.Ahex_CRM_BusinessDetails.FindAsync(Id);

            if (manageClient == null)
            {

                return null;
            }

            var fileLink = await HandleFileUploadAsync(file);

            manageClient.CompanyLogoLink = fileLink;

            await _context.SaveChangesAsync();

            return fileLink;
        }
        private async Task<string> HandleFileUploadAsync(IFormFile? file)
        {
            try
            {
                var accessKey = _configuration["Aws:AccessKey"];
                var secretKey = _configuration["Aws:SecretKey"];
                var bucketName = _configuration["Aws:BucketName"];
                var  regionString= _configuration["Aws:Region"];
                var region = Amazon.RegionEndpoint.GetBySystemName(regionString);

                using (var client = new AmazonS3Client(accessKey, secretKey, region))
                {
                    var key = $"{Guid.NewGuid()}.{file.FileName.Split('.')[1]}";

                    using (var newMemoryStream = new MemoryStream())
                    {

                        await file.CopyToAsync(newMemoryStream);

                        var uploadRequest = new TransferUtilityUploadRequest
                        {
                            InputStream = newMemoryStream,
                            Key = key,
                            BucketName = bucketName,
                            CannedACL = S3CannedACL.PublicRead
                        };

                        using (var fileTransferUtility = new TransferUtility(client))
                        {
                            await fileTransferUtility.UploadAsync(uploadRequest);
                        }
                    }


                    var fileLink = $"https://{bucketName}.s3.{region.SystemName}.amazonaws.com/{key}";

                    return fileLink;
                }
            }
            catch (Exception ex)
            {

                Console.WriteLine($"Error uploading file to S3: {ex.Message}");
                throw;
            }
        }

        public async Task<Guid?> GetRoleIdByRoleNameAsync(string roleName)
        {
            var roleId = await _context.Roles
                .Where(r => r.Name == roleName)
                .Select(r => r.Id)
                .FirstOrDefaultAsync();

            return roleId;
        }
        private bool IsRoleAllowed(string roleName)
        {
            var allowedRoles = new List<string> { RoleConstants.Admin, RoleConstants.Owner, RoleConstants.Teammate };
            return allowedRoles.Contains(roleName);
        }

        public async Task<bool> UpdateCompanyFieldsAsync(Guid currentUserId, Guid companyId, Ahex_CRM_BusinessDetailsDto updatedCompany)
        {
            try
            {

                var existingCompany = await _context.Ahex_CRM_BusinessDetails.FindAsync(companyId);

                if (existingCompany == null)
                {
                    return false;
                }
                /* if (!existingCompany.Status)
                 {
                     throw new InvalidOperationException("Company is inactive and cannot be updated.");
                 }*/

                if (!string.IsNullOrEmpty(updatedCompany.BusinessName))
                {
                    existingCompany.BusinessName = updatedCompany.BusinessName;
                }


                /* if (Enum.TryParse(updatedCompany.BusinessCategory.ToString(), out BusinessCategory category))
                 {
                     existingCompany.BusinessCategory = category;
                 }*/
                if (!string.IsNullOrEmpty(updatedCompany.BusinessWebsite))
                {
                    existingCompany.BusinessWebsite = updatedCompany.BusinessWebsite;
                }

                if (!string.IsNullOrEmpty(updatedCompany.BusinessEmail))
                {
                    existingCompany.BusinessEmail = updatedCompany.BusinessEmail;
                    var userToUpdate = await _context.Ahex_CRM_Users.FirstOrDefaultAsync(u => u.EmailAddress != existingCompany.BusinessEmail);
                    if (userToUpdate != null)
                    {

                        userToUpdate.EmailAddress = updatedCompany.BusinessEmail;
                    }
                }

                if (!string.IsNullOrEmpty(updatedCompany.PhoneNumber))
                {
                    var countryAndPhoneNumber = updatedCompany.PhoneNumber;
                    var countryCode = countryAndPhoneNumber.Substring(0, 3);
                    var phoneNumber = countryAndPhoneNumber.Substring(4);
                    var countryDetails = await _context.CountryDetails.FirstOrDefaultAsync(cd => cd.CountryCode == countryCode);
                    if (countryDetails == null)
                    {
                        throw new InvalidOperationException($"Country code '{countryCode}' not found.");
                    }
                    var phoneNumberWithSpace = $"{countryCode} {phoneNumber}";
                    existingCompany.PhoneNumber = phoneNumberWithSpace;
                    var userToUpdate = await _context.Ahex_CRM_Users.FirstOrDefaultAsync(u => u.PhoneNumber != updatedCompany.PhoneNumber);
                    if (userToUpdate == null)
                    {

                        userToUpdate.PhoneNumber = phoneNumberWithSpace;
                    }
                }
                if (!string.IsNullOrEmpty(updatedCompany.CountryName))
                {

                    var countryDetails = await _context.CountryDetails.FirstOrDefaultAsync(cd => cd.CountryName == updatedCompany.CountryName);
                    if (countryDetails == null)
                    {
                        throw new InvalidOperationException($"Country name '{updatedCompany.CountryName}' not found.");
                    }


                    existingCompany.CountryName = updatedCompany.CountryName;
                }
                if (!string.IsNullOrEmpty(updatedCompany.GSTNumber))
                {
                    existingCompany.GSTNumber = updatedCompany.GSTNumber;
                }
                if (!string.IsNullOrEmpty(updatedCompany.CompanyAddress))
                {
                    existingCompany.CompanyAddress = updatedCompany.CompanyAddress;
                }

                if (!string.IsNullOrEmpty(updatedCompany.Description))
                {
                    existingCompany.Description = updatedCompany.Description;
                }
                if (!string.IsNullOrEmpty(updatedCompany.CompanyLegalName))
                {
                    existingCompany.CompanyLegalName = updatedCompany.CompanyLegalName;
                }
                if (updatedCompany.CompanyRegistered != null)
                {
                    existingCompany.CompanyRegistered = updatedCompany.CompanyRegistered.Value;
                }
                /* if (Enum.TryParse(updatedCompany.CompanyType.ToString(), out CompanyType company))
                 {
                     existingCompany.CompanyType = company;
                 }*/

                if (!string.IsNullOrEmpty(updatedCompany.CustomerCareEmail))
                {
                    existingCompany.CustomerCareEmail = updatedCompany.CustomerCareEmail;
                }
                if (!string.IsNullOrEmpty(updatedCompany.CustomerCarePhone))
                {
                    var countryAndPhoneNumber = updatedCompany.CustomerCarePhone;
                    var countryCode = countryAndPhoneNumber.Substring(0, 3);
                    var phoneNumber = countryAndPhoneNumber.Substring(4);
                    var countryDetails = await _context.CountryDetails.FirstOrDefaultAsync(cd => cd.CountryCode == countryCode);
                    if (countryDetails == null)
                    {
                        throw new InvalidOperationException($"Country code '{countryCode}' not found.");
                    }
                    var phoneNumberWithSpace = $"{countryCode} {phoneNumber}";
                    existingCompany.CustomerCarePhone = phoneNumberWithSpace;
                }
                if (!string.IsNullOrEmpty(updatedCompany.GrievanceOfficerName))
                {
                    existingCompany.GrievanceOfficerName = updatedCompany.GrievanceOfficerName;
                }
                if (!string.IsNullOrEmpty(updatedCompany.GrievanceOfficerEmail))
                {
                    existingCompany.GrievanceOfficerEmail = updatedCompany.GrievanceOfficerEmail;
                }
                if (!string.IsNullOrEmpty(updatedCompany.GrievanceOfficerPhone))
                {
                    var countryAndPhoneNumber = updatedCompany.GrievanceOfficerPhone;
                    var countryCode = countryAndPhoneNumber.Substring(0, 3);
                    var phoneNumber = countryAndPhoneNumber.Substring(4);
                    var countryDetails = await _context.CountryDetails.FirstOrDefaultAsync(cd => cd.CountryCode == countryCode);
                    if (countryDetails == null)
                    {
                        throw new InvalidOperationException($"Country code '{countryCode}' not found.");
                    }
                    var phoneNumberWithSpace = $"{countryCode} {phoneNumber}";
                    existingCompany.GrievanceOfficerPhone = phoneNumberWithSpace;
                }
                if (updatedCompany.Image != null)
                {
                    string logoLink = await UploadLogoAsync1(existingCompany.Id, updatedCompany.Image);
                    if (logoLink != null)
                    {
                        existingCompany.CompanyLogoLink = logoLink;
                    }
                }

                await _context.SaveChangesAsync();

                return true;
            }
            catch (Exception)
            {
                return false;
            }
        }


        public async Task<AddClientResult> AddClientAsync(Guid currentUserId, Ahex_CRMClientDetailsDto clientDetails)
        {
            try
            {
                //var countryAndPhoneNumber = clientDetails.PhoneNumber;
                /* var countryCode = countryAndPhoneNumber.Substring(0, 3);
                 var phoneNumber = countryAndPhoneNumber.Substring(4);*/
                //  var phoneNumberWithSpace = $"{countryCode} {phoneNumber}";


                var countryDetails = await _context.CountryDetails.FirstOrDefaultAsync(cd => cd.CountryName == clientDetails.CountryName);
                /* if (countryDetails == null)
                 {
                     throw new InvalidOperationException($"Country code '{countryCode}' not found.");
                 }*/

                if (countryDetails.CountryName != clientDetails.CountryName)
                {

                    throw new InvalidOperationException("Provided country name does not match the country name associated with the country code.");
                }

                var existingUser = await _context.Ahex_CRM_Users.FirstOrDefaultAsync(u => u.EmailAddress == clientDetails.Email);

                if (existingUser != null)
                {

                    if (existingUser.Status)
                    {

                        throw new InvalidOperationException("Client with this email is already registered.");
                    }
                    else
                    {

                        existingUser.Status = true;
                        await _context.SaveChangesAsync();
                        return new AddClientResult
                        {
                            ErrorMessage = null,
                            ClientId = existingUser.Id
                        };
                    }
                }

                //  var logoFileLink = await HandleFileUploadAsync(logoFile);

                string temporaryPassword = GenerateTemporaryPassword();
                string passwordHash = HashPassword(temporaryPassword);
                var ClientAccountId = Guid.NewGuid();
                var businessId = Guid.NewGuid();
                var newUser = new Ahex_CRM_Users
                {
                    Id = ClientAccountId,
                    CompanyId = businessId.ToString(),
                    Name = clientDetails.ClientName,
                    EmailAddress = clientDetails.Email,
                    Password = passwordHash,
                    // PhoneNumber = clientDetails.PhoneNumber,
                    Address = clientDetails.Address,
                    CountryName = clientDetails.CountryName,
                    Status = true,

                };

                var newCompany = new Ahex_CRM_BusinessDetails
                {
                    Id = businessId,
                    CompanyLegalName = clientDetails.CompanyName,
                    CompanyAddress = clientDetails.Address,
                    CountryName = clientDetails.CountryName,
                    BusinessWebsite = clientDetails.OfficialWebsite,
                    CreatedAt = DateTime.UtcNow,
                    Status = true,
                };



                _context.Ahex_CRM_Users.Add(newUser);
                _context.Ahex_CRM_BusinessDetails.Add(newCompany);
                await _context.SaveChangesAsync();
                await _emailService.SendEmailAsync(
           clientDetails.Email,
           "Temporary Password",
           $"<p>Hi {clientDetails.ClientName},</p>" +
           $"<p>Your temporary password is: {temporaryPassword}</p>",
           //    $"<p>Click <a href='{RoleConstants.link}'>here</a> to login</p>",
           _configuration["SmtpSettings:LogoUrl"],
           _requestedLink
       );
                return new AddClientResult
                {
                    ErrorMessage = null,
                    ClientId = newCompany.Id
                };
            }
            catch (Exception ex)
            {
                Console.WriteLine(ex.ToString());


                return new AddClientResult
                {
                    ErrorMessage = ex.Message,
                    ClientId = Guid.Empty
                };
            }
        }

        private string GenerateTemporaryPassword()
        {

            const string allowedChars = "abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ1234567890";
            const int passwordLength = 12;

            Random random = new Random();
            char[] passwordArray = new char[passwordLength];

            for (int i = 0; i < passwordLength; i++)
            {
                passwordArray[i] = allowedChars[random.Next(0, allowedChars.Length)];
            }

            return new string(passwordArray);
        }
        private string HashPassword(string password)
        {

            return BCrypt.Net.BCrypt.HashPassword(password);
        }

        private bool VerifyPassword(string password, string hashedPassword)
        {

            return BCrypt.Net.BCrypt.Verify(password, hashedPassword);
        }
    }
}

