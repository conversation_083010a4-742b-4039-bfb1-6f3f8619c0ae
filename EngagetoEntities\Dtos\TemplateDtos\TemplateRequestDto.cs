﻿namespace EngagetoEntities.Dtos.TemplateDtos
{
    public class TemplateRequestDto
    {
        public string TemplateName { get; set; } = default!;
        public string? CountryCode { get; set; }
        public string Contact { get; set; } = default!;
        public string? Name { get; set; }
        public string? HeaderValue { get; set; }
        public List<string>? BodyVariableValues { get; set; } = new List<string>();
        public string? UserNumber { get; set; }  // New field
    }

    public class ButtonDto
    {
        public int Index { get; set; }
        public List<string> Values { get; set; } = new();
    }
}
