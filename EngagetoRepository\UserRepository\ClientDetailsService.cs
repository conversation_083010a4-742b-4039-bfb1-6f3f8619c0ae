﻿using Amazon.S3;
using Amazon.S3.Transfer;
using EngagetoContracts.GeneralContracts;
using EngagetoContracts.MetaContracts;
using EngagetoContracts.UserContracts;
using EngagetoDapper.Data.Interfaces.IUserInterfaces.IUserServices;
using EngagetoEntities.DdContext;
using EngagetoEntities.Dtos.BusinessDetailsDtos;
using EngagetoEntities.Entities;
using EngagetoEntities.Enums;
using EngagetoEntities.Settings;
using EngagetoEntities.UserEntities.Dtos;
using EngagetoEntities.UserEntities.Models;
using Mapster;
using Microsoft.AspNetCore.Http;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Options;
using Newtonsoft.Json.Linq;
using System.Data;

namespace EngagetoRepository.UserRepository
{
    public class ClientDetailsService : IClientDetailsService
    {
        private readonly ApplicationDbContext _context;
        private readonly IEmailService _emailService;
        private readonly IWalletService _walletService;
        private readonly IConfiguration _configuration;
        private readonly IRazorPayService _razorPayService;
        private readonly IEnvironmentService _environmentService;
        private readonly AdminEmails _adminEmails;
        private readonly string _websiteLink;
        private readonly IMetaApiService _metaApiService;
        private readonly IWebhookService _webhookService;
        public ClientDetailsService(IConfiguration config, ApplicationDbContext context,
            IEmailService emailService,
            IWalletService walletService,
            IRazorPayService razorPayService,
            IEnvironmentService environmentService,
            IOptions<AdminEmails> adminEmails,
            IMetaApiService metaApiService,
            IWebhookService webhookService)
        {
            _context = context;
            _emailService = emailService;
            _walletService = walletService;
            _configuration = config;
            _razorPayService = razorPayService;
            _environmentService = environmentService;
            _adminEmails = adminEmails.Value;
            _metaApiService = metaApiService;
            _webhookService = webhookService;

            if (_environmentService.IsDevelopment)
                _websiteLink = _environmentService.GetDevWebsiteLink();
            else
                _websiteLink = _environmentService.GetProdWebsiteLink();
        }


        public async Task<bool> AddClientAsync(Guid currentUserId, Ahex_CRMClientDetailsDto clientDetails)
        {
            try
            {
                var createdByAdmin = await _context.Users
                          .Where(u => u.Id == currentUserId)
                          .FirstOrDefaultAsync();
                var createdByAdminUsername1 = await _context.Users
                    .Where(u => u.Id == currentUserId)
                    .Select(u => u.Name)
                    .FirstOrDefaultAsync();

                var roleId = await _context.Roles
                    .Where(r => r.Name == RoleConstants.Owner)
                    .Select(r => r.Id)
                    .FirstOrDefaultAsync();

                var existingUser = await _context.Users
                    .FirstOrDefaultAsync(u => u.EmailAddress == clientDetails.Email);
                if (existingUser != null)
                {
                    throw new InvalidOperationException("Client with this email is already registered.");
                }

                // string temporaryPassword = GenerateTemporaryPassword();
                string temporaryPassword = "P@$$w0rd!";
                string passwordHash = HashPassword(temporaryPassword);
                var ClientAccountId = Guid.NewGuid();
                var businessId = Guid.NewGuid();
                var newUser = new Ahex_CRM_Users
                {
                    Id = ClientAccountId,
                    CompanyId = businessId.ToString(),
                    Name = clientDetails.ClientName,
                    EmailAddress = clientDetails.Email,
                    RoleId = roleId.ToString(),
                    Password = passwordHash,
                    Address = clientDetails.Address,
                    CountryName = clientDetails.CountryName,
                    CreatedBy = createdByAdminUsername1,
                    CreationDate = DateTime.UtcNow,
                    LastOnlineTime = DateTime.UtcNow,
                    Status = true,
                };

                var userRole = new UserRole
                {
                    Id = newUser.Id,
                    RoleId = roleId
                };

                var notificationid = await GetNotificationIdsAsync();


                foreach (var id in notificationid)
                {
                    var userNotificationEntities = new UserNotificationEntities
                    {
                        NotificationId = id,
                        UserId = ClientAccountId.ToString(),
                        CompanyId = businessId.ToString(),
                        isActive = true,

                    };

                    _context.UserNotificationEntities.Add(userNotificationEntities);
                    await _context.SaveChangesAsync();
                }

                await CreateDefaultRolesForCompany(businessId.ToString());


                var newCompany = new Ahex_CRM_BusinessDetails
                {
                    Id = businessId,
                    BusinessName = clientDetails.CompanyName,
                    CompanyAddress = clientDetails.Address,
                    CountryName = clientDetails.CountryName,
                    BusinessWebsite = clientDetails.OfficialWebsite,
                    BusinessEmail = clientDetails.BusinessEmail,
                    TenantId = clientDetails.TenantId,
                    CreatedAt = DateTime.UtcNow,
                    Status = true,
                };

                var menuIds = await GetMenuIdsAsync();
                var allRolesInCompany = await _context.Roles
                    .Where(r => r.CompanyId == businessId.ToString())
                    .ToListAsync();

                foreach (var menuId in menuIds)
                {
                    foreach (var role in allRolesInCompany)
                    {
                        var menuPermissions = new MenuwithRoleRelationDetails
                        {
                            MenuId = menuId,
                            RoleId = role.Id.ToString(),
                            CompanyId = businessId.ToString(),
                            Status = true
                        };

                        _context.MenuwithRoleRelationDetails.Add(menuPermissions);
                    }
                }

                await _context.SaveChangesAsync();


                foreach (var role in allRolesInCompany)
                {
                    foreach (var accessRole in allRolesInCompany)
                    {
                        if (role.Level < accessRole.Level)
                        {
                            await AddRolePermissionAsync(role.Id.ToString(), accessRole.Id.ToString(), ClientAccountId.ToString());
                        }
                    }
                }
                await _context.SaveChangesAsync();

                _context.UserRoles.Add(userRole);
                _context.Users.Add(newUser);
                _context.BusinessDetails.Add(newCompany);
                await _context.SaveChangesAsync();


                await _emailService.SendClientEmailAsync(
                  clientDetails.Email,
                  "Temporary Password",
                  clientDetails.ClientName,
                  clientDetails.Email,
                  temporaryPassword,
                  _configuration["SmtpSettings:LogoUrl"],
                  _websiteLink
              );

                string notificationName = "Get notified when Client  is created or updated";
                int? notificationId = await GetNotificationIdAsync(notificationName);

                var userNotification = await _context.UserNotificationEntities
                                                    .FirstOrDefaultAsync(un => un.UserId.ToLower() == createdByAdmin.Id.ToString().ToLower() &&
                                                                                 un.CompanyId.ToLower() == createdByAdmin.CompanyId.ToLower() &&
                                                                                 un.isActive == true &&
                                                                                 un.NotificationId == notificationId);
                if (userNotification != null)
                {
                    await _emailService.SendEmailAsync(
                       createdByAdmin.EmailAddress,
                       "New Client Added",
                       $"<p>Hi {createdByAdmin.Name},</p>" +
                       $"<p>A new client, {clientDetails.ClientName}, has been successfully added to your company.</p>",
                       _configuration["SmtpSettings:LogoUrl"],
                       _websiteLink
                    );
                }
                //Free Subscription
                await _razorPayService.FreePlanSubscriptionAsync(businessId.ToString(), currentUserId);
                // Create a wallet account for company
                await _walletService.CreateWalletAsync(new UserWalletEntity()
                {
                    UserId = ClientAccountId,
                    CompanyId = businessId.ToString(),
                    Balance = 100,
                    ExpectedWalletBallance = 100,
                    Currency = Currency.INR.ToString(),
                    CreatedAt = DateTime.Now,
                    CreatedBy = ClientAccountId,
                    UpdatedAt = DateTime.Now,
                    UpdatedBy = ClientAccountId,
                    IsDeleted = false,
                });
                return true;
            }
            catch (Exception ex)
            {
                Console.WriteLine(ex.ToString());
                return false;
            }
        }

        private async Task CreateDefaultRolesForCompany(string companyId)
        {
            var roles = new List<Role>
           {
                new Role
                {
                    Id = Guid.Parse("55AA1BA5-1507-47E2-888B-89D80CD41906"),
                    Name = "Owner",
                    Level = 1,
                    CompanyId = companyId
                },
                new Role
                {
                    Id = Guid.Parse("45AA1BA5-1507-47E2-888B-89D80CD41906"),
                    Name = "Admin",
                    Level = 2,
                    CompanyId = companyId
                },
                new Role
                {
                    Id = Guid.Parse("65AA1BA5-1507-47E2-888B-89D80CD41906"),
                    Name = "Teammate",
                    Level = 3,
                    CompanyId = companyId
                }
            };
            _context.Roles.AddRange(roles);
            await _context.SaveChangesAsync();
        }

        private async Task AddRolePermissionAsync(string roleId, string accessToRoleId, string currentUserCompanyId)
        {
            var rolePermission = new RolePermissions
            {
                RoleId = roleId,
                AccessToRoleId = accessToRoleId,
                CompanyId = currentUserCompanyId,
            };

            _context.RolePermissions.Add(rolePermission);
        }
        public async Task<List<Guid>> GetAllRoleIdsAsync()
        {

            var roleIds = await _context.Roles
                .Select(role => role.Id)
                .ToListAsync();

            return roleIds;
        }
        public async Task<IEnumerable<Guid>> GetPermissionIdsAsync()
        {
            try
            {

                var permissions = await _context.Permissions.ToListAsync();


                var permissionIds = permissions.Select(p => p.Id).ToList();

                return permissionIds;
            }
            catch (Exception ex)
            {

                Console.WriteLine($"Error fetching permission IDs: {ex.Message}");
                return Enumerable.Empty<Guid>();
            }
        }

        public async Task<IEnumerable<int>> GetMenuIdsAsync()
        {
            try
            {

                var permissions = await _context.MenuDetails.ToListAsync();


                var permissionIds = permissions.Select(p => p.MenuId).ToList();

                return permissionIds;
            }
            catch (Exception ex)
            {

                Console.WriteLine($"Error fetching permission IDs: {ex.Message}");
                return Enumerable.Empty<int>();
            }
        }

        public async Task UpdateMetaAccountByWebhookAsync(string waAccountId, long messageLimit, string businessStatus, string tier, string? displayNumber)
        {
            try
            {
                await SendMeatBusinessInfoOnEmailAsync(waAccountId, messageLimit, businessStatus, tier);
                var query = _context.BusinessDetailsMetas.Where(i => i.WhatsAppBusinessAccountID == waAccountId);

                if (!string.IsNullOrEmpty(displayNumber))
                {
                    var normalizedDisplayNumber = displayNumber.Replace("+", "").Replace(" ", "").Trim();

                    query = query.Where(x =>
                        (x.DisplayPhoneNumber ?? "")
                            .Replace("+", "")
                            .Replace(" ", "")
                            .Trim() == normalizedDisplayNumber
                    );
                }

                var account = await query.FirstOrDefaultAsync();
                if (account != null)
                {
                    if (messageLimit > 0)
                    {
                        account.MessageLimit = messageLimit;
                    }
                    if (!string.IsNullOrEmpty(businessStatus) && !string.IsNullOrEmpty(tier))
                    {
                        account.BusinessStatus = businessStatus;
                        account.Tier = tier;
                    }
                    _context.BusinessDetailsMetas.Update(account);
                    await _context.SaveChangesAsync();
                }
            }
            catch (Exception ex)
            {
                Console.WriteLine($"An error occurred: {ex.Message}");
            }
        }

        public async Task<BusinessMetaDetailsDto> GetClientDetailsAsync(string businessId)
        {
            try
            {
                var account = await _context.BusinessDetailsMetas.FirstOrDefaultAsync(i => i.BusinessId == businessId);
                if (account != null)
                {
                    var metaDetails = account.Adapt<BusinessMetaDetailsDto>();
                    var (success, res) = await _metaApiService.GetBusinessDetailsAsync(businessId);
                    var analytics = await _metaApiService.GetConversationAnalyticsAsync(businessId);
                    if (success)
                    {
                        var jsonResponse = res.ToString();
                        var jsonObject = JObject.Parse(jsonResponse);
                        var qualityRating = jsonObject["phone_numbers"]?["data"]?.FirstOrDefault(x => x?["id"]?.ToString() == account.PhoneNumberID)?["quality_rating"]?.ToString() ?? string.Empty;
                        var rating = GetQualityScoreEnum(qualityRating);
                        if (account.QualityRating != rating)
                        {
                            account.QualityRating = rating;
                            _context.Update(account);
                            await _context.SaveChangesAsync(true);
                        }
                        metaDetails.QualityRating = rating;
                    }
                    if (analytics.Success)
                    {
                        var analyticsDetails = analytics.Result.ToObject<ConversationAnalyticsResponseDto>();
                        if (analyticsDetails != null && analyticsDetails?.ConversationAnalytics?.Data?.Count > 0
                            && (analyticsDetails?.ConversationAnalytics?.Data?.FirstOrDefault()?.DataPoints?.Any() ?? false))
                        {
                            var dataPoints = analyticsDetails?.ConversationAnalytics?.Data.FirstOrDefault()?.DataPoints;
                            var conversation = dataPoints?.Where(x => x.Cost > 0).Sum(x => x.Conversation);
                            var cost = dataPoints?.Sum(x => x.Cost);
                            metaDetails.ConversationCount = conversation;
                            metaDetails.Cost = cost;
                        }
                        else
                        {
                            metaDetails.ConversationCount = 0;
                            metaDetails.Cost = 0;
                        }
                    }
                    return metaDetails;
                }
                throw new Exception("Not found meat account id.");
            }
            catch (Exception ex)
            {
                throw;
            }
        }


        private string GenerateTemporaryPassword()
        {

            const string allowedChars = "abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ1234567890";
            const int passwordLength = 12;

            Random random = new Random();
            char[] passwordArray = new char[passwordLength];

            for (int i = 0; i < passwordLength; i++)
            {
                passwordArray[i] = allowedChars[random.Next(0, allowedChars.Length)];
            }

            return new string(passwordArray);
        }
        private string HashPassword(string password)
        {

            return BCrypt.Net.BCrypt.HashPassword(password);
        }

        private bool VerifyPassword(string password, string hashedPassword)
        {

            return BCrypt.Net.BCrypt.Verify(password, hashedPassword);
        }
        public async Task<bool> UpdateClientAsync(Guid currentUserId, Guid clientId, Ahex_CRMClientDetailsUpdateDto updateClientDetails)
        {
            try
            {
                var existingClient = await _context.BusinessDetails.FindAsync(clientId);

                if (existingClient == null)
                {
                    return false;
                }

                existingClient.BusinessName = string.IsNullOrEmpty(updateClientDetails.CompanyName) ? string.Empty : updateClientDetails.CompanyName;
                existingClient.TenantId = updateClientDetails.TenantId;

                existingClient.BusinessWebsite = string.IsNullOrEmpty(updateClientDetails.OfficialWebsite) ? string.Empty : updateClientDetails.OfficialWebsite;

                if (Enum.TryParse(updateClientDetails.BusinessCategory.ToString(), out BusinessCategory category))
                {
                    existingClient.BusinessCategory = category.ToString();
                }
                else
                {
                    existingClient.BusinessCategory = string.Empty;
                }
                existingClient.BusinessEmail = string.IsNullOrEmpty(updateClientDetails.BusinessEmail) ? string.Empty : updateClientDetails.BusinessEmail;

                if (!string.IsNullOrEmpty(updateClientDetails.CountryName))
                {
                    var countryDetails = await _context.CountryDetails.FirstOrDefaultAsync(cd => cd.CountryName == updateClientDetails.CountryName);
                    if (countryDetails == null)
                    {
                        throw new InvalidOperationException($"Country name '{updateClientDetails.CountryName}' not found.");
                    }
                    existingClient.CountryName = updateClientDetails.CountryName;
                }
                else
                {
                    existingClient.CountryName = string.Empty;
                }

                if (!string.IsNullOrEmpty(updateClientDetails.Address))
                {
                    existingClient.CompanyAddress = updateClientDetails.Address;

                }
                else
                {
                    existingClient.CompanyAddress = string.Empty;

                }


                if (updateClientDetails.CompanyLogoLink != null)
                {
                    string logoLink = await UploadLogoAsync(existingClient.Id, updateClientDetails.CompanyLogoLink);
                    if (logoLink != null)
                    {
                        existingClient.CompanyLogoLink = logoLink;
                    }
                }

                await _context.SaveChangesAsync();
                var adminUser = await _context.Users.FindAsync(currentUserId);
                if (adminUser != null)
                {
                    string notificationName = "Get notified when Client  is created or updated";
                    int? notificationId = await GetNotificationIdAsync(notificationName);

                    var userNotification = await _context.UserNotificationEntities
                                                        .FirstOrDefaultAsync(un => un.UserId.ToLower() == adminUser.Id.ToString().ToLower() &&
                                                                                     un.CompanyId.ToLower() == adminUser.CompanyId.ToLower() &&
                                                                                     un.isActive == true &&
                                                                                     un.NotificationId == notificationId);
                    if (userNotification != null)
                    {
                        await _emailService.SendEmailAsync(
                        adminUser.EmailAddress,
                        "Client Details Updated",
                        $"<p>Hi {adminUser.Name},</p>" +
                        $"<p>You have successfully updated the details of the client {existingClient.CompanyLegalName}.</p>",
                        _configuration["SmtpSettings:LogoUrl"],
                        _websiteLink
                    );
                    }
                }
                return true;
            }
            catch (Exception)
            {
                return false;
            }
        }

        public async Task<bool> DeleteClientAsync(Guid currentUserId, Guid clientId)
        {
            try
            {
                var businessDetailsToDelete = await _context.BusinessDetails.FirstOrDefaultAsync(b => b.Id == clientId);
                if (businessDetailsToDelete != null)
                {
                    businessDetailsToDelete.Status = false;
                }

                await _context.SaveChangesAsync();

                return true;
            }
            catch (Exception)
            {
                return false;
            }
        }

        public async Task<object> GetClientByIdAsync(Guid currentUserId, Guid clientId)
        {
            try
            {
                var userDetails = await _context.BusinessDetails
                    .Where(user => user.Id.ToString().ToLower() == clientId.ToString().ToLower())
                    .FirstOrDefaultAsync();

                var CurrentPlan = await _context.Subscriptions.ToListAsync();
                var PlanDetails = await _context.PlanEntities.Where(m => m.Status).ToListAsync();
                return new
                {

                    userDetails.Id,
                    userDetails.BusinessName,
                    userDetails.BusinessCategory,
                    userDetails.BusinessWebsite,
                    userDetails.CompanyLogoLink,
                    userDetails.BusinessEmail,
                    userDetails.PhoneNumber,
                    userDetails.CountryCode,
                    userDetails.CountryName,
                    CreationDate = userDetails.CreatedAt,
                    Address = userDetails.CompanyAddress,



                    CurrentPlan = PlanDetails.FirstOrDefault(p => p.Id == (CurrentPlan.FirstOrDefault(a => a.CompanyId.ToLower() == userDetails.Id.ToString().ToLower())?.PlanId))?.PlanName

                };
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Error fetching client with users: {ex.Message}");
                return null;
            }
        }

        public async Task<IEnumerable<object>> GetAllClientsAsync(Guid currentUserId, string searchQuery, bool includeInactive, string sortBy, bool isSortAscending)
        {
            try
            {
                var client = await _context.Users.FindAsync(currentUserId);
                var query = _context.BusinessDetails
                    .ToList();

                var CurrentPlan = await _context.Subscriptions.ToListAsync();
                var userWalletEntities = await _context.UserWalletEntities.ToListAsync();
                var PlanDetails = await _context.PlanEntities.Where(m => m.Status).ToListAsync();


                foreach (var user in query)
                {
                    var userdata = _context.Users.Where(x => x.CompanyId == user.Id.ToString()).ToList();
                    user.users = userdata;
                }


                if (!string.IsNullOrEmpty(searchQuery))
                {
                    searchQuery = searchQuery.ToLower();

                    if (Guid.TryParse(searchQuery, out Guid clientId))
                    {
                        query = query.Where(client => client.Id == clientId).ToList();
                    }
                    else if (searchQuery.Length == 3 && searchQuery.All(char.IsDigit))
                    {
                        query = query.Where(company => company.PhoneNumber != null && company.PhoneNumber.StartsWith(searchQuery)).ToList();
                    }
                    else if (searchQuery.Contains("@"))
                    {
                        query = query.Where(company => company.BusinessEmail != null && company.BusinessEmail.ToLower().Contains(searchQuery)).ToList();
                    }
                    else
                    {
                        query = await _context.BusinessDetails.Where(x => x.BusinessName != null && x.BusinessName.ToLower().Contains(searchQuery)).ToListAsync();
                    }


                }

                if (!includeInactive)
                {
                    query = query.Where(client => client.Status == includeInactive).ToList();
                }


                switch (sortBy?.ToLower())
                {
                    case "businessname":
                        query = isSortAscending ? query.OrderBy(company => company.BusinessName).ToList() : query.OrderByDescending(company => company.BusinessName).ToList();
                        break;

                    default:
                        query = query.OrderBy(company => company.BusinessName).ToList();
                        break;
                }


                return query.Select(m => new
                {
                    Id = m.Id,
                    BusinessName = m.BusinessName,
                    BusinessCategory = m.BusinessCategory,
                    BusinessWebsite = m.BusinessWebsite,
                    BusinessEmail = m.BusinessEmail,
                    CountryCode = m.CountryCode,
                    PhoneNumber = m.PhoneNumber,
                    CountryName = m.CountryName,
                    GSTNumber = m.GSTNumber,
                    CompanyAddress = m.CompanyAddress,
                    Description = m.Description,
                    CompanyLegalName = m.CompanyLegalName,
                    CompanyType = m.CompanyType,
                    CompanyRegistered = m.CompanyRegistered,
                    CustomerCareEmail = m.CustomerCareEmail,
                    CustomerCareCountryCode = m.CustomerCareCountryCode,
                    CustomerCarePhone = m.CustomerCarePhone,
                    GrievanceOfficerName = m.GrievanceOfficerName,
                    GrievanceOfficerEmail = m.GrievanceOfficerEmail,
                    GrievanceOfficerCountryCode = m.GrievanceOfficerCountryCode,
                    GrievanceOfficerPhone = m.GrievanceOfficerPhone,
                    CompanyLogoLink = m.CompanyLogoLink,
                    CreatedAt = m.CreatedAt,
                    Status = m.Status,
                    Wallet = userWalletEntities.FirstOrDefault(x => x.CompanyId == m.Id.ToString()),
                    CurrentPlan = PlanDetails.FirstOrDefault(p => p.Id == (CurrentPlan.FirstOrDefault(a => a.CompanyId.ToLower() == m.Id.ToString().ToLower())?.PlanId))?.PlanName,
                    Users = m.users

                }).ToList();
            }
            catch (Exception ex)
            {
                throw new Exception("Error occurred while fetching clients.", ex);
            }
        }



        public async Task<string?> UploadLogoAsync(Guid clientId, IFormFile? file)
        {
            var manageClient = await _context.BusinessDetails.FindAsync(clientId);

            if (manageClient == null)
            {

                return null;
            }
            if (file == null)
            {
                return null;
            }

            var fileLink = await HandleFileUploadAsync(file);

            manageClient.CompanyLogoLink = fileLink;

            await _context.SaveChangesAsync();

            return fileLink;
        }
        private async Task<string?> HandleFileUploadAsync(IFormFile? file)
        {
            if (file == null)
            {
                return null;
            }

            try
            {
                var accessKey = _configuration["Aws:AccessKey"];
                var secretKey = _configuration["Aws:SecretKey"];
                var bucketName = _configuration["Aws:BucketName"];
                var regionString = _configuration["Aws:Region"];
                var region = Amazon.RegionEndpoint.GetBySystemName(regionString);

                using (var client = new AmazonS3Client(accessKey, secretKey, region))
                {
                    var key = $"{Guid.NewGuid()}.{file.FileName.Split('.')[1]}";

                    using (var newMemoryStream = new MemoryStream())
                    {
                        await file.CopyToAsync(newMemoryStream);

                        var uploadRequest = new TransferUtilityUploadRequest
                        {
                            InputStream = newMemoryStream,
                            Key = key,
                            BucketName = bucketName,
                            CannedACL = S3CannedACL.PublicRead
                        };

                        using (var fileTransferUtility = new TransferUtility(client))
                        {
                            await fileTransferUtility.UploadAsync(uploadRequest);
                        }
                    }

                    var fileLink = $"https://{bucketName}.s3.{region.SystemName}.amazonaws.com/{key}";

                    return fileLink;
                }
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Error uploading file to S3: {ex.Message}");
                throw;
            }
        }
        public async Task<int?> GetNotificationIdAsync(string notificationName)
        {

            var notification = await _context.NotificationEntities
                .FirstOrDefaultAsync(n => n.Name == notificationName);


            return notification?.Id;
        }
        public async Task<IEnumerable<int>> GetNotificationIdsAsync()
        {
            try
            {
                var notifications = await _context.NotificationEntities.ToListAsync();
                var notificationIds = notifications.Select(p => p.Id).ToList();
                return notificationIds;
            }
            catch (Exception ex)
            {

                Console.WriteLine($"Error fetching permission IDs: {ex.Message}");
                return Enumerable.Empty<int>();
            }
        }

        public async Task<bool> AddClientAnonymousAsync(Ahex_CRMClientDetailsDto clientDetails)
        {

            try
            {


                var roleId = await _context.Roles
                    .Where(r => r.Name == RoleConstants.Owner)
                    .Select(r => r.Id)
                    .FirstOrDefaultAsync();


                var existingUser = await _context.Users
                    .FirstOrDefaultAsync(u => u.EmailAddress == clientDetails.Email);
                if (existingUser != null)
                {
                    throw new InvalidOperationException("Client with this email is already registered.");
                }

                // string temporaryPassword = GenerateTemporaryPassword();
                string temporaryPassword = "P@$$w0rd!";
                string passwordHash = HashPassword(temporaryPassword);
                var ClientAccountId = Guid.NewGuid();
                var businessId = Guid.NewGuid();
                var newUser = new Ahex_CRM_Users
                {
                    Id = ClientAccountId,
                    CompanyId = businessId.ToString(),
                    Name = clientDetails.ClientName,
                    EmailAddress = clientDetails.Email,
                    RoleId = roleId.ToString(),
                    Password = passwordHash,
                    Address = clientDetails.Address,
                    CountryName = clientDetails.CountryName,
                    CreatedBy = "",
                    CreationDate = DateTime.UtcNow,
                    LastOnlineTime = DateTime.UtcNow,
                    Status = true,
                };

                var userRole = new UserRole
                {
                    Id = newUser.Id,
                    RoleId = roleId
                };

                var notificationid = await GetNotificationIdsAsync();


                foreach (var id in notificationid)
                {
                    var userNotificationEntities = new UserNotificationEntities
                    {
                        NotificationId = id,
                        UserId = ClientAccountId.ToString(),
                        CompanyId = businessId.ToString(),
                        isActive = true,

                    };

                    _context.UserNotificationEntities.Add(userNotificationEntities);
                    await _context.SaveChangesAsync();
                }

                await CreateDefaultRolesForCompany(businessId.ToString());


                var newCompany = new Ahex_CRM_BusinessDetails
                {
                    Id = businessId,
                    BusinessName = clientDetails.CompanyName,
                    CompanyAddress = clientDetails.Address,
                    CountryName = clientDetails.CountryName,
                    BusinessWebsite = clientDetails.OfficialWebsite,
                    BusinessEmail = clientDetails.BusinessEmail,
                    TenantId = clientDetails.TenantId,
                    CreatedAt = DateTime.UtcNow,
                    Status = true,
                };

                var menuIds = await GetMenuIdsAsync();
                var allRolesInCompany = await _context.Roles
                    .Where(r => r.CompanyId == businessId.ToString())
                    .ToListAsync();

                foreach (var menuId in menuIds)
                {
                    foreach (var role in allRolesInCompany)
                    {
                        var menuPermissions = new MenuwithRoleRelationDetails
                        {
                            MenuId = menuId,
                            RoleId = role.Id.ToString(),
                            CompanyId = businessId.ToString(),
                            Status = true
                        };

                        _context.MenuwithRoleRelationDetails.Add(menuPermissions);
                    }
                }

                await _context.SaveChangesAsync();


                foreach (var role in allRolesInCompany)
                {
                    foreach (var accessRole in allRolesInCompany)
                    {
                        if (role.Level < accessRole.Level)
                        {
                            await AddRolePermissionAsync(role.Id.ToString(), accessRole.Id.ToString(), ClientAccountId.ToString());
                        }
                    }
                }
                await _context.SaveChangesAsync();

                _context.UserRoles.Add(userRole);
                _context.Users.Add(newUser);
                _context.BusinessDetails.Add(newCompany);
                await _context.SaveChangesAsync();


                await _emailService.SendClientEmailAsync(
                  clientDetails.Email,
                  "Temporary Password",
                  clientDetails.ClientName,
                  clientDetails.Email,
                  temporaryPassword,
                  _configuration["SmtpSettings:LogoUrl"],
                  _websiteLink
              );

                string notificationName = "Get notified when Client  is created or updated";
                int? notificationId = await GetNotificationIdAsync(notificationName);


                foreach (var email in _adminEmails.Emails)
                {
                    await _emailService.SendEmailAsync(
                       email,
                       "New Client Added",
                       $"<p>Hello Team,</p>" +
                       $"<p>A new client, {clientDetails.ClientName}, has been successfully added to your company.</p>",
                       _configuration["SmtpSettings:LogoUrl"],
                       _websiteLink
                    );
                }

                //Free Subscription
                await _razorPayService.FreePlanSubscriptionAsync(businessId.ToString(), Guid.NewGuid());

                await _walletService.CreateWalletAsync(new UserWalletEntity()
                {
                    UserId = ClientAccountId,
                    CompanyId = businessId.ToString(),
                    Balance = 100,
                    ExpectedWalletBallance = 100,
                    Currency = Currency.INR.ToString(),
                    CreatedAt = DateTime.Now,
                    CreatedBy = ClientAccountId,
                    UpdatedAt = DateTime.Now,
                    UpdatedBy = ClientAccountId,
                    IsDeleted = false,
                });
                return true;
            }
            catch (Exception ex)
            {
                Console.WriteLine(ex.ToString());
                return false;
            }
        }
        private QualityScore GetQualityScoreEnum(string score)
        {
            return score.ToLower() switch
            {
                "green" => QualityScore.GREEN,
                "yellow" => QualityScore.YELLOW,
                "red" => QualityScore.RED,
                _ => QualityScore.UNKNOWN,
            };
        }
        private async Task SendMeatBusinessInfoOnEmailAsync(string waAccountId, long messageLimit, string status, string tier)
        {
            try
            {
                await _emailService.SendEmailViaUtility
                    (
                        "Update WA Phone Number Quality",
                        $@"Hello Team,

                        Updates have been received for the WhatsApp Account ID: {waAccountId}.
                        Please find the details below:

                        - **Message Limit**: {messageLimit}
                        - **Business Status**: {status}
                        - **Tier**: {tier}

                        Thank you for your attention. Please review the status at your earliest convenience.

                        Best regards,  
                        Engageto",
                        null,
                        _adminEmails.Emails,
                        null,
                        null
                    );
            }
            catch (Exception ex) { }
        }

        public async Task<List<TenantDtos>> GetTenantAccountsAsync(string tenant)
        {
            var result = await _context.BusinessDetails
                .Join(_context.UserWalletEntities,
                      b => b.Id.ToString(),
                      u => u.CompanyId,
                      (b, u) => new { b, u })
                .Join(_context.BusinessDetailsMetas,
                      bu => bu.b.Id.ToString(),
                      m => m.BusinessId,
                      (bu, m) => new { bu.b, bu.u, m })
                .Where(x => x.b.TenantId == tenant)
                .Select(x => new TenantDtos
                {
                    BusinessId = x.b.Id,
                    TenantId = x.b.TenantId,
                    BusinessName = x.b.BusinessName ?? string.Empty,
                    BusinessCategory = x.b.BusinessCategory ?? string.Empty,
                    Status = x.b.Status,
                    CompanyLogoLink = x.b.CompanyLogoLink ?? string.Empty,
                    CreatedAt = x.b.CreatedAt,
                    Balance = x.u.Balance,
                    PhoneNumber = (x.b.CountryCode ?? string.Empty) + (x.b.PhoneNumber ?? string.Empty),
                    QualityRating = x.m.QualityRating,
                    MessageLimit = x.m.MessageLimit ?? 0,
                    Tier = x.m.Tier ?? string.Empty,
                    Cost = 0,
                    ConversationCount = 0
                })
                .ToListAsync();

            var businessIds = result.Select(i => i.BusinessId.ToString()).ToArray();

            if (businessIds != null)
            {
                var analyticsResults = new Dictionary<string, ConversationAnalyticsResponseDto>();

                foreach (var businessId in businessIds)
                {
                    if (businessId != null)
                    {
                        var analytics = await _metaApiService.GetConversationAnalyticsAsync(businessId);
                        if (analytics.Success)
                        {
                            var analyticsDetails = analytics.Result?.ToObject<ConversationAnalyticsResponseDto>();
                            if (analyticsDetails != null)
                            {
                                analyticsResults[businessId] = analyticsDetails;
                            }
                        }
                    }
                }
                foreach (var tenantDto in result)
                {
                    if (analyticsResults.TryGetValue(tenantDto.BusinessId.ToString() ?? string.Empty, out var analyticsDetails) &&
                        analyticsDetails?.ConversationAnalytics?.Data?.Count > 0 &&
                        analyticsDetails?.ConversationAnalytics?.Data.FirstOrDefault()?.DataPoints?.Any() == true)
                    {
                        var dataPoints = analyticsDetails.ConversationAnalytics.Data.FirstOrDefault()?.DataPoints;
                        tenantDto.Cost = dataPoints?.Sum(x => x.Cost) ?? 0;
                        tenantDto.ConversationCount = dataPoints?.Sum(x => x.Conversation) ?? 0;
                    }
                    else
                    {
                        tenantDto.Cost = 0;
                        tenantDto.ConversationCount = 0;
                    }
                }
            }
            return result;
        }
    }
}
