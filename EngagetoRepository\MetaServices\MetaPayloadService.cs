﻿using EngagetoContracts.MetaContracts;
using EngagetoEntities.Dtos.MetaDto;
using EngagetoEntities.Dtos.TemplateDtos;
using EngagetoEntities.Entities;
using EngagetoEntities.Enums;
using EngagetoEntities.Utilities;
using Newtonsoft.Json.Linq;
using System.Text.RegularExpressions;

namespace EngagetoRepository.MetaServices
{
    public class MetaPayloadService : IMetaPayloadService
    {
        public void AddBodyComponent(ref JArray components, string? body, List<string>? bodyVariableValues)
        {
            var bodyValueCount = Regex.Matches(body ?? "", @"\{\{(\d+)\}\}")?.Count ?? 0;

            if (bodyValueCount > 0 && bodyValueCount != (bodyVariableValues?.Count ?? 0))
                throw new Exception("Body value count mismatch");

            if (bodyValueCount > 0)
            {
                var bodyComponent = JObject.Parse(MetaPayloadDto.BodyComponentTemplate);
                var bodyParameters = bodyComponent["parameters"] as JArray ?? new JArray();

                foreach (var bodyValue in bodyVariableValues ?? new List<string>())
                {
                    var bodyParameter = JObject.Parse(MetaPayloadDto.CreateBodyParameter(bodyValue));
                    bodyParameters.Add(bodyParameter);
                }
                components.Add(bodyComponent);
            }
        }
        public void AddBodyComponent(ref JArray components, List<string>? bodyVariableValues)
        {
            if (bodyVariableValues?.Any() == true)
            {
                var bodyComponent = JObject.Parse(MetaPayloadDto.BodyComponentTemplate);
                var bodyParameters = bodyComponent["parameters"] as JArray ?? new JArray();

                foreach (var bodyValue in bodyVariableValues ?? new List<string>())
                {
                    var bodyParameter = JObject.Parse(MetaPayloadDto.CreateBodyParameter(bodyValue));
                    bodyParameters.Add(bodyParameter);
                }
                components.Add(bodyComponent);
            }
        }
        public string AddButtonComponent(ref JArray components, List<Button>? buttons, List<ButtonDto>? buttonVeriableValues)
        {
            var nonParameterButtonUrls = buttons?
                .Where(x => x.ButtonType == "URL" && !Regex.IsMatch(x.ButtonValue ?? "", @"\{\{(\d+)\}\}"))
                .Select(x => x.ButtonValue)
                .ToList() ?? new();

            var redirectUrls = string.Join(",", nonParameterButtonUrls);

            var urlButtons = buttons?
                .Where(x => x.ButtonType == "URL" && Regex.IsMatch(x.ButtonValue ?? "", @"\{\{(\d+)\}\}"))
                .ToList() ?? new();

            if (urlButtons.Any())
            {
                int index = (buttons?.Any(x => x.ButtonType == "PHONE_NUMBER") ?? false) ? 0 : 1;
                foreach (var button in urlButtons)
                {
                    var buttonComponent = JObject.Parse(MetaPayloadDto.CreateButtonComponent(button.ButtonType?.ToLowerInvariant() ?? "", index)) ?? new JObject();
                    var buttonParameters = buttonComponent["parameters"] as JArray ?? new JArray();

                    var buttonMatches = Regex.Matches(button.ButtonValue ?? "", @"\{\{(\d+)\}\}");
                    var buttonValue = buttonVeriableValues?.FirstOrDefault(x => x.Index == index);

                    if (buttonMatches.Count != (buttonValue?.Values.Count ?? 0))
                        throw new Exception($"Button value count mismatch for index {index}");

                    redirectUrls += (!string.IsNullOrEmpty(redirectUrls) ? "," : "") +
                                    ReplaceMatches(button.ButtonValue ?? "", buttonMatches, (buttonValue?.Values ?? new()));

                    foreach (var value in buttonValue?.Values ?? new List<string>())
                    {
                        var buttonParameter = MetaPayloadDto.CreateButtonParameter(value);
                        buttonParameters.Add(buttonParameter);
                    }

                    components.Add(buttonComponent);
                    index++;
                }
            }

            return redirectUrls;
        }

        public void AddHeaderComponent(ref JArray components, string? header, MediaType mediatype, string? headerValue)
        {
            if (mediatype != MediaType.NONE)
            {
                var headerComponent = JObject.Parse(MetaPayloadDto.HeaderComponentTemplate);
                var headerParameters = headerComponent["parameters"] as JArray ?? new JArray();

                var headerTextCount = Regex.Matches(header ?? "", @"\{\{(\d+)\}\}")?.Count ?? 0;
                if (mediatype == MediaType.TEXT && headerTextCount > 0)
                {
                    if (string.IsNullOrEmpty(headerValue))
                        throw new Exception("Header values can't be empty");

                    var headerParameter = MetaPayloadDto.CreateTextHeaderParameter(headerValue);
                    headerParameters.Add(JObject.Parse(headerParameter));
                }
                else if(mediatype != MediaType.TEXT)
                {
                    AddMediaHeaderParameters(headerValue, mediatype, headerParameters);
                }
                components.Add(headerComponent);
            }

        }
        public void AddHeaderComponent(ref JArray components,MediaType mediatype, string? headerValue)
        {
            if (mediatype != MediaType.NONE && !string.IsNullOrEmpty(headerValue))
            {
                var headerComponent = JObject.Parse(MetaPayloadDto.HeaderComponentTemplate);
                var headerParameters = headerComponent["parameters"] as JArray ?? new JArray();
                if (mediatype == MediaType.TEXT)
                {
                    var headerParameter = MetaPayloadDto.CreateTextHeaderParameter(headerValue);
                    headerParameters.Add(JObject.Parse(headerParameter));
                }
                else if (mediatype != MediaType.TEXT)
                {
                    AddMediaHeaderParameters(headerValue, mediatype, headerParameters);
                }
                components.Add(headerComponent);
            }
        }
        public JArray GetComponents()
        {
            return new JArray();
        }

        public JObject InitializeBasePayload(string contact, string templateName, string languageCode)
        {
            var basePayloadTemplate = MetaPayloadDto.CreateBasePayload(contact, templateName, languageCode);
            JObject jsonObject = JObject.Parse(basePayloadTemplate);
            return jsonObject;
        }
        public async Task<JObject> AddHeaderForCreateComponentRequest(CreateTemplateDto model, string Headerhandler)
        {
            switch (model.MediaType)
            {
                case MediaType.TEXT:
                    int headerCount = StringHelper.GetVariableCount(model.Header ?? string.Empty);
                    if (headerCount == 0)
                        return JObject.Parse(CreateMetaPayloadJsonDto
                                .CreateTextHeaderPayload(model?.Header ?? string.Empty));
                    else
                        return JObject.Parse(CreateMetaPayloadJsonDto
                                .CreateHeaderWithVariablesPayload(model?.Header ?? string.Empty, new List<string> { "1" }));

                case MediaType.IMAGE:
                    return JObject.Parse(CreateMetaPayloadJsonDto
                            .CreateMediaHeaderPayload(model.MediaType.ToString() ?? string.Empty, new List<string> { Headerhandler }));

                case MediaType.VIDEO:
                    return JObject.Parse(CreateMetaPayloadJsonDto
                            .CreateMediaHeaderPayload(model.MediaType.ToString() ?? string.Empty, new List<string> { Headerhandler }));

                case MediaType.DOCUMENT:
                    return JObject.Parse(CreateMetaPayloadJsonDto
                            .CreateMediaHeaderPayload(model.MediaType.ToString() ?? string.Empty, new List<string> { Headerhandler }));
            }
            return new JObject();
        }

        public List<Button> AddButtonForCreateComponetRequest(CreateTemplateDto model, ref JArray components)
        {
            List<Button> buttons = new List<Button>();
            var buttonJsonArray = new JArray();
            if (!string.IsNullOrEmpty(model.PhoneNumber))
            {
                var phoneNumberButton = CreateMetaPayloadJsonDto
                    .CreatePhoneNumberButton(model.CallButtonName, model.CountryCode + model.PhoneNumber);
                CreateMetaPayloadJsonDto.AddButtons(phoneNumberButton, ref buttonJsonArray);
                buttons.Add(new Button()
                {
                    ButtonName = model.CallButtonName,
                    ButtonType = "PHONE_NUMBER",
                    ButtonValue = (model.CountryCode?.Substring(1) ?? "") + model.PhoneNumber
                });
            }
            if (model.RedirectUrl?.Count > 0)
            {
                for (int i = 0; i < model.RedirectUrl?.Count; i++)
                {
                    var urlButton = CreateMetaPayloadJsonDto
                    .CreateUrlButton(model.UrlButtonName[i], model.RedirectUrl[i]);
                    CreateMetaPayloadJsonDto.AddButtons(urlButton, ref buttonJsonArray);
                    buttons.Add(new Button()
                    {
                        ButtonName = model.UrlButtonName[i],
                        ButtonType = "URL",
                        ButtonValue = model.RedirectUrl[i]
                    });
                }
            }
            if (model.QuickReply?.Count > 0)
            {
                foreach (var button in model.QuickReply)
                {
                    var quickReplyButton = CreateMetaPayloadJsonDto
                    .CreateQuickReply(button);
                    CreateMetaPayloadJsonDto.AddButtons(quickReplyButton, ref buttonJsonArray);
                    buttons.Add(new Button()
                    {
                        ButtonName = null,
                        ButtonType = "QUICK_REPLY",
                        ButtonValue = button
                    });
                }
            }
            if (buttonJsonArray.Count > 0)
                components.Add(JObject.Parse(CreateMetaPayloadJsonDto.CreateButtonPayload(buttonJsonArray)));
            return buttons;
        }

        public void AddFooterForCreateComponentRequest(CreateTemplateDto model, ref JArray components)
        {
            if (!string.IsNullOrEmpty(model.Footer))
            {
                var jobject = JObject.Parse(CreateMetaPayloadJsonDto.CreateFooter(model.Footer));
                components.Add(jobject);
            }
        }
        public JObject AddBodyComponentRequest(string text)
        {

            var bodyVeriableCount = StringHelper.GetVariableCount(text);
            if (bodyVeriableCount > 0)
            {
                var intList = Enumerable.Range(1, bodyVeriableCount).ToList();
                var stringList = intList.Select(i => i.ToString()).ToList();
                return JObject.Parse(CreateMetaPayloadJsonDto
                    .CreateBodyWithVariablesPayload(text, stringList));
            }
            else
            {
                var t = CreateMetaPayloadJsonDto.CreateBodyPayload(text);
                return JObject.Parse(t);
            }
        }

        #region Healper
        private void AddMediaHeaderParameters(string? mediaAwsUrl, MediaType mediatype, JArray headerParameters)
        {
            switch (mediatype)
            {
                case MediaType.IMAGE:
                    var imageHeaderParameter = MetaPayloadDto
                        .CreateMediaHeaderParameter("image", mediaAwsUrl ?? "");
                    headerParameters.Add(JObject.Parse(imageHeaderParameter));
                    break;
                case MediaType.VIDEO:
                    var videoHeaderParameter = MetaPayloadDto
                        .CreateMediaHeaderParameter("video", mediaAwsUrl ?? "");
                    headerParameters.Add(JObject.Parse(videoHeaderParameter));
                    break;
                case MediaType.DOCUMENT:
                    var documentHeaderParameter = MetaPayloadDto
                        .CreateMediaHeaderParameter("document", mediaAwsUrl ?? "");
                    headerParameters.Add(JObject.Parse(documentHeaderParameter));
                    break;
            }
        }
        private static string ReplaceMatches(string input, MatchCollection matches, List<string> values)
        {
            foreach (Match match in matches.Cast<Match>())
            {
                if (int.TryParse(match.Groups[1].Value, out int index) && index >= 1 && index <= values.Count)
                {
                    string pattern = $"{{{{{index}}}}}";
                    input = input.Replace(pattern, values[index - 1]);
                }
            }
            return input;
        }
        #endregion
    }
}
