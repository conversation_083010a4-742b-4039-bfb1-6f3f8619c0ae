using EngagetoMeta;

try
{
    var builder = WebApplication.CreateBuilder(args);
    builder.WebHost.UseUrls("http://0.0.0.0:80");

    // Configure logging
    builder.Logging.ClearProviders();
    builder.Logging.AddConsole();
    builder.Logging.AddDebug();
    builder.Logging.AddAzureWebAppDiagnostics(); // Azure-specific

    // Configure services using Startup class
    Startup.ConfigureServices(builder.Services, builder.Configuration);

    var app = builder.Build();

    // Log application startup
    app.Logger.LogInformation("Starting Engageto.Meta application");

    // Configure the application using Startup class
    Startup.Configure(app, app.Environment);

    app.Run();
}
catch (Exception ex)
{
    // Log unhandled exceptions during startup
    Console.WriteLine($"Unhandled exception during startup: {ex}");
    // Optionally use Serilog or other logger here too
    throw;
}
finally
{
    Console.WriteLine("Engageto.Meta shutting down.");
}
