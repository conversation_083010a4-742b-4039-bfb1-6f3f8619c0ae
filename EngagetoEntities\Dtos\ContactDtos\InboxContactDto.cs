﻿namespace EngagetoEntities.Dtos.ContactDtos
{
    public class InboxContactDto
    {
        public string Contact { get; set; } = default!;
        public string Name { get; set; } = default!;
        public Guid ContactId { get; set; }
        public string ChatStatus { get; set; } = default!;
        public string Assignee { get; set; } = default!;
        public string[] Tags { get; set; } = default!;
        public TimeSpan? LastMessageAt { get; set; }
        public string? LastMessage { get; set; }
        public int? UnRead { get; set; }
        public bool? IsSpam { get; set; }
        public int IsOptIn { get; set; }

    }
}
