﻿using EngagetoContracts.UserContracts;
using EngagetoEntities.DdContext;
using EngagetoEntities.Dtos.BusinessDetailsDtos;
using EngagetoEntities.Entities;
using EngagetoEntities.Response;
using EngagetoEntities.UserEntities.Dtos;
using EngagetoEntities.UserEntities.Models;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using Microsoft.EntityFrameworkCore;
using SixLabors.ImageSharp.PixelFormats;
using System.Security.Claims;


namespace Engageto.Controllers.UserControllers
{
    [Route("api/[controller]")]
    [ApiController]
    public class CompanyDetailsController : ControllerBase
    {
        private readonly ICompanyDetailsService _manageClientsService;
        private readonly ApplicationDbContext _context;
        private readonly IWalletService _walletService;
        public CompanyDetailsController(ICompanyDetailsService manageClientsService, ApplicationDbContext context, IWalletService walletService)
        {
            _manageClientsService = manageClientsService;
            _context = context;
            _walletService = walletService;
        }


        /* [HttpPost("add-company")]
         [Authorize]
         public async Task<IActionResult> AddCompany([FromForm] CompanyRequest companyRequest, IFormFile companylogo)
         {
             try
             {
                 var currentUserIdClaim = User.Claims.FirstOrDefault(c => c.Type == ClaimTypes.Name);

                 if (currentUserIdClaim == null || !Guid.TryParse(currentUserIdClaim.Value, out Guid currentUserId))
                 {
                     return BadRequest(new { Message = "Invalid current user." });
                 }

                 var result = await _manageClientsService.AddCompanyAsync(currentUserId, companyRequest, companylogo);


                 return Ok(new { CompanyId = result });
             }
             catch (InvalidOperationException ex)
             {
                 return BadRequest(new { Message = ex.Message });
             }
             catch (Exception ex)
             {
                 return StatusCode(500, new { Message = $"Internal Server Error: {ex.Message}" });
             }
         }
 */


        [HttpPut("update-company/{companyId}")]
        //[AuthorizeRoles(RoleConstants.Admin, RoleConstants.Owner)]
        [Authorize]
        //   [Authorize("editCompanyDetails")]
        public async Task<IActionResult> UpdateCompany(Guid companyId, [FromForm] Ahex_CRM_BusinessDetailsDto updatedCompany)
        {
            try
            {
                var currentUserIdClaim = User.Claims.FirstOrDefault(c => c.Type == ClaimTypes.Name);

                if (currentUserIdClaim == null || !Guid.TryParse(currentUserIdClaim.Value, out Guid currentUserId))
                {
                    return BadRequest(new { Message = "Invalid current user." });
                }
                updatedCompany ??= new Ahex_CRM_BusinessDetailsDto();
                if (!string.IsNullOrEmpty(updatedCompany.CompanyAddress) && updatedCompany.CompanyAddress.Length > 256)
                {
                    return BadRequest(new { Message = "Address must be at most 256 characters long." });
                }
                if (!string.IsNullOrEmpty(updatedCompany.Description) && updatedCompany.Description.Length > 512)
                {
                    return BadRequest(new { Message = "Description must be at most 512 characters long." });
                }
                if (updatedCompany.Image != null)
                {
                    if (updatedCompany.Image.ContentType == "image/jpeg" || updatedCompany.Image.ContentType == "image/png")
                    {
                        using (var imageStream = updatedCompany.Image.OpenReadStream())
                        {
                            var image = SixLabors.ImageSharp.Image.Load<Rgba32>(imageStream);
                            if (image.Width < 192 || image.Height < 192)
                            {
                                return BadRequest(new { Message = "Image resolution must be greater than 192x192 pixels." });
                            }
                        }
                    }
                    else
                    {
                        return BadRequest(new { Message = "Image ContentType must be in image/jpeg or image/png." });
                    }

                }
                var metaDetails = await _context.BusinessDetailsMetas.FirstOrDefaultAsync(md => md.BusinessId == companyId.ToString());
                if (metaDetails == null)
                {
                    throw new InvalidOperationException($"Meta details not found for company.");
                }
                var result = await _manageClientsService.UpdateCompanyFieldsAsync(currentUserId, companyId, updatedCompany);
                if (result)
                {
                    return Ok(new { Message = "Company updated successfully." });
                }
                else
                {
                    return BadRequest(new { Message = "Failed to update company." });

                }
            }
            catch (InvalidOperationException ex)
            {
                return BadRequest(new { Message = ex.Message });
            }
            catch (Exception ex)
            {
                return BadRequest(new { Message = $"{ex.Message}" });
            }
        }



        [HttpDelete("delete-company/{companyId}")]
        // [AuthorizeRoles(RoleConstants.Admin, RoleConstants.Owner)]
        [Authorize]
        public async Task<IActionResult> DeleteCompany([FromRoute] Guid companyId)
        {
            try
            {
                var currentUserIdClaim = User.Claims.FirstOrDefault(c => c.Type == ClaimTypes.Name);

                if (currentUserIdClaim == null || !Guid.TryParse(currentUserIdClaim.Value, out Guid currentUserId))
                {
                    return BadRequest(new { Message = "Invalid current user." });
                }

                var result = await _manageClientsService.DeleteCompanyAsync(currentUserId, companyId);

                if (result)
                {
                    return Ok(new { Message = "Company deleted successfully." });
                }
                else
                {
                    return BadRequest(new { Message = "Failed to delete company." });
                }
            }
            catch (Exception ex)
            {
                return StatusCode(500, new { Message = $"Internal Server Error: {ex.Message}" });
            }
        }
        [HttpPatch]
        //[AuthorizeRoles(RoleConstants.Admin, RoleConstants.Owner)]
        [Authorize]
        [Route("UpdateBillingDetails")]
        public async Task<IActionResult> UpdateBillingDetails(BillingDetailsDto detailsDto)
        {
            try
            {
                var currentUserIdClaim = User.Claims.FirstOrDefault(c => c.Type == ClaimTypes.Name);
                if (currentUserIdClaim == null || !Guid.TryParse(currentUserIdClaim.Value, out Guid currentUserId))
                {
                    throw new Exception("Invalid current user");
                }
                if (detailsDto.Id == null)
                    throw new Exception("Id is a required field");

                var billingDetails = await _manageClientsService.UpdateBillingDetailsAsyc(detailsDto);
                if (billingDetails != null)
                    return Ok(new ApiResponse<Ahex_CRM_BusinessDetails>()
                    {
                        Success = true,
                        Data = billingDetails,
                        Message = "Updated billing details"
                    });
                throw new Exception("Failed to update billing details");
            }
            catch (Exception ex)
            {
                return StatusCode(500, new ApiResponse<string?>()
                {
                    Message = ex.Message,
                    Success = false,
                    Errors = ex.StackTrace
                });
            }
        }

        [HttpGet("get-company/{companyId}")]
        // [AuthorizeRoles(RoleConstants.Admin, RoleConstants.Owner, RoleConstants.Teammate)]
        [Authorize]
        public async Task<IActionResult> GetCompany(Guid companyId)
        {
            try
            {
                var currentUserIdClaim = User.Claims.FirstOrDefault(c => c.Type == ClaimTypes.Name);

                if (currentUserIdClaim == null || !Guid.TryParse(currentUserIdClaim.Value, out Guid currentUserId))
                {
                    return BadRequest(new { Message = "Invalid current user." });
                }

                var company = await _manageClientsService.GetCompanyByIdAsync(companyId);
                var CurrentPlan = await _context.Subscriptions.ToListAsync();
                var PlanDetails = await _context.PlanEntities.Where(m => m.Status).ToListAsync();
                if (company != null)
                {
                    return Ok(new { Company = company, Plan = await _walletService.GetWalletAndSubscriptionPlanAsync(companyId.ToString(), currentUserId) });
                }
                else
                {
                    return NotFound(new { Message = "Company not found." });
                }
            }
            catch (Exception ex)
            {
                return StatusCode(500, new { Message = "Internal Server Error" });
            }
        }
        [HttpGet("get-all-companies")]
        // [AuthorizeRoles(RoleConstants.Admin, RoleConstants.Owner, RoleConstants.Teammate)]
        [Authorize]
        public async Task<IActionResult> GetAllCompanies(
          [FromQuery] string searchQuery = null,
          [FromQuery] bool? includeInactive = true,
          [FromQuery] string sortBy = null,
          [FromQuery] bool isSortAscending = true)
        {
            try
            {
                var currentUserIdClaim = User.Claims.FirstOrDefault(c => c.Type == ClaimTypes.Name);

                if (currentUserIdClaim == null || !Guid.TryParse(currentUserIdClaim.Value, out Guid currentUserId))
                {
                    return BadRequest(new { Message = "Invalid current user." });
                }

                bool includeInactiveValue = includeInactive ?? true;

                var companies = await _manageClientsService.GetAllCompaniesAsync(currentUserId, searchQuery, includeInactiveValue, sortBy, isSortAscending);

                if (companies != null)
                {
                    return Ok(companies);
                }
                else
                {
                    return BadRequest(new { Message = "Failed to retrieve companies." });
                }
            }
            catch (Exception ex)
            {
                return StatusCode(500, new { Message = $"Internal Server Error: {ex.Message}" });
            }
        }

        [HttpGet("tenants")]
        [AllowAnonymous]
        public async Task<IActionResult> GetLeadratTenant()
        {
            try
            {
                var result = await _manageClientsService.GetTenantsAsync();
                return Ok(new ApiResponse<List<TenantDtos>>
                {
                    Success = true,
                    Data = result,
                    Message = "Tenants list"
                });
            }
            catch (Exception ex)
            {
                return BadRequest(new ApiResponse<string>
                {
                    Success = false,
                    Message = ex.Message,
                    Errors = ex.StackTrace
                });
            }
        }

    }
}
