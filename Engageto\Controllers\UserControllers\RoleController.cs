﻿using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using System.Security.Claims;
using EngagetoContracts.UserContracts;
using Microsoft.AspNet.SignalR.Messaging;
using EngagetoEntities.Entities;

namespace Engageto.Controllers.UserControllers
{
    [Route("api/[controller]")]
    [ApiController]
    public class RoleController : ControllerBase
    {
        private readonly IRoleService _roleService;
        private readonly IWalletService _walletService;

        public RoleController(IRoleService roleService,
          //  ISubscription subscription,
            IWalletService walletService)
        {
            _roleService = roleService;
            _walletService = walletService;
        }
        [HttpGet("DisplayAllRoles/{companyId}")]
        [Authorize]
        public async Task<IActionResult> GetAllRoles(string companyId,
                                                         [FromQuery] string searchQuery = "",
                                                         [FromQuery] string sortBy = "",
                                                         [FromQuery] bool isSortAscending = true)
        {
            try
            {

                var roles = await _roleService.GetAllRolesAsync(companyId, searchQuery, sortBy, isSortAscending);

                return Ok(roles);
            }
            catch (Exception ex)
            {

                return StatusCode(500, new { Message = "Internal Server Error" });
            }
        }

        [HttpGet("{roleId}")]
        // [AuthorizeRoles(RoleConstants.Admin, RoleConstants.Owner)]
        [Authorize]
        public async Task<IActionResult> GetRoleById(Guid roleId)
        {
            var role = await _roleService.GetRoleByIdAsync(roleId);

            if (role == null)
            {
                return NotFound();
            }

            return Ok(role);
        }

        [HttpPost]
        // [AuthorizeRoles(RoleConstants.Admin, RoleConstants.Owner)]
        [Authorize]
        public async Task<IActionResult> AddRole([FromBody] RoleDto role)
        {
            var currentUserIdClaim = User.Claims.FirstOrDefault(c => c.Type == ClaimTypes.Name);
            if (currentUserIdClaim == null || !Guid.TryParse(currentUserIdClaim.Value, out Guid currentUserId))
                return BadRequest("Invalid current user.");

            var walletAndSubscription = await _walletService.GetWalletAndSubscriptionPlanAsync(role.CompanyId,null);
            var subscriptionPlan = walletAndSubscription?.subscriptionPlan;
            if (subscriptionPlan == null || !subscriptionPlan.IsActive)
                return BadRequest(new { Message = "Your plan is not active." });

            if (subscriptionPlan.PlanName == "Intro")
                return BadRequest(new { Message = "You cannot add roles under the current plan." });

            var roles = await _roleService.GetAllRolesAsync(role.CompanyId, "", "", true);
            if (roles.Count >= 5)
                return BadRequest(new { Message = "You cannot add more than 5 roles. Contact the support team to add more roles." });
            
            var result = await _roleService.AddRoleAsync(role, currentUserId);
            if (result)
                return Ok(new { Message = "Role added successfully." });

            return BadRequest(new { Message = "Failed to add role." });
        }


        [HttpPut("{roleId}")]
        // [AuthorizeRoles(RoleConstants.Admin, RoleConstants.Owner)]
        [Authorize]
        public async Task<IActionResult> UpdateRole(Guid roleId, [FromBody] RoleDto updatedRole)
        {
            var currentUserIdClaim = User.Claims.FirstOrDefault(c => c.Type == ClaimTypes.Name);

            if (currentUserIdClaim == null || !Guid.TryParse(currentUserIdClaim.Value, out Guid currentUserId))
            {
                return BadRequest("Invalid current user.");
            }

            var result = await _roleService.UpdateRoleAsync(roleId, updatedRole, currentUserId);

            if (result)
            {
                return Ok(new { Message = "Role updated successfully." });
            }
            else
            {
                return BadRequest(new { Message = "Failed to update role." });
            }
        }

        [HttpDelete("{roleId}")]
        //[AuthorizeRoles(RoleConstants.Admin, RoleConstants.Owner)]
        [Authorize]
        public async Task<IActionResult> DeleteRole(Guid roleId)
        {
            var currentUserIdClaim = User.Claims.FirstOrDefault(c => c.Type == ClaimTypes.Name);

            if (currentUserIdClaim == null || !Guid.TryParse(currentUserIdClaim.Value, out Guid currentUserId))
            {
                return BadRequest("Invalid current user.");
            }

            var result = await _roleService.DeleteRoleAsync(roleId);

            if (result)
            {
                return Ok(new { Message = "Role deleted successfully." });
            }
            else
            {
                return BadRequest(new { Message = "Failed to delete role." });
            }
        }
    }
}
