﻿using EngagetoEntities.Dtos.AutomationDtos;
using EngagetoEntities.Entities;

namespace EngagetoDapper.Data.Interfaces.AutomationInterfaces
{
    public interface IAutoReplyAutomationService
    {
        Task<AutoReplyAutomationEntity?> GetAutoReplyAutomationByInput(Guid companyId, string input, string? inputVariations = null);
        Task<IEnumerable<WorkflowCustomerResponseDto>?> GetWorkflowCustomerResponseAsync(Guid? contactId = null, string? workflowName = null, Guid? companyId = null,long? workflowStartId = null);
    }
}
