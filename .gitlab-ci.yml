  
stages:          # List of stages for jobs, and their order of execution
  - build  
  - test
  - deploy
before_script:
  - git clean -fdx  # Clean the workspace
  - git fetch --unshallow  # Fetch all history
  

 
     
build:
  stage: build
  script:
    - dotnet restore
    - dotnet build -c Release  
    - dotnet publish Engageto/Engageto.csproj -c Release
  
test:
  stage: test
  script:
    - dotnet test
  

deploy_master: 
  stage : deploy        
  script:
    - dotnet publish Engageto/Engageto.csproj -c Release -o Publish -p:PublishProfile="prod-deploy" -p:password="$DEPLOY_PASS" 
  only:
     - master
     
deploy_dev: 
  stage : deploy        
  script:
    - dotnet publish Engageto/Engageto.csproj -c Release -o Publish -p:PublishProfile="dev-deploy" -p:password="$DEPLOY_PASS" 
  only:
     - dev


   
    

           
       
       

  





