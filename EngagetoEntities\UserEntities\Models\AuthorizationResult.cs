﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace EngagetoEntities.UserEntities.Models
{
    public class AuthorizationResult
    {
        public bool Authorized { get; set; }
        public ErrorDetails Error { get; set; }

        public static AuthorizationResult Success()
        {
            return new AuthorizationResult { Authorized = true };
        }

        public static AuthorizationResult Fail(string message, int code)
        {
            return new AuthorizationResult
            {
                Authorized = false,
                Error = new ErrorDetails { Message = message, Code = code }
            };
        }
    }

    public class ErrorDetails
    {
        public int Code { get; set; }
        public string Message { get; set; }
    }

}
