﻿using EngagetoEntities.Enums;

namespace EngagetoEntities.UserEntities.Dtos
{
    public class ExpiringOrExpiredSubscriptionsDto
    {
        public Guid BusinessId { get; set; }
        public string? BusinessName { get; set; }
        public string? Email { get; set;}
        public string? CountryCode { get; set; }
        public string? PhoneNumber { get; set;}
        public int? SubscriptionId { get; set; }
        public int? PlanId { get; set; }
        public string? PlanName { get; set; }
        public DateTime? PlanEndDate { get; set; }
        public PlanType? DurationType { get; set; }
    }
}
