﻿using EngagetoEntities.Enums;
using Newtonsoft.Json;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace EngagetoEntities.Entities
{
    [Table("Campaigns")]
    public class Campaign
    {
        [Key]
        public Guid CampaignId { get; set; }
        public string BusinessId { get; set; }
        public Guid UserId { get; set; }
        public string CampaignTitle { get; set; }
        public string? WhatsAppMessagesId { get; set; }
        public string? Audiance { get; set; }
        public string? SendTextType { get; set; }
        public Guid? TemplateId { get; set; }
        public string? HeaderValue { get; set; }
        public string? BodyValues { get; set; }
        [StringLength(2000, ErrorMessage = "Maximum {1} characters allowed.")]
        public string? RedirectUrlsValue { get; set; }
        public string? MediaUrl { get; set; }
        public CampaignState State { get; set; }
        public string? Createdby { get; set; }
        public string? Editedby { get; set; }

        [DataType(DataType.DateTime)]
        public DateTime? DateSetLive { get; set; }
        public SchedulType? SchedulRepeater { get; set; }
        public string? ScheduleJobId { get; set; }
        public DateTime? CreatedDate { get; set; }
        public DateTime? EditedDate { get; set; }
        public string? AutomationJson { get; set; }
        public int? UploadedFileId { get; set; }
        public string? ErrorCause {  get; set; }
        public string? ChildScheduleJobIds {  get; set; }
        public string? CarouselVariables { get; set; }        
        public string? ScheduledFunctionUrls { get; set; }
        public Campaign(){}
        public Campaign(string businessId, Guid userId, string title, string? audiance, string? text, Guid? templateId, string? headerValue, string? CarouselVariables, string? bodyValues, string? mediaUrl, string userName, DateTime? dateSetLive)
        {
            CampaignId = Guid.NewGuid();
            BusinessId = businessId;
            UserId = userId;
            CampaignTitle = title;
            Audiance = audiance;
            SendTextType = text;
            TemplateId = templateId;
            HeaderValue = headerValue;
            BodyValues = bodyValues;
            MediaUrl = mediaUrl;
            State = CampaignState.Incompleted;
            Createdby = userName;
            Editedby = userName;
            DateSetLive = dateSetLive;
            SchedulRepeater = SchedulType.None;
            CreatedDate = DateTime.UtcNow;
            EditedDate = DateTime.UtcNow;
            CarouselVariables = CarouselVariables  ?? string.Empty;
        }
    }
}







