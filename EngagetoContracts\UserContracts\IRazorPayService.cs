﻿using EngagetoEntities.Entities;



namespace EngagetoContracts.UserContracts
{
    public interface IRazorPayService
    {
        Task<Subscriptions> GetLastPaidPlanByUserIdAsync(string companyId);

        Task<string> CreateOrderAsync(Guid currentUser, OrderRequestModel request);
        Task<string> VerifyPaymentAsync(Guid currentUser, PaymentVerificationModel request);
        Task<string> CreateOrder(decimal amount, string currency, string receipt);
        Task<bool> VerifyPaymentSignature(string orderId, string paymentId, string signature);
        Task<string> CreateWalletOrderAsync(Guid userId, decimal amount, string currency);
        Task<bool> VerifyWalletPaymentAsync(string orderId, string paymentId, string signature);
        Task<(bool isSuccess, string message)> AddAmountToWalletAsync(string paymentId, Guid currentUser);
        Task<PaymentWalletDetail> GetSubscriptionDeductionsByIdAsync(int id);
        Task<PaymentWalletDetail> SavePaymentWalletAsync(PaymentWalletDetail paymentWalletDetail);
        Task<Subscriptions> SaveSubscriptionAsync(Subscriptions subscription, bool isUpdate = false);
        Task<List<PaymentWalletDetail>> GetPaymentWalletDetailsByCompanyIdAsync(Guid userId, Guid discountId);
        Task<bool> FreePlanSubscriptionAsync(string companyId, Guid userId);

        //Task<IEnumerable<UserEntities.Models.Plan>> GetAllPlansAsync();
        //Task<IEnumerable<RazorpaySubscriptionPlan>> GetSubscriptionPlans();
    }
}
