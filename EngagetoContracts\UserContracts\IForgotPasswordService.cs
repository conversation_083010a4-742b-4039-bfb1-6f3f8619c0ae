﻿using Microsoft.AspNetCore.Http;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace EngagetoContracts.UserContracts
{
    public interface IForgotPasswordService
    {
        Task<bool> UpdatePassword(string email, string newPassword, string confirmPassword, HttpContext httpContext);
        Task<bool> VerifyOTPToken(string otp, string email);
        Task<bool> SendOTPTokenAsync(string email);

    }
}
