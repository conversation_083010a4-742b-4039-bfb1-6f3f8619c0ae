﻿using EngagetoEntities.Dtos.CommonDtos;
using EngagetoEntities.Dtos.ContactDtos;
using EngagetoEntities.Entities;

namespace EngagetoContracts.Services
{
    public interface IContactService : ITransientService
    {

        Task<ContactImportTracker> GetContactTrackerByIdAsync(int uploadedId);
        Task<PaginatedImportTrackerDto> GetContactImportTrackersAsync(int pageNumber, int pageSize);
        Task<FileColumnDto> UploadExcelFileAsync(UploadFileDto uploadedFile);
        Task<ContactImportTracker> CreateBulkUploadAsync(BulkContactUploadDto bulkContactUploadDto);
        Task<List<Tags>> GetTagDetailsByNameAsync(Guid businessId, List<string> tagsName);

    }
}
