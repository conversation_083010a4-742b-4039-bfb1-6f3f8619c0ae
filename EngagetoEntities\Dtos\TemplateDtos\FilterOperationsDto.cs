﻿
using EngagetoEntities.Dtos.FilterDtos;

namespace EngagetoEntities.Dtos.TemplateDtos
{
    public class FilterOperationsDto
    {

        public TemplateSearch? Searching { get; set; }
        public TemplateSort? Sorting { get; set; }
        public TemplateFilterGroup? Filtering { get; set; }
        public bool  IsDeleted { get; set; }
        public List<DateRangeFilter>? DateRangeFilters { get; set; }
    }
    public class TemplateSearch
    {
        public string? Value { get; set; }
    }

    public class TemplateSort
    {
        public string? Column { get; set; }
        public string? Order { get; set; }
    }

    public class TemplateFilterGroup
    {
        public string? FilterType { get; set; }
        public List<TemplateFilterCondition>? Conditions { get; set; }
    }

    public class TemplateFilterCondition
    {
        public string? Column { get; set; }
        public string? Operator { get; set; }
        public string? Value { get; set; }
    }
}
