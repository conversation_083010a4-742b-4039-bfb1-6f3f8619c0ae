﻿using EngagetoEntities.Enums;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace EngagetoEntities.UserEntities.Dtos
{
    public class ConversationCostDto
    {
        public ConversationCategoriesEnum ConversationCategory { get; set; }
        public decimal Cost { get; set; }
        public int Year { get; set; }
        public int PlanId { get; set; }
        public PlanType PlanType { get; set; }
        public string? CompanyId { get; set; }
    }
}
