﻿using EngagetoEntities.Enums;

namespace EngagetoEntities.Dtos.ContactDtos
{
    public class ContactInfoDto
    {
        public string? ContactInfo { get; set; }
        public string? CountryCode { get; set; }
        public string Contact { get; set; } = default!;
        public Guid BusinessId { get; set; }
        public Guid ContactId { get; set; }
        public string? Name { get; set; }
        public string? Email { get; set; }
        public string? CountryName { get; set; }
        public string? ContactNumber { get; set; }
        public ChatStatus? Status { get; set; }
        public DateTime? Date { get; set; }
        public Guid? UserId { get; set; }
    }
}
