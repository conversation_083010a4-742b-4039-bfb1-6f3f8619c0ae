﻿using System.Text.RegularExpressions;

namespace EngagetoEntities.Utilities
{
    public class InboxMethods
    {

        public List<string> ExtractUrls(List<string> texts)
        {
            var urls = new List<string>();
            var urlPrefixes = new List<string>
        {
            "https://", "http://", "ftp://", "file://", "mailto:", "news:", "telnet:", "gopher:", "wais:", "www.", "ftps://", "sftp://", "ssh://", "git://", "svn://"
        };
            var regex = new Regex(@"\b(?:https?|ftp|file|mailto|news|telnet|gopher|wais|www|ftps|sftp|ssh|git|svn)://[^\s/$.?#].[^\s]*|www\.[^\s/$.?#].[^\s]*", RegexOptions.Compiled | RegexOptions.IgnoreCase);

            foreach (var text in texts)
            {
                foreach (var prefix in urlPrefixes)
                {
                    int startIndex = 0;
                    while ((startIndex = text.IndexOf(prefix, startIndex, StringComparison.OrdinalIgnoreCase)) != -1)
                    {
                        int endIndex = text.IndexOf(' ', startIndex);
                        if (endIndex == -1)
                        {
                            endIndex = text.Length;
                        }

                        var potentialUrl = text.Substring(startIndex, endIndex - startIndex);

                        var matches = regex.Matches(potentialUrl);
                        foreach (Match match in matches)
                        {
                            urls.Add(match.Value);
                        }

                        startIndex = endIndex;
                    }
                }

            }
            return urls;
        }
    }
}