﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Security.Cryptography;
using System.Text;
using System.Threading.Tasks;

namespace EngagetoEntities.UserEntities.Models
{
    public class EncryptionHelper
    {
        private static readonly string Key = "1234567890123456"; 

        public static string Encrypt(string plainText)
        {
            using (var aes = Aes.Create())
            {
                aes.Key = Encoding.UTF8.GetBytes(Key);
                aes.GenerateIV();

                using (var encryptor = aes.CreateEncryptor(aes.Key, aes.IV))
                using (var ms = new MemoryStream())
                {
                    ms.Write(aes.IV, 0, aes.IV.Length);
                    using (var cs = new CryptoStream(ms, encryptor, CryptoStreamMode.Write))
                    using (var sw = new StreamWriter(cs))
                    {
                        sw.Write(plainText);
                    }
                    return Convert.ToBase64String(ms.ToArray());
                }
            }
        }

        public static string Decrypt(string cipherText)
        {
            var fullCipher = Convert.FromBase64String(cipherText);

            using (var aes = Aes.Create())
            {
                aes.Padding = PaddingMode.PKCS7; // Explicitly set padding
                aes.BlockSize = 128; // Ensure correct block size for AES

                var iv = new byte[16]; // AES requires 16-byte IV
                var cipher = new byte[fullCipher.Length - iv.Length];

                // Extract the IV from the encrypted message
                Array.Copy(fullCipher, iv, iv.Length);
                Array.Copy(fullCipher, iv.Length, cipher, 0, cipher.Length);

                aes.Key = Encoding.UTF8.GetBytes(Key); // Ensure correct key size (16, 24, or 32 bytes)
                aes.IV = iv;

                using (var decryptor = aes.CreateDecryptor(aes.Key, aes.IV))
                using (var ms = new MemoryStream(cipher))
                using (var cs = new CryptoStream(ms, decryptor, CryptoStreamMode.Read))
                using (var sr = new StreamReader(cs))
                {
                    return sr.ReadToEnd();
                }
            }
        }


    }
    public class RequestModel
    {
        public string Property1 { get; set; }
        public int Property2 { get; set; }
        // Other properties as needed
    }

    public class ResponseModel
    {
        public string Property1 { get; set; }
        public int Property2 { get; set; }
        // Other properties as needed
    }

}
