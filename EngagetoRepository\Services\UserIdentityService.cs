﻿using EngagetoContracts.Services;
using System.Security.Claims;

namespace EngagetoRepository.Services
{
    public class UserIdentityService : IUserIdentityService
    {
        public string Name { get; set; } = string.Empty;
        public string Email { get; set; } = string.Empty;
        public string BusinessId { get; set; } = string.Empty;
        public Guid UserId { get; set; } = Guid.Empty;

        public void SetClaims(IEnumerable<Claim> claims)
        {
            foreach (var claim in claims)
            {
                switch (claim.Type)
                {
                    case ClaimTypes.Name:
                        Guid.TryParse(claim.Value, out var userId);
                        UserId = userId;
                        break;
                    case "UserName":
                        Name = claim.Value;
                        break;
                    case "BusinessId":
                        BusinessId = claim.Value;
                        break;
                    case "Email":
                        Email = claim.Value;
                        break;
                }
            }
        }
    }
}
