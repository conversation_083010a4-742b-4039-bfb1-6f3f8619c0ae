﻿using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using EngagetoEntities.Enums;

namespace EngagetoEntities.Abstract
{
    public abstract class Notfication : BaseEntity
    {
        [Key]
        public Guid Id { get; set; }
        public NotificationCategory Category { get; set; }

        public string Title { get; set; }
        public string Description { get; set; }
        public bool IsRead { get; set; } = false;
        public NotificationType Type { get; set; }

    }
}
