﻿using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace EngagetoEntities.UserEntities.Models
{

    public class LoginRequestModel
    {
        [Required(ErrorMessage = "Email is required.")]
        [EmailAddress(ErrorMessage = "Invalid email address.")]
        [RegularExpression(@"^\S+@\S+\.\S+$", ErrorMessage = "Email must be in a valid format.")]
        public string Email { get; set; }


        [Required(ErrorMessage = "Password is required")]
      
        public string Password { get; set; }
    }
}

