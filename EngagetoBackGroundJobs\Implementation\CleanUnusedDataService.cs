﻿using EngagetoBackGroundJobs.Interfaces;
using EngagetoDapper.Data.Interfaces.GenericInterfaces;
using EngagetoEntities.Entities;
using EngagetoEntities.Utilities;

namespace EngagetoBackGroundJobs.Implementation
{
    internal class CleanUnusedDataService : ICleanUnusedDataService
    {
        private readonly IGenericRepository _genericRepository;
        public CleanUnusedDataService(IGenericRepository genericRepository)
        {
            _genericRepository = genericRepository;
        }
        public async Task<bool> CleanUnusedDataAsync()
        {
            try
            {
                var tableName = StringHelper.GetTableName<ConversationAnalyticsJobRequest>();
                var WebhookEventsTableName = StringHelper.GetTableName<WebhookEvents>();
                await _genericRepository.DeleteRecordAsync<bool>(tableName, new List<EngagetoDapper.Data.Dtos.RequestFilterDto>()
                {
                    new EngagetoDapper.Data.Dtos.RequestFilterDto()
                    {
                        Key = "StartDate",
                        Value = DateTime.UtcNow.AddDays(-2),
                        Operator = "<"
                    }
                });

                await _genericRepository.DeleteRecordAsync<bool>(WebhookEventsTableName, new List<EngagetoDapper.Data.Dtos.RequestFilterDto>()
                {
                    new EngagetoDapper.Data.Dtos.RequestFilterDto()
                    {
                        Key = "SentAt",
                        Value = DateTime.UtcNow.AddDays(-1),
                        Operator = "<"
                    }
                });
                return true;
            }
            catch (Exception ex)
            {
                throw;
            }
        }
    }
}
