﻿using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace EngagetoEntities.Entities
{
    [Table("PlanEntities")]
    public class PlanEntities : ICloneable
    {
        [Key]
        [DatabaseGenerated(DatabaseGeneratedOption.Identity)]
        public int Id { get; set; }
        public string? PlanName { get; set; }
        public decimal? Price { get; set; }
        public bool Status { get; set; }
        public string? CreatedBy { get; set; }
        public string? UpdatedBy { get; set; }
        public DateTime? CreatedAt { get; set; }
        public DateTime? UpdatedAt { get; set; }
        public object Clone()
        {
            return MemberwiseClone();
        }


    }
    public class PlanEntitiesDto
    {
        public string? PlanName { get; set; }
        public decimal? Price { get; set; }
        //  public string? CreatedBy { get; set; }
        //  public DateTime? CreatedAt { get; set; }
    }
    public class UpdatePlanEntitiesDto
    {
        public string? PlanName { get; set; }
        public decimal? Price { get; set; }
        // public string? UpdatedBy { get; set; }
        // public DateTime? UpdatedAt { get; set; }
    }
    public class MenuWithPlanDto
    {
        public int PlanId { get; set; }
        public string PlanName { get; set; }
        public string MenuName { get; set; }
    }
}
