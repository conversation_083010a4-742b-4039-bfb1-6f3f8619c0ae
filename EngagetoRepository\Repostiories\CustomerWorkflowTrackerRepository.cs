﻿using EngagetoContracts.WorkflowRepository;
using EngagetoEntities.DdContext;
using EngagetoEntities.Entities;
using Microsoft.EntityFrameworkCore;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace EngagetoRepository.Repostiories
{
    public class CustomerWorkflowTrackerRepository : ICustomerWorkflowTrackerRepository
    {
        private readonly ApplicationDbContext _dbContext;

        public CustomerWorkflowTrackerRepository(ApplicationDbContext dbContext)
        {
            _dbContext = dbContext;
        }

        public async Task<CustomerWorkflowTracker> GetActiveTrackerAsync(Guid customerId, Guid workflowId)
        {
           return await _dbContext.CustomerWorkflowTrackers
                .Where(t => t.ContactId == customerId &&
                           t.WorkflowId == workflowId )
              .FirstOrDefaultAsync();
        }

        public async Task<CustomerWorkflowTracker> GetTrackerByNodeAsync(Guid customerId, Guid nodeId)
        {
            return await _dbContext.CustomerWorkflowTrackers
                .Where(t => t.ContactId == customerId &&
                           t.NodeId == nodeId)
                .FirstOrDefaultAsync();
        }

        public async Task<List<CustomerWorkflowTracker>> GetActiveTrackersForCustomerAsync(Guid customerId)
        {
            return await _dbContext.CustomerWorkflowTrackers
                .Where(t => t.ContactId == customerId )
                .ToListAsync();
        }

        public async Task<List<CustomerWorkflowTracker>> GetTrackerByWorkflowAsync(Guid customerId, Guid? workflowId)
        {
            return await _dbContext.CustomerWorkflowTrackers
                .Where(t => t.ContactId == customerId && t.WorkflowId == workflowId && t.CompletedAt == null) 
                .ToListAsync();
        }

        public async Task<CustomerWorkflowTracker> CreateTrackerAsync(CustomerWorkflowTracker tracker)
        {
            tracker.CreatedAt = DateTime.UtcNow;
            tracker.UpdatedAt = DateTime.UtcNow;

            await _dbContext.CustomerWorkflowTrackers.AddAsync(tracker);
            await _dbContext.SaveChangesAsync();

            return tracker;
        }

        public async Task<CustomerWorkflowTracker> UpdateTrackerAsync(CustomerWorkflowTracker tracker)
        {
            tracker.UpdatedAt = DateTime.UtcNow;

            _dbContext.CustomerWorkflowTrackers.Update(tracker);
            await _dbContext.SaveChangesAsync();

            return tracker;
        }

        public async Task<bool> CompleteTrackerAsync(Guid trackerId)
        {
            var tracker = await _dbContext.CustomerWorkflowTrackers.FindAsync(trackerId);

            if (tracker == null)
                return false;
            tracker.UpdatedAt = DateTime.UtcNow;

            _dbContext.CustomerWorkflowTrackers.Update(tracker);
            await _dbContext.SaveChangesAsync();

            return true;
        }

   
    }
}

