using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using System.Security.Claims;
using EngagetoContracts.AttributeName;
using EngagetoEntities.Dtos.AttributeNameDtos;
using EngagetoContracts.Services;
using EngagetoRepository.Services;

namespace Engageto.Controllers.AttributeNameController
{
    [ApiController]
    [Route("api/[controller]")]
    public class AttributeNameController : BaseController
    {
        private readonly IAttributeNameService _attributeNameService;
        private readonly IUserIdentityService _userIdentityService;
        public AttributeNameController(
            IAttributeNameService attributeNameService,
            IUserIdentityService userIdentityService
            )
        {
            _attributeNameService = attributeNameService;
            _userIdentityService = userIdentityService;
        }

        [HttpPost]
        [Authorize]
        public async Task<IActionResult> CreateAttributeName([FromBody] CreateAttributeNameDtos attributeNameDto)
        {
            try
            {
                var currentUserId = _userIdentityService.UserId;
                var result = await _attributeNameService.CreateAttributeNameAsync(attributeNameDto, currentUserId);
                return Ok(CreateSuccessResponse(result, "Attribute name created successfully"));
            }
            catch (Exception ex)
            {
                return StatusCode(500, CreateErrorResponse<string>(ex.Message, ex.StackTrace));
            }
        }

        [HttpPut("{id}")]
        [Authorize]
        public async Task<IActionResult> UpdateAttributeName(Guid id, [FromBody] UpdateAttributeNameDtos attributeNameDto)
        {
            try
            {
               var currentUserId =   _userIdentityService.UserId;         
                var result = await _attributeNameService.UpdateAttributeNameAsync(id, attributeNameDto, currentUserId);
                return Ok(CreateSuccessResponse(result, "Attribute name updated successfully"));
            }
            catch (Exception ex)
            {
                return StatusCode(500, CreateErrorResponse<string>(ex.Message, ex.StackTrace));
            }
        }

        [HttpDelete("{id}")]
        [Authorize]
        public async Task<IActionResult> DeleteAttributeName(Guid id)
        {
            try
            {

              var currentUserId = _userIdentityService.UserId;

                var result = await _attributeNameService.DeleteAttributeNameAsync(id, currentUserId);
                return Ok(CreateSuccessResponse(result, "Attribute name deleted successfully"));
            }
            catch (Exception ex)
            {
                return StatusCode(500, CreateErrorResponse<string>(ex.Message, ex.StackTrace));
            }
        }

        [HttpGet]
        [Authorize]
        public async Task<IActionResult> GetAllAttributeNames()
        {
            try
            {
                var result = await _attributeNameService.GetAllAttributeNamesAsync();
                return Ok(CreateSuccessResponse(result, "Attribute names retrieved successfully"));
            }
            catch (Exception ex)
            {
                return StatusCode(500, CreateErrorResponse<string>(ex.Message, ex.StackTrace));
            }
        }

        [HttpGet("{id}")]
        [Authorize]
        public async Task<IActionResult> GetAttributeNameById(Guid id)
        {
            try
            {
                var result = await _attributeNameService.GetAttributeNameByIdAsync(id);
                return Ok(CreateSuccessResponse(result, "Attribute name retrieved successfully"));
            }
            catch (Exception ex)
            {
                return StatusCode(500, CreateErrorResponse<string>(ex.Message, ex.StackTrace));
            }
        }
    }
} 