﻿using Amazon.S3;
using EngagetoContracts.Services;
using EngagetoEntities.Dtos.CommonDtos;
using EngagetoEntities.Entities;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;

namespace Engageto.Controllers.UtlitiesControllers
{
    [Route("api/[controller]")]
    [ApiController]
    public class BlobStorageController : BaseController
    {
        private IBlobStorageService _blobStorageService;
        private IUserIdentityService _userIdentityService;
        public BlobStorageController(IBlobStorageService blobStorageService, IUserIdentityService IUserIdentityService)
        {
            _blobStorageService = blobStorageService;
            _userIdentityService = IUserIdentityService;
        }

        [HttpPost("upload")]
        [Authorize]
        public async Task<IActionResult> UploadFile([FromForm] UploadFileDto file)
        {
            try
            {
                var result = await _blobStorageService.UploadAsync(file);

                return Ok(CreateSuccessResponse<ViewUploadFileDto>(result, "File uploaded successfully."));
            }
            catch (InvalidOperationException ex)
            {
                return BadRequest(CreateErrorResponse<string>(ex.Message, null));
            }
            catch (Exception ex)
            {
                return StatusCode(500, CreateErrorResponse<string>(ex.Message, ex.StackTrace));
            }
        }

        // Retrieve File
        [HttpGet("download/{fileName}")]
        [Authorize]
        public async Task<IActionResult> DownloadFile(string fileName)
        {
            try
            {
                var result = await _blobStorageService.DownloadAsync(fileName);
                return result;
            }
            catch (AmazonS3Exception ex) when (ex.StatusCode == System.Net.HttpStatusCode.NotFound)
            {
                return BadRequest(CreateErrorResponse<string>("File not found.", ex.Message));
            }
            catch (Exception ex)
            {
                return StatusCode(500, CreateErrorResponse<string>(ex.Message, ex.StackTrace));
            }
        }

        [HttpGet("get-url/{fileName}")]
        [Authorize]
        public async Task<IActionResult> GetFileUrl(string fileName)
        {
            try
            {
                var result = await _blobStorageService.GetUrlAsync(fileName);
                return Ok(CreateSuccessResponse<string>(result,"File url"));
            }
            catch (Exception ex)
            {
                return StatusCode(500, CreateErrorResponse<string>(ex.Message, ex.StackTrace));
            }
        }


        [HttpGet("get/{id}")]
        [Authorize]
        public async Task<IActionResult> GetBlobStorageById(int id)
        {
            try
            {
                var result = await _blobStorageService.GetUploadedFile(id);

                if (result == null)
                {
                    return NotFound(CreateErrorResponse<string>($"No file found with ID {id}", "File not found"));
                }

                return Ok(CreateSuccessResponse<ViewUploadedFileDto>(result, "uploaded file details"));
            }
            catch (Exception ex)
            {
                return StatusCode(500, CreateErrorResponse<string>(ex.Message, ex.StackTrace));
            }
        }



    }
}

