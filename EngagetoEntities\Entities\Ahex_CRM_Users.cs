﻿using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace EngagetoEntities.Entities
{
    [Table("Users")]
    public class Ahex_CRM_Users
    {
        [Key]
        public Guid Id { get; set; }
        public string? CompanyId { get; set; }
        public string? Name { get; set; }
        public string? Designation { get; set; }
        public string? EmailAddress { get; set; }
        public string? Password { get; set; }
        public string? RoleId { get; set; }
        public string? CountryCode { get; set; }
        public string? PhoneNumber { get; set; }
        public string? CountryName { get; set; }
        public string? FacebookBusinessManagerId { get; set; }
        public string? WhatsAppBusinessId { get; set; }
        public string? Address { get; set; }
        public string? About { get; set; }
        public string? Image { get; set; }
        public string? CreatedBy { get; set; }
        public DateTime? LastOnlineTime { get; set; }
        public DateTime? CreationDate { get; set; }
        public bool Status { get; set; }
        public string? RefreshToken { get; set; }
        public ICollection<UserRole>? UserRoles { get; set; }
        [NotMapped]
        public List<Ahex_CRM_BusinessDetails>? users { get; set; }
        [NotMapped]
        public string? CompanyLegalName { get; set; }
        [NotMapped]
        public string? CompanyWebsite { get; set; }
        [NotMapped]
        public string? BusinessCategory { get; set; }
        [NotMapped]
        public string? CompanyLogoLink { get; set; }
        [NotMapped]
        public Role? Role { get; set; }
        [NotMapped]
        public string? BusinessName { get; set; }
        [NotMapped]
        public string? BusinessEmail { get; set; }
    }
}
