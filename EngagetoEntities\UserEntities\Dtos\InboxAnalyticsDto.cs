﻿using EngagetoEntities.Enums;

namespace EngagetoEntities.UserEntities.Dtos
{
    public class InboxAnalyticsDto
    {
        public int OpenCount { get; set; }
        public int ResolvedCount { get; set; }
        public int PendingCount { get; set; }
        public int ExpiredCount { get; set; }
        public int UnAssinedCount {get ; set;}

    }
    public class StatusChatDto
    {
        public string MonthYear { get; set; } = default!;
        public int OpenCount { get; set; }
        public int PendingCount { get; set; }
        public int ResolvedCount { get; set; }
        public int ExpiredCount { get; set; }

    }
    public class InboxAnalyticsTotalMessagsDto
    {
        public string CompanyId { get; set; } = default!;
        public int SentCount { get; set; }
        public int ReceivedCount { get; set; }
        public int FaildCount { get; set; }
        public int? SpamCount { get; set; } = 0;
    }
    public class TopUserPerformanceDto
    {
        public string CompanyId { get; set; } = default!;
        public string? UserId { get; set; }
        public string? Name { get; set; }
        public int? MessageCount { get; set; } = 0;
    }
    public class CampaignAnalytsicDto 
    {
        public Guid CampaignId { get; set; }
        public int AttemptedCount { get; set; }
        public int DeliveredCount { get; set; }
        public int? FailedCount { get; set; }
        public int? RepliedCount { get; set; } = 0;
        public int? ReadCount { get; set; } = 0;
        public int? Undelivered { get; set; }
    }
    public class InboxAnalyticsDasboardDto
    {
        public InboxAnalyticsDto InboxAnalytics { get; set; } = new();
        public List<StatusChatDto> StatusChat { get; set; } = new();
        public InboxAnalyticsTotalMessagsDto AnalyticsTotalMessage { get; set; } = new();
        public List<TopUserPerformanceDto> TopUserPerformance { get; set; } = new();
    }
    public class CampaignReportsDto
    {
        public string? Name { get; set; }
        public string? CreatedBy { get; set; }
        public CampaignState? State { get; set; }
        public DateTime? ScheduleDate { get; set; }
        public string? TemplateName { get; set; }
        public  ConvStatus? Status { get; set; }
        public MessageType? MessageType { get; set; }
        public string? CustomerName { get; set; }
        public string? PhoneNumber { get; set; }
        public string? Error { get; set; }
    }

}
