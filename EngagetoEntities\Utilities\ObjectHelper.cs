﻿using Newtonsoft.Json.Linq;

namespace EngagetoEntities.Utilities
{
    public static class ObjectHelper
    {
        public static Dictionary<string, string> ConvertToDictionary(object obj, string parentKey = "")
        {
            var dictionary = new Dictionary<string, string>();

            if (obj == null)
            {
                return dictionary;
            }

            var properties = obj.GetType().GetProperties();

            foreach (var property in properties)
            {
                var key = string.IsNullOrEmpty(parentKey) ? property.Name : $"{parentKey}.{property.Name}";
                var value = property.GetValue(obj);

                if (value != null && IsComplexType(value))
                {
                    // Recursively process nested objects
                    var nestedDict = ConvertToDictionary(value, key);
                    foreach (var nestedItem in nestedDict)
                    {
                        dictionary[nestedItem.Key] = nestedItem.Value;
                    }
                }
                else
                {
                    dictionary[key] = value?.ToString() ?? string.Empty;
                }
            }

            return dictionary;
        }
        public static Dictionary<string, object> ConvertJObjectToDictionary(JObject jObject, string parentKey = "")
        {
            var dict = new Dictionary<string, object>();

            foreach (var property in jObject.Properties())
            {
                string key = string.IsNullOrEmpty(parentKey) ? property.Name : $"{parentKey}.{property.Name}";

                if (property.Value is JObject nestedObject)
                {
                    foreach (var kvp in ConvertJObjectToDictionary(nestedObject, key))
                    {
                        dict[kvp.Key] = kvp.Value;
                    }
                }
                else if (property.Value is JArray array)
                {
                    int index = 0;
                    foreach (var item in array)
                    {
                        if (item is JObject arrayObject)
                        {
                            foreach (var kvp in ConvertJObjectToDictionary(arrayObject, $"{key}[{index}]"))
                            {
                                dict[kvp.Key] = kvp.Value;
                            }
                        }
                        else
                        {
                            dict[$"{key}[{index}]"] = item;
                        }
                        index++;
                    }
                }
                else
                {
                    dict[key] = property.Value.ToString();
                }
            }
            return dict;
        }
        private static bool IsComplexType(object value)
        {
            return value.GetType().IsClass && value.GetType() != typeof(string);
        }
        public static JObject ConvertToJObject(Dictionary<string, object> flatData)
        {
            JObject root = new JObject();

            foreach (var kvp in flatData)
            {
                SetNestedProperty(root, kvp.Key, kvp.Value);
            }
            return root;
        }

        public static void SetNestedProperty(JObject root, string path, object value)
        {
            string[] parts = path.Split('.');
            JToken current = root;

            for (int i = 0; i < parts.Length; i++)
            {
                string part = parts[i];

                if (part.Contains("["))
                {
                    // Handle array indexing (e.g., contacts[0].type)
                    string arrayName = part.Substring(0, part.IndexOf("["));
                    int index = int.Parse(part.Substring(part.IndexOf("[") + 1, 1));

                    if (!((JObject)current).ContainsKey(arrayName))
                        ((JObject)current)[arrayName] = new JArray();

                    JArray array = (JArray)((JObject)current)[arrayName];

                    while (array.Count <= index)
                        array.Add(new JObject());

                    current = array[index];
                }
                else
                {
                    // Handle regular object properties
                    if (i == parts.Length - 1)
                    {
                        ((JObject)current)[part] = JToken.FromObject(value);
                    }
                    else
                    {
                        if (!((JObject)current).ContainsKey(part))
                            ((JObject)current)[part] = new JObject();

                        current = ((JObject)current)[part];
                    }
                }
            }
        }
        public static JToken ConvertToCamelCase(JToken token)
        {
            if (token.Type == JTokenType.Object)
            {
                var obj = new JObject();
                foreach (var prop in token.Children<JProperty>())
                {
                    var camelKey = Char.ToLowerInvariant(prop.Name[0]) + prop.Name.Substring(1);
                    obj[camelKey] = ConvertToCamelCase(prop.Value);
                }
                return obj;
            }
            else if (token.Type == JTokenType.Array)
            {
                var array = new JArray();
                foreach (var item in token.Children())
                {
                    array.Add(ConvertToCamelCase(item));
                }
                return array;
            }
            else
            {
                return token.DeepClone(); // leave values as-is
            }
        }
    }
}
