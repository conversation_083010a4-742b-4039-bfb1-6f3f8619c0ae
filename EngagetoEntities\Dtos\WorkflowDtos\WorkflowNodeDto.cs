﻿
using EngagetoEntities.Dtos.CampaignDto;
using EngagetoEntities.Dtos.CommonDtos.PaymentGatwayDtos;
using EngagetoEntities.Entities;
using EngagetoEntities.Enums;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace EngagetoEntities.Dtos.WorkflowDtos
{
    public class WorkflowNodeDto
    {
        public Guid Id { get; set; }
        public NodeType Type { get; set; }
        public NodePayloadDto Data { get; set; } = new NodePayloadDto();
        public string? AttributeId { get; set; }
        public double PositionX { get; set; }
        public double PositionY { get; set; }
        public bool IsEntry { get; set; }
        public bool IsFinal { get; set; }
    }

    public class NodePayloadDto
    {
        public TemplateDto? Template { get; set; }
        public InteractiveMessageDto? InteractiveMessage { get; set; }
        public HttpRequestDto? HttpRequest { get; set; }
        public FlowStartDto? FlowStart { get; set; }
        public ConditionDto? Condition { get; set; }
    }

    public class FlowStartDto
    {
        public EntryNodeType EntryNodeType { get; set; }
        public List<LeadSourceDto>? LeadSource { get; set; }
        public List<LeadStatusDto>? LeadStatus { get; set; }
        public List<string>? LeadProject { get; set; }
    }
    public class FinalDto
    {
        public string? Message { get; set; }
    }

    public class TemplateDto
    {
        public Guid TemplateId { get; set; }
        public string TemplateName { get; set; } = string.Empty;
        public List<VariableModelDto>? BodyVariableValues { get; set; }
        public VariableModelDto? HeaderValue { get; set; }
        public List<CarouselVariablesDto>? CarouselVariables { get; set; }
        public int? TimeInMinutes { get; set; }
        public bool IsScheduleReminder { get; set; } = false;
        public int? TimeOut { get; set; }
        public bool IsEnbaleSetTimeOut { get; set; } = false;
    }
    public class VariableModelDto
    {
        public string? Variable { get; set; } = default!;
        public string? Value { get; set; }
        public string? FallbackValue { get; set; } = default!;
    }


    public class InteractiveMessageDto
    {
        public InteractiveType Type { get; set; }
        public MediaType mediaType { get; set; }
        public string? mediaFile { get; set; }
        public string? Header { get; set; }
        public string Body { get; set; } = default!;
        public string? Footer { get; set; }
        public List<ButtonDto>? Buttons { get; set; }
        public ListDto? List { get; set; }
        public List<VariableModelDto>? Variables { get; set; }
        public int? TimeOut { get; set; }
        public bool IsEnbaleSetTimeOut { get; set; } = false;

    }

    public class ListDto
    {
        public string? ButtonText { get; set; }
        public List<SectionDto>? Sections { get; set; }
    }
    public class SectionDto
    {
        public string? Title { get; set; }

        public List<RowDto>? Rows { get; set; }
    }
    public class RowDto
    {
        public string? Id { get; set; }
        public string Title { get; set; }
        public string? Description { get; set; }
    }

    public class ButtonDto
    {
        public string? Id { get; set; }
        public string Name { get; set; }
    }


    public class HttpRequestDto
    {
        public string Url { get; set; } = default!;
        public string Method { get; set; } = default!;
        public string ContentType { get; set; } = default!;
        public Dictionary<string, object>? Headers { get; set; }
        public Dictionary<string, object>? QueryParameters { get; set; }
        public Dictionary<string, object>? FormData { get; set; }
        public string? JsonBody { get; set; }
        public List<VariableModel>? VariableValues { get; set; }
    }

    public class ConditionDto
    {
        public string? Attribute { get; set; }
        public ConditionOperator Operator { get; set; }
        public string Value { get; set; } = string.Empty;
        public List<ButtonDto> Buttons { get; set; }
    }
    public class LeadSourceDto
    {
        public string? Source { get; set; }
        public List<string>? SubSource { get; set; }
    }
    public class LeadStatusDto
    {
        public string? Status { get; set; }
        public List<string>? SubStatus { get; set; }
    }
}
