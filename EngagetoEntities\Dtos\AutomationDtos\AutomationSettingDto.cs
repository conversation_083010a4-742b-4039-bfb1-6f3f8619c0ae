﻿using EngagetoEntities.Enums;

namespace EngagetoEntities.Dtos.AutomationDtos
{
    public class AutomationSettingDto : BaseAutomationSettingDto
    {
        public int Id { get; set; }
        public string? BusinessId { get; set; }
        public DateTime? CreatedAt { get; set; }
        public DateTime? UpdatedAt { get; set; }
        public Guid? CreatedBy { get; set; }
        public Guid? UpdatedBy { get; set; }
    }
    public class BaseAutomationSettingDto
    {
        public AutomationType AutomationType { get; set; }
        public List<WelcomeMessageSetting>? WelcomeSetting { get; set; }
        public List<OutOfficeSetting>? OutOfOfficeSetting { get; set; }
        public List<DelaySetting>? DelaySetting { get; set; }
    }
    public class ViewAutomationSettingDto : AutomationSettingDto { }
    public class EditAutomationSettingDto : AutomationSettingDto { }
    public class WelcomeMessageSetting : AutomationMessageSetting
    {
        public KeywordSetting? KeywordSettings { get; set; }
    }

    public class OutOfficeSetting : AutomationMessageSetting
    {
        public string? TimeZone { get; set; }
        public DateTime? OfficeTime { get; set; }
    }

    public class DelaySetting : AutomationMessageSetting
    {
        public int DelayMinute { get; set; }
        public int DelayHours { get; set; }
    }

    public class AutomationMessageSetting
    {

        public AutomationMessage? Message { get; set; }


        public AutomatonTemplate? Templates { get; set; }
    }

    public class KeywordSetting
    {
        public MessageSendType SendType {get;set;}
        public Condition Condition { get; set; }
        public List<string>? Keywords { get; set; }
    }

    public class AutomationMessage
    {
        public string Text { get; set; }
        public string? FileName { get; set; }
        public string? MediaUrl { get; set; }
        public List<BaseVariableDto>? BodyVariable { get; set; }
    }

    public class AutomatonTemplate
    {
        public Guid Id { get; set; } // template id
        public string? Name { get; set; }
        public List<BaseVariableDto>? BodyVariable { get; set; }
        public List<BaseVariableDto>? HeaderVariable { get; set; }
    }
}
