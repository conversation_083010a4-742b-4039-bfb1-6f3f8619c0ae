﻿using EngagetoEntities.Entities;
using Microsoft.EntityFrameworkCore;

namespace EngagetoEntities.UserEntities.Models
{
    public class ApplicationDBContext : DbContext
    {
        public ApplicationDBContext()
        {
        }
        public ApplicationDBContext(DbContextOptions<ApplicationDBContext> options)
            : base(options)
        {
        }
        // public DbSet<User> Users { get; set; }
        public DbSet<Role> Roles { get; set; }

        public DbSet<UserRole> UserRoles { get; set; }

        //  public DbSet<UserImages> UserImages { get; set; }
        //  public DbSet<NotificationDetails> NotificationPreferences { get; set; }
        // public DbSet<AccountDetails> ManageAccounts { get; set; }


        public DbSet<HelpCenter> HelpCenters { get; set; }
        /*  // public DbSet<NewFeatures> NewFeatures { get; set; }
          public DbSet<TutorialInfo> Tutorials { get; set; }
          public DbSet<TutorialVideosInfo> TutorialVideos { get; set; }
          // public DbSet<Language> Languages { get; set; }
          public DbSet<DemoDetails> BookDemos { get; set; }
          public DbSet<KeyboardShortcut> KeyboardShortcuts { get; set; }
          //   public DbSet<TeamDetails> TeamMembers { get; set; }
          // public DbSet<CompanyDetails> ManageClients { get; set; }
          //public DbSet<SubscriptionDetails> SubscriptionPlans { get; set; }
          //public DbSet<Payments> Payments { get; set; }

          //public DbSet<PaymentsDetails> RazorpayPayments { get; set; }*/

        public DbSet<Language> Languages { get; set; }
        public DbSet<LanguagePreference> UserLanguagePreferences { get; set; }

        public DbSet<ForgotPassword> ForgotPasswords { get; set; }
        // public DbSet<ComplianceInfo> ComplianceInfos { get; set; }
        // public DbSet<BusinessTypeDetails> BusinessTypes { get; set; }
        public DbSet<CountryDetails> CountryDetails { get; set; }
        public DbSet<Permissions> Permissions { get; set; }
        public DbSet<AssignPermissions> AssignPermissionsToRoleIds { get; set; }
        public DbSet<PaymentCardDetails> PaymentCardDetails { get; set; }
        //  public DbSet<BusinessDetails> BusinessDetails { get; set; }
        //public DbSet<ClientDetails> ClientDetails { get; set; }
        //  public DbSet<ClientsAccountDetails> ClientsAccountDetails { get; set; }
        //public DbSet<MenuEntities> MenuEntities { get; set; }
        // public DbSet<MenuRoleRelationshipEntities> MenuRoleRelationshipEntities { get; set; }
        // public DbSet<ClientTeamDetails> ClientTeamDetails { get; set; }
        //public DbSet<BusinessDetails_Meta> BusinessDetails_Metas { get; set; }
        public DbSet<Ahex_CRM_Users> Ahex_CRM_Users { get; set; }
        public DbSet<Ahex_CRM_BusinessDetails> Ahex_CRM_BusinessDetails { get; set; }
        public DbSet<RolePermissions> RolePermissions { get; set; }
        public DbSet<Payments> Payments { get; set; }
        // public DbSet<RazorpaySubscriptionPlan> Plans { get; set; }  
        public DbSet<MenuDetails> MenuDetails { get; set; }
        public DbSet<MenuwithRoleRelationDetails> MenuwithRoleRelationDetails { get; set; }

        public DbSet<SubscriptionPermissions> SubscriptionPermissions { get; set; }
        public DbSet<PlanEntities> PlanEntities { get; set; }
        public DbSet<PaymentWalletDetail> PaymentWalletDetails { get; set; }
        /* public DbSet<ActionEntities> ActionEntities { get; set; }
         public DbSet<ActionWisePermissions> ActionWisePermissions { get; set; }*/
        public DbSet<UserWalletEntity> UserWalletEntities { get; set; }
        public DbSet<TransactionHistoryEntity> TransactionHistoryEntities { get; set; }
        public DbSet<WalletEntity> WalletEntities { get; set; }
        public DbSet<Subscriptions> Subscriptions { get; set; }
        public DbSet<DiscountEntity> DiscountEntities { get; set; }
        public DbSet<WalletNotification> WalletNotificationEntities { get; set; }
        public DbSet<NotificationEntities> NotificationEntities { get; set; }
        public DbSet<UserNotificationEntities> UserNotificationEntities { get; set; }
        public DbSet<ConversationAnalyticsEntity> ConversationAnalyticsEntities { get; set; }
        public DbSet<EnvironmentUrlEntity> EnvironmentUrlEntities { get; set; }

        protected override void OnConfiguring(DbContextOptionsBuilder optionsBuilder)
        {
            optionsBuilder.EnableSensitiveDataLogging();

            base.OnConfiguring(optionsBuilder);
        }
        protected override void OnModelCreating(ModelBuilder modelBuilder)

        {
            /* var padmin = Guid.Parse("45AA1BA5-1507-47E2-888B-89D80CD41906");
             modelBuilder.Entity<UserRole>()
             .HasKey(ur => new { ur.Id, ur.RoleId });

             modelBuilder.Entity<Role>().HasData(

             new Role
             {
                 Sno = 1,
                 Id = Guid.Parse("55AA1BA5-1507-47E2-888B-89D80CD41906"),
                 Name = "Owner",
                 Level = 1,

             },
              new Role
              {
                  Sno = 2,
                  Id = padmin,
                  Name = "Admin",
                  Level = 2,

              },
             new Role
             {
                 Sno = 3,
                 Id = Guid.Parse("65AA1BA5-1507-47E2-888B-89D80CD41906"),
                 Name = "Teammate",
                 Level = 3,

             });
             var adminUserId = Guid.NewGuid();
             var adminPassword = "Ravi@2000";

             var companyId = Guid.Parse("99969011-7B1D-4C2D-92A6-FBA9CA31A261");
             modelBuilder.Entity<Ahex_CRM_Users>().HasData(new Ahex_CRM_Users
             {
                 Id = adminUserId,
                 RoleId = padmin.ToString(),
                 CompanyId = companyId.ToString(),
                 Password = HashPassword(adminPassword),
                 EmailAddress = "<EMAIL>",
                 PhoneNumber = "9160422130",
                 CountryCode = "+91",
                 Status = true,

             });

             modelBuilder.Entity<Ahex_CRM_BusinessDetails>().HasData(new Ahex_CRM_BusinessDetails
             {
                 Id = companyId,
                 Status = true,
             });

             modelBuilder.Entity<UserRole>().HasData(new UserRole
             {
                 Id = adminUserId,
                 RoleId = padmin,
             });*/

            /*   
               modelBuilder.Entity<UserRole>()
                   .HasKey(ur => new { ur.Id, ur.RoleId });

               base.OnModelCreating(modelBuilder);*/

            modelBuilder.Entity<PaymentWalletDetail>().ToTable("PaymentWalletDetails");

            modelBuilder.Entity<WalletEntity>().ToTable("WalletEntities");

            // Configure the default value for CreatedAt and UpdatedAt
            modelBuilder.Entity<WalletEntity>()
                .Property(w => w.CreatedAt)
                .HasDefaultValueSql("GETDATE()");

            modelBuilder.Entity<WalletEntity>()
                .Property(w => w.UpdatedAt)
                .HasDefaultValueSql("GETDATE()");

            modelBuilder.Entity<PaymentWalletDetail>()
                .Property(p => p.CreatedAt)
                .HasDefaultValueSql("GETDATE()");

            modelBuilder.Entity<PaymentWalletDetail>()
                .Property(p => p.UpdatedAt)
                .HasDefaultValueSql("GETDATE()");



        }
        private string HashPassword(string password)
        {

            return BCrypt.Net.BCrypt.HashPassword(password);
        }
    }
}
