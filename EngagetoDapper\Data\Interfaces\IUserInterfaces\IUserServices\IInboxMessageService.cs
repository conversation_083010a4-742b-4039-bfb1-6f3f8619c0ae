﻿using EngagetoEntities.Dtos;
using EngagetoEntities.Dtos.MetaDto;

namespace EngagetoDapper.Data.Interfaces
{
    public interface IInboxMessageService
    {
        Task<List<Object>> TextOrEmojiMessageAsync(TextData textData, Guid businessId);
        Task<List<Object>> SendMediaMessageAsync(MediaData mediaData, Guid businessId);
        Task<List<Object>> SendWhatsappMediaAsync(MediaData data, Object companyId);
        Task<List<Object>> SendWhatsappTextAsync(TextData data, Object companyId);
        Task<Object> GetUserDetailsAsync(Guid businessId, string contact);
        Task<bool> AssignChatAsync(Guid businessId, Guid? assignUserId, ContactN<PERSON>bers contacts, Guid currentUserId);
        Task<(List<EngagetoEntities.Entities.Conversations>?, WhatsAppErrorResponse?)> SendTextEmojiMediaMessageAsync(TextMediaMassages messages, Object companyId);
    }
}
