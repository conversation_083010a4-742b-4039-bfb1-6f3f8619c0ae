﻿using EngagetoEntities.Dtos.WebhookDtos;
using EngagetoEntities.Entities;
using EngagetoEntities.Enums;

namespace EngagetoEntities.Dtos.IntegrationDtos
{
    public class IntegrationPayloadForExternal
    {
        public Contacts? Contact { get; set; }
        public Conversations? Conversations { get; set; } = null;
        public WebhookResponseTemplateDto? Template { get; set; } = null; 
    }
    public record IntegrationAccountRecord(string businessId,
        Contacts? contact = null, 
        Conversations? conversation = null,
        WebhookResponseTemplateDto? template = null, 
        IntegrationPayloadForExternal? integration = null,
        List<IntegrationEvent>? @events = null);
}
