﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Security.Claims;
using System.Text;
using System.Threading.Tasks;

namespace EngagetoEntities.UserEntities.Models
{
    public class LogoutModel
    {
       
        public bool IsValid { get; private set; }
        public int StatusCode { get; private set; }
        public string Error { get; private set; }

        private LogoutModel(bool isValid, int statusCode, string error)
        {
            IsValid = isValid;
            StatusCode = statusCode;
            Error = error;
        }

        public static LogoutModel Success()
        {
            return new LogoutModel(true, 200, null);
        }

        public static LogoutModel Fail(string error, int statusCode)
        {
            return new LogoutModel(false, statusCode, error);
        }
    }
}
