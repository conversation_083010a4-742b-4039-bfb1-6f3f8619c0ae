﻿using EngagetoEntities.Entities;

namespace EngagetoBackGroundJobs.Helper
{
    public static class WalletHelper
    {
        public static Dictionary<string, decimal> CalculateConversationAnalyticsCost(List<ConversationAnalyticsEntity> conversations)
        {
            var result = conversations?
            .GroupBy(c => c.ConversationCategory)?
            .ToDictionary(
                g => g.Key,
                g => g.Sum(c => c.Cost)
            );

            return result;
        }

    }
}
