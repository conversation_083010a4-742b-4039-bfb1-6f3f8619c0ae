﻿using EngagetoEntities.Enums;
using System.ComponentModel.DataAnnotations.Schema;

namespace EngagetoEntities.Entities
{
    [Table("ChatStatusEntities")]
    public class ChatStatusEntity
    {
        public Guid Id { get; set; }
        public Guid ContactId { get; set; }
        public ChatStatus Status { get; set; }
        public DateTime Date { get; set; }
        public Guid? UserId { get; set; }
        public ChatStatusEntity()
        {
        }
        public ChatStatusEntity(Guid contactId, ChatStatus status, Guid? userId)
        {
            Id = Guid.NewGuid();
            ContactId = contactId;
            Status = status;
            Date = DateTime.UtcNow;
            UserId = userId;
        }
    }
}
