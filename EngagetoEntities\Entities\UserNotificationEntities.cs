﻿using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations.Schema;
using System.ComponentModel.DataAnnotations;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace EngagetoEntities.Entities
{
    [Table("UserNotificationEntities")]
    public class UserNotificationEntities
    {
        [Key]
        [DatabaseGenerated(DatabaseGeneratedOption.Identity)]
        public int Id { get; set; }
        public int NotificationId { get; set; }
        public string? UserId { get; set; }
        public string? CompanyId { get; set; }
        public bool? isActive { get; set; }
        public bool? isEmailSent { get; set; }
    }
}
