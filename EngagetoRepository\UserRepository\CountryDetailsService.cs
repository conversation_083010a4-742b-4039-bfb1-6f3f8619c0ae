﻿using Amazon.S3;
using Amazon.S3.Transfer;
using EngagetoContracts.UserContracts;
using EngagetoEntities.DdContext;
using EngagetoEntities.Entities;
using EngagetoEntities.UserEntities.Dtos;
using Microsoft.AspNetCore.Http;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Configuration;
using System.Text.RegularExpressions;

namespace EngagetoRepository.UserRepository
{
    public class CountryDetailsService : ICountryDetailsService
    {
        private readonly ApplicationDbContext _context;
        private readonly IConfiguration _configuration;

        public CountryDetailsService(IConfiguration config, ApplicationDbContext context)
        {
            _context = context;
            _configuration = config;
        }

        public async Task<Guid> AddCountryDetails(Guid currentUserId, string countryName, string countryCode, IFormFile countryImage)
        {
            /* var isAdminOrOwner = await CheckAdminOrOwnerRole(currentUserId);
             if (!isAdminOrOwner)
             {
                 return Guid.Empty;
             }*/
            var logoFileLink = await HandleFileUploadAsync(countryImage);
            var countryDetails = new CountryDetails
            {
                Id = Guid.NewGuid(),
                CountryName = countryName,
                CountryCode = countryCode,
                CountryImage = logoFileLink
            };

            _context.CountryDetails.Add(countryDetails);
            await _context.SaveChangesAsync();

            return countryDetails.Id;
        }

        public async Task<bool> UpdateCountryDetails(Guid currentUserId, Guid countryId, UpdateCountryDto updateCountryDto)
        {
            /* var isAdminOrOwner = await CheckAdminOrOwnerRole(currentUserId);
             if (!isAdminOrOwner)
             {
                 return false;
             }*/

            var countryDetails = await _context.CountryDetails.FindAsync(countryId);
            if (countryDetails == null)
            {
                return false;
            }

            if (!string.IsNullOrEmpty(updateCountryDto.CountryName))
            {
                if (!Regex.IsMatch(updateCountryDto.CountryName, @"^[a-zA-Z\s]+$"))
                {
                    throw new ArgumentException("Country name should only contain alphabets.");
                }
                countryDetails.CountryName = updateCountryDto.CountryName;
            }

            if (!string.IsNullOrEmpty(updateCountryDto.CountryCode))
            {
                if (!Regex.IsMatch(updateCountryDto.CountryCode, @"^\+\d{1,3}$"))
                {
                    throw new ArgumentException("Country code should start with '+' followed by digits.");
                }
                countryDetails.CountryCode = updateCountryDto.CountryCode;
            }
            if (updateCountryDto.CountryImage != null)
            {
                string newCountryImageLink = await UploadLogoAsync(updateCountryDto.CountryImage);
                if (newCountryImageLink != null)
                {
                    countryDetails.CountryImage = newCountryImageLink;
                }
            }
            await _context.SaveChangesAsync();
            return true;
        }

        public async Task<bool> DeleteCountryDetails(Guid currentUserId, Guid countryId)
        {
            /*  var isAdminOrOwner = await CheckAdminOrOwnerRole(currentUserId);
              if (!isAdminOrOwner)
              {
                  return false;
              }*/

            var countryDetails = await _context.CountryDetails.FindAsync(countryId);
            if (countryDetails == null)
            {
                return false;
            }

            _context.CountryDetails.Remove(countryDetails);
            await _context.SaveChangesAsync();
            return true;
        }

        /*  private async Task<bool> CheckAdminOrOwnerRole(Guid currentUserId)
          {
              return await _context.UserRoles
                  .Include(ur => ur.Role)
                  .AnyAsync(ur => ur.UserId == currentUserId && (ur.Role.Id == "Admin" || ur.Role.Id == "Owner"));
          }*/
        public async Task<CountryDetails> GetCountryByCode(string countryCode)
        {
            return await _context.CountryDetails.FirstOrDefaultAsync(cd => cd.CountryCode == countryCode);
        }

        public async Task<CountryDetails> GetCountryByCompanyName(string companyName)
        {
            return await _context.CountryDetails.FirstOrDefaultAsync(cd => cd.CountryName == companyName);
        }
        public async Task<List<CountryDetails>> GetAllCountries(string searchQuery)
        {
            var query = _context.CountryDetails.AsQueryable();

            if (!string.IsNullOrEmpty(searchQuery))
            {

                if (Guid.TryParse(searchQuery, out Guid id))
                {
                    query = query.Where(cd => cd.Id == id);
                }
                else
                {
                    query = query.Where(cd =>
                        cd.CountryName.Contains(searchQuery) ||
                        cd.CountryCode.Contains(searchQuery)
                    );
                }
            }
            query = query.OrderBy(cd => cd.CountryName);

            return await query.ToListAsync();
        }
        public async Task<List<GetCounrtyCodewithImage>> GetAllCountryCodesAsync()
        {
            var countryCodes = await _context.CountryDetails
                                                            .OrderBy(cd => cd.CountryName)
                                                            .Select(cd => new GetCounrtyCodewithImage
                                                            {
                                                                CountryCode = cd.CountryCode,
                                                                CountryImage = cd.CountryImage
                                                            })
                                                            .ToListAsync();

            return countryCodes;
        }
        public async Task<string?> UploadLogoAsync(IFormFile? file)
        {
            var countrydetails = await _context.CountryDetails.FirstOrDefaultAsync();

            if (countrydetails == null)
            {
                return null;
            }
            if (file == null)
            {
                return null;
            }

            var fileLink = await HandleFileUploadAsync(file);

            countrydetails.CountryImage = fileLink;

            await _context.SaveChangesAsync();

            return fileLink;
        }
        private async Task<string?> HandleFileUploadAsync(IFormFile? file)
        {
            if (file == null)
            {
                return null;
            }

            try
            {
                var accessKey = _configuration["Aws:AccessKey"];
                var secretKey = _configuration["Aws:SecretKey"];
                var bucketName = _configuration["Aws:BucketName"];
                var regionString = _configuration["Aws:Region"];
                var region = Amazon.RegionEndpoint.GetBySystemName(regionString);

                using (var client = new AmazonS3Client(accessKey, secretKey, region))
                {
                    var key = $"{Guid.NewGuid()}.{file.FileName.Split('.')[1]}";

                    using (var newMemoryStream = new MemoryStream())
                    {
                        await file.CopyToAsync(newMemoryStream);

                        var uploadRequest = new TransferUtilityUploadRequest
                        {
                            InputStream = newMemoryStream,
                            Key = key,
                            BucketName = bucketName,
                            CannedACL = S3CannedACL.PublicRead
                        };

                        using (var fileTransferUtility = new TransferUtility(client))
                        {
                            await fileTransferUtility.UploadAsync(uploadRequest);
                        }
                    }

                    var fileLink = $"https://{bucketName}.s3.{region.SystemName}.amazonaws.com/{key}";

                    return fileLink;
                }
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Error uploading file to S3: {ex.Message}");
                throw;
            }
        }
    }
}
