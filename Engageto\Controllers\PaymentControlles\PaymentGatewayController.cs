﻿using EngagetoContracts.Services;
using EngagetoEntities.Dtos.CommonDtos.PaymentGatwayDtos;
using EngagetoEntities.Entities;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;

namespace Engageto.Controllers.PaymentControlles
{
    [Route("api/[controller]")]
    [ApiController]
    public class PaymentGatewayController : BaseController
    {
        private readonly IPaymentService _paymentService;
        public PaymentGatewayController(IPaymentService paymentService)
        {
            _paymentService = paymentService;
        }

        [HttpPost("wallet-cashfree-order")]
        [Authorize]
        public async Task<IActionResult> CreateOrder(OrderDto order)
        {
            try
            {
                var result = await _paymentService.CreateOrderAsyn(order);
                return Ok(CreateSuccessResponse<CashfreeOrderDetailsDto>(result, "order created successfull."));
            }
            catch (Exception ex)
            {
                return BadRequest(CreateErrorResponse<string>(ex.Message, ex.StackTrace));
            }
        }

        [HttpGet("wallet-cashfree-order/{orderId}")]
        [Authorize]
        public async Task<IActionResult> GetOrder(string orderId)
        {
            try
            {
                var result = await _paymentService.FetchOrderAsync(orderId);
                return Ok(CreateSuccessResponse<UserWalletEntity>(result, "order Get successfull."));
            }
            catch (Exception ex)
            {
                return BadRequest(CreateErrorResponse<string>(ex.Message, ex.StackTrace));
            }
        }
    }
}
