﻿namespace EngagetoEntities.UserEntities.Dtos
{
    public class PlanDiscountDto
    {
        public int PlanId { get; set; }
        public int? Year { get; set; }
        public string PlanType { get; set; } = default!;
        public string DiscountType { get; set; } = default!;
        public decimal? WeeklyDiscount { get; set; }
        public decimal? MonthlyDiscount { get; set; }
        public decimal? QuarterlyDiscount { get; set; }
        public decimal? AnnuallyDiscount { get; set; }
        public bool IsDelete { get; set; } = false;
    }
}
