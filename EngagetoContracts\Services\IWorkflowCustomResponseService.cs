using EngagetoEntities.Dtos.WorkflowDtos;
using EngagetoEntities.Entities;

namespace EngagetoContracts.Services
{
    public interface IWorkflowCustomResponseService
    {
        Task<WorkflowCustomerResponseDto> CreateWorkflowCustomResponseAsync(WorkflowCustomerResponseDto request, Guid currentUserId);
        Task<WorkflowCustomerResponseDto> UpdateWorkflowCustomResponseAsync(Guid id, WorkflowCustomerResponseDto request, Guid currentUserId);
        Task<List<WorkflowCustomerResponseDto>> GetAllWorkflowCustomResponsesAsync();
        Task<WorkflowCustomerResponseDto> GetWorkflowCustomResponseByIdAsync(Guid id);
        Task<List<WorkflowCustomerResponseDto>> GetWorkflowCustomResponseByWorkflowIdAsync(Guid workflowId);
        Task<WorkflowCustomerResponse> GetWorkflowCustomResponseAsync(Guid  nodeId ,Guid workflowId,Guid businessId);
        Task<bool> DeleteCustomerResponseByAttributeIdAsync(Guid AttributeId);   
    }
} 