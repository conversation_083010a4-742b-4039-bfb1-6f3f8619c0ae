using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using System.Text.Json;

namespace EngagetoEntities.Entities
{
    [Table("WorkflowEdges")]
    public class WorkflowEdge : BaseEntity
    {
        [Key]
        public Guid Id { get; set; }
        public Guid WorkflowId { get; set; }
        public string? BusinessId { get; set; }
        public Guid SourceId { get; set; }
        public string SourceHandle { get; set; } = default!;
        public string TargetHandle { get; set; } = default!;
        public string Type { get; set; } = default!;
        public string? MarkerEndJson { get; set; }

        // Navigation properties
        [ForeignKey("WorkflowId")]
        public virtual Workflow Workflow { get; set; } = default!;

        [ForeignKey("SourceId")]
        public virtual WorkflowNode Source { get; set; } = default!;

        public virtual ICollection<WorkflowEdgeTargetNode> Targets { get; set; }
            = new List<WorkflowEdgeTargetNode>();

        [NotMapped]
        public MarkerEndModel MarkerEnd
        {
            get => string.IsNullOrEmpty(MarkerEndJson) 
                ? null 
                : JsonSerializer.Deserialize<MarkerEndModel>(MarkerEndJson);
            set => MarkerEndJson = value == null 
                ? null 
                : JsonSerializer.Serialize(value);
        }
    }
}
public class MarkerEndModel
{
    public string Type { get; set; } = default!;
    public int Width { get; set; }
    public int Height { get; set; }
    public string Color { get; set; } = default!;
}
