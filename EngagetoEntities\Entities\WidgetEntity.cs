﻿using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace EngagetoEntities.Entities
{
    [Table("WidgetEntities")]
    public class WidgetEntity
    {
        [Key]
        public Guid Id { get; set; }
        public string? BusinessId { get; set; }
        public Guid? UserId { get; set; }
        public string PhoneNumber { get; set; } 
        public string? CtaText { get; set; } 
        public string? ButtonBackground { get; set; } 
        public int? MarginBottom { get; set; }
        public int? MarginLeft { get; set; } 
        public int? MarginRight { get; set; } 
        public int? BorderRadius { get; set; } 
        public string? DefaultMessage { get; set; }
        public string? Position { get; set; } 

        public string BrandName { get; set; } 
        public string BrandSubtitle { get; set; }
        public string? BrandColor { get; set; } 
        public string? WidgetCtaText { get; set; }
        public string? BrandImageUrl { get; set; } 
        public string? DefaultOnScreenMessage { get; set; } 
        public string? OpenWidgetOnMobileScreen { get; set; } 
        public string? OpenWidgetByDefault { get; set; } 
        public string? StartChat { get; set; } 
        public virtual ICollection<WidgetUrlFieldEntity>? UrlFields { get; set; }
    }
}
