﻿using Engageto.Attributes;
using EngagetoContracts.MetaContracts;
using EngagetoContracts.TemplateContracts;
using EngagetoEntities;
using EngagetoEntities.DdContext;
using EngagetoEntities.Dtos.CommonDtos;
using EngagetoEntities.Dtos.MetaDto;
using EngagetoEntities.Dtos.TemplateDtos;
using EngagetoEntities.Response;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Mvc.ModelBinding;
using Newtonsoft.Json;
using System.ComponentModel.DataAnnotations;
using System.Runtime.InteropServices;
using System.Security.Claims;


namespace Engageto.Controllers.TemplateControllers
{
    [Route("api/[controller]")]
    [ApiController]
    public class TemplateController : ControllerBase
    {
        private readonly ITemplate _templateService;
        private readonly JsonSerializer _serializer = new JsonSerializer();
        private readonly EngagetoDapper.Data.Interfaces.IUserInterfaces.IUserServices.IUserService _userService;
        private readonly IMetaApiService _metaApiService;
        public IHttpClientFactory _httpClientFactory { get; set; }
        private IConfiguration _configuration { get; set; }
        public TemplateController(IHttpClientFactory _httpClient,
                                  IConfiguration _configurations,
                                  ITemplate templateService,
                                  ApplicationDbContext templateDbContext,
                                  EngagetoDapper.Data.Interfaces.IUserInterfaces.IUserServices.IUserService userService,
                                  IMetaApiService metaApiService)

        {
            _templateService = templateService;
            _httpClientFactory = _httpClient;
            _configuration = _configurations;
            _userService = userService;
            _metaApiService = metaApiService;
        }


        [HttpGet("check-template-name/{BusinessId}/{TemplateName}")]
        [Authorize]
        public async Task<IActionResult> CheckTemplateNames(string BusinessId, string TemplateName)
        {
            try
            {
                if (!ValidateAccount(BusinessId, out Guid currentUserId))
                    return BadRequest(new { Message = "Invalid current user." });

                await _templateService.IsTemplateExist(BusinessId, TemplateName);
                return Ok(new { Message = $"Template name '{TemplateName}' is valid" });
            }
            catch (Exception ex)
            {
                return StatusCode(500, new { Message = ex.Message });
            }
        }

        [HttpGet("auth-template-preview")]
        [Authorize]
        public async Task<IActionResult> GetAuthTemplatePreview([FromQuery] AuthTemplatePreviewRequestDto? requestDto)
        {
            try
            {
                requestDto = requestDto ?? new();
                var result = await _metaApiService.GetAuthTemplatePreviewAsync(requestDto);
                return Ok(new ApiResponse<List<BaseAuthTemplatePreviewDto>>()
                {
                    Success = true,
                    Data = result?.Data ?? new(),
                    Message = "Authentication template preview."
                });
            }
            catch (Exception ex)
            {
                return BadRequest(new ApiResponse<string>()
                {
                    Success = false,
                    Message = ex.Message,
                    Errors = ex.StackTrace
                });
            }
        }

        [Authorize]
        [HttpPost("create-auth-template")]
        public async Task<IActionResult> CreateAuthTemplate(string businessId, AuthTemplateDto dto, CancellationToken cancellationToken)
        {
            try
            {
                var currentUserNameClaim = User.Claims.FirstOrDefault(c => c.Type == "UserName");
                var currentUserIdClaim = User.Claims.FirstOrDefault(c => c.Type == ClaimTypes.Name);
                if (currentUserNameClaim == null || !Guid.TryParse(currentUserIdClaim?.Value, out Guid currentUserId))
                    throw new Exception("Invalid current user.");

                await _templateService.ProcessAuthTemplatePayloadAsync(dto, businessId, currentUserNameClaim.Value, currentUserId);
                return Ok(new ApiResponse<string>()
                {
                    Success = true,
                    Message = "Template created sucessfully."
                });
            }
            catch (Exception ex)
            {
                return BadRequest(new ApiResponse<string>() { Success = false, Message = ex.Message });
            }
        }

        [HttpPost("uploadFile")]
        [Authorize]
        public async Task<IActionResult> UploadMediaFile([FromForm] UploadFileDto file)
        {
            try
            {
                var result = await _templateService.UploadMediaFile(file);
                return Ok(result);
            }
            catch (Exception ex)
            {
                return BadRequest(new { Message = ex.Message });
            }
        }

        [HttpPost("CreateTemplate")]
        [Authorize]
        public async Task<IActionResult> CreateTemplateAsync([FromBody] CreateTemplateDto model, bool Draft)
        {
            try
            {
                if (!ValidateAccount(model.BusinessId, out Guid currentUserId))
                    return BadRequest(new { Message = "Invalid current user." });
                if (!Draft)
                {
                    var response = await _templateService.CreateTemplateAsync(model, Draft);
                    if (response.Category != model.Category)
                    {
                        return Ok(new { Message = "Template created successfully. The Template Category has been updated from 'Utility' to 'Marketing' as the associated content appears to be marketing-related." });
                    }
                    return Ok(new { Message = "Template Created Successfully" });
                }
                else
                {
                    return Ok(new { Message = "Template saved into draft" });
                }
            }
            catch (Exception ex)
            {
                return BadRequest(new { Message = ex.Message });
            }
        }

        [HttpPost("EditTemplate")]
        [Authorize]
        public async Task<IActionResult> EditTemplateAsync([FromBody] EditTemplateDto model)
        {
            try
            {
                if (!ValidateAccount(model.BusinessId, out Guid currentUserId))
                    return BadRequest(new { Message = "Invalid current user." });
                var response = await _templateService.EditTemplateAsync(model);
                if (response.IsSuccessStatusCode)
                {
                    return Ok(new { Message = "Template updated successfully" });
                }
                else
                {
                    var errorContent = await response.Content.ReadAsStringAsync();
                    var errorResponse = JsonConvert.DeserializeObject<EditTemplateErrorResponseDto>(errorContent);
                    return BadRequest(new
                    {
                        Message = errorResponse?.EditError.Message,
                    });

                }
            }
            catch (Exception ex)
            {
                return BadRequest(new { Message = ex.Message });
            }
        }


        [HttpPost("SendTemplate")]
        [Authorize]
        public async Task<IActionResult> SendTemplateByUsingContact([FromBody] SendTemplateDto model)
        {
            try
            {
                if (!ValidateAccount(model.BusinessId, out Guid currentUserId))
                    return BadRequest(new { Message = "Invalid current user." });
                var response = await _templateService.SendTemplateAsync(model);
                if (response.IsSuccessStatusCode)
                {
                    return Ok(new { Message = "Template sent successfully" });
                }
                else
                {
                    var errorContent = await response.Content.ReadAsStringAsync();
                    SendTemplateErrorResponseDto errorResponse;
                    using (var reader = new StringReader(errorContent))
                    using (var jsonReader = new JsonTextReader(reader))
                    {
                        errorResponse = _serializer.Deserialize<SendTemplateErrorResponseDto>(jsonReader) ?? new();
                    }

                    var SendTemplateErrorResponse = new SendTemplateErrorResponseDto
                    {
                        SendTemplateErrorMessage = new ErrorDetails
                        {
                            Message = errorResponse.SendTemplateErrorMessage.Message,
                            ErrorData = errorResponse.SendTemplateErrorMessage.ErrorData,
                        }
                    };
                    return BadRequest(new
                    {
                        Message = SendTemplateErrorResponse.SendTemplateErrorMessage.Message,

                    });
                }
            }
            catch (Exception ex)
            {
                return BadRequest(new { Message = ex.Message });
            }
        }



        [HttpPost("TemplateFilters")]
        [Authorize]
        public async Task<ActionResult<object>> GetAllItems([FromBody, Optional] FilterOperationsDto Operations, [FromQuery, Required] string BusinessId,
                                                              [FromQuery, BindRequired] Guid UserId, [Range(1, 100), Required, FromQuery] int page = 1,
                                                              [Range(1, 1000), Required, FromQuery] int per_page = 10)
        {
            try
            {
                var result = await _templateService.GetAllTemplateFilterAsync(Operations, BusinessId, UserId, page, per_page);
                return Ok(result);
            }
            catch (Exception ex)
            {
                return StatusCode(500, new { Message = ex.Message });
            }
        }

        [HttpGet("TemplatebyId")]
        [Authorize]
        public IActionResult GetTemplates([FromQuery, BindRequired] string BusinessId, [FromQuery, BindRequired] Guid UserId, [FromQuery] List<Guid>? id)
        {
            try
            {
                var result = _templateService.GetTemplateByIdAsync(id ?? new(), BusinessId, UserId);
                return Ok(result);
            }
            catch (Exception ex)
            {
                return BadRequest(new { Message = ex.Message });
            }
        }



        [HttpGet("GetApprovedTemplate")]
        [Header("Api-Key")]
        [ApiKeyAuthentication]
        public async Task<IActionResult> GetApprovedTemplate([FromQuery] string? TemplateName)
        {
            try
            {
                if (!HttpContext.Items.TryGetValue("CompanyId", out var companyId))
                {
                    return Unauthorized(new { Message = "API key is not valid" });
                }
                var templates = await _templateService.GetApprovedTemplates(companyId?.ToString() ?? string.Empty, TemplateName);
                if (templates.Any())
                {
                    return Ok(new ApiResponse<List<object>>
                    {
                        Message = "Templates retrieved successfully.",
                        Success = true,
                        Data = templates
                    });
                }
                else
                {
                    return NotFound(new ApiResponse<object>
                    {
                        Message = "No templates found.",
                        Success = false,
                        Data = null
                    });
                }
            }
            catch (Exception ex)
            {
                return BadRequest(new ApiResponse<object>
                {
                    Message = ex.Message,
                    Success = false,
                    Data = null
                });
            }
        }

        [HttpDelete("Delete-TemplateById")]
        [Authorize]
        public async Task<IActionResult> MarkTemplateAsDeleted([FromBody] DeleteTemplateDto model)
        {
            try
            {
                await _templateService.MarkTemplateAsDeletedAsync(model);
                return Ok(new ApiResponse<DeleteTemplateDto>
                {
                    Success = true,
                    Message = "Template is sucessfully deleted.",
                });
            }
            catch (Exception ex)
            {
                return BadRequest(new { Message = ex.Message });
            }
        }

        [HttpPost("Restored-TemplateById")]
        [Authorize]
        public async Task<IActionResult> RestoreDeletedTemplate([FromBody] RestoredTemplateDto model)
        {
            try
            {
                bool result = await _templateService.RestoredDeletedTemplate(model);
                return Ok(new ApiResponse<RestoredTemplateDto>
                {
                    Success = true,
                    Message = "Deleted Template retrieved successfully.",

                });
            }
            catch (Exception ex)
            {
                return BadRequest(new { Message = ex.Message });
            }
        }

        [HttpPost("SendWhatsappTemplate")]
        [Header("Api-Key")]
        [ApiKeyAuthentication]
        public async Task<IActionResult> SendWhatsappTemplate(TemplateRequestDto templateRequestDto)
        {
            try
            {
                var companyId = GetCompanyIdFromContext();
                _ = Guid.TryParse(companyId, out var businessId);
                var result = await _templateService.GetSendTemplateAsync(companyId, businessId, templateRequestDto);
                return Ok(new ApiResponse<List<EngagetoEntities.Entities.Conversations>>
                {
                    Message = "Template sent successfully.",
                    Success = true,
                    Data = new List<EngagetoEntities.Entities.Conversations> { result }
                });
            }
            catch (Exception ex)
            {
                return BadRequest(new ApiResponse<string>
                {
                    Message = ex.Message,
                    Success = false,
                    Errors = ex.Message
                });
            }
        }

        [HttpPost("SendAuthTemplate")]
        [Header("Api-Key")]
        [ApiKeyAuthentication]
        public async Task<IActionResult> SendAuthTemplateAsync(SendAuthTemplateDto templateDto)
        {
            try
            {
                var companyId = GetCompanyIdFromContext();
                var response = await _templateService.SendAuthTemplateServiceAsync(companyId, templateDto);

                return Ok(new ApiResponse<List<EngagetoEntities.Entities.Conversations>>
                {
                    Message = "Auth template sent to customer successfully.",
                    Success = true,
                    Data = new List<EngagetoEntities.Entities.Conversations> { response }
                });
            }
            catch (Exception ex)
            {
                return BadRequest(new ApiResponse<string>
                {
                    Message = "Error while sending auth template.",
                    Success = false,
                    Errors = ex.Message
                });
            }
        }

        [HttpPost("CreateCarouselTemplate")]
        [Authorize]
        public async Task<IActionResult> CreateTemplateAsync([FromBody] CarouselTemplateDto model, bool Draft)
        {
            try
            {
                if (!ValidateAccount(model.BusinessId, out Guid currentUserId))
                    return BadRequest(new { Message = "Invalid current user." });
                var response = await _templateService.CreateCarouselTemplateAsync(model, Draft);
                if (!Draft)
                {
                    if (response.IsSuccessStatusCode)
                    {
                        return Ok(new { Message = "Template Created Successfully" });
                    }
                    else
                    {
                        var errorContent = await response.Content.ReadAsStringAsync();
                        var errorResponse = JsonConvert.DeserializeObject<CreateTempateErrorResponseDto>(errorContent);
                        return BadRequest(new
                        {
                            Message = errorResponse?.ErrrorMessage.ErrorUserMsg,

                        });
                    }
                }
                else
                {
                    return Ok(new { Message = "Template saved into draft" });
                }

            }
            catch (Exception ex)
            {
                return BadRequest(new { Message = ex.Message });
            }
        }

        [HttpPost("SendCarouselTemplate")]
        [Authorize]
        public async Task<IActionResult> SendTemplateByUsingContact([FromBody] SendCarouselTemplateDto model)
        {
            try
            {
                var sendResponse = await _templateService.SendCarouselTemplateAsync(model);
                if (sendResponse.IsSuccessStatusCode)
                {
                    return Ok(new { Message = "Carousel template sent successfully." });
                }

                var errorContent = await sendResponse.Content.ReadAsStringAsync();
                var errorResponse = JsonConvert.DeserializeObject<SendTemplateErrorResponseDto>(errorContent);
                return BadRequest(new { Message = errorResponse?.SendTemplateErrorMessage.Message, ErrorData = errorResponse?.SendTemplateErrorMessage.ErrorData });
            }
            catch (Exception ex)
            {
                return BadRequest(new { Message = ex.Message });
            }
        }

        [HttpPost("SendAuthenticationTemplate")]
        [Authorize]
        public async Task<IActionResult> SendAuthenticationTemplate(SendAuthenticationTemplateDto templateDto)
        {
            try
            {
                string oneTimeOtp = "";
                var sendResponse = await _templateService.SendAuthenticationTemplateAsync(templateDto, oneTimeOtp);
                if (sendResponse.IsSuccessStatusCode)
                {
                    return Content("Authentication template sent successfully.");
                }

                var errorContent = await sendResponse.Content.ReadAsStringAsync();
                var errorResponse = JsonConvert.DeserializeObject<SendTemplateErrorResponseDto>(errorContent);
                return BadRequest(new { Message = errorResponse?.SendTemplateErrorMessage.Message, ErrorData = errorResponse?.SendTemplateErrorMessage.ErrorData });
            }
            catch (Exception ex)
            {
                throw new InvalidOperationException(ex.Message);
            }

        }


        #region HELPER METHOD
        private string GetCompanyIdFromContext()
        {
            if (!HttpContext.Items.TryGetValue("CompanyId", out var companyId))
                throw new InvalidOperationException("Api key is not valid");
            return companyId?.ToString() ?? string.Empty;
        }

        private bool ValidateAccount(string businessId, out Guid currentUserId)
        {
            currentUserId = Guid.Empty;
            var currentUserIdClaim = User.Claims.FirstOrDefault(c => c.Type == ClaimTypes.Name);

            var result = _userService.IsValidateMetaAccount(businessId, currentUserId);

            return currentUserIdClaim != null && Guid.TryParse(currentUserIdClaim.Value, out currentUserId);
        }
        #endregion

    }
}










