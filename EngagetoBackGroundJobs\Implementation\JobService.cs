﻿using EngagetoBackGroundJobs.Interfaces;
using Hangfire;
using Microsoft.Extensions.Logging;
using System.Linq.Expressions;

namespace EngagetoBackGroundJobs.Implementation
{
    public class JobService : IJobService
    {
        ILogger<JobService> _logger;
        public JobService(ILogger<JobService> logger)
        {
            _logger = logger;
        }

        public bool Delete(string jobId)
        {
            return BackgroundJob.Delete(jobId);
        }

        public string Enqueue(Expression<Func<Task>> methodCall)
        {
            return BackgroundJob.Enqueue(methodCall);
        }

        public bool Requeue(string jobId)
        {
            return BackgroundJob.Requeue(jobId);
        }

        public bool Reschedule(string jobId, DateTimeOffset enqueueAt)
        {
            return BackgroundJob.Reschedule(jobId, enqueueAt);
        }

        public string Schedule(Expression<Func<Task>> methodCall, DateTimeOffset enqueueAt)
        {
            return BackgroundJob.Schedule(methodCall, enqueueAt);
        }

        public string Schedule(Expression<Func<Task>> methodCall, TimeSpan delay)
        {
            _logger.LogInformation($"Job started to get the data. Conversation analytics cost: time {DateTime.Now}, {DateTime.Now.Millisecond}");
            return BackgroundJob.Schedule(methodCall, delay);
        }
        public string JobState(string? jobId)
        {
            string? jobState=string.Empty;
            if (jobId != null)
            {
                 jobState = JobStorage.Current.GetMonitoringApi().JobDetails(jobId)?.History[0].StateName;
               
            }
            return jobState;
        }
    }

}