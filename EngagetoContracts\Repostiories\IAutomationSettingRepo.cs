﻿using EngagetoContracts.Services;
using EngagetoEntities.Entities;

namespace EngagetoContracts.Repostiories
{
    public interface IAutomationSettingRepo : ITransientServiceWithScoped
    {
        Task<int> SaveAutomationSettingAsync(AutomationSetting settingDto);
        Task<AutomationSetting> GetAutomationSettingAsync(string businessId);
        Task<AutomationSetting> UpdateAutomationSettingAsync(AutomationSetting settingDto);
    }
}
