﻿
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using EngagetoEntities.Entities;

namespace EngagetoContracts.UserContracts
{
    public interface ITransactionHistoryService
    {
        public Task<TransactionHistoryEntity> CreateTransactionHistoryAsync(TransactionHistoryEntity transactionHistoryEntity);
        public Task<TransactionHistoryEntity> UpdateTransactionHistoryAsync(TransactionHistoryEntity transactionHistoryEntity);
        public Task<List<TransactionHistoryEntity>> GetTransactionHistoryByUserIdAsync(Guid userId);
        public Task<TransactionHistoryEntity> GetTransactionHistoryByOrderIdAsync(string orderId);
        public Task<List<TransactionHistoryEntity>> GetTrasactionHistoryAsync();
    }
}
