﻿namespace EngagetoEntities.UserEntities.Dtos
{
    public class AnalyticsOverviewFilterDto
    {
        public List<string>? Tags { get; set; }
        public List<string>? Events { get; set; }
        public DateTime? FromDate { get; set; } = DateTime.UtcNow.Date;
        public DateTime? ToDate { get; set; } = DateTime.UtcNow.Date;
        public bool? IsExportData { get; set; } = false;
        public List<Guid>? ContactIds { get; set; } = new();
    }

    public class InboxAnalyticsFilterDto
    {
        public List<string>? Tags { get; set; }
        public bool? IsExportData { get; set; } = false;
        public DateTime? FromDate { get; set; } = DateTime.UtcNow.Date;
        public DateTime? ToDate { get; set; } = DateTime.UtcNow.Date;
    }
}
