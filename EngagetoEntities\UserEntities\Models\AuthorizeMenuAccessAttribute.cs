﻿using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Mvc.Filters;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.DependencyInjection;
using System;
using System.Linq;
using System.Security.Claims;
using System.Threading.Tasks;
using EngagetoEntities.UserEntities.Models;

public class AuthorizeMenuAccessAttribute : Attribute, IAsyncAuthorizationFilter
{
    private readonly string _menuName;

    public AuthorizeMenuAccessAttribute(string menuName)
    {
        _menuName = menuName;
    }

    public async Task OnAuthorizationAsync(AuthorizationFilterContext context)
    {
        var dbContext = context.HttpContext.RequestServices.GetRequiredService<ApplicationDBContext>();

        // Get the logged-in user's ID from claims
        var requestingUserId = context.HttpContext.User.FindFirstValue(ClaimTypes.Name);
        if (string.IsNullOrEmpty(requestingUserId) || !Guid.TryParse(requestingUserId, out Guid userId))
        {
            context.Result = new UnauthorizedResult();
            return;
        }

        // Fetch the user from the database
        var user = await dbContext.Ahex_CRM_Users
            .FirstOrDefaultAsync(u => u.Id == userId);

        if (user == null)
        {
            context.Result = new UnauthorizedResult();
            return;
        }

        // Get the user's company ID
        var userCompany = user.CompanyId;

        // Fetch the menu from the database
        var menu = await dbContext.MenuDetails
            .FirstOrDefaultAsync(m => m.MenuName == _menuName);

        if (menu == null)
        {
            context.Result = new UnauthorizedResult();
            return;
        }

        // Check if the user is authorized to access the menu
        var isAuthorized = await IsMenuAuthorized(dbContext, menu.MenuId, user.Id, userCompany);
        if (!isAuthorized)
        {
            context.Result = new UnauthorizedResult();
            return;
        }
    }

    private async Task<bool> IsMenuAuthorized(ApplicationDBContext dbContext, int menuId, Guid userId, string userCompany)
    {
        // Get the latest plan ID for the user and company
        var latestSubscription = await dbContext.Subscriptions
            .Where(s => s.UserId == userId.ToString() && s.CompanyId == userCompany)
            .OrderByDescending(s => s.UpdatedAt)
            .FirstOrDefaultAsync();

        if (latestSubscription == null)
        {
            return false;
        }

        var planId = latestSubscription.PlanId;

        // Check if the plan has access to the menu and if it is active
        var isAuthorized = await dbContext.SubscriptionPermissions
            .AnyAsync(sp => sp.PlanId == planId && sp.MenuId == menuId && sp.isActive);

        return isAuthorized; 
    }
}
