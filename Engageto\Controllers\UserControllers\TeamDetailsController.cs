﻿using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using Microsoft.EntityFrameworkCore;
using System.Security.Claims;
using EngagetoContracts.UserContracts;
using EngagetoEntities.UserEntities.Models;
using EngagetoRepository.UserRepository;
using EngagetoEntities.UserEntities.Dtos;

namespace Engageto.Controllers.UserControllers
{
    [Route("api/[controller]")]
    [ApiController]
    public class TeamDetailsController : ControllerBase
    {
        private readonly ITeamDetailsService _teamRepository;
        private readonly ApplicationDBContext _dbContext;

        public TeamDetailsController(ITeamDetailsService teamRepository, ApplicationDBContext dbContext)
        {
            _teamRepository = teamRepository;
            _dbContext = dbContext;
        }

        [HttpGet("get-all")]
        [Authorize]
        //[AuthorizeRoles(RoleConstants.Admin, RoleConstants.Owner, RoleConstants.Teammate)]
        public async Task<IActionResult> GetTeamMembers([FromQuery] string searchQuery = null,
      [FromQuery] bool? isActive = true,
      [FromQuery] string sortBy = null,
      [FromQuery] bool isSortAscending = true)
        {
            try
            {
                var currentUserIdClaim = User.Claims.FirstOrDefault(c => c.Type == ClaimTypes.Name);

                if (currentUserIdClaim == null || !Guid.TryParse(currentUserIdClaim.Value, out Guid currentUserId))
                {
                    return BadRequest(new { Message = "Invalid current user." });
                }
                bool includeInactiveValue = isActive ?? true;
                var teamMembers = await _teamRepository.GetTeamMembersAsync(currentUserId, searchQuery, includeInactiveValue, sortBy, isSortAscending);

                if (teamMembers != null)
                {
                    return Ok(teamMembers);
                }
                else
                {
                    return BadRequest(new { Message = "Failed to retrieve team members." });
                }
            }
            catch (Exception ex)
            {
                return StatusCode(500, new { Message = $"Internal Server Error: {ex.Message}" });
            }
        }
        [HttpGet("{companyId}")]
        [Authorize]
        // [AuthorizeRoles(RoleConstants.Admin, RoleConstants.Owner, RoleConstants.Teammate)]
        public async Task<IActionResult> GetTeamDetailsByCompanyId(string companyId, [FromQuery] bool? isActive = null, [FromQuery] string searchQuery = null, [FromQuery] string sortBy = null, bool ascending = true)
        {
            try
            {
                var currentUserIdClaim = User.Claims.FirstOrDefault(c => c.Type == ClaimTypes.Name);

                if (currentUserIdClaim == null || !Guid.TryParse(currentUserIdClaim.Value, out Guid currentUserId))
                {
                    return BadRequest(new { Message = "Invalid current user." });
                }

                if (!Request.Query.ContainsKey("isActive"))
                {
                    isActive = null;
                }

                var teamDetails = await _teamRepository.GetTeamDetailsByCompanyIdAsync(companyId, isActive, searchQuery, sortBy, ascending);

                return Ok(teamDetails);
            }
            catch (InvalidOperationException ex)
            {
                return NotFound(new { Message = ex.Message });
            }
            catch (Exception ex)
            {
                return StatusCode(500, new { Message = $"Internal Server Error: {ex.Message}" });
            }
        }
        [HttpPost("add-teammember")]
        [Authorize]
        //[AuthorizeRoles(RoleConstants.Admin, RoleConstants.Owner)]
        // [AuthorizeMenu("addTeamMember")]
        public async Task<IActionResult> AddTeamMember([FromBody] User_TeamMember teamMemberRequest)
        {
            try
            {

                var currentUserIdClaim = User.Claims.FirstOrDefault(c => c.Type == ClaimTypes.Name);

                if (currentUserIdClaim == null || !Guid.TryParse(currentUserIdClaim.Value, out Guid currentUserId))
                {
                    return BadRequest(new { Message = "Invalid current user." });
                }
                var existingTeamMember = await _dbContext.Ahex_CRM_Users.FirstOrDefaultAsync(u => u.EmailAddress == teamMemberRequest.Email && u.Status == true);

                if (existingTeamMember != null)
                {
                    throw new InvalidOperationException("Team member with this email is already registered.");
                }
                var existingMobile = await _dbContext.Ahex_CRM_Users.FirstOrDefaultAsync(u => u.PhoneNumber == teamMemberRequest.PhoneNumber  && u.Status == true);
                if (existingMobile != null)
                {
                    throw new InvalidOperationException("Team member with this phone number is already registered.");
                }
                var result = await _teamRepository.AddTeamMemberAsync(currentUserId, teamMemberRequest);

                if (result)
                    return Ok(new { Message = "Team member added successfully." });
                else
                    return BadRequest(new { Message = "Failed to add team member." });
            }
            catch (InvalidOperationException ex)
            {
                return BadRequest(new { Message = ex.Message });
            }
            catch (Exception ex)
            {
                return StatusCode(500, new { Message = "Internal Server Error" });
            }
        }

        [HttpPost("add-client-teammember")]
        [Authorize]
        // [AuthorizeRoles(RoleConstants.Admin, RoleConstants.Owner, RoleConstants.Teammate)]
        // [AuthorizeMenu("addMember")]
        public async Task<IActionResult> AddClientTeamMember([FromBody] User_ClientTeamMember teamMemberRequest)
        {
            try
            {

                var currentUserIdClaim = User.Claims.FirstOrDefault(c => c.Type == ClaimTypes.Name);

                if (currentUserIdClaim == null || !Guid.TryParse(currentUserIdClaim.Value, out Guid currentUserId))
                {
                    return BadRequest(new { Message = "Invalid current user." });
                }
                var existingTeamMember = await _dbContext.Ahex_CRM_Users.FirstOrDefaultAsync(u => u.EmailAddress == teamMemberRequest.Email);
                if (existingTeamMember != null)
                {
                    throw new InvalidOperationException("Team member with this email is already registered.");
                }
                var existingMobile = await _dbContext.Ahex_CRM_Users.FirstOrDefaultAsync(u => u.PhoneNumber == teamMemberRequest.PhoneNumber);
                if (existingMobile != null)
                {
                    throw new InvalidOperationException("Team member with this phone number is already registered.");
                }
                var result = await _teamRepository.AddClientTeamMemberAsync(currentUserId, teamMemberRequest);

                if (result)
                    return Ok(new { Message = "Team member added successfully." });
                else
                    return BadRequest(new { Message = "Failed to add team member." });
            }
            catch (InvalidOperationException ex)
            {
                return BadRequest(new { Message = ex.Message });
            }
            catch (Exception ex)
            {
                return StatusCode(500, new { Message = "Internal Server Error" });
            }
        }

        [HttpPut("updateTeamMember/{teamMemberId}")]
        [Authorize]
       // [AuthorizeRoles(RoleConstants.Admin, RoleConstants.Owner)]
        // [AuthorizeMenu("editTeamMember", "editMember")]
        // [AuthorizeMenuAccess("deleteTeamMember")]
        public async Task<IActionResult> UpdateTeamMember(Guid teamMemberId, [FromBody] User_TeamMemberUpdate updatedTeamMemberRequest)
        {
            try
            {
                var currentUserIdClaim = User.Claims.FirstOrDefault(c => c.Type == ClaimTypes.Name);
                if (currentUserIdClaim == null || !Guid.TryParse(currentUserIdClaim.Value, out Guid currentUserId))
                {
                    return BadRequest(new { Message = "Invalid current user." });
                }
                var existing = _dbContext?.Ahex_CRM_Users?.FirstOrDefault(m => m.Id == teamMemberId);
                var existingTeamMembers = _dbContext?.Ahex_CRM_Users?.Select(m => new { m.EmailAddress, m.PhoneNumber }).ToList();
                List<string> existingEmails = existingTeamMembers?.Select(m => m.EmailAddress).ToList();
                existingEmails.Remove(existing?.EmailAddress);
                if (existingEmails.Contains(updatedTeamMemberRequest?.Email))
                {
                    throw new InvalidOperationException("Team member with this email is already registered.");
                }
                List<string> existingPhones = existingTeamMembers?.Select(m => m.PhoneNumber).ToList();
                existingPhones.Remove(existing?.PhoneNumber);
                if (existingPhones.Contains(updatedTeamMemberRequest?.PhoneNumber))
                {
                    throw new InvalidOperationException("Team member with this phone number is already registered.");
                }
                var result = await _teamRepository.UpdateTeamMemberAsync(currentUserId, teamMemberId, updatedTeamMemberRequest);

                if (result)
                    return Ok(new { Message = "Team member updated successfully." });
                else
                    return BadRequest(new { Message = "Failed to update team member." });
            }
            catch (InvalidOperationException ex)
            {
                return BadRequest(new { Message = ex.Message });
            }
            catch (Exception ex)
            {
                return StatusCode(500, new { Message = "Internal Server Error" });
            }
        }

        [HttpDelete("delete-team-member/{teamMemberId}")]
        [Authorize]
        //[AuthorizeRoles(RoleConstants.Admin, RoleConstants.Owner)]
        //[AuthorizeMenu("deleteTeamMember", "deleteMember")]
        public async Task<IActionResult> DeleteTeamMember(Guid teamMemberId)
        {
            try
            {
                var adminUserIdClaim = User.Claims.FirstOrDefault(c => c.Type == ClaimTypes.Name);

                if (adminUserIdClaim == null || !Guid.TryParse(adminUserIdClaim.Value, out Guid adminUserId))
                {
                    return BadRequest(new { Message = "Invalid admin user." });
                }

                var isDeleted = await _teamRepository.DeleteTeamMemberAsync(adminUserId, teamMemberId);

                if (isDeleted)
                {
                    return Ok(new { Message = "Team member deleted successfully." });
                }
                else
                {
                    return BadRequest(new { Message = "Failed to delete team member." });
                }
            }
            catch (Exception ex)
            {
                return StatusCode(500, new { Message = $"Internal Server Error: {ex.Message}" });
            }
        }
    }
}
