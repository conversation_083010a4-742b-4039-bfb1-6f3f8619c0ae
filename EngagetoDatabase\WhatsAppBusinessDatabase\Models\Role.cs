﻿using System;
using System.Collections.Generic;

namespace EngagetoDatabase.WhatsAppBusinessDatabase.Models;

public partial class Role
{
    public Guid Id { get; set; }

    public int Sno { get; set; }

    public string Name { get; set; } = null!;

    public int Level { get; set; }

    public string? CompanyId { get; set; }

    public virtual ICollection<UserRole> UserRoles { get; set; } = new List<UserRole>();
}
