﻿using EngagetoEntities.Enums;
using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace EngagetoEntities.Entities
{
    [Table("VeriableEntities")]
    public class VeriableEntity : BaseEntity
    {
        [Key]
        public Guid Id { get; set; }

        [Required]
        public Guid ReferenceId { get; set; }

        [Required]
        public int Index { get; set; }

        public string? Veriable { get; set; }

        public string? Value { get; set; }

        public string? FallbackValue { get; set; }
        public VariableType? Type { get; set; }

        public ReferenceTableType? ReferenceTableType { get; set; }
        public Guid? CompanyId { get; set; }

        public Guid? UserId { get; set; }
    }

}
