﻿using EngagetoContracts.Services;
using EngagetoDapper.Data.Interfaces.GenericInterfaces;
using EngagetoEntities.Dtos.SubscriptionDtos;
using EngagetoEntities.Entities;
using EngagetoEntities.UserEntities.Models;
using Microsoft.EntityFrameworkCore;

namespace EngagetoRepository.Services
{
    public class SubscriptionService : ISubscriptionService
    {
        private readonly IGenericRepository _genericRepository;
        private readonly ApplicationDBContext _applicationDbContext;
        public SubscriptionService(IGenericRepository genericRepository, ApplicationDBContext applicationDbContext)
        {
            _genericRepository = genericRepository;
            _applicationDbContext = applicationDbContext;
        }

        public async Task<TenantSubscriptionDto?> GetTenantSubscriptionAsync(string tenantId)
        {
            try
            {
                var businessDetails = (await _genericRepository.GetByObjectAsync<Ahex_CRM_BusinessDetails>(new Dictionary<string, object> { { "TenantId", tenantId } }))?.FirstOrDefault();
                if (businessDetails == null) return null;

                var plans = await _applicationDbContext.PlanEntities.ToListAsync();
                var subscriptionWithPayments = await (
                        from subscription in _applicationDbContext.Subscriptions
                        join plan in _applicationDbContext.PlanEntities
                            on subscription.PlanId equals plan.Id
                        join payment in _applicationDbContext.PaymentWalletDetails
                            on subscription.Id equals payment.SubscriptionId into paymentGroup
                        select new
                        {
                            Subscription = subscription,
                            Plan = plan,
                            Payments = paymentGroup.ToList()
                        })
                        .FirstOrDefaultAsync();

                TenantSubscriptionDto? tenantSubscriptionDto = new()
                {
                    TenantId = tenantId,
                    PlanName = subscriptionWithPayments?.Plan.PlanName,
                    PlanType = subscriptionWithPayments?.Subscription.DurationType,
                    Id = subscriptionWithPayments?.Subscription.Id,
                    StartDate = subscriptionWithPayments?.Subscription.RenewStartDate ?? subscriptionWithPayments?.Subscription.PlanStartDate ?? DateTime.MinValue,
                    EndDate = subscriptionWithPayments?.Subscription.RenewEndDate ?? subscriptionWithPayments?.Subscription.PlanEndDate ?? DateTime.MinValue,
                    IsActive = (subscriptionWithPayments?.Subscription.RenewEndDate ?? subscriptionWithPayments?.Subscription.PlanEndDate ?? DateTime.MinValue).Date >= DateTime.UtcNow.Date,
                    PaymentDetails = subscriptionWithPayments?.Payments.Select(i => new PaymentDetailsDto
                    {
                        Id = i.Id,
                        Amount = i.OrderAmount,
                        IgstAmount = i.IGSTAmount,
                        TotalAmount = i.TotalAmount,
                        CreatedAt = i.CreatedAt,
                        PlanName = plans.FirstOrDefault(x => x.Id == i.PlanId)?.PlanName,
                        PlanType = i.DurationType
                    }).ToList()
                };
                return tenantSubscriptionDto;
            }
            catch (Exception ex)
            {
                throw;
            }
        }
    }
}
