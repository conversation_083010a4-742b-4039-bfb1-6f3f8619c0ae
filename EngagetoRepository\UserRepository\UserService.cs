﻿using Amazon.S3;
using Amazon.S3.Transfer;
using EngagetoContracts.GeneralContracts;
using EngagetoContracts.UserContracts;
using EngagetoEntities.Entities;
using EngagetoEntities.UserEntities.Models;
using Microsoft.AspNetCore.Http;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Configuration;

namespace EngagetoRepository.UserRepository
{
    public class UserService : IUserService
    {
        private readonly ApplicationDBContext _context;
        private readonly IHttpContextAccessor _httpContextAccessor;
        private readonly IEmailService _emailService;
        private readonly IConfiguration _configuration;
        private readonly string _websiteLink;
        IEnvironmentService _environmentService;
        public UserService(IConfiguration config, ApplicationDBContext context, IHttpContextAccessor httpContextAccessor, IEmailService emailService, IEnvironmentService environmentService)
        {
            _context = context;
            _httpContextAccessor = httpContextAccessor;
            _emailService = emailService;
            _configuration = config;
            _environmentService = environmentService;

            if (_environmentService.IsDevelopment)
                _websiteLink = _environmentService.GetDevWebsiteLink();
            else
                _websiteLink = _environmentService.GetProdWebsiteLink();
        }



        public async Task<bool> UpdateAssignedRoleAsync(string email, string companyId, Guid newRoleId)
        {
            try
            {
                var user = await _context.Ahex_CRM_Users
                    .FirstOrDefaultAsync(u => u.EmailAddress == email && u.CompanyId == companyId);

                if (user == null)
                {
                    return false;
                }

                var userRoles = await _context.UserRoles
                                              .Where(ur => ur.Id == user.Id)
                                              .ToListAsync();

                _context.UserRoles.RemoveRange(userRoles);

                var newUserRole = new UserRole
                {
                    Id = user.Id,
                    RoleId = newRoleId
                };

                _context.UserRoles.Add(newUserRole);

                await _context.SaveChangesAsync();

                // Notification for the user whose role is changed
                string userNotificationName = "Let me know when user role is changed";
                int? userNotificationId = await GetNotificationIdAsync(userNotificationName);

                var userNotification = await _context.UserNotificationEntities
                    .FirstOrDefaultAsync(un => un.UserId.ToLower() == user.Id.ToString().ToLower() &&
                                                un.CompanyId.ToLower() == user.CompanyId.ToLower() &&
                                                un.isActive == true &&
                                                un.NotificationId == userNotificationId);
                if (userNotification != null)
                {
                    await _emailService.SendEmailAsync(
                        user.EmailAddress,
                        "Role Updated Successfully",
                        $"<p>Hi {user.Name},</p>" +
                        $"<p>Your role has been successfully updated to the new role.</p>",
                        _configuration["SmtpSettings:LogoUrl"],
                        _websiteLink
                    );
                }

                // Fetch the admin user's details
                var adminUser = await _context.Ahex_CRM_Users.FindAsync(user.Id); // Assuming adminUserId is the same as the userId who changed the role
                if (adminUser != null)
                {
                    string adminNotificationName = "Get notified when user role is changed";
                    int? adminNotificationId = await GetNotificationIdAsync(adminNotificationName);

                    var adminUserNotification = await _context.UserNotificationEntities
                        .FirstOrDefaultAsync(un => un.UserId.ToLower() == adminUser.Id.ToString().ToLower() &&
                                                    un.CompanyId.ToLower() == adminUser.CompanyId.ToLower() &&
                                                    un.isActive == true &&
                                                    un.NotificationId == adminNotificationId);
                    if (adminUserNotification != null)
                    {
                        await _emailService.SendEmailAsync(
                            adminUser.EmailAddress,
                            "User Role Updated Successfully",
                            $"<p>Hi {adminUser.Name},</p>" +
                            $"<p>You have successfully updated the role of the user {user.Name}.</p>",
                            _configuration["SmtpSettings:LogoUrl"],
                            _websiteLink
                        );
                    }
                }

                return true;
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Error updating assigned role: {ex.Message}");
                return false;
            }
        }



        public async Task<bool> DeleteAssignedRolesAsync(Guid currentUserId, Guid targetUserId, Guid roleId)
        {
            try
            {

                var user = await _context.Ahex_CRM_Users.Include(u => u.UserRoles)
                    .FirstOrDefaultAsync(u => u.Id == targetUserId);

                if (user != null)
                {
                    var userRoleToDelete = user.UserRoles.FirstOrDefault(ur => ur.RoleId == roleId);

                    if (userRoleToDelete != null)
                    {

                        user.UserRoles.Remove(userRoleToDelete);
                        await _context.SaveChangesAsync();
                        return true;
                    }
                }

                return false;
            }
            catch (Exception)
            {
                return false;
            }
        }


        public async Task<bool> UpdateUserPasswordAsync(string email, string newPassword, string confirmPassword, string temporaryPassword)
        {
            try
            {
                var manageAccountUser = await _context.Ahex_CRM_Users.FirstOrDefaultAsync(u => u.EmailAddress == email);

                if (!VerifyPassword(temporaryPassword, manageAccountUser.Password))
                {
                    return false;
                }

                if (newPassword != confirmPassword)
                {
                    return false;
                }

                string newPasswordHash = HashPassword(newPassword);
                manageAccountUser.Password = newPasswordHash;

                await _context.SaveChangesAsync();

                string notificationName = "Let me know when user password is updated";
                int? notificationId = await GetNotificationIdAsync(notificationName);

                var userNotification = await _context.UserNotificationEntities
                                                    .FirstOrDefaultAsync(un => un.UserId.ToLower() == manageAccountUser.Id.ToString().ToLower() &&
                                                                                 un.CompanyId.ToLower() == manageAccountUser.CompanyId.ToLower() &&
                                                                                 un.isActive == true &&
                                                                                 un.NotificationId == notificationId);
                if (userNotification != null)
                {

                    await _emailService.SendEmailAsync(
                                   manageAccountUser.EmailAddress,
                                   "Password Updated Successfully",
                                   $"<p>Hi {manageAccountUser.Name},</p>" +
                                   $"<p>Your password has been successfully updated.</p>",
                                   _configuration["SmtpSettings:LogoUrl"],
                                   _websiteLink
                    );
                }
                return true;

            }
            catch (Exception)
            {
                return false;
            }
        }
        public async Task<int?> GetNotificationIdAsync(string notificationName)
        {

            var notification = await _context.NotificationEntities
                .FirstOrDefaultAsync(n => n.Name == notificationName);


            return notification?.Id;
        }

        private string GenerateTemporaryPassword()
        {

            const string allowedChars = "abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ1234567890";
            const int passwordLength = 12;

            Random random = new Random();
            char[] passwordArray = new char[passwordLength];

            for (int i = 0; i < passwordLength; i++)
            {
                passwordArray[i] = allowedChars[random.Next(0, allowedChars.Length)];
            }

            return new string(passwordArray);
        }
        private string HashPassword(string password)
        {

            return BCrypt.Net.BCrypt.HashPassword(password);
        }

        private bool VerifyPassword(string password, string hashedPassword)
        {

            return BCrypt.Net.BCrypt.Verify(password, hashedPassword);
        }

        private async Task<string> HandleFileUploadAsync(IFormFile file, Guid userId)
        {
            try
            {
                var accessKey = _configuration["Aws:AccessKey"];
                var secretKey = _configuration["Aws:SecretKey"];
                var bucketName = _configuration["Aws:BucketName"];
                var regionString = _configuration["Aws:Region"];
                var region = Amazon.RegionEndpoint.GetBySystemName(regionString);

                using (var s3Client = new AmazonS3Client(accessKey, secretKey, region))
                {
                    var key = $"{userId}.{file.FileName.Split('.')[1]}";
                    using (var newMemoryStream = new MemoryStream())
                    {
                        await file.CopyToAsync(newMemoryStream);

                        var uploadRequest = new TransferUtilityUploadRequest
                        {
                            InputStream = newMemoryStream,
                            Key = key,
                            BucketName = bucketName,
                            CannedACL = S3CannedACL.PublicRead
                        };

                        using (var fileTransferUtility = new TransferUtility(s3Client))
                        {
                            await fileTransferUtility.UploadAsync(uploadRequest);
                        }
                    }

                    var fileLink = $"https://{bucketName}.s3.{region.SystemName}.amazonaws.com/{key}";
                    return fileLink;
                }
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Error uploading file to S3: {ex.Message}");
                return null;
            }
        }

        public async Task<AssignRoleResult> AssignRoleToUserAsync(Guid currentUserId, Guid targetUserId, Guid roleId)
        {
            try
            {
                var user = await _context.Ahex_CRM_Users.FirstOrDefaultAsync(u => u.Id == targetUserId);
                var role = await _context.Roles.FirstOrDefaultAsync(r => r.Id == roleId);

                if (role != null && user != null)
                {

                    user.RoleId = roleId.ToString();
                    var newUserRole = new UserRole
                    {
                        Id = targetUserId,
                        RoleId = roleId
                    };

                    _context.UserRoles.Add(newUserRole);

                    await _context.SaveChangesAsync();

                    var permissionIds = await GetPermissionIdsAsync();


                    foreach (var permissionId in permissionIds)
                    {
                        var assignPermission = new AssignPermissions
                        {
                            Id = Guid.NewGuid(),
                            RoleId = roleId,
                            PermissionId = permissionId,
                            CompanyId = user.CompanyId,
                            Status = false,

                        };

                        _context.AssignPermissionsToRoleIds.Add(assignPermission);
                    }

                    await _context.SaveChangesAsync();
                    return new AssignRoleResult { Success = true, Message = "Role assigned successfully." };
                }

                return new AssignRoleResult { Success = false, Message = "Failed to assign role to user." };
            }
            catch (Exception ex)
            {
                return new AssignRoleResult { Success = false, Message = "Internal Server Error" };
            }
        }

        public async Task<IEnumerable<Guid>> GetPermissionIdsAsync()
        {
            try
            {

                var permissions = await _context.Permissions.ToListAsync();


                var permissionIds = permissions.Select(p => p.Id).ToList();

                return permissionIds;
            }
            catch (Exception ex)
            {

                Console.WriteLine($"Error fetching permission IDs: {ex.Message}");
                return Enumerable.Empty<Guid>();
            }
        }


    }
}
