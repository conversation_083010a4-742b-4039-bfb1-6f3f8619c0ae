﻿
using System.ComponentModel;

namespace EngagetoEntities.UserEntities.Dtos
{
    public class ContactDto
    {
        public Guid ContactId { get; set; }
        public string Name { get; set; } = default!;
        public string? BusinessId { get; set; }
        public Guid? UserId { get; set; }
        public string? Contact { get; set; }
        public ChatStatus ChatStatus { get; set; }
        public string? Assignee { get; set; }
        public string? Tags { get; set; }
        public string? LastMessage { get; set; }
        public DateTime? LastMessageAt { get; }
        public bool? IsSpam { get; set; }
        public int? Unread { get; set; }
    }

    public enum ChatStatus
    {
        [Description("open")]
        Open,
        [Description("closed")]
        Closed,
        [Description("expired")]
        Expired,
        [Description("new")]
        New 
    }

    public class ContactsIds
    {
        public Guid[] ContactIds { get; set; } = new Guid[0];
    }

    public class ContactsTagsUpdate
    {
        public Guid[] TagIds { get; set; } = new Guid[0];
        public List<Guid> ContactIds { get; set; } = new();
    }

    public class TagsDto
    {
        public string Tag { get; set; } = default!;
    }

    public class NotesDto
    {
        public Guid ContactId { get; set; }
        public List<string> Note { get; set; } = new();
    }

    public class ContactsTagsRemoveDto
    {
        public string[] Tag { get; set; } = default!;
        public List<Guid> ContactIds { get; set; } = new();
    }

    public class TagsIds
    {
        public Guid[] TagIds { get; set; } = default!;
    }
}
