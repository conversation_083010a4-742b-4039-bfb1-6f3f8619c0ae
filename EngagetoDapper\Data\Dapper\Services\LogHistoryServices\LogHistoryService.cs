﻿using EngagetoDapper.Data.Dtos;
using EngagetoDapper.Data.Interfaces.GenericInterfaces;
using EngagetoDapper.Data.Interfaces.ILogInterfaces;
using EngagetoEntities.Entities;
using EngagetoEntities.Enums;
using Newtonsoft.Json;

namespace EngagetoDapper.Data.Dapper.Services.LogHistoryServices
{
    public class LogHistoryService : ILogHistoryService
    {
        private readonly IGenericRepository _genericRepository;
        public LogHistoryService(IGenericRepository genericRepository)
        {
            _genericRepository = genericRepository;
        }
        public async Task<IEnumerable<LogHistoryEntitity>> GetLogHistoryAsync(LogType logType, DateTime? startDate, DateTime? EndDate)
        {
            try
            {
                List<RequestFilterDto> filters = new List<RequestFilterDto>()
                    {
                        new RequestFilterDto()
                        {
                            Key = "LogType",
                            Value = logType,
                            Operator = "="
                        }
                    };
                if (startDate.HasValue)
                {
                    filters.Add(new RequestFilterDto()
                    {
                        Key = "CreatedAt",
                        Value = startDate.Value.Date,
                        Operator = ">="
                    });
                }
                if (EndDate.HasValue)
                {
                    filters.Add(new RequestFilterDto()
                    {
                        Key = "CreatedAt",
                        Value = EndDate.Value.Date,
                        Operator = "<="
                    });
                }

                var logRequest = await _genericRepository.GetRecordByRequestFilter<LogHistoryEntitity>(filters);
                return logRequest;
            }
            catch (Exception ex)
            {
                return Enumerable.Empty<LogHistoryEntitity>();
            }
        }

        public async Task<bool> SaveErrorLogHistoryAsyn(string? apiName, object? requestBody, string? notes, string? errorMessage, object? stackTrace)
        {
            try
            {
                var result = await _genericRepository.SaveAsync(new LogHistoryEntitity(apiName,
                    JsonConvert.SerializeObject(requestBody),
                    null,
                    notes,
                    LogType.Error,
                    errorMessage,
                    JsonConvert.SerializeObject(stackTrace)));
                return result > 0;
            }
            catch (Exception ex)
            {
                return false;
            }
        }

        public async Task<bool> SaveInformationLogHistoryAsyn(string? apiName, object? requestBody, object? response, string? notes)
        {
            try
            {
                var result = await _genericRepository.SaveAsync(new LogHistoryEntitity(apiName,
                    JsonConvert.SerializeObject(requestBody),
                    JsonConvert.SerializeObject(response),
                    notes,
                    LogType.Information,
                    null,
                    null));
                return result > 0;
            }
            catch (Exception ex)
            {
                return false;
            }
        }

        public async Task<bool> SaveSuccessLogHistoryAsyn(string? apiName, object? requestBody, object? response, string? notes)
        {
            try
            {
                var result = await _genericRepository.SaveAsync(new LogHistoryEntitity(apiName,
                   JsonConvert.SerializeObject(requestBody),
                   response != null ? JsonConvert.SerializeObject(response) : string.Empty,
                   notes,
                   LogType.Success,
                   null,
                   null));
                return result > 0;
            }
            catch (Exception ex)
            {
                return false;
            }
        }
    }
}
