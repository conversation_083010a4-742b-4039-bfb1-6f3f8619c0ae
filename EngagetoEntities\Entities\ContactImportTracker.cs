﻿using EngagetoEntities.Enums;

namespace EngagetoEntities.Entities
{
    public class ContactImportTracker : BaseEntity
    {
        public Guid Id { get; set; } = Guid.NewGuid();
        public Guid BusinessId { get; set; }
        public Guid UserId { get; set; }
        public int UploadedFilesId { get; set; }
        public int TotalCount { get; set; }
        public int DistinctCount { get; set; }
        public int TotalUploadedCount { get; set; }
        public int DuplicateCount { get; set; }
        public int InvalidCount { get; set; }
        public string? S3BucketKey { get; set; }
        public string? SheetName { get; set; }
        public string? FileName { get; set; }
        public UploadStatus status { get; set; }
        public string? Message { get; set; }
        public string? InvalidDataS3BucketKey { get; set; }
    }
}
