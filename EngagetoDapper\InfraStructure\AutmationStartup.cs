﻿
using EngagetoDapper.Data;
using EngagetoDapper.Data.Dapper.Repositories.AutomationRepositories;
using EngagetoDapper.Data.Dapper.Repositories.CampaignRespositories;
using EngagetoDapper.Data.Dapper.Repositories.ConversationRepositories;
using EngagetoDapper.Data.Dapper.Repositories.UserRepositories;
using EngagetoDapper.Data.Dapper.Services.AutomationServices;
using EngagetoDapper.Data.Dapper.Services.CampaignServices;
using EngagetoDapper.Data.Dapper.Services.ConversationAnalyticsServices;
using EngagetoDapper.Data.Dapper.Services.UserServices;
using EngagetoDapper.Data.Interfaces;
using EngagetoDapper.Data.Interfaces.AutomationInterfaces;
using EngagetoDapper.Data.Interfaces.CampaignInterfaces.ICampaignRepositories;
using EngagetoDapper.Data.Interfaces.CampaignInterfaces.ICampaignServices;
using EngagetoDapper.Data.Interfaces.IConversationAnalyticsInterfaces;
using EngagetoDapper.Data.Interfaces.IUserInterfaces.IUserRepositories;
using EngagetoDapper.Data.Interfaces.IUserInterfaces.IUserServices;
using Microsoft.Extensions.DependencyInjection;


namespace EngagetoDapper.InfraStructure
{
    public static class AutmationStartup
    {
        public static IServiceCollection AddAutomation(this IServiceCollection service)
        {
            service.AddScoped<IAutoReplyMessageService, AutoReplyMessageService>()
            .AddScoped<IAutoReplyMessageRepository, AutoReplyMessageRepository>()
            .AddScoped<IAutoReplyAutomationRepostry, AutoReplyAutomationRepostry>()
            .AddScoped<IAutomationWorkflowService, AutomationWorkflowService>()
            .AddScoped<IConversationAnalyticsService, ConversationAnalyticsService>()
            .AddScoped<IConversationAnalyticsRepository, ConversationAnalyticsRepository>()
            .AddScoped<ICampaignRespository, CampaignRespository>()
            .AddScoped<IConversationsService, ConversationsService>()
            .AddScoped<IUserRepository,UserRepository>()
            .AddScoped<IUserService,UserService>()
            .AddScoped<IAutoReplyAutomationService,AutoReplyAutomationService>()
            .AddScoped<IInboxMessageService, InboxMessageService>();
            return service;
        }
    }
}
