﻿using EngagetoEntities.Dtos.AutomationDtos;
using EngagetoEntities.Enums;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using System.Text.Json;
using System.Text.Json.Serialization;

namespace EngagetoEntities.Entities
{
    public class AutomationSetting : BaseEntity
    {
        [Key]
        public int Id { get; set; }
        public AutomationType AutomationType { get; set; }
        public string BusinessId { get; set; }
        public Guid? UserId { get; set; }

        // Property used for storing JSON in the database
        public string? WelcomeSettingJson { get; set; }
        public string? OutOfOfficeSettingJson { get; set; }
        public string? DelaySettingJson { get; set; }

        // Convenience properties for handling JSON
        [NotMapped]
        public List<WelcomeMessageSetting>? WelcomeSetting
        {
            get => string.IsNullOrWhiteSpace(WelcomeSettingJson)
                    ? null
                    : JsonSerializer.Deserialize<List<WelcomeMessageSetting>>(WelcomeSettingJson);
            set => WelcomeSettingJson = value == null ? null : JsonSerializer.Serialize(value);
        }

        [NotMapped]
        public List<OutOfficeSetting>? OutOfOfficeSetting
        {
            get => string.IsNullOrWhiteSpace(OutOfOfficeSettingJson)
                    ? null
                    : JsonSerializer.Deserialize<List<OutOfficeSetting>>(OutOfOfficeSettingJson);
            set => OutOfOfficeSettingJson = value == null ? null : JsonSerializer.Serialize(value);
        }

        [NotMapped]
        public List<DelaySetting>? DelaySetting
        {
            get => string.IsNullOrWhiteSpace(DelaySettingJson)
                    ? null
                    : JsonSerializer.Deserialize<List<DelaySetting>>(DelaySettingJson);
            set => OutOfOfficeSettingJson = value == null ? null : JsonSerializer.Serialize(value);
        }
    }
}
