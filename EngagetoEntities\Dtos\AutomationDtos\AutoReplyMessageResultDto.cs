﻿using EngagetoEntities.Enums;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace EngagetoEntities.Dtos.AutomationDtos
{
    public class AutoReplyMessageResultDto
    {
        public Guid Id { get; set; }
        public Guid CompanyId { get; set; }
        public Guid UserId { get; set; }
        public string Input { get; set; }
        public string? InputVariation { get; set; }
        public int AutoReplyType { get; set; }
        public Guid AutoReplyCustomMessageId { get; set; }
        public Guid? VeriableId { get; set; }
        public string BodyMessage { get; set; }
        public string? Buttons { get; set; }
        public int? Index { get; set; }
        public string? Veriable { get; set; }
        public string? Value { get; set; }
        public string? FallbackValue { get; set; }
        public ReferenceTableType ReferenceTableType { get; set; }
    }
}
