﻿using Newtonsoft.Json;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace EngagetoEntities.Dtos.MetaDto
{
    public class DocumentMessageRequest
    {
        [JsonProperty("messaging_product")]
        public string MessagingProduct { get; private set; } = "whatsapp";

        [JsonProperty("recipient_type")]
        public string RecipientType { get; private set; } = "individual";

        [JsonProperty("to")]
        public string To { get; set; }

        [JsonProperty("type")]
        public string Type { get; private set; } = "document";

        [JsonProperty("document")]
        public WhatsAppDocument Document { get; set; }
        [JsonProperty("context")]
        public MessageContext Context { get; set; }
    }

    public class WhatsAppDocument
    {


        [JsonProperty("link")]
        public string Link { get; set; }
        [JsonProperty("caption")]
        public string Caption { get; set; }
        [JsonProperty("filename")]
        public string FileName { get; set; }
    }
}
