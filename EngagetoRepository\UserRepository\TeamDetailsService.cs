﻿using EngagetoContracts.GeneralContracts;
using EngagetoContracts.UserContracts;
using EngagetoDapper.Data.Interfaces.IUserInterfaces.IUserServices;
using EngagetoEntities.Entities;
using EngagetoEntities.UserEntities.Dtos;
using EngagetoEntities.UserEntities.Models;
using Microsoft.AspNetCore.Http;
using Microsoft.Data.SqlClient;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Configuration;


namespace EngagetoRepository.UserRepository
{
    public class TeamDetailsService : ITeamDetailsService
    {
        private readonly ApplicationDBContext _dbContext;
        private readonly IHttpContextAccessor _httpContextAccessor;
        private readonly IEmailService _emailService;
        private readonly IConfiguration _configuration;
        private readonly string _websiteLink;
        private IResourcePermissionService _resourcePermissionService;
        IEnvironmentService _environmentService;
        public TeamDetailsService(IConfiguration config,
            ApplicationDBContext dbContext,
            IEmailService emailService,
            IHttpContextAccessor httpContextAccessor,
            IResourcePermissionService resourcePermissionService,
            IEnvironmentService environmentService
            )
        {
            _dbContext = dbContext;
            _emailService = emailService;
            _httpContextAccessor = httpContextAccessor;
            _configuration = config;
            _resourcePermissionService = resourcePermissionService;
            _environmentService = environmentService;

            if (_environmentService.IsDevelopment)
                _websiteLink = _environmentService.GetDevWebsiteLink();
            else
                _websiteLink = _environmentService.GetProdWebsiteLink();
        }

        public async Task<Guid?> GetRoleIdByRoleNameAsync(string roleName)
        {
            var roleId = await _dbContext.Roles
                .Where(r => r.Name == roleName)
                .Select(r => r.Id)
                .FirstOrDefaultAsync();

            return roleId;
        }
        public async Task<bool> AddTeamMemberAsync(Guid adminUserId, User_TeamMember teamMemberRequest)
        {
            try
            {
                var createdByAdmin = await _dbContext.Ahex_CRM_Users
                          .Where(u => u.Id == adminUserId)
                          .FirstOrDefaultAsync();
                var createdByAdminUsername1 = await _dbContext.Ahex_CRM_Users
                    .Where(u => u.Id == adminUserId)
                    .Select(u => u.Name)
                    .FirstOrDefaultAsync();

                // Check if the role is allowed for the current user's company
                if (!await IsRoleAllowedAsync(teamMemberRequest.Role, adminUserId))
                {
                    return false;
                }

                var roleId = await GetRoleIdByRoleNameAsync(teamMemberRequest.Role, adminUserId);
                if (roleId == null)
                {
                    return false;
                }
                var companyDetails = await _dbContext.Ahex_CRM_Users.FirstOrDefaultAsync(b => b.Id == adminUserId);
                if (companyDetails == null || string.IsNullOrEmpty(companyDetails.Id.ToString()))
                {
                    throw new InvalidOperationException("Current user does not have a CountryId in BusinessDetails.");
                }

                if (!await isAddAgentPermission(companyDetails.CompanyId))
                    throw new UnauthorizedAccessException("You don't have permission to add more agents. If you want to add more agents, you need to change your plan.");

                var countryDetails = await _dbContext.CountryDetails.FirstOrDefaultAsync(cd => cd.CountryCode == teamMemberRequest.CountryCode);
                if (countryDetails == null)
                {
                    throw new InvalidOperationException($"Country code '{teamMemberRequest.CountryCode}' not found.");
                }
                var existingTeamMember = await _dbContext.Ahex_CRM_Users.FirstOrDefaultAsync(u => u.EmailAddress == teamMemberRequest.Email && u.Status == true);
                if (existingTeamMember != null)
                {
                    throw new InvalidOperationException("TeamMember with this email is already registered.");
                }


                string temporaryPassword = GenerateTemporaryPassword();
                string passwordHash = HashPassword(temporaryPassword);

                var accountId = Guid.NewGuid();
                var newUser = new Ahex_CRM_Users
                {
                    Id = accountId,
                    CompanyId = companyDetails.CompanyId,
                    Name = teamMemberRequest.FullName,
                    EmailAddress = teamMemberRequest.Email,
                    CountryCode = countryDetails.CountryCode,
                    PhoneNumber = teamMemberRequest.PhoneNumber,
                    Password = passwordHash,
                    RoleId = roleId.ToString(),
                    CreatedBy = createdByAdminUsername1,
                    LastOnlineTime = DateTime.Now,
                    CreationDate = DateTime.Now,
                    Status = true,
                };



                var userRole = new UserRole
                {
                    Id = accountId,
                    RoleId = roleId.Value,

                };

                var notificationid = await GetNotificationIdsAsync();


                foreach (var id in notificationid)
                {
                    var userNotificationEntities = new UserNotificationEntities
                    {
                        NotificationId = id,
                        UserId = accountId.ToString(),
                        CompanyId = companyDetails.CompanyId,
                        isActive = true,

                    };

                    _dbContext.UserNotificationEntities.Add(userNotificationEntities);
                    await _dbContext.SaveChangesAsync();
                }

                await _dbContext.SaveChangesAsync();

                _dbContext.Ahex_CRM_Users.Add(newUser);
                _dbContext.UserRoles.Add(userRole);
                await _dbContext.SaveChangesAsync();


                await _emailService.SendEmailAsync(
                    teamMemberRequest.Email,
                    "Temporary Password",
                    $"<p>Hi {teamMemberRequest.FullName},</p>" +
                    $"<p>Your temporary password is: {temporaryPassword}</p>",
                    _configuration["SmtpSettings:LogoUrl"],
                    _websiteLink
                );

                string notificationName = "Get notified when TeamMember  details are created or updated";
                int? notificationId = await GetNotificationIdAsync(notificationName);

                var userNotification = await _dbContext.UserNotificationEntities
                                                    .FirstOrDefaultAsync(un => un.UserId.ToLower() == createdByAdmin.Id.ToString().ToLower() &&
                                                                                 un.CompanyId.ToLower() == createdByAdmin.CompanyId.ToLower() &&
                                                                                 un.isActive == true &&
                                                                                 un.NotificationId == notificationId);
                if (userNotification != null)
                {
                    await _emailService.SendEmailAsync(
                       createdByAdmin.EmailAddress,
                       "New Team Member Added",
                       $"<p>Hi {createdByAdmin.Name},</p>" +
                       $"<p>A new team member {teamMemberRequest.FullName}, has been successfully added to your team.</p>",
                       _configuration["SmtpSettings:LogoUrl"],
                       _websiteLink
                    );
                }

                return true;
            }
            catch (Exception ex)
            {

                Console.WriteLine($"Error adding team member and sending temporary password: {ex.Message}");
                return false;
            }
        }

        private async Task AddRolePermissionAsync(string roleId, string accessToRoleId, string companyId)
        {

            var existingRolePermission = await _dbContext.RolePermissions
                .AnyAsync(rp => rp.RoleId == roleId && rp.AccessToRoleId == accessToRoleId && rp.CompanyId == companyId);

            if (!existingRolePermission)
            {
                var rolePermission = new RolePermissions
                {
                    RoleId = roleId,
                    AccessToRoleId = accessToRoleId,
                    CompanyId = companyId,
                    // CreatedAt = DateTime.UtcNow
                };

                _dbContext.RolePermissions.Add(rolePermission);
            }
        }

        public async Task<List<Guid>> GetAllRoleIdsAsync()
        {

            var roleIds = await _dbContext.Roles
                .Select(role => role.Id)
                .ToListAsync();

            return roleIds;
        }

        public async Task<IEnumerable<Guid>> GetPermissionIdsAsync()
        {
            try
            {

                var menus = await _dbContext.Permissions.ToListAsync();


                var menuid = menus.Select(p => p.Id).ToList();

                return menuid;
            }
            catch (Exception ex)
            {

                Console.WriteLine($"Error fetching permission IDs: {ex.Message}");
                return Enumerable.Empty<Guid>();
            }
        }
        public async Task<IEnumerable<int>> GetMenuIdsAsync()
        {
            try
            {

                var permissions = await _dbContext.MenuDetails.ToListAsync();


                var permissionIds = permissions.Select(p => p.MenuId).ToList();

                return permissionIds;
            }
            catch (Exception ex)
            {

                Console.WriteLine($"Error fetching permission IDs: {ex.Message}");
                return Enumerable.Empty<int>();
            }
        }
        public async Task<bool> AddClientTeamMemberAsync(Guid adminUserId, User_ClientTeamMember teamMemberRequest)
        {
            try
            {

                var createdByAdminUsername1 = await _dbContext.Ahex_CRM_Users
                    .Where(u => u.Id == adminUserId)
                    .Select(u => u.Name)
                    .FirstOrDefaultAsync();
                var createdByAdmin = await _dbContext.Ahex_CRM_Users
                                          .Where(u => u.Id == adminUserId)
                                          .FirstOrDefaultAsync();

                if (!await IsClientRoleAllowedAsync(teamMemberRequest.Role, teamMemberRequest.CompanyId))
                {
                    return false;
                }


                var roleId = await GetRoleIdByRoleNameAsync(teamMemberRequest.Role, teamMemberRequest.CompanyId);
                if (roleId == null)
                {

                    return false;
                }
                var companyDetails = await _dbContext.Ahex_CRM_BusinessDetails.FirstOrDefaultAsync(b => b.Id.ToString() == teamMemberRequest.CompanyId);
                if (companyDetails == null || string.IsNullOrEmpty(companyDetails.Id.ToString()))
                {
                    throw new InvalidOperationException("Current user does not have a CountryId in BusinessDetails.");
                }

                var countryDetails = await _dbContext.CountryDetails.FirstOrDefaultAsync(cd => cd.CountryCode == teamMemberRequest.CountryCode);
                if (countryDetails == null)
                {
                    throw new InvalidOperationException($"Country code '{teamMemberRequest.CountryCode}' not found.");
                }
                var existingTeamMember = await _dbContext.Ahex_CRM_Users.FirstOrDefaultAsync(u => u.EmailAddress == teamMemberRequest.Email);
                if (existingTeamMember != null)
                {

                    if (existingTeamMember.Status)
                    {

                        throw new InvalidOperationException("TeamMember with this email is already registered.");
                    }
                    else
                    {
                        existingTeamMember.Status = true;
                        await _dbContext.SaveChangesAsync();
                        return true;
                    }
                }


                string temporaryPassword = GenerateTemporaryPassword();
                string passwordHash = HashPassword(temporaryPassword);

                var accountId = Guid.NewGuid();
                var newUser = new Ahex_CRM_Users
                {
                    Id = accountId,
                    CompanyId = companyDetails.Id.ToString(),
                    Name = teamMemberRequest.FullName,
                    EmailAddress = teamMemberRequest.Email,
                    CountryCode = countryDetails.CountryCode,
                    PhoneNumber = teamMemberRequest.PhoneNumber,
                    Password = passwordHash,
                    RoleId = roleId.ToString(),
                    CreatedBy = createdByAdminUsername1,
                    LastOnlineTime = DateTime.Now,
                    CreationDate = DateTime.Now,
                    Status = true,
                };



                var userRole = new UserRole
                {
                    Id = accountId,
                    RoleId = roleId.Value,

                };


                var notificationid = await GetNotificationIdsAsync();


                foreach (var id in notificationid)
                {
                    var userNotificationEntities = new UserNotificationEntities
                    {
                        NotificationId = id,
                        UserId = accountId.ToString(),
                        CompanyId = companyDetails.Id.ToString(),
                        isActive = true,

                    };

                    _dbContext.UserNotificationEntities.Add(userNotificationEntities);
                    await _dbContext.SaveChangesAsync();
                }


                await _dbContext.SaveChangesAsync();
                _dbContext.Ahex_CRM_Users.Add(newUser);
                _dbContext.UserRoles.Add(userRole);
                await _dbContext.SaveChangesAsync();


                await _emailService.SendEmailAsync(
                        teamMemberRequest.Email,
                        "Temporary Password",
                        $"<p>Hi {teamMemberRequest.FullName},</p>" +
                        $"<p>Your temporary password is: {temporaryPassword}</p>",
                        _configuration["SmtpSettings:LogoUrl"],
                        _websiteLink
                );

                string notificationName = "Get notified when TeamMember  details are created or updated";
                int? notificationId = await GetNotificationIdAsync(notificationName);

                var userNotification = await _dbContext.UserNotificationEntities
                                                    .FirstOrDefaultAsync(un => un.UserId.ToLower() == createdByAdmin.Id.ToString().ToLower() &&
                                                                                 un.CompanyId.ToLower() == createdByAdmin.CompanyId.ToLower() &&
                                                                                 un.isActive == true &&
                                                                                 un.NotificationId == notificationId);
                if (userNotification != null)
                {
                    await _emailService.SendEmailAsync(
                       createdByAdmin.EmailAddress,
                       "New Team Member Added",
                       $"<p>Hi {createdByAdmin.Name},</p>" +
                       $"<p>A new team member {teamMemberRequest.FullName}, has been successfully added to your team.</p>",
                       _configuration["SmtpSettings:LogoUrl"],
                       _websiteLink
                    );
                }
                return true;
            }
            catch (Exception ex)
            {

                Console.WriteLine($"Error adding team member and sending temporary password: {ex.Message}");
                return false;
            }
        }
        private async Task<List<string>> GetAllowedRolesAsync(string companyId)
        {
            var roles = await _dbContext.Roles
                .Where(r => r.CompanyId == companyId)
                .Select(r => r.Name)
                .ToListAsync();

            return roles;
        }

        private async Task<bool> IsClientRoleAllowedAsync(string roleName, string companyId)
        {
            var allowedRoles = await GetAllowedRolesAsync(companyId);
            return allowedRoles.Contains(roleName);
        }
        private async Task<Guid?> GetRoleIdByRoleNameAsync(string roleName, string companyId)
        {
            var role = await _dbContext.Roles
                .Where(r => r.CompanyId == companyId && r.Name == roleName)
                .Select(r => r.Id)
                .FirstOrDefaultAsync();

            return role == Guid.Empty ? (Guid?)null : role;
        }
        private async Task<Guid?> GetRoleIdByRoleNameAsync(string roleName, Guid currentUserId)
        {
            var currentUserCompanyId = await _dbContext.Ahex_CRM_Users
                .Where(u => u.Id == currentUserId)
                .Select(u => u.CompanyId)
                .FirstOrDefaultAsync();

            if (currentUserCompanyId == null)
            {
                throw new InvalidOperationException("Current user company not found.");
            }

            var role = await _dbContext.Roles
                .Where(r => r.CompanyId == currentUserCompanyId && r.Name == roleName)
                .FirstOrDefaultAsync();

            return role?.Id;
        }


        private async Task<bool> IsRoleAllowedAsync(string roleName, Guid currentUserId)
        {
            var roleId = await GetRoleIdByRoleNameAsync(roleName, currentUserId);
            return roleId.HasValue;
        }
        public async Task<IEnumerable<Ahex_CRM_Users>> GetTeamMembersAsync(Guid currentUserId, string searchQuery, bool isActive, string sortBy, bool isSortAscending)
        {
            try
            {


                var query = _dbContext.Ahex_CRM_Users.AsQueryable();

                if (!string.IsNullOrEmpty(searchQuery))
                {

                    if (Guid.TryParse(searchQuery, out Guid teamMemberId))
                    {
                        query = query.Where(member => member.Id == teamMemberId);
                    }
                    else
                    {
                        if (searchQuery.Length == 3 && searchQuery.All(char.IsDigit))
                        {

                            query = query.Where(company => company.PhoneNumber.StartsWith(searchQuery));
                        }
                        else
                        {
                            query = query.Where(member =>
                                member.Name.Contains(searchQuery) ||
                                member.EmailAddress.Contains(searchQuery) ||
                                member.PhoneNumber.Substring(3).Contains(searchQuery) ||
                                member.RoleId.Contains(searchQuery)
                            );
                        }
                    }
                }

                if (!isActive)
                {
                    query = query.Where(member => member.Status == isActive);
                }

                switch (sortBy?.ToLower())
                {
                    case "fullname":
                        query = isSortAscending ? query.OrderBy(member => member.Name) : query.OrderByDescending(member => member.Name);
                        break;
                    case "email":
                        query = isSortAscending ? query.OrderBy(member => member.EmailAddress) : query.OrderByDescending(member => member.EmailAddress);
                        break;
                    default:
                        query = query.OrderBy(member => member.Name);
                        break;
                }

                return await query.ToListAsync();
            }
            catch (Exception)
            {
                return null;
            }
        }


        public async Task<bool> UpdateTeamMemberAsync(Guid adminUserId, Guid teamMemberId, User_TeamMemberUpdate updatedTeamMemberRequest)
        {
            try
            {
                var teamMemberToUpdate = await _dbContext.Ahex_CRM_Users.FindAsync(teamMemberId);

                if (teamMemberToUpdate == null)
                {
                    return false;
                }

                if (!teamMemberToUpdate.Status)
                {
                    throw new InvalidOperationException("Teammember is inactive and cannot be updated.");
                }
                teamMemberToUpdate.Name = string.IsNullOrEmpty(updatedTeamMemberRequest.FullName) ? string.Empty : updatedTeamMemberRequest.FullName;

                teamMemberToUpdate.EmailAddress = string.IsNullOrEmpty(updatedTeamMemberRequest.Email) ? string.Empty : updatedTeamMemberRequest.Email;

                if (!string.IsNullOrEmpty(updatedTeamMemberRequest.CountryCode))
                {
                    var countryDetails = await _dbContext.CountryDetails.FirstOrDefaultAsync(cd => cd.CountryCode == updatedTeamMemberRequest.CountryCode);
                    if (countryDetails == null)
                    {
                        throw new InvalidOperationException($"Country code '{updatedTeamMemberRequest.CountryCode}' not found.");
                    }
                    teamMemberToUpdate.CountryCode = countryDetails.CountryCode;
                }
                else
                {
                    teamMemberToUpdate.CountryCode = string.Empty;
                }

                teamMemberToUpdate.PhoneNumber = string.IsNullOrEmpty(updatedTeamMemberRequest.PhoneNumber) ? string.Empty : updatedTeamMemberRequest.PhoneNumber;

                bool roleUpdated = false;
                string previousRole = null;

                if (!string.IsNullOrEmpty(updatedTeamMemberRequest.Role))
                {
                    teamMemberToUpdate = await _dbContext.Ahex_CRM_Users
                                            .Where(u => u.Id == teamMemberId)
                                            .FirstOrDefaultAsync();

                    if (teamMemberToUpdate == null)
                    {
                        return false;
                    }

                    previousRole = teamMemberToUpdate.RoleId;

                    var companyId = teamMemberToUpdate.CompanyId;
                    var allowedRoles = await GetAllowedRolesAsync(companyId);

                    if (!allowedRoles.Contains(updatedTeamMemberRequest.Role))
                    {
                        return false;
                    }
                    var roleId = await GetRoleIdByRoleNameAsync(updatedTeamMemberRequest.Role);
                    if (roleId == null)
                    {
                        return false;
                    }
                    teamMemberToUpdate.RoleId = roleId.ToString();

                    var userRole = await _dbContext.UserRoles.FirstOrDefaultAsync(ur => ur.Id == teamMemberId);
                    if (userRole != null)
                    {
                        _dbContext.UserRoles.Remove(userRole);
                        var newUserRole = new UserRole
                        {
                            Id = teamMemberId,
                            RoleId = roleId.Value
                        };
                        _dbContext.UserRoles.Add(newUserRole);
                    }
                    else
                    {
                        var newUserRole = new UserRole
                        {
                            Id = teamMemberId,
                            RoleId = roleId.Value
                        };
                        _dbContext.UserRoles.Add(newUserRole);
                    }
                    roleUpdated = true;
                }
                else
                {
                    teamMemberToUpdate.RoleId = string.Empty;
                    roleUpdated = true;

                }
                await _dbContext.SaveChangesAsync();

                var adminUser = await _dbContext.Ahex_CRM_Users.FindAsync(adminUserId);
                if (adminUser != null)
                {
                    string notificationName = "Get notified when TeamMember  details are created or updated";
                    int? notificationId = await GetNotificationIdAsync(notificationName);

                    var userNotification = await _dbContext.UserNotificationEntities
                                                        .FirstOrDefaultAsync(un => un.UserId.ToLower() == adminUser.Id.ToString().ToLower() &&
                                                                                     un.CompanyId.ToLower() == adminUser.CompanyId.ToLower() &&
                                                                                     un.isActive == true &&
                                                                                     un.NotificationId == notificationId);
                    if (userNotification != null)
                    {
                        await _emailService.SendEmailAsync(
                        adminUser.EmailAddress,
                        "Team Member Details Updated",
                        $"<p>Hi {adminUser.Name},</p>" +
                        $"<p>You have successfully updated the details of the team member {teamMemberToUpdate.Name}.</p>",
                        _configuration["SmtpSettings:LogoUrl"],
                        _websiteLink
                             );
                    }
                }
                if (roleUpdated && previousRole != teamMemberToUpdate.RoleId)
                {
                    string notificationName = "Let me know when user role is changed";
                    int? notificationId = await GetNotificationIdAsync(notificationName);

                    var userNotification = await _dbContext.UserNotificationEntities
                        .FirstOrDefaultAsync(un => un.UserId.ToLower() == teamMemberToUpdate.Id.ToString().ToLower() &&
                                                   un.CompanyId.ToLower() == teamMemberToUpdate.CompanyId.ToLower() &&
                                                   un.isActive == true &&
                                                   un.NotificationId == notificationId);
                    if (userNotification != null)
                    {
                        await _emailService.SendEmailAsync(
                            teamMemberToUpdate.EmailAddress,
                            "Your Role Has Been Updated",
                            $"<p>Hi {teamMemberToUpdate.Name},</p>" +
                            $"<p>Your role has been updated to {updatedTeamMemberRequest.Role}.</p>",
                            _configuration["SmtpSettings:LogoUrl"],
                            _websiteLink
                        );
                    }
                }
                return true;
            }
            catch (Exception)
            {
                throw;
            }
        }

        public async Task<IEnumerable<Ahex_CRM_Users>> GetTeamDetailsByCompanyIdAsync(string companyId, bool? isActive, string searchQuery = null, string sortBy = null, bool ascending = true)
        {
            IQueryable<Ahex_CRM_Users> query = _dbContext.Ahex_CRM_Users.Where(td => td.CompanyId == companyId && td.Status == true);


            if (!string.IsNullOrWhiteSpace(searchQuery))
            {
                if (searchQuery.Length == 3 && searchQuery.All(char.IsDigit))
                {

                    query = query.Where(member => member.PhoneNumber.StartsWith(searchQuery));
                }
                else
                {
                    query = query.Where(member =>
                        member.Name.Contains(searchQuery) ||
                        member.EmailAddress.Contains(searchQuery) ||
                        member.PhoneNumber.Substring(3).Contains(searchQuery) ||
                        member.RoleId.Contains(searchQuery)
                    );
                }
            }

            if (isActive != null)
            {
                query = query.Where(member => member.Status == isActive);
            }
            var usersWithRoleIds = await query
                                          .Select(user => new
                                          {
                                              User = user,
                                              RoleId = user.RoleId
                                          })
                                          .ToListAsync();


            var userIdToRoleName = await _dbContext.Roles
                                         .Where(role => role.CompanyId == companyId.ToString() && usersWithRoleIds
                                         .Select(u => u.RoleId).Contains(role.Id.ToString()))
                                         .ToDictionaryAsync(role => role.Id.ToString(), role => role.Name);

            var users = usersWithRoleIds.Select(u => u.User);
            foreach (var user in users)
            {
                if (user.RoleId != null && userIdToRoleName.TryGetValue(user.RoleId, out string roleName))
                {
                    user.RoleId = roleName;
                }
                else
                {

                    user.RoleId = null;
                }
            }

            if (!string.IsNullOrWhiteSpace(sortBy))
            {
                switch (sortBy.ToLower())
                {
                    case "fullname":
                        query = ascending ? query.OrderBy(member => member.Name) : query.OrderByDescending(member => member.Name);
                        break;
                    case "email":
                        query = ascending ? query.OrderBy(member => member.EmailAddress) : query.OrderByDescending(member => member.EmailAddress);
                        break;
                    case "mobile":
                        query = ascending ? query.OrderBy(member => member.PhoneNumber) : query.OrderByDescending(member => member.PhoneNumber);
                        break;
                    case "role":
                        query = ascending ? query.OrderBy(member => member.RoleId) : query.OrderByDescending(member => member.RoleId);
                        break;
                    default:
                        throw new ArgumentException("Invalid sortBy parameter.");
                }
            }

            return await query.ToListAsync();
        }


        public async Task<bool> DeleteTeamMemberAsync(Guid adminUserId, Guid teamMemberId)
        {
            try
            {

                var teamMemberToDelete = await _dbContext.Ahex_CRM_Users.FindAsync(teamMemberId);

                if (teamMemberToDelete == null)
                {
                    return false;
                }

                teamMemberToDelete.Status = false;

                await _dbContext.SaveChangesAsync();

                return true;
            }
            catch (Exception)
            {
                return false;
            }
        }
        private bool IsDuplicateEmailException(DbUpdateException ex)
        {
            var sqlException = ex.InnerException as SqlException;
            return sqlException != null && (sqlException.Number == 2601 || sqlException.Number == 2627);
        }




        private string GenerateTemporaryPassword()
        {

            const string allowedChars = "abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ1234567890";
            const int passwordLength = 12;

            Random random = new Random();
            char[] passwordArray = new char[passwordLength];

            for (int i = 0; i < passwordLength; i++)
            {
                passwordArray[i] = allowedChars[random.Next(0, allowedChars.Length)];
            }

            return new string(passwordArray);
        }
        private string HashPassword(string password)
        {

            return BCrypt.Net.BCrypt.HashPassword(password);
        }

        private bool VerifyPassword(string password, string hashedPassword)
        {

            return BCrypt.Net.BCrypt.Verify(password, hashedPassword);
        }
        public async Task<int?> GetNotificationIdAsync(string notificationName)
        {

            var notification = await _dbContext.NotificationEntities
                .FirstOrDefaultAsync(n => n.Name == notificationName);


            return notification?.Id;
        }

        public async Task<IEnumerable<int>> GetNotificationIdsAsync()
        {
            try
            {

                var notifications = await _dbContext.NotificationEntities.ToListAsync();


                var notificationIds = notifications.Select(p => p.Id).ToList();

                return notificationIds;
            }
            catch (Exception ex)
            {

                Console.WriteLine($"Error fetching permission IDs: {ex.Message}");
                return Enumerable.Empty<int>();
            }
        }

        private async Task<bool> isAddAgentPermission(string companyId)
        {
            try
            {
                var resourcePermission = await _resourcePermissionService.GetResourcePermissionsAsync();
                if (resourcePermission is not null)
                {
                    var subscription = await _dbContext.Subscriptions
                        .FirstOrDefaultAsync(subscription => subscription.CompanyId == companyId);
                    var userCounts = await _dbContext.Ahex_CRM_Users.CountAsync(user => user.CompanyId == companyId && user.Status);
                    if (subscription is not null)
                    {
                        var agentLimit = resourcePermission?.FirstOrDefault(x => x.PlanId == subscription.PlanId)?.AgentLimit ?? -1;
                        if (agentLimit == -1)
                        {
                            return true;
                        }
                        else if (userCounts < agentLimit)
                            return true;
                        return false;
                    }
                    return false;
                }
                return true;
            }
            catch (Exception ex)
            {
                throw;
            }
        }
    }
}
