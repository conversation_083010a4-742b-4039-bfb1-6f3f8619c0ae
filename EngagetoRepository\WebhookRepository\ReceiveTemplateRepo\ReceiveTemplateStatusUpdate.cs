﻿using EngagetoContracts.UserContracts;
using EngagetoContracts.WebhookContracts.ReceivedNotification;
using EngagetoEntities.DdContext;
using EngagetoEntities.Dtos.TemplateDtos;
using EngagetoEntities.Enums;
using Microsoft.EntityFrameworkCore;
using Newtonsoft.Json;


namespace EngagetoDatabase.WebhookRepository.ReceiveTemplateRepo
{
    public class ReceiveTemplateStatusUpdate : TemplateStatus
    {
        private ApplicationDbContext _appContext;
        private EngagetoEntities.UserEntities.Models.ApplicationDBContext _dbContext;
        private readonly EngagetoDapper.Data.Interfaces.IUserInterfaces.IUserServices.IWebhookService _webhookService;
        private readonly IEmailService _emailService;
        public ReceiveTemplateStatusUpdate(ApplicationDbContext apiContext,
            EngagetoDapper.Data.Interfaces.IUserInterfaces.IUserServices.IWebhookService webhookService,
            IEmailService emailService,
            EngagetoEntities.UserEntities.Models.ApplicationDBContext dbContext
            )
        {
            _appContext = apiContext;
            _webhookService = webhookService;
            _emailService = emailService;
            _dbContext = dbContext;
        }

        public async Task UpdateCreateTemplateStatus(dynamic entry)
        {
            try
            {
                var value = entry["changes"]?[0]?["value"];
                if (value == null)
                {
                    Console.WriteLine("Value is missing or cannot be parsed.");
                    return;
                }
                var result = value["event"]?.ToString() ?? "";
                var statusReason = value["reason"]?.ToString() ?? "None";
                string messageTemplateId = value["message_template_id"]?.ToString() ?? "";
                string businessId = "";

                if (entry["id"] != null)
                {
                    string id = Convert.ToString(entry["id"]);

                    //businessId = (await _appContext.BusinessDetailsMetas
                        //.FirstOrDefaultAsync(m => m.WhatsAppBusinessAccountID == id))?.BusinessId ?? string.Empty;
                }
                var output = await _appContext.Templates
                    .FirstOrDefaultAsync(m => m.MetaId == messageTemplateId);

                if (output != null)
                {
                    if (result.Equals("approved", StringComparison.OrdinalIgnoreCase))
                    {
                        output.Status = EngagetoEntities.Enums.WATemplateStatus.APPROVED;
                        output.StatusReason = statusReason;
                    }
                    if (result.Equals("rejected", StringComparison.OrdinalIgnoreCase))
                    {
                        await _emailService.SendEmailViaUtility("Rejected template", Convert.ToString(entry), null, new List<string> { "<EMAIL>", "<EMAIL>" }, null, null);
                        output.Status = EngagetoEntities.Enums.WATemplateStatus.REJECTED;
                        output.StatusReason = statusReason;
                    }
                    _appContext.Templates.Update(output);
                    _appContext.SaveChanges();

                    Guid templateId = output.TemplateId;
                    await _webhookService.SendTemplateWebhookAsync(businessId, templateId);
                }          
            }
            catch (Exception ex)
            {
                Console.WriteLine($"An error occurred: {ex.Message}");
            }
        }
        public async Task TemplateQualityUpdate(dynamic entry)
        {

            try
            {
                await _emailService.SendEmailViaUtility("Template Quility", Convert.ToString(entry), null, new List<string> { "<EMAIL>","<EMAIL>" },null,null);
                var value = entry["changes"][0]["value"];
                var previous_quality_score = Convert.ToString(value["previous_quality_score"]);
                var new_quality_score = Convert.ToString(value["new_quality_score"]);
                var message_template_name = Convert.ToString(value["message_template_name"]);
                string messageTemplateId = Convert.ToString(value["message_template_id"]);
                string businessId = "";

                //if (entry["id"] != null)
                //{
                //    string id = Convert.ToString(entry["id"]);
                //    businessId = (await _appContext.BusinessDetailsMetas
                //        .FirstOrDefaultAsync(m => m.WhatsAppBusinessAccountID == id))?.BusinessId ?? string.Empty;
                //}

                if (!string.IsNullOrEmpty(message_template_name))
                {
                    var template = await _appContext.Templates
                        .FirstOrDefaultAsync(m => m.MetaId == messageTemplateId);

                    if (template != null)
                    {
                        var ratingObject = new QualityScoreDtos
                        {
                            PreviousScore = GetQualityScoreEnum(previous_quality_score),
                            NewScore = GetQualityScoreEnum(new_quality_score),
                            CreatedAt = DateTime.UtcNow,
                        };
                        
                        var existingRatings = string.IsNullOrEmpty(template.Rating)
                            ? new List<QualityScoreDtos>()
                            : JsonConvert.DeserializeObject<List<QualityScoreDtos>>(template.Rating);

                        existingRatings?.Add(ratingObject);

                        // Serialize back to string and update Rating (which is stored in DB)
                        template.Rating = JsonConvert.SerializeObject(existingRatings);

                        var roleId = await _dbContext.Roles.Where(i => (i.Name == "owner" || i.Name == "admin") && i.CompanyId== businessId).Select(i => i.Id.ToString()).ToListAsync();

                        var userDetails = await _appContext.Users
                                      .Where(i => i.CompanyId == businessId && roleId.Contains(i.RoleId))
                                     .Select(i => new { i.EmailAddress, i.Name })
                                       .ToListAsync();

                        var emails = userDetails.Select(i => i.EmailAddress).ToList();
                        var clientName = userDetails.Select(i => i.Name).ToList();

                        await  _emailService.SendQualityRatingEmail(emails,previous_quality_score ,new_quality_score, clientName);

                        _appContext.Templates.Update(template);
                        await _appContext.SaveChangesAsync();
                    }

                }
            }
            catch (Exception ex)
            {
                await _emailService.SendEmailViaUtility("Error:Template Quility", Convert.ToString(entry), null, new List<string> { "<EMAIL>", "<EMAIL>" }, null, null);
                Console.WriteLine($"An error occurred: {ex.Message}");
            }
        }

        private QualityScore GetQualityScoreEnum(string score)
        {
            return score.ToLower() switch
            {
                "green" => QualityScore.GREEN,
                "yellow" => QualityScore.YELLOW,
                "red" => QualityScore.RED,
                _ => QualityScore.UNKNOWN, 
            };
        }

    }
}
