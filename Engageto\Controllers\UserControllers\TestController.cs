﻿using Engageto.Attributes;
using EngagetoBackGroundJobs.Interfaces;
using EngagetoDapper.Data.Interfaces.ILogInterfaces;
using EngagetoEntities.DdContext;
using Microsoft.AspNetCore.Mvc;
using Microsoft.EntityFrameworkCore;
using Newtonsoft.Json;
using Newtonsoft.Json.Linq;
using System.Net.Http.Headers;

namespace Engageto.Controllers.UserControllers
{
    [Route("api/[controller]")]
    [ApiController]
    public class TestController : ControllerBase
    {
        private readonly ILogger<TestController> _logger;
        private readonly IJobTaskService _jobTaskService;
        private readonly ILogHistoryService _logHistoryService;
        private readonly ApplicationDbContext _context;
        public TestController(ILogger<TestController> logger, IJobTaskService jobTaskService,
            ILogHistoryService logHistoryService,ApplicationDbContext context)
        {
            _logger = logger;
            _jobTaskService = jobTaskService;
            _logHistoryService = logHistoryService;
            _context = context;
        }

        [HttpPost("ReceiveMessage")]
        [ApiKeyAuthentication]
        public async Task<IActionResult> ReceiveMessage(object conversation)
        {
            try
            {
                _logger.LogInformation($"Receive Message Successfully:{JsonConvert.SerializeObject(conversation)}");
                await _logHistoryService.SaveInformationLogHistoryAsyn("ReceiveMessage->", conversation, null, "");
                return Ok(conversation);
            }
            catch (Exception ex)
            {
                await _logHistoryService.SaveErrorLogHistoryAsyn("Error:ReceiveMessage->", conversation, "", ex.Message, ex.StackTrace);
                _logger.LogInformation($"An error occurred while receiving the message:{JsonConvert.SerializeObject(ex)}");
                return BadRequest(ex.Message);
            }
        }
        //[HttpGet("conversation-analytics")]
        //[AllowAnonymous]
        //public async Task<IActionResult> ConversationAnalytics()
        //{
        //    try
        //    {
        //        await _jobTaskService.GetConversationAnalyticsCost(null);
        //        return Ok();
        //    }
        //    catch (Exception ex)
        //    {
        //        _logger.LogInformation($"An error occurred while receiving the message:{JsonConvert.SerializeObject(ex)}");
        //        return BadRequest(ex.Message);
        //    }
        //}

        [HttpPost("update-meta-displayNumbers")]
        public async Task<IActionResult> UpdateMetaDisplayNumbers()
        {
            try
            {
                var waIds = new string[] { "297817036758193", "406321129224891", "412576248605777", "470271766163991", "422524297621354", "491158047411980", "506563822531615", "481572705040124", "442894822248447", "459204633954097", "451982971342128", "529788473547902", "485316068006240", "551961397991507", "508651985671081", "463044953568880", "537494702780895", "515422411663120", "565673523293902", "572910065901679", "3957148341195646", "529788473547902", "545715068618139", "511912532011266", "505368579326192", "525474610638841", "548699808319915", "285898731273254" };
                foreach (var waId in waIds)
                {
                    string url = $"https://graph.facebook.com/v21.0/{waId}?fields=id,name,phone_numbers";
                    string accessToken = "EAAmPDzk55QABO6MrY3ljv5FuAeQAPZAigyIQlSRAoq6BtJUqTVqpboigPVKMZCHmmHXTbwDEAV6qFEVMV9CoSZCOIm6mcmadTrqZCwZAkT71f3WJlPACl43ylHhqjbrfXDP46KLvzPCcoGvzZCAV4NW2d6T1V8HkRipgGMMZCk1QRlngfZCCJh11MnOiZAfajf2wV"; // Replace with actual token

                    using var httpClient = new HttpClient();
                    httpClient.DefaultRequestHeaders.Authorization = new AuthenticationHeaderValue("Bearer", accessToken);

                    HttpResponseMessage response = await httpClient.GetAsync(url);

                    if (response.IsSuccessStatusCode)
                    {
                        string result = await response.Content.ReadAsStringAsync();
                        JObject jsonObj = JObject.Parse(result);

                        // Extract "data" array from "phone_numbers"
                        JArray phoneNumbers = (JArray)jsonObj["phone_numbers"]["data"];

                        // Extract and display all IDs
                        foreach (JObject phone in phoneNumbers)
                        {
                            string id = phone["id"]?.ToString();
                            string displayPhoneNumber = phone["display_phone_number"]?.ToString();
                            var account = await _context.BusinessDetailsMetas.FirstOrDefaultAsync(x => x.WhatsAppBusinessAccountID == waId && x.PhoneNumberID == id);
                            if(account != null)
                            {
                                account.DisplayPhoneNumber = displayPhoneNumber;
                                _context.BusinessDetailsMetas.Update(account);
                                await _context.SaveChangesAsync();
                            }
                            Console.WriteLine("Phone ID: " + id);
                        }
                        Console.WriteLine("Response: " + result);
                    }
                    else
                    {
                        Console.WriteLine($"Error: {response.StatusCode} - {await response.Content.ReadAsStringAsync()}");
                    }
                }
                return Ok();
            }
            catch (Exception ex) 
            { 
                return BadRequest(ex.Message);
            }
        }
    }
}
