﻿using EngagetoContracts.WebhookContracts.SentMessage;
using EngagetoDapper.Data.Interfaces.IUserInterfaces.IUserServices;
using EngagetoEntities.Dtos.ConversationDtos;
using Mapster;
using Microsoft.Extensions.DependencyInjection;
using System.Diagnostics;


public class ScheduledOperation
{
    private readonly IServiceScopeFactory _serviceScopeFactory;

    public ScheduledOperation(IServiceScopeFactory serviceScopeFactory)
    {
        _serviceScopeFactory = serviceScopeFactory;
    }

    public async Task RunTaskAsync(Guid conversationId, int delayInSeconds, CancellationToken cancellationToken)
    {
       _ = Task.Run(async () =>
        {
            await UpdateErrorMessageWithDelayAsync(conversationId, delayInSeconds, cancellationToken);
        });
    }

    private async Task UpdateErrorMessageWithDelayAsync(Guid conversationId, int delayInSeconds, CancellationToken cancellationToken)
    {
        try
        {
            var stopwatch = Stopwatch.StartNew();

            await Task.Delay(delayInSeconds * 1000, cancellationToken);

            using (var scope = _serviceScopeFactory.CreateScope())
            {
                var inboxService = scope.ServiceProvider.GetRequiredService<IInboxService>();
                var webhookService = scope.ServiceProvider.GetRequiredService<IWebhookService>();
                var whatAppBusinessNotification = scope.ServiceProvider.GetRequiredService<IWhatsAppBusinessNotificarion>();
                await inboxService.UpdateErrorMessageAsync(conversationId, cancellationToken);
                var convs = await inboxService.GetConversationsAsync(conversationId,cancellationToken);
                if (convs?.Count > 0)
                {
                    convs = convs.Where(x => x.Status == EngagetoEntities.Enums.ConvStatus.sent)?.ToList();
                    if (!Guid.TryParse(convs?.FirstOrDefault()?.From, out var businessId))
                    {
                        _ = Guid.TryParse(convs?.FirstOrDefault()?.To, out businessId);
                    }
                    var convDtos = convs.Adapt<List<ConversationDto>>();
                    await whatAppBusinessNotification.RenderErrorMessagesBySignalR(businessId.ToString(), convDtos);
                    //updating the error message to other clients.
                    foreach (var conv in convs ?? new()) 
                    {
                        await webhookService.SaveWebhookEventsAsync(businessId.ToString(), "ErrorMessageUpdate", conv);
                    }
                }
            }

            stopwatch.Stop();
            Console.WriteLine($"Operation completed in {stopwatch.Elapsed.TotalSeconds} seconds.");
        }
        catch (OperationCanceledException)
        {
            Console.WriteLine("Operation was canceled.");
        }
        catch (Exception ex)
        {
            Console.WriteLine($"Error: {ex.Message}");
        }
    }
}
