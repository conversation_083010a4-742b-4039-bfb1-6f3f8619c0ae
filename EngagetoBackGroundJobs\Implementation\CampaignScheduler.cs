﻿using EngagetoBackGroundJobs.Interfaces;
using EngagetoContracts.MetaContracts;
using EngagetoDapper.Data.Dapper.Repositories.InboxRepositories;
using EngagetoDapper.Data.Dtos;
using EngagetoDapper.Data.Interfaces.CampaignInterfaces.ICampaignRepositories;
using EngagetoDapper.Data.Interfaces.GenericInterfaces;
using EngagetoDapper.Data.Interfaces.ILogInterfaces;
using EngagetoEntities;
using EngagetoEntities.Dtos.ContactDtos;
using EngagetoEntities.Dtos.ConversationDtos;
using EngagetoEntities.Dtos.MetaDto;
using EngagetoEntities.Dtos.TemplateDtos;
using EngagetoEntities.Dtos.WorkflowDtos;
using EngagetoEntities.Entities;
using EngagetoEntities.Enums;
using EngagetoEntities.Utilities;
using Humanizer;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Logging;
using Newtonsoft.Json;
using Newtonsoft.Json.Linq;
using PhoneNumbers;
using System.Collections.Concurrent;
using System.Text;
using System.Threading.Channels;


namespace EngagetoBackGroundJobs.Implementation
{
    public class CampaignScheduler : ICampaignScheduler
    {
        private readonly IGenericRepository _genericRepository;
        private readonly ILogHistoryService _logHistoryService;
        private readonly IInboxRepository _inboxRepository;
        private readonly IMetaApiService _metaApiService;
        private readonly IHttpClientFactory _httpClientFactory;
        private readonly IConfiguration _configuration;
        private readonly ILogger<CampaignScheduler> _logger;
        private readonly ICampaignRespository _campaignRespository;

        public CampaignScheduler(IGenericRepository genericRepository,
            ILogHistoryService logHistoryService, IInboxRepository inboxRepository,
            IMetaApiService metaApiService,
            IConfiguration configuration, IHttpClientFactory httpClientFactory,
            ILogger<CampaignScheduler> logger,
            ICampaignRespository campaignRespository)
        {
            _genericRepository = genericRepository;
            _logHistoryService = logHistoryService;
            _inboxRepository = inboxRepository;
            _metaApiService = metaApiService;
            _configuration = configuration;
            _httpClientFactory = httpClientFactory;
            _logger = logger;
            _campaignRespository = campaignRespository;
        }

        public async Task PartitionCampaignBatchesAsync(Campaign campaign, bool isDevelopment)
        {
            var errorLog = new List<object>();
            await _logHistoryService.SaveInformationLogHistoryAsyn("CreateCampaignAsync/ProcessCampaignAsync", JsonConvert.SerializeObject(campaign), "Campaign Start", isDevelopment.ToString());
            int batchNumber = 0;
            int totalCount = 0;
            try
            {
                if ((campaign.UploadedFileId ?? 0) > 0)
                {
                    await GetContactIdsToExcel(campaign.UploadedFileId ?? 0, campaign);
                }
                totalCount = await _genericRepository.Count<CampaignTracker>(
                    new Dictionary<string, object>
                    {
                        { nameof(CampaignTracker.CampaignId), campaign.CampaignId }
                    }
                );
                //int totalAudienceCount = audienceList?.Count ?? 0;
                //var audienceBatches = new List<List<string>>();
                const int batchSize = 2000;

                var baseScheduledTime = (campaign.DateSetLive.HasValue && campaign.DateSetLive.Value > DateTime.UtcNow)
                                        ? campaign.DateSetLive.Value
                                        : DateTime.UtcNow;
                int counter = 0;
                Dictionary<int, int> batches = new();

                var jobIds = new List<string>();
                var functionUrls = new List<string>();

                for (int i = 0; i < totalCount; i += batchSize)
                {
                    batches[counter] = batchSize;
                    counter++;
                }
                for (int i = 0; i < batches.Count; i++)
                {
                    if (i == (batches.Count - 1))
                    {
                        campaign.State = CampaignState.Completed;
                    }
                    var payload = new
                    {
                        type = "Campaign",
                        id = campaign.CampaignId.ToString(),
                        businessId = campaign.BusinessId,
                        jsonPayload = JsonConvert.SerializeObject(campaign),
                        page = i + 1,
                        pageSize = batches[i],
                        scheduleDateTime = i == 0 ? baseScheduledTime : baseScheduledTime.AddMinutes(2)
                    };

                    var jsonPayload = JsonConvert.SerializeObject(payload);
                    var jsonResponse = await CallFunctionAsync(payload, isDevelopment);
                    try
                    {
                        var functionResult = JObject.Parse(jsonResponse);
                        var jobId = functionResult["Id"]?.ToString() ?? $"UnknownBatch_{i + 1}";
                        jobIds.Add(jobId);
                        functionUrls.Add(functionResult.ToString(Formatting.None));
                    }
                    catch (Exception parseEx)
                    {
                        jobIds.Add($"ParseErrorBatch_{i + 1}");
                        functionUrls.Add(jsonResponse);
                        _logger.LogError(parseEx, "Failed to parse Azure Function response for batch {BatchNumber}", i + 1);
                    }
                }

                campaign.ChildScheduleJobIds = string.Join(",", jobIds);
                campaign.ScheduledFunctionUrls = JsonConvert.SerializeObject(functionUrls) ?? string.Empty;

                var updateConditions = new Dictionary<string, object>
                {
                   { nameof(Campaign.BusinessId), campaign.BusinessId },
                   { nameof(Campaign.CampaignId), campaign.CampaignId }
                };

                var updateColumns = new List<string>
                {
                   nameof(Campaign.ChildScheduleJobIds),
                   nameof(Campaign.ScheduledFunctionUrls)
                };

                await _genericRepository.UpdateRecordAsync<Campaign>("Campaigns", updateColumns, campaign, updateConditions);
            }
            catch (Exception ex)
            {
                await _logHistoryService.SaveErrorLogHistoryAsyn("CreateCampaignAsync/ProcessCampaignAsync", $"{batchNumber}:{totalCount},{JsonConvert.SerializeObject(campaign)}", "Campaign Error", ex.Message, JsonConvert.SerializeObject(ex.StackTrace));

                campaign.ErrorCause = $"CreateCampaignAsync/ProcessCampaignAsync, {batchNumber}:{totalCount}, Campaign Error: {ex.Message}, {JsonConvert.SerializeObject(ex.StackTrace)}";
                var conditions = new Dictionary<string, object>()
                {
                     {nameof(Campaign.BusinessId), campaign.BusinessId },
                     {nameof(Campaign.CampaignId), campaign.CampaignId }
                };
                var columns = new List<string>() { nameof(Campaign.ErrorCause) };
                await _genericRepository.UpdateRecordAsync<Campaign>("Campaigns", columns, campaign, conditions);
            }
        }

        private async Task<string> CallFunctionAsync(object requestBody, bool isDevelopment)
        {
            string functionUrl = isDevelopment
                ? _configuration["FunctionSettings:Dev_ProcessSubBatchesUrl"]
                : _configuration["FunctionSettings:Prod_ProcessSubBatchesUrl"];

            if (string.IsNullOrWhiteSpace(functionUrl))
            {
                _logger.LogError("Function URL is not configured for environment: {Environment}", isDevelopment ? "Development" : "Production");
                throw new InvalidOperationException("Function URL is not configured.");
            }

            try
            {
                var client = _httpClientFactory.CreateClient("LongRunning");

                string jsonPayload = JsonConvert.SerializeObject(requestBody);
                var content = new StringContent(jsonPayload, Encoding.UTF8, "application/json");

                _logger.LogInformation("Sending request to Azure Function: {Url}", functionUrl);
                _logger.LogDebug("Request Payload: {Payload}", jsonPayload);

                var response = await client.PostAsync(functionUrl, content);

                string result = await response.Content.ReadAsStringAsync();

                if (!response.IsSuccessStatusCode)
                {
                    _logger.LogWarning("Function returned non-success status {StatusCode}: {Response}", response.StatusCode, result);
                }
                else
                {
                    _logger.LogInformation("Function call successful. Status: {StatusCode}", response.StatusCode);
                }

                return result;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error while calling Azure Function: {Url}", functionUrl);
                throw;
            }
        }

        public async Task ProcessCampaignBatchAsync(string businessId, string campaignId, Campaign? campaign = null, int? page = 0, int? pageSize = 0)
        {
            _logger.LogInformation("🚀 Starting ProcessCampaignBatchAsync - CampaignId: {CampaignId}, Page: {Page}, PageSize: {PageSize}",
                campaignId, page, pageSize);


            var campaignData = (await _genericRepository.GetByObjectAsync<Campaign>(new() { { nameof(Campaign.CampaignId), campaignId } }, "Campaigns"))
                .FirstOrDefault();

            if (campaign != null)
            {
                campaignData.State = campaign.State;
            }
            // PHASE 2: Query pending CampaignTracker records for this batch
            var pendingContactIds = await GetPendingCampaignContactIdsAsync(Guid.Parse(campaignId), page ?? 0, pageSize ?? 200);

            if (pendingContactIds.Any())
            {
                _logger.LogInformation("📊 Found {TrackerCount} pending CampaignTracker records for processing", pendingContactIds.Count);
                // Mark trackers as Processing
                var campaignTrackersStatus = pendingContactIds.Select(x => new CampaignTrackerStatus() { ContactId = x }).ToList();
                await UpdateCampaignTrackerStatusAsync(campaignTrackersStatus, businessId, Guid.Parse(campaignId), "Processing");

                _logger.LogInformation("📋 Updated audience list from CampaignTracker: {AudienceCount} contacts", pendingContactIds.Count);
            }
            else
            {
                _logger.LogInformation("ℹ️ No pending CampaignTracker records found, using provided audience list");
            }

            List<string> waIds = new List<string>();
            List<Guid> sendContactListIds = new List<Guid>();
            var errorLog = new List<object>();
            try
            {
                (string mimeType, string mediaType) = string.IsNullOrEmpty(campaign.MediaUrl) ? ("text", string.Empty) : (await GetMediaTypeAsync(campaign.MediaUrl));
                _logger.LogInformation("📄 Media type determined - MimeType: {MimeType}, MediaType: {MediaType}", mimeType, mediaType);

                var metaDetails = (await _genericRepository.GetByObjectAsync<BusinessDetailsMeta>(new Dictionary<string, object> { { "BusinessId", campaign.BusinessId.ToString() } }))?.FirstOrDefault();

                if (metaDetails == null)
                {
                    _logger.LogError("❌ Meta details not found for BusinessId: {BusinessId}", campaign.BusinessId);
                    throw new InvalidOperationException($"Meta details not found for BusinessId: {campaign.BusinessId}");
                }
                if (pendingContactIds?.Any() == true && metaDetails != null)
                {
                    _logger.LogInformation("✅ Found {ValidGuidCount}", string.Join(",", pendingContactIds));

                    pageSize = 250;

                    int totalPages = (int)Math.Ceiling(pendingContactIds.Count / (double)pageSize);

                    for (int pageNumber = 1; pageNumber <= totalPages; pageNumber++)
                    {
                        var currentBatch = pendingContactIds.Skip((pageNumber - 1) * pageSize ?? 1000).Take(pageSize ?? 1000).ToList();

                        var validContactIds = currentBatch.Select(x => Guid.TryParse(x, out var id) ? id : (Guid?)null)
                            .Where(id => id.HasValue)
                            .Select(id => id.Value).ToList();

                        _logger.LogInformation(
                            "📄 Processing page {PageNumber}/{TotalPages} - Records {Start} to {End}", pageNumber, totalPages,
                            (pageNumber - 1) * pageSize + 1,
                            (pageNumber - 1) * pageSize + currentBatch.Count
                        );

                        var contactsResult = await _inboxRepository.GetContactDetailsByIds(campaign.BusinessId, validContactIds, CancellationToken.None);

                        var contacts = contactsResult.ToList();
                        _logger.LogInformation("📞 Retrieved {ContactCount} contact details from database", contacts.Count);

                        Template? template = null;
                        List<Button> buttons = new();

                        if (campaign.TemplateId != null && campaign.TemplateId != Guid.Empty)
                        {
                            _logger.LogInformation("📝 Loading template and buttons for TemplateId: {TemplateId}", campaign.TemplateId);

                            template = (await _genericRepository.GetByObjectAsync<Template>(
                                new Dictionary<string, object>
                                {
                                    { "BusinessId", campaign.BusinessId },
                                    { "TemplateId", campaign.TemplateId ?? Guid.Empty }
                                },
                                "Templates")).FirstOrDefault();

                            buttons = await _genericRepository.GetByObjectAsync<Button>(
                                new Dictionary<string, object> { { "TemplateId", campaign.TemplateId ?? Guid.Empty } }, "ButtonDetails");

                            if (template != null)
                                _logger.LogInformation("✅ Template loaded: {TemplateName}, Buttons: {ButtonCount}", template.TemplateName, buttons?.Count ?? 0);
                            else
                                _logger.LogWarning("⚠️ Template not found for TemplateId: {TemplateId}", campaign.TemplateId);
                        }
                        _logger.LogInformation("🚀 Starting parallel processing for page {PageNumber}", pageNumber);

                        await ProcessContactBatchWithChannel(contacts, template, buttons, campaign,
                            metaDetails, mimeType, mediaType);

                        _logger.LogInformation("✅ Completed processing page {PageNumber}", pageNumber);
                    }
                    _logger.LogInformation("✅ Database operations completed at batch level - Total processed: Trackers={TrackerCount}",
                         pendingContactIds.Count);
                }
                else
                {
                    _logger.LogWarning("⚠️ No audiences to process or meta details missing");
                }
                await _logHistoryService.SaveInformationLogHistoryAsyn("Campaign Updated WAMessageIds",
                            $"Campaign {campaign.CampaignId} updated successfully.", string.Join(",", waIds), "Update Success");

                _logger.LogInformation("✅ ProcessCampaignBatchAsync completed successfully for CampaignId: {CampaignId}", campaign.CampaignId);
            }
            catch (Exception ex)
            {
                _logger.LogError("Error Found to processign the {CampaignId},Error: {Error}", campaign?.CampaignId, ex.Message);
                Console.WriteLine(ex.Message);
                await _logHistoryService.SaveErrorLogHistoryAsyn("CreateCampaignAsync/ProcessCampaignBatchAsync->2", $"{page}:{pageSize},{JsonConvert.SerializeObject(campaign)}", "Campaign Error", ex.Message, JsonConvert.SerializeObject(ex.StackTrace));

                campaign.ErrorCause = $"CreateCampaignAsync/ProcessCampaignBatchAsync->2, {page}:{pageSize}, Campaign Error: {ex.Message}, {JsonConvert.SerializeObject(ex.StackTrace)}";
                var conditions = new Dictionary<string, object>()
                {
                     {nameof(Campaign.BusinessId), campaign.BusinessId },
                     {nameof(Campaign.CampaignId), campaign.CampaignId }
                };
                var columns = new List<string>() { nameof(Campaign.ErrorCause) };
                await _genericRepository.UpdateRecordAsync<Campaign>("Campaigns", columns, campaign, conditions);
            }
            finally
            {
                // Ensure campaign is updated even in case of failure
                if (campaignData != null)
                {
                    try
                    {
                        var columns = new List<string> { "State" };
                        var conditions = new Dictionary<string, object>
                        {
                            { "BusinessId", campaignData.BusinessId },
                            { "CampaignId", campaignData.CampaignId }
                        };
                        await _genericRepository.UpdateRecordAsync<Campaign>("Campaigns", columns, campaignData, conditions);
                        await _logHistoryService.SaveInformationLogHistoryAsyn("Campaign Updated",
                            $"Campaign {campaignData.CampaignId} updated successfully.", campaignData, "Update Success");
                    }
                    catch (Exception updateEx)
                    {
                        _logger.LogError("Error Found to processign the {CampaignId},Error: {Error}", campaign?.CampaignId, updateEx.Message);
                        await _logHistoryService.SaveErrorLogHistoryAsyn("Update Campaign Error",
                            $"Campaign {campaignData?.CampaignId} update failed.", "Campaign Error",
                            updateEx.Message, JsonConvert.SerializeObject(updateEx.StackTrace));

                        campaign.ErrorCause = $"Update Campaign Error, Campaign Error: {updateEx.Message}, {JsonConvert.SerializeObject(updateEx.StackTrace)}";
                        var conditions = new Dictionary<string, object>()
                        {
                            {nameof(Campaign.BusinessId), campaign.BusinessId },
                            {nameof(Campaign.CampaignId), campaign.CampaignId }
                        };
                        var columns = new List<string>() { nameof(Campaign.ErrorCause) };
                        await _genericRepository.UpdateRecordAsync<Campaign>("Campaigns", columns, campaign, conditions);
                    }
                }

            }
        }
        #region Get conversation formate
        private Conversations GetConversationFormateForTemplate(Template templateDto, List<Button>? buttonDetailDto, List<string>? bodyValue, string? headerValue, Guid campaignId, List<CarouselCardVariableDto> carouselVariableValues, Guid contactId)
        {
            var bodyMessage = StringHelper.ReplacePlaceholders(StringHelper.ReplaceAndExtractVariables(templateDto.Body).UpdatedMessage, bodyValue ?? new());

            var headerMessage = templateDto.MediaType == MediaType.TEXT
                ? StringHelper.ReplaceAndExtractVariables(templateDto.Header ?? string.Empty).UpdatedMessage.Replace("{{1}}", headerValue)
                : templateDto.Header;

            var phoneNumberButtons = buttonDetailDto?.FirstOrDefault(b => b.ButtonType == "PHONE_NUMBER");
            var urlButtons = buttonDetailDto?.Where(b => b.ButtonType == "URL").ToList();
            var quickReplyButtons = buttonDetailDto?.Where(b => b.ButtonType == "QUICK_REPLY").ToList();


            List<CarouselCardsDto> jsonCardsConverted = new List<CarouselCardsDto>();
            string conversationCarouselCards = "";
            if (templateDto.MediaType == MediaType.CAROUSEL)
            {
                jsonCardsConverted = JsonCardsConverter(templateDto.CarouselCardsJson ?? string.Empty, carouselVariableValues);
                conversationCarouselCards = UpdatedCarouselCardsJson(jsonCardsConverted, carouselVariableValues ?? new());
            }

            return new Conversations
            {
                Id = Guid.NewGuid(),
                From = templateDto.BusinessId,
                BusinessId = Guid.TryParse(templateDto.BusinessId, out var tempGuid) ? tempGuid : Guid.Empty,
                ContactId = contactId,
                TemplateBody = bodyMessage,
                TemplateHeader = headerMessage,
                TemplateMediaUrl = templateDto.MediaAwsUrl,
                TemplateFooter = templateDto.Footer,
                TemplateMediaType = templateDto.MediaType,
                MessageType = MessageType.Campaigns,
                CreatedAt = DateTime.UtcNow,
                ReferenceId = campaignId.ToString(),
                CallButtonName = phoneNumberButtons?.ButtonName ?? "",
                PhoneNumber = phoneNumberButtons?.ButtonValue ?? "",
                UrlButtonNames = urlButtons?.Any() == true ? string.Join(",", urlButtons?.Select(m => m.ButtonName)) : string.Empty,
                RedirectUrls = urlButtons?.Any() == true ? string.Join(",", urlButtons?.Select(x => x.ButtonValue)) : string.Empty,
                QuickReplies = quickReplyButtons?.Any() == true ? string.Join(",", quickReplyButtons?.Select(m => m.ButtonValue)) : string.Empty,
                CarouselCards = conversationCarouselCards
            };
        }

        public string SendCarouselCardsPayload(List<CarouselCardsDto> carouselCards, Template template, List<CarouselCardVariableDto> variables)
        {
            var regex = StringHelper.GetVariableRegexs();
            if (carouselCards == null || !carouselCards.Any())
            {
                throw new Exception("Carousel cards not found!");
            }
            var mediaType = MediaExtentionsHelper.GetMediaTypeFromUrl(template.MediaAwsUrl ?? string.Empty);
            var cards = carouselCards.Select((card, index) =>
            {
                var cardVariables = variables.ElementAtOrDefault(index);
                var bodyParameters = !string.IsNullOrEmpty(card.Body) && cardVariables?.BodyCarouselVariableValues != null
                    ? cardVariables.BodyCarouselVariableValues.Select(value => $@"{{ ""type"": ""text"", ""text"": ""{value}"" }}").ToList()
                    : new List<string>();


                var formateBody = StringHelper.FormateTemplateComponents(card.Body);
                var leadratVarBody = StringHelper.ReplaceAndExtractVariables(formateBody).UpdatedMessage;
                var bodyPayload = regex.IsMatch(leadratVarBody) && bodyParameters.Any()
                    ? $@"{{ ""type"": ""body"",
                        ""parameters"": [{string.Join(",", bodyParameters)}]
                     }}"
                    : string.Empty;

                var headerPayload = !string.IsNullOrEmpty(template.MediaAwsUrl)
                ? $@"{{ ""type"": ""header"",
                 ""parameters"": [{{
                                    ""type"": ""{card.MediaUrlType?.ToString().ToLower()}"",
                                    ""{card.MediaUrlType?.ToString().ToLower()}"": {{ ""link"": ""{card.HeaderMediaUrl}"" }}
                                  }}]
                }}"
            : string.Empty;


                var buttonsPayload = SendCarouselButtonPayload(card, cardVariables ?? new());

                var cardComponents = new List<string> { headerPayload, bodyPayload, buttonsPayload }
                    .Where(component => !string.IsNullOrEmpty(component))
                    .ToList();

                return $@"{{ ""card_index"": ""{index}"", ""components"": [{string.Join(",", cardComponents)}] }}";
            }).ToList();

            return $@"{{ ""type"": ""carousel"", ""cards"": [{string.Join(",", cards)}] }}";
        }
        private static string SendCarouselButtonPayload(CarouselCardsDto card, CarouselCardVariableDto variables)
        {
            if (card.UrlButtonName?.Count > 0 && card.RedirectUrl?.Count == card.UrlButtonName.Count)
            {
                var buttonsArray = card.UrlButtonName.Select((name, index) => new JObject
                {
                   { "type", "button" },
                   { "sub_type", "url" },
                   { "index", index.ToString() },
                   { "parameters", new JArray  { new JObject
                    {
                        { "type", "text" },
                        { "text", variables?.RedirectUrlVariableValues != null && variables.RedirectUrlVariableValues.Length > index
                            ? variables.RedirectUrlVariableValues[index]
                            : name
                        }
                    }
                }
            }
            }).ToArray();

                return string.Join(",", buttonsArray.Select(x => x.ToString(Newtonsoft.Json.Formatting.None)));
            }
            return string.Empty;
        }
        public static string UpdatedCarouselCardsJson(List<CarouselCardsDto> jsonCardsConverted, List<CarouselCardVariableDto> carouselCardVariables)
        {
            try
            {
                var updatedCards = jsonCardsConverted.Select((card, index) =>
                {
                    if (index < carouselCardVariables.Count)
                    {
                        var carouselVariable = carouselCardVariables[index];

                        if (!string.IsNullOrEmpty(card.Body) && carouselVariable.BodyCarouselVariableValues != null)
                        {
                            var leadratVar = StringHelper.ReplaceAndExtractVariables(card.Body).UpdatedMessage;
                            card.Body = StringHelper.ReplacePlaceholders(leadratVar, carouselVariable.BodyCarouselVariableValues.ToList());

                        }

                        if (card.RedirectUrl != null && carouselVariable.RedirectUrlVariableValues != null)
                        {
                            for (int i = 0; i < card.RedirectUrl.Count(); i++)
                            {
                                if (i < carouselVariable.RedirectUrlVariableValues.Count())
                                {
                                    var leadratUrlVar = StringHelper.ReplaceAndExtractVariables(card.RedirectUrl[i]).UpdatedMessage;
                                    card.RedirectUrl[i] = StringHelper.ReplacePlaceholders(leadratUrlVar, new List<string> { carouselVariable.RedirectUrlVariableValues[i] });
                                }
                            }
                        }

                        if (!string.IsNullOrEmpty(carouselVariable.MediaUrl))
                        {
                            card.HeaderMediaUrl = carouselVariable.MediaUrl;
                        }
                    }
                    return card;
                }).ToList();

                var jsonArray = JArray.FromObject(updatedCards);
                return jsonArray.ToString();
            }
            catch (Exception ex)
            {
                return $"Error processing carouselCards: {ex.Message}";
            }
        }
        public static List<CarouselCardsDto> JsonCardsConverter(string carouselCardsJson, List<CarouselCardVariableDto>? carouselVariables)
        {
            if (string.IsNullOrEmpty(carouselCardsJson))
            {
                throw new ArgumentException("CarouselCardsJson cannot be null or empty.");
            }

            List<CarouselCardsDto> cards = new List<CarouselCardsDto>();
            try
            {
                var jsonArray = JsonConvert.DeserializeObject<List<string>>(carouselCardsJson);

                if (jsonArray != null)
                {
                    foreach (var jsonString in jsonArray)
                    {
                        var card = JsonConvert.DeserializeObject<CarouselCardsDto>(jsonString);
                        if (card != null)
                        {
                            cards.Add(card);
                        }
                    }
                }
                else
                {
                    cards = JsonConvert.DeserializeObject<List<CarouselCardsDto>>(carouselCardsJson) ?? new List<CarouselCardsDto>();
                }
            }
            catch (JsonSerializationException ex)
            {
                throw new ArgumentException("Error deserializing CarouselCardsJson. Ensure the JSON matches the expected format.", ex);
            }

            return cards;
        }
        public Conversations GetTextMediaConversationFormate(string? textMessage, string from, string to, string? mediaType, string? caption, string? mediaMimeType, string? mediaUrl, string waMessageId, string? replyId, Guid campaignId, Guid contactId)
        {
            Guid businessId = Guid.TryParse(from, out var bid) ? bid : Guid.TryParse(to, out bid) ? bid : Guid.Empty;

            Conversations conv = new Conversations
            {
                Id = Guid.NewGuid(),
                CreatedAt = DateTime.UtcNow,
                WhatsAppMessageId = waMessageId,
                TextMessage = textMessage ?? string.Empty,
                MediaCaption = caption ?? string.Empty,
                ReplyId = replyId,
                To = to.ToLowerInvariant().Replace("+", ""),
                From = from.ToString().ToLowerInvariant().Replace("+", ""),
                MediaUrl = mediaUrl,
                Status = EngagetoEntities.Enums.ConvStatus.sent,
                MediaMimeType = mediaMimeType,
                MessageType = MessageType.Campaigns,
                ReferenceId = campaignId.ToString(),
                BusinessId = businessId,
                ContactId = contactId
            };
            return conv;
        }
        #endregion
        #region Save conversations
        private async Task SaveConversationsAsync(List<Conversations> conversations)
        {
            if (conversations == null || !conversations.Any())
            {
                _logger.LogWarning("⚠️ No conversations to save - list is null or empty");
                return;
            }

            try
            {
                _logger.LogInformation("💾 Saving {ConversationCount} conversations to database", conversations.Count);

                var columns = StringHelper.GetPropertyNames<Conversations>(isNotMappedAttributeColumn: false);
                bool isInserted = await _genericRepository.InsertRecordsAsync("Conversations", columns, conversations);

                if (isInserted)
                {
                    _logger.LogInformation("✅ Successfully saved {ConversationCount} conversations", conversations.Count);

                    await _logHistoryService.SaveSuccessLogHistoryAsyn(
                        "SaveConversationsAsync",
                        $"Saved {conversations.Count} conversations",
                        "Success",
                        "Conversations saved successfully");
                }
                else
                {
                    _logger.LogError("❌ Failed to save conversations - InsertRecordsAsync returned false");

                    await _logHistoryService.SaveErrorLogHistoryAsyn(
                        "SaveConversationsAsync",
                        $"Failed to save {conversations.Count} conversations",
                        "Database Insert Failed",
                        "InsertRecordsAsync returned false",
                        "No exception thrown but insert failed");
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "❌ Error saving {ConversationCount} conversations: {ErrorMessage}",
                    conversations.Count, ex.Message);

                await _logHistoryService.SaveErrorLogHistoryAsyn(
                    "SaveConversationsAsync",
                    JsonConvert.SerializeObject(new { ConversationCount = conversations.Count, ConversationIds = conversations.Select(c => c.Id).Take(10) }),
                    "Conversation Save Error",
                    ex.Message,
                    ex.StackTrace);

                return;
            }
        }
        #endregion

        #region Get Media type and mime type
        private async Task<(string MimeType, string MediaType)> GetMediaTypeAsync(string url)
        {
            try
            {
                var client = new HttpClient();
                var response = await client.GetAsync(url);

                if (!response.IsSuccessStatusCode)
                    throw new InvalidOperationException("Failed to retrieve content from the provided URL.");

                var mimeType = response.Content.Headers.ContentType?.MediaType;
                if (string.IsNullOrEmpty(mimeType))
                    throw new InvalidOperationException("Content type not found.");

                var mediaType = GetMediaCategory(mimeType);
                return (mimeType, mediaType);
            }
            catch (Exception ex)
            {
                throw new InvalidOperationException("The provided URL is not valid or accessible.", ex);
            }
        }
        private string GetMediaCategory(string mimeType)
        {
            if (mimeType.StartsWith("application/"))
            {
                var documentTypes = new HashSet<string>
                {
                    "application/pdf",
                    "application/msword",
                    "application/vnd.openxmlformats-officedocument.wordprocessingml.document",
                    "application/vnd.ms-excel",
                    "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet",
                    "text/plain"
                };

                return documentTypes.Contains(mimeType) ? "document" : "Others";
            }

            var mediaType = mimeType.Split('/')[0]; // Example: "image/jpeg" -> "image"
            return mediaType switch
            {
                "image" => "image",
                "video" => "video",
                "audio" => "audio",
                _ => "Others"
            };
        }
        #endregion
        #region Map Contact details for body and header
        private string MapContact(ContactInfoDto contact, string variables)
        {
            if (contact == null || string.IsNullOrWhiteSpace(variables))
                return variables;

            List<string> values = new List<string>();
            var dictContact = ObjectHelper.ConvertToDictionary(contact);

            var result = variables
                .Split(',')
                .Select(x => dictContact.ContainsKey(x) ? !string.IsNullOrEmpty(dictContact[x]) ? dictContact[x] : x : x)
                .ToList();

            return string.Join(",", result);
        }

        #endregion

        #region 🚀 OPTIMIZED PARALLEL PROCESSING METHODS
        /// <summary>
        /// Process a single batch of contacts with optimized HTTP client usage
        /// </summary>
        private async Task ProcessContactBatchWithChannel(
            List<ContactInfoDto> contacts,
            Template? template,
            List<Button>? buttons,
            Campaign campaign,
            BusinessDetailsMeta metaDetails,
            string mimeType,
            string mediaType)
        {
            var conversations = new ConcurrentBag<Conversations>();
            var successTrackers = new ConcurrentBag<CampaignTrackerStatus>();
            var failedTrackers = new ConcurrentBag<CampaignTrackerStatus>();
            var LastMessageDateUpdateForContacts = new ConcurrentBag<Guid>();

            var firstId = contacts.FirstOrDefault()?.ContactId;
            var lastId = contacts.LastOrDefault()?.ContactId;

            _logger.LogInformation("📦 Starting batch for {Count} contacts. FirstId: {FirstId}, LastId: {LastId}, MAX_CONSUMERS: {MaxConsumers}",
                contacts.Count, firstId, lastId, 10);

            var channel = Channel.CreateUnbounded<ContactInfoDto>();

            // Producer
            var producer = Task.Run(async () =>
            {
                foreach (var contact in contacts)
                {
                    await channel.Writer.WriteAsync(contact).ConfigureAwait(false);
                }
                channel.Writer.Complete();
            });

            const int MAX_CONSUMERS = 10;

            var consumers = Enumerable.Range(0, MAX_CONSUMERS).Select(consumerId => Task.Run(async () =>
            {
                int processedByThisConsumer = 0;
                _logger.LogInformation("🔄 Consumer {ConsumerId} started", consumerId);

                await foreach (var contact in channel.Reader.ReadAllAsync().ConfigureAwait(false))
                {
                    try
                    {
                        processedByThisConsumer++;
                        var fullContact = $"{contact.CountryCode}{contact.Contact}".Replace("+", "");

                        //_logger.LogDebug("📲 Consumer {ConsumerId} processing ContactId: {ContactId}", consumerId, contact.ContactId);

                        var result = await ProcessSingleContact(contact, fullContact, template, buttons, campaign, metaDetails, mimeType, mediaType)
                            .ConfigureAwait(false);

                        LastMessageDateUpdateForContacts.Add(contact.ContactId);
                        if (result != null)
                        {
                            conversations.Add(result.Conversation);
                            successTrackers.Add(new CampaignTrackerStatus
                            {
                                ContactId = contact.ContactId.ToString(),
                                WhatsAppMessageId = result.Conversation.WhatsAppMessageId,
                                ConvStatus = ConvStatus.sent
                            });
                        }
                        else
                        {
                            failedTrackers.Add(new CampaignTrackerStatus
                            {
                                ContactId = contact.ContactId.ToString(),
                                WhatsAppMessageId = string.Empty,
                                ConvStatus = ConvStatus.failed
                            });
                        }
                    }
                    catch (Exception ex)
                    {
                        _logger.LogError(ex, "❌ Consumer {ConsumerId} failed processing ContactId: {ContactId}", consumerId, contact.ContactId);

                        failedTrackers.Add(new CampaignTrackerStatus
                        {
                            ContactId = contact.ContactId.ToString(),
                            WhatsAppMessageId = string.Empty,
                            ConvStatus = ConvStatus.failed,
                            ErrorMessage = ex.Message
                        });
                    }
                }
                _logger.LogInformation("✅ Consumer {ConsumerId} finished. Processed {Count} contacts", consumerId, processedByThisConsumer);
            }));

            await Task.WhenAll(consumers.Prepend(producer)).ConfigureAwait(false);

            await SaveConversationsAsync(conversations.ToList()).ConfigureAwait(false);
            await UpdateLastMessageInContactsAsync(LastMessageDateUpdateForContacts.ToList()).ConfigureAwait(false);
            await UpdateCampaignTrackerStatusAsync(successTrackers.ToList(), campaign.BusinessId.ToString(), campaign.CampaignId, "Completed")
                .ConfigureAwait(false);

            if (failedTrackers.Any())
            {
                await UpdateCampaignTrackerStatusAsync(failedTrackers.ToList(), campaign.BusinessId.ToString(), campaign.CampaignId, "Failed")
                    .ConfigureAwait(false);
            }

            _logger.LogInformation("🏁 Completed batch for contacts: {FirstId} to {LastId}. ✅ Success: {SuccessCount}, ❌ Failures: {FailCount}",
                firstId, lastId, successTrackers.Count, failedTrackers.Count);
        }

        private async Task UpdateLastMessageInContactsAsync(List<Guid> contactIds)
        {
            try
            {
                var columnsToUpdate = new List<string> { "LastMessageAt" };
                var entity = new { LastMessageAt = DateTime.UtcNow };

                await _genericRepository.UpdateRecordsByIdsAsync(
                    tableName: "Contacts",
                    columnsToUpdate: columnsToUpdate,
                    updateValues: entity,
                    idColumn: "ContactId",
                    ids: contactIds
                );

            }
            catch (Exception ex)
            {
                Console.WriteLine($"Error updating LastMessageAt: {ex.Message}");
            }
        }

        /// <summary>
        /// Process a single contact and return the result
        /// </summary>
        private async Task<ContactProcessingResult?> ProcessSingleContact(
            EngagetoEntities.Dtos.ContactDtos.ContactInfoDto contact,
            string phoneNumber,
            Template? template,
            List<Button>? buttons,
            Campaign campaign,
            BusinessDetailsMeta metaDetails,
            string mimeType,
            string mediaType)
        {
            if (template != null)
            {
                return await ProcessTemplateMessage(contact, phoneNumber, template, buttons, campaign,
                    metaDetails);
            }
            else
            {
                return await ProcessTextMediaMessage(contact, phoneNumber, campaign, metaDetails,
                    mimeType, mediaType);
            }
        }

        /// <summary>
        /// Process template message for a single contact
        /// </summary>
        private async Task<ContactProcessingResult?> ProcessTemplateMessage(
            EngagetoEntities.Dtos.ContactDtos.ContactInfoDto contact,
            string phoneNumber,
            Template template,
            List<Button>? buttons,
            Campaign campaign,
            BusinessDetailsMeta metaDetails)
        {
            try
            {
                _logger.LogInformation("🚀 Processing template message for contact {ContactId} with phone {PhoneNumber}",
                    contact.ContactId, phoneNumber);

                string? bodyValues = MapContact(contact, campaign.BodyValues ?? string.Empty);
                string? headerValue = MapContact(contact, campaign.HeaderValue ?? string.Empty);

                if (template.MediaType != MediaType.TEXT && template.MediaType != MediaType.NONE)
                {
                    headerValue = template.MediaAwsUrl;
                }

                List<CarouselCardVariableDto> carouselVariableValues =
                    JsonConvert.DeserializeObject<List<CarouselCardVariableDto>>(campaign.CarouselVariables ?? "[]") ?? new();
           
                var conv = GetConversationFormateForTemplate(template, buttons, bodyValues?.Split(",").ToList(),
                    headerValue, campaign.CampaignId, carouselVariableValues, contact.ContactId).Clone() as Conversations;

                conv.To = phoneNumber;

                _logger.LogInformation("📝 Template prepared for contact {ContactId}: Template={TemplateName}, MediaType={MediaType}",
                    contact.ContactId, template.TemplateName, template.MediaType);

                // Process carousel or regular template
                if (template.MediaType == MediaType.CAROUSEL)
                {
                    _logger.LogInformation("🎠 Processing carousel template for contact {ContactId}", contact.ContactId);
                    var response = await ProcessCarouselTemplate(template, phoneNumber, bodyValues,
                        carouselVariableValues, campaign, metaDetails);

                    var result = ProcessTemplateResponse(response, conv);
                    bool isSuccess = result != null;
                    _logger.LogInformation("✅ Carousel template processed for contact {ContactId}, Success: {Success}", contact.ContactId, isSuccess);
                    return result;
                }
                else
                {
                    _logger.LogInformation("📤 Sending regular template for contact {ContactId}", contact.ContactId);
                    var response = await _metaApiService.SendTemplateAsync(
                        campaign.BusinessId.ToString(),
                        template.LanguageCode.ToString(),
                        phoneNumber,
                        template.TemplateName,
                        bodyValues?.Split(",").ToList(),
                        template.MediaType,
                        headerValue,
                        metaDetails.Token,
                        metaDetails.PhoneNumberID,
                        metaDetails.WhatsAppBusinessAccountID,
                        campaign.CampaignId.ToString(),
                        campaign.CampaignTitle
                        );

                    var result = ProcessTemplateResponse(response, conv);
                    _logger.LogInformation("✅ Regular template processed for contact {ContactId}, Success: {Success}",
                        contact.ContactId, result != null);
                    return result;
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "❌ Error processing template for contact {ContactId}: {ErrorMessage}",
                    contact.ContactId, ex.Message);

                await _logHistoryService.SaveErrorLogHistoryAsyn(
                    "ProcessTemplateMessage",
                    JsonConvert.SerializeObject(new { ContactId = contact.ContactId, PhoneNumber = phoneNumber, TemplateName = template.TemplateName }),
                    "Template Processing Error",
                    ex.Message,
                    ex.StackTrace);

                return null;
            }
        }

        /// <summary>
        /// Process text/media message for a single contact
        /// </summary>
        private async Task<ContactProcessingResult?> ProcessTextMediaMessage(
            EngagetoEntities.Dtos.ContactDtos.ContactInfoDto contact,
            string phoneNumber,
            Campaign campaign,
            BusinessDetailsMeta metaDetails,
            string mimeType,
            string mediaType)
        {
            try
            {
                _logger.LogInformation("📱 Processing text/media message for contact {ContactId} with phone {PhoneNumber}, MediaType: {MediaType}",
                    contact.ContactId, phoneNumber, mediaType);

                var response = await _metaApiService.SendTextWithMediaMessageAsync(
                    campaign.BusinessId,
                    phoneNumber,
                    StringHelper.FormateEscapeSequences(campaign.SendTextType ?? string.Empty),
                    mediaType,
                    campaign.MediaUrl,
                    null,
                    metaDetails.Token,
                    metaDetails.PhoneNumberID,
                    metaDetails.WhatsAppBusinessAccountID);

                var caption = !string.IsNullOrEmpty(campaign.MediaUrl) ? campaign.SendTextType : string.Empty;
                var text = !string.IsNullOrEmpty(campaign.MediaUrl) ? string.Empty : campaign.SendTextType;

                var conv = GetTextMediaConversationFormate(text, campaign.BusinessId, phoneNumber, mediaType,
                    caption, mimeType, campaign.MediaUrl, string.Empty, string.Empty, campaign.CampaignId, contact.ContactId);

                if (response.IsSuccess)
                {
                    var result = response.Result.ToObject<WhatsAppResponse>();
                    conv.Status = ConvStatus.sent;
                    conv.WhatsAppMessageId = result?.Messages?.FirstOrDefault()?.Id ?? string.Empty;

                    _logger.LogInformation("✅ Text/media message sent successfully for contact {ContactId}, MessageId: {MessageId}",
                        contact.ContactId, conv.WhatsAppMessageId);

                    return new ContactProcessingResult
                    {
                        Conversation = conv,
                        WhatsAppId = conv.WhatsAppMessageId
                    };
                }
                else
                {
                    var result = response.Result.ToObject<WhatsAppErrorResponse>();
                    conv.Status = ConvStatus.failed;
                    conv.ErrorMessage = result?.Error?.Message ?? string.Empty;
                    conv.ErrorDetails = JsonConvert.SerializeObject(response.Result) ?? string.Empty;

                    _logger.LogWarning("⚠️ Text/media message failed for contact {ContactId}, Error: {ErrorMessage}",
                        contact.ContactId, conv.ErrorMessage);

                    return new ContactProcessingResult
                    {
                        Conversation = conv,
                        WhatsAppId = conv.WhatsAppMessageId
                    };
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "❌ Error processing text/media for contact {ContactId}: {ErrorMessage}",
                    contact.ContactId, ex.Message);

                await _logHistoryService.SaveErrorLogHistoryAsyn(
                    "ProcessTextMediaMessage",
                    JsonConvert.SerializeObject(new { ContactId = contact.ContactId, PhoneNumber = phoneNumber, MediaType = mediaType }),
                    "Text/Media Processing Error",
                    ex.Message,
                    ex.StackTrace);

                return null;
            }
        }

        /// <summary>
        /// Process carousel template response
        /// </summary>
        private async Task<dynamic> ProcessCarouselTemplate(
            Template template,
            string phoneNumber,
            string? bodyValues,
            List<CarouselCardVariableDto> carouselVariableValues,
            Campaign campaign,
            BusinessDetailsMeta metaDetails)
        {
            List<CarouselCardsDto> jsonCardsConverted = JsonCardsConverter(template.CarouselCardsJson ?? string.Empty, carouselVariableValues);

            var regex = StringHelper.GetVariableRegexs();
            var carouselBody = "";
            foreach (var card in jsonCardsConverted)
            {
                var formateCardBody = StringHelper.FormateTemplateComponents(card.Body);
                var leadratCardVarBody = StringHelper.ReplaceAndExtractVariables(formateCardBody).UpdatedMessage;
                if (regex.IsMatch(leadratCardVarBody))
                {
                    carouselBody = string.Join(",", carouselVariableValues.Select(card =>
                        string.Join(",", card.BodyCarouselVariableValues.Select(value =>
                            $@"{{ ""type"": ""text"", ""text"": ""{value}"" }}"))));
                }
            }

            var carouselCardsPayload = SendCarouselCardsPayload(jsonCardsConverted, template, carouselVariableValues);
            var jsonPayload = CreateTemplatePayloads.SendCarouselTemplatePayload(
                template.TemplateName,
                phoneNumber,
                bodyValues?.Split(",").ToList() ?? new(),
                template.LanguageCode,
                carouselCardsPayload,
                carouselBody,
                campaign.CampaignId.ToString(),
                campaign.CampaignTitle
                );

            JObject json = JObject.Parse(jsonPayload);
            return await _metaApiService.SendCarouselTemplateAsync(campaign.BusinessId, json, metaDetails.PhoneNumberID, metaDetails.Token);
        }

        /// <summary>
        /// Process template response and create result
        /// </summary>
        private ContactProcessingResult? ProcessTemplateResponse((bool IsSuccess, Newtonsoft.Json.Linq.JObject Result) response, Conversations conv)
        {
            _logger.LogInformation("📋 Processing template response - IsSuccess: {IsSuccess}", response.IsSuccess);

            try
            {
                if (response.IsSuccess)
                {
                    var waId = StringHelper.GetValueFromResponse(response.Result, "messages[0].id") ?? string.Empty;
                    conv.WhatsAppMessageId = waId;
                    conv.Status = ConvStatus.sent;

                    _logger.LogInformation("✅ Template response processed successfully - MessageId: {MessageId}", waId);

                    return new ContactProcessingResult
                    {
                        Conversation = conv,
                        WhatsAppId = waId
                    };
                }
                else
                {
                    conv.WhatsAppMessageId = StringHelper.GetValueFromResponse(response.Result, "messages[0].id") ?? string.Empty;
                    conv.Status = ConvStatus.failed;
                    conv.ErrorMessage = StringHelper.GetValueFromResponse(response.Result, "error.message") ?? "Unknown error";
                    conv.ErrorDetails = JsonConvert.SerializeObject(response.Result);

                    _logger.LogWarning("⚠️ Template response failed - Error: {ErrorMessage}", conv.ErrorMessage);

                    return new ContactProcessingResult
                    {
                        Conversation = conv,
                        WhatsAppId = conv.WhatsAppMessageId
                    };
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "❌ Error processing template response: {ErrorMessage}", ex.Message);

                conv.Status = ConvStatus.failed;
                conv.ErrorMessage = "Failed to process response";
                conv.ErrorDetails = response.Result?.ToString() ?? "No response data";

                return new ContactProcessingResult
                {
                    Conversation = conv,
                    WhatsAppId = conv.WhatsAppMessageId
                };
            }
        }

        /// <summary>
        /// Result class for contact processing
        /// </summary>
        private class ContactProcessingResult
        {
            public Conversations Conversation { get; set; } = new();
            public string WhatsAppId { get; set; } = string.Empty;
        }

        #endregion

        public async Task GetContactIdsToExcel(int uploadFileId, Campaign campaign)
        {
            var uploadedFile = (await _genericRepository.GetByObjectAsync<UploadedFile>(
                new Dictionary<string, object> { { "Id", campaign.UploadedFileId ?? 0 } }
            ))?.FirstOrDefault();

            if (uploadedFile == null || string.IsNullOrEmpty(uploadedFile.FilePath))
            {
                _logger.LogWarning("⚠️ Uploaded file not found or file path is empty for UploadFileId: {UploadFileId}", uploadFileId);
            }

            _logger.LogInformation("🚀 Starting chunked Excel processing for file: {FilePath}", uploadedFile.FilePath);

            // Use chunked processing approach for memory efficiency
            await ProcessExcelInChunksWithCampaignTracker(uploadedFile.FilePath, campaign);
        }

        /// <summary>
        /// Process Excel file in memory-efficient chunks of 500 records and create CampaignTracker records
        /// </summary>
        private async Task ProcessExcelInChunksWithCampaignTracker(string filePath, Campaign campaign)
        {
            const int chunkSize = 500; // Process 500 records at a time for optimal memory usage
            var allContactIds = new List<string>();
            var processedCount = 0;

            try
            {
                _logger.LogInformation("🚀 Starting memory-efficient Excel processing with chunk size: {ChunkSize}", chunkSize);

                // Use streaming approach to avoid loading entire file into memory
                //var allContactIds_temp = await ProcessExcelInMemoryEfficientChunks(filePath, campaign, chunkSize);
                await ProcessExcelInMemoryEfficientChunks(filePath, campaign, chunkSize);

                _logger.LogInformation("🎯 Excel processing completed. Total contacts processed: {TotalProcessed}, CampaignTracker records created", chunkSize);
                //return allContactIds_temp;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "❌ Error during chunked Excel processing: {ErrorMessage}", ex.Message);
                throw;
            }
        }

        /// <summary>
        /// Process Excel file with TRUE streaming using ExcelProcessorcs helper - reads only 500 records at a time from file
        /// </summary>
        private async Task ProcessExcelInMemoryEfficientChunks(string filePath, Campaign campaign, int chunkSize)
        {
            var allContactIds = new List<string>();
            var chunkNumber = 0;

            try
            {
                _logger.LogInformation("🚀 Starting TRUE streaming Excel processing using ExcelProcessorcs helper with chunk size: {ChunkSize}", chunkSize);

                // Use ExcelProcessorcs streaming method that never loads entire file into memory
                // ExcelProcessorcs now handles duplicate filtering internally
                var excelProcessor = new ExcelProcessorcs(ExcelMapping.ContactColumnMapping);

                await foreach (var chunk in excelProcessor.ProcessExcelInChunks<Contacts>(filePath, chunkSize))
                {
                    chunkNumber++;

                    if (chunk == null || !chunk.Any())
                    {
                        _logger.LogInformation("📭 Empty chunk {ChunkNumber}, skipping", chunkNumber);
                        continue;
                    }

                    _logger.LogInformation("📦 Processing chunk {ChunkNumber} with {ContactCount} unique contacts (duplicates filtered by ExcelProcessorcs)",
                        chunkNumber, chunk.Count);

                    // Process this chunk and immediately release memory
                    // No need for additional duplicate filtering as ExcelProcessorcs handles it
                    await ProcessContactChunkWithTracker(chunk.DistinctBy(i => i.Contact).ToList(), campaign, chunkNumber);
                    //allContactIds.AddRange(chunkContactIds);

                    // Clear chunk from memory immediately
                    chunk.Clear();

                    // Force garbage collection every 5 chunks to keep memory usage low
                    if (chunkNumber % 5 == 0)
                    {
                        GC.Collect();
                        GC.WaitForPendingFinalizers();
                        _logger.LogInformation("🧹 Memory cleanup performed after chunk {ChunkNumber}", chunkNumber);
                    }

                    _logger.LogInformation("✅ Completed chunk {ChunkNumber}: {ProcessedCount} total contacts processed",
                        chunkNumber, allContactIds.Count);
                }

                // Final cleanup
                GC.Collect();

                _logger.LogInformation("🎯 TRUE streaming Excel processing completed using ExcelProcessorcs. Total contacts processed: {TotalProcessed}", allContactIds.Count);
                //return allContactIds;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "❌ Error during TRUE streaming Excel processing: {ErrorMessage}", ex.Message);
                throw;
            }
        }

        /// <summary>
        /// Process a chunk of contacts with CampaignTracker creation
        /// </summary>
        private async Task ProcessContactChunkWithTracker(List<Contacts> contactChunk, Campaign campaign, int chunkNumber)
        {
            var contactIds = new List<string>();

            try
            {
                _logger.LogInformation("📦 Processing chunk {ChunkNumber} with {ContactCount} contacts", chunkNumber, contactChunk.Count);

                // Clean and format contact data
                contactChunk.ForEach(x =>
                {
                    if (string.IsNullOrEmpty(x.Name))
                        x.Name = x.Contact;
                    if (string.IsNullOrEmpty(x.CountryCode))
                        x.CountryCode = "+91";
                    else if (!x.CountryCode.StartsWith("+"))
                        x.CountryCode = $"+{x.CountryCode.TrimStart('+')}";
                });

                // Handle tags for this chunk
                Dictionary<string, Guid> tags = new Dictionary<string, Guid>();
                if (contactChunk.Any(x => !string.IsNullOrEmpty(x.Tags)))
                {
                    var allTags = contactChunk.Where(x => !string.IsNullOrWhiteSpace(x.Tags))
                         .SelectMany(x => x.Tags?.Split(",", StringSplitOptions.RemoveEmptyEntries | StringSplitOptions.TrimEntries))
                         .Select(t => t.ToLowerInvariant())
                         .Distinct()
                         .ToList();
                    tags = await CreateAndGetTags(allTags, campaign.BusinessId, campaign.UserId);
                }

                // Check existing contacts to avoid duplicates (using smaller batch size for memory efficiency)
                var contactNumbers = contactChunk.Select(x => x.Contact).ToList();
                var existingContacts = await FetchExistingContactsInBatchesAsync(contactNumbers, Guid.Parse(campaign.BusinessId), 500);
                var newContacts = contactChunk.Where(x => !existingContacts.Select(c => c.Contact).Contains(x.Contact)).ToList();

                // Prepare tag lookup for new contacts
                var tagLookup = tags.ToDictionary(kvp => kvp.Key.ToLowerInvariant(), kvp => kvp.Value.ToString());

                // Process new contacts
                if (newContacts.Any())
                {
                    newContacts.ForEach(x =>
                    {
                        x.ContactId = Guid.NewGuid();
                        x.BusinessId = Guid.Parse(campaign.BusinessId);
                        x.ChatStatus = ChatStatus.New;
                        x.IsOptIn = Is_OptIn.optin;
                        x.CreatedDate = DateTime.UtcNow;
                        x.Source = SourceType.Excel;
                        x.IsActive = true;

                        // Process tags
                        var contactTags = x.Tags?.Split(",", StringSplitOptions.RemoveEmptyEntries | StringSplitOptions.TrimEntries)
                                                .Select(t => t.ToLowerInvariant())
                                                .ToHashSet() ?? new HashSet<string>();

                        x.Tags = string.Join(",", contactTags
                            .Where(tagLookup.ContainsKey)
                            .Select(tag => tagLookup[tag]));
                    });

                    // Bulk insert new contacts
                    var inserted = await _genericRepository.InsertRecordsAsync("Contacts",
                        StringHelper.GetPropertyNames<Contacts>(), newContacts);

                    if (!inserted)
                    {
                        _logger.LogError("❌ Failed to insert contacts for chunk {ChunkNumber}", chunkNumber);
                        throw new Exception($"Failed to insert contacts for chunk {chunkNumber}");
                    }

                    _logger.LogInformation("✅ Inserted {NewContactCount} new contacts for chunk {ChunkNumber}", newContacts.Count, chunkNumber);
                }

                // Collect all contact IDs (existing + new)
                var allContactIds = existingContacts.Select(x => x.ContactId).ToList();
                allContactIds.AddRange(newContacts.Select(x => x.ContactId));

                // PHASE 1: Create CampaignTracker records for ALL contacts (new + existing)
                var campaignTrackers = CreateCampaignTrackers(campaign.CampaignId, allContactIds, Guid.Parse(campaign.BusinessId), campaign.UserId);

                    // Bulk insert CampaignTracker records
                    var trackersInserted = await _genericRepository.InsertRecordsAsync("CampaignTracker",
                        GetCampaignTrackerPropertyNames(), campaignTrackers);

                    if (!trackersInserted)
                    {
                        _logger.LogError("❌ Failed to insert campaign trackers for chunk {ChunkNumber}", chunkNumber);
                        throw new Exception($"Failed to insert campaign trackers for chunk {chunkNumber}");
                    }

                contactIds.AddRange(allContactIds.Select(id => id.ToString()));

                _logger.LogInformation("✅ Chunk {ChunkNumber} completed: {NewContacts} new, {ExistingContacts} existing, {TotalTrackers} trackers created",
                    chunkNumber, newContacts.Count, existingContacts.Count, campaignTrackers.Count);

                // Clear temporary collections to free memory
                newContacts.Clear();
                existingContacts.Clear();
                campaignTrackers.Clear();
                contactNumbers.Clear();
                allContactIds.Clear();
                //return contactIds;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "❌ Error processing chunk {ChunkNumber}: {ErrorMessage}", chunkNumber, ex.Message);
                throw;
            }
        }

        /// <summary>
        /// Create CampaignTracker records for a list of contact IDs
        /// </summary>
        private List<CampaignTracker> CreateCampaignTrackers(Guid campaignId, List<Guid> contactIds, Guid businessId, Guid userId)
        {
            return contactIds.Distinct().Select(contactId => new CampaignTracker
            {
                Id = Guid.NewGuid(),
                CampaignId = campaignId,
                ContactId = contactId.ToString(),
                BusinessId = businessId.ToString(),
                UserId = userId,
                Status = "Pending", // Default status for new trackers
                CreatedAt = DateTime.UtcNow,
                CreatedBy = userId,
                UpdatedAt = DateTime.UtcNow,
                UpdatedBy = userId,
                IsDeleted = false
            }).ToList();
        }

        /// <summary>
        /// Get property names for CampaignTracker entity
        /// </summary>
        private List<string> GetCampaignTrackerPropertyNames()
        {
            return StringHelper.GetPropertyNames<CampaignTracker>(isNotMappedAttributeColumn: false);
        }
        /// <summary>
        /// Get pending CampaignTracker records for batching
        /// </summary>
        private async Task<List<string>> GetPendingCampaignContactIdsAsync(Guid campaignId, int page, int pageSize)
        {
            var filters = new List<RequestFilterDto>
            {
                new(nameof(CampaignTracker.Status), "Pending", "="),
                new(nameof(CampaignTracker.CampaignId), campaignId,"=")
            };

            var ids = await _genericRepository.GetColumnValuesAsync<string>
                (
                    filters, columnName: nameof(CampaignTracker.ContactId), tableName: nameof(CampaignTracker),
                    orderByColumn: nameof(CampaignTracker.Id),
                    orderDirection: "ASC",
                    page: 1,
                    pageSize: pageSize
                );
            return ids;
        }

        /// <summary>
        /// Update CampaignTracker status after processing
        /// </summary>
        private async Task UpdateCampaignTrackerStatusAsync(List<CampaignTrackerStatus> trackerStatuses, string businessId, Guid campaignId, string status)
        {
            var trackers = new List<CampaignTracker>();

            foreach (var kvp in trackerStatuses)
            {
                trackers.Add(new CampaignTracker
                {
                    ContactId = kvp.ContactId,
                    WhatsAppMessagesId = kvp.WhatsAppMessageId,
                    BusinessId = businessId,
                    CampaignId = campaignId,
                    Status = status,
                    ErrorMessage = kvp.ErrorMessage,
                    ConvStatus = kvp.ConvStatus,
                });
            }

            await _campaignRespository.BulkUpdateCampaignTrackerAsync(trackers);
        }
        private async Task<List<Contacts>> FetchExistingContactsInBatchesAsync(List<string> contactNumbers, Guid businessId, int batchSize = 1000)
        {
            var allMatchingContacts = new List<Contacts>();
            int total = contactNumbers.Count;
            int totalBatches = (int)Math.Ceiling(total / (double)batchSize);

            for (int i = 0; i < totalBatches; i++)
            {
                var currentBatch = contactNumbers
                    .Skip(i * batchSize)
                    .Take(batchSize)
                    .ToList();

                _logger.LogInformation("🔍 Fetching batch {Batch} of {Total} - Count={Count}", i + 1, totalBatches, currentBatch.Count);

                var filters = new List<RequestFilterDto>
                {
                    new("Contact", currentBatch, "in"),
                    new("IsActive", true, "="),
                    new("BusinessId", businessId, "=")
                };
                var results = await _genericRepository.GetRecordByRequestFilter<Contacts>(
                    filters, "Contacts",
                    0, 0,
                    new List<string> { "ContactId", "Contact", "CountryCode", "Name" }
                );

                if (results?.Any() == true)
                {
                    allMatchingContacts.AddRange(results);
                }
            }
            return allMatchingContacts.DistinctBy(i=>i.Contact).ToList();
        }
        private async Task<Dictionary<string, Guid>> CreateAndGetTags(List<string> tags, string businessId, Guid userId)
        {
            Dictionary<string, Guid> tagDetails = new();

            var disctinictTags = tags.Select(x => x.ToLowerInvariant()).ToList().Distinct().ToList();

            var companyId = Guid.Parse(businessId);

            var existingTags = await _genericRepository.GetRecordByRequestFilter<Tags>(new List<RequestFilterDto>
            {
                new("Tag",disctinictTags,"in"),
                new("IsActive",true,"="),
                new("BusinessId",Guid.Parse(businessId),"=")
            }, "Tags");

            if (existingTags.Any())
            {
                tagDetails = existingTags
                    .GroupBy(tag => tag.Tag)
                    .Select(group => group.First())
                    .ToDictionary(dict => dict.Tag, dict => dict.Id);
            }

            var notExistingTags = disctinictTags.Except(existingTags.Select(x => x.Tag), StringComparer.OrdinalIgnoreCase);

            if (notExistingTags.Any())
            {
                var newTags = notExistingTags.Select(x => new Tags()
                {
                    Id = Guid.NewGuid(),
                    BusinessId = companyId,
                    CreatedAt = DateTime.UtcNow,
                    CreatedBy = userId.ToString(),
                    UpdatedAt = DateTime.UtcNow,
                    UpdatedBy = userId.ToString(),
                    UserId = userId,
                    Tag = x,
                    IsActive = true
                }).ToList();
                await _genericRepository.InsertRecordsAsync<Tags>("Tags", StringHelper.GetPropertyNames<Tags>(false), newTags);
                newTags.ForEach(tag => tagDetails.TryAdd(tag.Tag, tag.Id));
            }
            return tagDetails;
        }
    }

}
