﻿using EngagetoEntities.Entities;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace EngagetoContracts.WorkflowRepository
{
    public interface ICustomerWorkflowTrackerRepository
    {

        Task<CustomerWorkflowTracker> GetTrackerByNodeAsync(Guid customerId, Guid nodeId);
        Task<List<CustomerWorkflowTracker>> GetActiveTrackersForCustomerAsync(Guid customerId);
        Task<CustomerWorkflowTracker> CreateTrackerAsync(CustomerWorkflowTracker tracker);
        Task<CustomerWorkflowTracker> UpdateTrackerAsync(CustomerWorkflowTracker tracker);
        Task<List<CustomerWorkflowTracker>> GetTrackerByWorkflowAsync(Guid customerId, Guid? workflowId);

    }
}
