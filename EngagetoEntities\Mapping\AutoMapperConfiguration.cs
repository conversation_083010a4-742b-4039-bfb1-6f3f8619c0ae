﻿using AutoMapper;

namespace EngagetoEntities.Mapping
{
    public static class AutoMapperConfiguration
    {
        public static IMapper Configure()
        {
            var mapperConfig = new MapperConfiguration(cfg =>
            {
                cfg.AddProfile<OrderToTransactionHistoryProfile>();
                cfg.AddProfile<RazorpayOrderProfile>();
                cfg.AddProfile<TransactionHistoryProfile>();
                cfg.AddProfile<UserWalletProfile>();
                cfg.AddProfile<DiscountProfile>();
                cfg.AddProfile<UserWalletNotificationProfile>();
                cfg.AddProfile<ConversationAnalyticsProfile>();
                // Add other profiles if needed
            });
            mapperConfig.AssertConfigurationIsValid();
            return mapperConfig.CreateMapper();
        }
    }
}
