﻿using System;
using System.Collections.Generic;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata;
using Microsoft.Extensions.Configuration;

namespace EngagetoDatabase.ContactsDatabase.Models
{
    public partial class db_aa80b1_whatsappbusinessContext : DbContext
    {
        public IConfiguration _configuration { get; set; }   
       public db_aa80b1_whatsappbusinessContext(IConfiguration configuration)
    {
        _configuration = configuration;
    }
        public db_aa80b1_whatsappbusinessContext(DbContextOptions<db_aa80b1_whatsappbusinessContext> options)
            : base(options)
        {
        }

        public virtual DbSet<BusinessDetailsMeta> BusinessDetailsMetas { get; set; } = null!;
        public virtual DbSet<CountryDetail> CountryDetails { get; set; } = null!;
        public virtual DbSet<Role> Roles { get; set; } = null!;
        public virtual DbSet<User> Users { get; set; } = null!;
        public virtual DbSet<UserRole> UserRoles { get; set; } = null!;

        protected override void OnConfiguring(DbContextOptionsBuilder optionsBuilder)
        {
            if (!optionsBuilder.IsConfigured)
            {
                var connectionString = _configuration.GetConnectionString("ConnStr");
                optionsBuilder.UseSqlServer(connectionString);
            }
        }

        protected override void OnModelCreating(ModelBuilder modelBuilder)
        {
            modelBuilder.Entity<BusinessDetailsMeta>(entity =>
            {
                entity.ToTable("BusinessDetails_Metas");

                entity.Property(e => e.Id).ValueGeneratedNever();

                entity.Property(e => e.PhoneNumberId).HasColumnName("PhoneNumberID");

                entity.Property(e => e.WhatsAppBusinessAccountId).HasColumnName("WhatsAppBusinessAccountID");
            });

            modelBuilder.Entity<CountryDetail>(entity =>
            {
                entity.Property(e => e.Id).ValueGeneratedNever();
            });

            modelBuilder.Entity<Role>(entity =>
            {
                entity.ToTable("Role");

                entity.Property(e => e.Id).ValueGeneratedNever();

                entity.Property(e => e.Name).HasMaxLength(100);

                entity.Property(e => e.Sno).ValueGeneratedOnAdd();
            });

            modelBuilder.Entity<User>(entity =>
            {
                entity.Property(e => e.Id).ValueGeneratedNever();
            });

            modelBuilder.Entity<UserRole>(entity =>
            {
                entity.HasKey(e => new { e.Id, e.RoleId });

                entity.ToTable("UserRole");

                entity.HasIndex(e => e.AhexCrmUsersId, "IX_UserRole_Ahex_CRM_UsersId");

                entity.HasIndex(e => e.RoleId, "IX_UserRole_RoleId");

                entity.Property(e => e.AhexCrmUsersId).HasColumnName("Ahex_CRM_UsersId");

                entity.HasOne(d => d.AhexCrmUsers)
                    .WithMany(p => p.UserRoles)
                    .HasForeignKey(d => d.AhexCrmUsersId);

                entity.HasOne(d => d.Role)
                    .WithMany(p => p.UserRoles)
                    .HasForeignKey(d => d.RoleId);
            });

            OnModelCreatingPartial(modelBuilder);
        }

        partial void OnModelCreatingPartial(ModelBuilder modelBuilder);
    }
}
