﻿using AutoMapper;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Logging;
using Razorpay.Api;
using System;
using System.Collections.Generic;
using System.Linq;
using EngagetoContracts.UserContracts;
using EngagetoEntities.UserEntities.Dtos;
using EngagetoEntities.UserEntities.Models;

namespace EngagetoRepository.UserRepository
{
    public class RazorPaymentGatewayService : IRazorPaymentGatewayService
    {
        private readonly ILogger<RazorPaymentGatewayService> _logger;
        private readonly IConfiguration _configuration;
        private readonly RazorpayClient _razorpayClient;
        private readonly ApplicationDBContext _dbContext;
        private readonly IMapper _mapper;


        public RazorPaymentGatewayService(ILogger<RazorPaymentGatewayService> logger, IConfiguration configuration, ApplicationDBContext dbContext, IMapper mapper)
        {
            _configuration = configuration;
            _logger = logger;
            var apiKey = _configuration["Razorpay:ApiKey"];
            var apiSecret = _configuration["Razorpay:ApiSecret"];
            _razorpayClient = new RazorpayClient(apiKey, apiSecret);
            _dbContext = dbContext;
            _mapper = mapper;
        }
        public async Task<EngagetoEntities.UserEntities.Dtos.OrderDto> CreateOrderAsync(RazorpayOderDto razorpayOderDto, Guid userId)
        {
            var order = new Dictionary<string, object> {
            { "amount", razorpayOderDto.Amount * 100 },
            { "currency", razorpayOderDto.Currency.ToString() },
            { "receipt", razorpayOderDto.Receipt ?? "engageto_1000"}};

            var response = _razorpayClient.Order.Create(order);
         
            var OrderDto = _mapper.Map<EngagetoEntities.UserEntities.Dtos.OrderDto>(response);
            return OrderDto;
        }

        public async Task<EngagetoEntities.UserEntities.Dtos.OrderDto> GetOrderDetailsAsync(string orderId)
        {
            Razorpay.Api.Order order = _razorpayClient.Order.Fetch(orderId);
            var OrderDto = _mapper.Map<EngagetoEntities.UserEntities.Dtos.OrderDto>(order);
            return OrderDto;
        }

        public async Task<Payment> GetPaymentDetailsAsync(string paymentId)
        {
            Razorpay.Api.Payment payment = _razorpayClient.Payment.Fetch(paymentId);
            return payment;
        }
    }
}
