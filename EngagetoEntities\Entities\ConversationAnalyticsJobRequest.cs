﻿using Newtonsoft.Json;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Text.Json.Nodes;
using System.Threading.Tasks;

namespace EngagetoEntities.Entities
{
    public class ConversationAnalyticsJobRequest
    {
        public int Id { get; set; }
        public string CompanyId { get; set; }
        public bool IsCompleted { get; set; }
        public string? ErrorMessage { get; set; }
        public DateTime StartDate { get; set; }
        public DateTime EndDate { get; set; }
        public string ResponseMessage { get; set; }

        public ConversationAnalyticsJobRequest() { }
        public ConversationAnalyticsJobRequest(string companyId, bool isCompleted, DateTime starDate, string? errorMessage = null, List<string>? responseMessage = null)
        {
            CompanyId = companyId;
            IsCompleted = isCompleted;
            StartDate = starDate;
            EndDate = starDate.AddHours(1);
            ErrorMessage = errorMessage;
            ResponseMessage = JsonConvert.SerializeObject(responseMessage);
        }
    }
}
