﻿using Newtonsoft.Json;
using System.ComponentModel.DataAnnotations.Schema;


namespace EngagetoEntities.Entities
{
    [Table("InputListEntities")]
    public class InputListEntity : BaseEntity
    {
        public Guid Id { get; set; }
        public Guid CompanyId { get; set; }
        public Guid UserId { get; set; }
        public string ListName { get; set; }
        public string ButtonName { get; set; }
        public string Inputs { get; set; }

    }

}
