﻿using AutoMapper;
using EngagetoContracts.UserContracts;
using EngagetoEntities.Entities;
using EngagetoEntities.Enums;
using EngagetoEntities.Response;
using EngagetoEntities.UserEntities.Dtos;
using EngagetoEntities.Utilities;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using System.Security.Claims;

namespace Engageto.Controllers.UserControllers
{
    [Route("api/[controller]")]
    [ApiController]
    public class PaymentGatewayController : ControllerBase
    {
        private readonly ILogger<PaymentGatewayController> _logger;
        private readonly IRazorPaymentGatewayService _razorpayService;
        private readonly IWalletService _walletService;
        private readonly ITransactionHistoryService _transactionHistoryService;
        private readonly IWalletNotificationService _walletNotificationService;
        private readonly IAccountDetailsService _accountDetailsService;
        private readonly IMapper _mapper;
        private decimal _lowBalanceAmount = 1000;

        public PaymentGatewayController(ITransactionHistoryService transactionHistoryService,
            IWalletService walletService, IWalletNotificationService walletNotificationService,
            IAccountDetailsService accountDetailsService,
            ILogger<PaymentGatewayController> logger,
            IRazorPaymentGatewayService razorpayService,
            IMapper mapper)
        {
            _transactionHistoryService = transactionHistoryService;
            _walletService = walletService;
            _walletNotificationService = walletNotificationService;
            _accountDetailsService = accountDetailsService;
            _logger = logger;
            _razorpayService = razorpayService;
            _mapper = mapper;
        }

        [HttpPost]
        [Route("CreateOrder")]
        [Authorize]
        public async Task<IActionResult> CreateOrder([FromBody] RazorpayOderDto request)
        {
            try
            {
                var currentUserIdClaim = User.Claims.FirstOrDefault(c => c.Type == ClaimTypes.Name);

                if (currentUserIdClaim == null || !Guid.TryParse(currentUserIdClaim.Value, out Guid currentUserId))
                {
                    return BadRequest(new { Message = "Invalid current user." });
                }
                request.UserId = currentUserId;
                var accountDetails = await _accountDetailsService.GetAccountDetails(currentUserId);
                UserWalletEntity walletEntity = await _walletService.GetWalletAsync(accountDetails.CompanyId);

                var count = (await _transactionHistoryService.GetTrasactionHistoryAsync())?.Count ?? 1;
                request.Receipt = "engageto_" + 1000 + count;
                var paymentDetail = await _razorpayService.CreateOrderAsync(request, currentUserId);

                var transaction = _mapper.Map<TransactionHistoryEntity>(paymentDetail);
                transaction.CompanyId = accountDetails.CompanyId;
                transaction.UserID = currentUserId;
                transaction.WalletId = walletEntity.WalletId;
                transaction.DiscountId = request.DiscountId;
                transaction.CreatedAt = DateTime.UtcNow;
                transaction.CreatedBy = currentUserId;
                transaction.UpdatedAt = DateTime.UtcNow;
                transaction.UpdatedBy = currentUserId;

                var resultTransaction = await _transactionHistoryService.CreateTransactionHistoryAsync(transaction);

                return Ok(new ApiResponse<TransactionHistoryEntity>()
                {
                    Success = true,
                    Message = "Create Order Successfully",
                    Data = resultTransaction,
                });
            }
            catch (Exception ex)
            {
                _logger.LogError($"Exception occurs in PaymentGatewayController/CreateOrder: {ex}");
                return StatusCode(StatusCodes.Status500InternalServerError, new ApiResponse<string>()
                {
                    Success = false,
                    Message = ex.Message,
                    Errors = ex.StackTrace
                });
            }
        }

        [HttpGet]
        [Route("GetOrderDetails")]
        [Authorize]
        public async Task<IActionResult> GetOrderDetails(string paymentId, string orderId)
        {
            try
            {
                var currentUserIdClaim = User.Claims.FirstOrDefault(c => c.Type == ClaimTypes.Name);

                if (currentUserIdClaim == null || !Guid.TryParse(currentUserIdClaim.Value, out Guid currentUserId))
                {
                    return BadRequest(new { Message = "Invalid current user." });
                }

                var payment = await _razorpayService.GetOrderDetailsAsync(orderId);
                var paymentDetails = await _razorpayService.GetPaymentDetailsAsync(paymentId);

                var trasaction = await _transactionHistoryService.GetTransactionHistoryByOrderIdAsync(orderId);
                //Maped Payment details with Transaction History table
                CommonHelper.MapPaymentDetailsWithTransactionHistory(ref trasaction, paymentDetails);

                if (trasaction.Status != "paid")
                {
                    trasaction.Status = payment.Status;
                    trasaction.Attempts = payment.Attempts;
                    trasaction.AmountPaid = payment.AmountPaid == 0 ? payment.AmountPaid : payment.AmountPaid / 100;
                    trasaction.AmountDue = payment.AmountDue == 0 ? payment.AmountDue : payment.AmountDue / 100;
                    trasaction.PaymentId = paymentId;

                    var updateTransactionHistory = await _transactionHistoryService.UpdateTransactionHistoryAsync(trasaction);
                    if (updateTransactionHistory != null)
                    {
                        if (updateTransactionHistory.Status == "paid")
                        {
                            var accountDetails = await _accountDetailsService.GetAccountDetails(currentUserId);
                            //Get Wallet Details
                            var wallet = await _walletService.GetWalletAsync(accountDetails.CompanyId);

                            wallet.Balance += CommonHelper.CurrencyConvertIntoINR(updateTransactionHistory.Amount, updateTransactionHistory._currency);
                            wallet.ExpectedWalletBallance = (wallet.ExpectedWalletBallance ?? 0) + CommonHelper.CurrencyConvertIntoINR(updateTransactionHistory.Amount, updateTransactionHistory._currency);
                            var updateWalletResult = await _walletService.UpdateWalletAsync(wallet);
                            if (updateTransactionHistory.Amount < _lowBalanceAmount)
                            {
                                var walletNotification = new WalletNotification()
                                {
                                    Category = NotificationCategory.Critical,
                                    Type = NotificationType.LowBallence,
                                    Title = "Low Balance Detected",
                                    WalletId = wallet.WalletId,
                                    TransactionId = updateTransactionHistory.Transactionid,
                                    Amount = (payment?.Amount / 100) ?? 0,
                                    Description = $"Wallet balance has dropped below *{trasaction.Currency} {_lowBalanceAmount}* \n Add Sufficiant balance to your wallet to ensure that your campaigns are not interrupted.",
                                    CreatedAt = DateTime.Now,
                                    UpdatedAt = DateTime.Now,
                                    CreatedBy = currentUserId,
                                    UpdatedBy = currentUserId

                                };
                                var notification = await _walletNotificationService.CreateWalletNotificationAsync(walletNotification);
                            }
                        }
                    }
                    return Ok(new ApiResponse<TransactionHistoryEntity>()
                    {
                        Success = true,
                        Message = "Amount has been successfully added to your wallet.",
                        Data = trasaction,
                    });
                }
                return Ok(new ApiResponse<TransactionHistoryEntity>()
                {
                    Success = true,
                    Message = "Amount has been successfully added to your wallet.",
                    Data = trasaction,
                });
            }
            catch (Exception ex)
            {
                _logger.LogError($"Exception occurs in PaymentGatewayController/GetOrderDetails: {ex}");
                return StatusCode(StatusCodes.Status500InternalServerError, new ApiResponse<string>()
                {
                    Success = false,
                    Message = ex.Message,
                    Errors = ex.StackTrace
                });
            }
        }
        //[HttpGet]
        //[Route("GetConverstationAnalytics")]
        //[AllowAnonymous]
        //public async Task<IActionResult> GetConverstationAnalytics()
        //{
        //    await _jobTaskService.GetConversationAnalyticsCost(null);
        //    return Ok();
        //}


    }
}
