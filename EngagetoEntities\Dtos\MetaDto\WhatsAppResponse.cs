﻿using Newtonsoft.Json;

namespace EngagetoEntities.Dtos.MetaDto
{
    public class WhatsAppResponse
    {
        [JsonProperty("messaging_product")]
        public string? MessagingProduct { get; set; }

        [JsonProperty("contacts")]
        public List<WAContact>? Contacts { get; set; }

        [JsonProperty("messages")]
        public List<Message>? Messages { get; set; }
    }

    public class WAContact
    {
        [JsonProperty("input")]
        public string? Input { get; set; }

        [JsonProperty("wa_id")]
        public string? WaId { get; set; }
    }

    public class Message
    {
        [JsonProperty("id")]
        public string? Id { get; set; }
    }
}
