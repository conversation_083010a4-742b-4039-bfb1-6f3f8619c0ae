﻿using EngagetoEntities.Enums;

namespace EngagetoEntities.Dtos.IntegrationDtos
{
    public class HeaderParameterDto
    {
        public HeaderParameter Parameter { get; set; }
        public Dictionary<string, string>? Headers { get; set; }
    }
    public class PropertyMappingDto
    {
        public string Key { get; set; } // Represents the mapping key or identifier
        public string? TargetPropertyName { get; set; } // Represents the target property/variable name to assign the value
        public string Value { get; set; } // Represents the value to assign
    }

}
