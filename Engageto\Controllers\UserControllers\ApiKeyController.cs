﻿using EngagetoDapper.Data.Interfaces.IUserInterfaces.IUserServices;
using EngagetoEntities.Entities;
using EngagetoEntities.Response;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using System.ComponentModel.DataAnnotations;

namespace Engageto.Controllers.UserControllers
{
    [Route("api/[controller]")]
    [ApiController]
    public class ApiKeyController : ControllerBase
    {
        private readonly IApiKeyService _apiKeyService;
        public ApiKeyController(IApiKeyService apiKeyService)
        {
            _apiKeyService = apiKeyService;
        }

        [HttpGet("GetApiKeyDetails")]
        [Authorize]
        public async Task<IActionResult> GetApiKeDetails([Required] string companyId)
        {
            try
            {
                var result = await _apiKeyService.GetApiDetailsAsync(companyId)
                    ??
                    throw new Exception("Something issue to fetch the data.");

                return Ok(new ApiResponse<ApiKeyEntity>
                {
                    Success = true,
                    Message = "Get Api key details.",
                    Data = result
                });
            }
            catch (Exception ex)
            {
                return BadRequest(new ApiResponse<string>()
                {
                    Success = false,
                    Message = ex.Message,
                    Errors = ex.StackTrace
                });
            }
        }
        [HttpPost]
        [Authorize]
        public async Task<IActionResult> SaveApiKey(string companyId)
        {
            try
            {
                var result = await _apiKeyService.SaveApiKeyAsync(companyId);
                if (!result)
                    throw new Exception("There is some problem saving the data.");

                return Ok(new ApiResponse<ApiKeyEntity>
                {
                    Success = true,
                    Message = "Save Api key Successfully."
                });
            }
            catch (Exception ex)
            {
                return BadRequest(new ApiResponse<string>()
                {
                    Success = false,
                    Message = ex.Message,
                    Errors = ex.StackTrace
                });
            }
        }
        [HttpPut]
        [Authorize]
        public async Task<IActionResult> UpdateApiKey(string companyId)
        {
            try
            {
                var result = await _apiKeyService.UpdateApiKeyAsync(companyId)
                    ??
                    throw new Exception("There is some problem saving the data.");

                return Ok(new ApiResponse<ApiKeyEntity>
                {
                    Success = true,
                    Message = "Save Api key Successfully.",
                    Data = result
                });
            }
            catch (Exception ex)
            {
                return BadRequest(new ApiResponse<string>()
                {
                    Success = false,
                    Message = ex.Message,
                    Errors = ex.StackTrace
                });
            }
        }
    }
}
