﻿using EngagetoEntities.Entities;
using EngagetoEntities.UserEntities.Dtos;
using Microsoft.AspNetCore.Http;

namespace EngagetoContracts.UserContracts
{
    public interface ICountryDetailsService
    {
        Task<Guid> AddCountryDetails(Guid currentUserId, string countryName, string countryCode, IFormFile countryImage);
        Task<bool> UpdateCountryDetails(Guid currentUserId, Guid countryId, UpdateCountryDto updateCountryDto);
        Task<bool> DeleteCountryDetails(Guid currentUserId, Guid countryId);
        Task<CountryDetails> GetCountryByCode(string countryCode);
        Task<CountryDetails> GetCountryByCompanyName(string companyName);
        Task<List<CountryDetails>> GetAllCountries(string searchQuery);
        Task<string?> UploadLogoAsync(IFormFile? file);
        Task<List<GetCounrtyCodewithImage>> GetAllCountryCodesAsync();
    }
}
