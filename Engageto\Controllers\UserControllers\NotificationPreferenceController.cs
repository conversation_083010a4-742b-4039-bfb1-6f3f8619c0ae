﻿using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using System.Security.Claims;
using EngagetoContracts.UserContracts;
using EngagetoEntities.Entities;

namespace Engageto.Controllers.UserControllers
{
    [Route("api/[controller]")]
    [ApiController]
    public class NotificationPreferenceController : ControllerBase
    {
        private readonly INotificationPreferenceService _notificationPreferenceService;

        public NotificationPreferenceController(INotificationPreferenceService notificationPreferenceService)
        {
            _notificationPreferenceService = notificationPreferenceService;
        }

        /*  [HttpGet("get-notification-preference/{userId}")]
          [Authorize]
          public async Task<IActionResult> GetNotificationPreference(Guid userId)
          {
              var notificationPreference = await _notificationPreferenceService.GetNotificationPreferencesAsync(userId);

              if (notificationPreference == null)
              {
                  return NotFound(); 
              }

              return Ok(notificationPreference);
          }

          [HttpPost("set-notificationpreference")]
          [Authorize]
          public async Task<IActionResult> SetNotificationPreferences([FromBody] NotificationDetailsDto preferences)
          {
              try
              {
                  var userId = GetCurrentUserId();
                  var responseDto = await _notificationPreferenceService.SetNotificationPreferences(userId, preferences);
                  return Ok(responseDto);
              }
              catch (Exception ex)
              {
                  return BadRequest(new { ErrorMessage = "An error occurred while setting notification preferences.", ex.Message });
              }
          }


          private Guid GetCurrentUserId()
          {

              var userIdClaim = User?.FindFirst(ClaimTypes.Name);


              if (userIdClaim != null && Guid.TryParse(userIdClaim.Value, out var userId))
              {
                  return userId;
              }


              return Guid.Empty;
          }*/
        [HttpGet("get-notificationsList")]
        [Authorize]
        public async Task<ActionResult<IEnumerable<NotificationDto>>> GetNotifications([FromQuery] string userId, [FromQuery] string companyId)
        {
            var notifications = await _notificationPreferenceService.GetNotificationHierarchyAsync(userId, companyId);
            return Ok(notifications);
        }
        [HttpPut("update-notifications")]
        [Authorize]
        public async Task<IActionResult> UpdateNotificationStatus([FromQuery] string userId, string companyId, string notificationName, bool isActive)
        {
            var result = await _notificationPreferenceService.UpdateNotificationStatusAsync(userId, companyId, notificationName, isActive);

            if (!result)
            {
                return NotFound();
            }

            return Ok(new { Message = "Notification updated successfully." });
        }
    }
}
