﻿using EngagetoDapper.Data.Interfaces.IUserInterfaces.IUserServices;
using EngagetoEntities.Response;
using EngagetoEntities.UserEntities.Dtos;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using System.Security.Claims;

namespace Engageto.Controllers.UserControllers
{
    [Route("api/[controller]")]
    [ApiController]
    public class ResourcePermissionController : ControllerBase
    {
        private readonly IResourcePermissionService _resourcePermission;
        public ResourcePermissionController(IResourcePermissionService resourcePermission)
        {
            _resourcePermission = resourcePermission;
        }

        [HttpPost]
        [Authorize]
        public async Task<IActionResult> ResourcePermission(RequestResourcePermissionDto requestResource)
        {
            try
            {
                var currentUserIdClaim = User.Claims.FirstOrDefault(c => c.Type == ClaimTypes.Name);

                if (currentUserIdClaim == null || !Guid.TryParse(currentUserIdClaim.Value, out Guid currentUserId))
                    throw new Exception("Invalid current user.");

                var result = await _resourcePermission.AddResourcePermissionAsync(requestResource, currentUserId);
                if (!result) throw new Exception("There is some problem inserting the record.");
                return Ok(new ApiResponse<string>()
                {
                    Success = true,
                    Message = "The record has been inserted successfully."
                });

            }
            catch (Exception ex)
            {
                return BadRequest(new ApiResponse<string>()
                {
                    Success = false,
                    Message = ex.Message,
                    Errors = ex.StackTrace
                });
            }
        }
        [HttpGet]
        [Authorize]
        public async Task<IActionResult> GetResourcePermission()
        {
            try
            {
                var result = await _resourcePermission.GetResourcePermissionsAsync();
                return Ok(new ApiResponse<List<RequestResourcePermissionDto>>()
                {
                    Success = true,
                    Message = "Record received successfully.",
                    Data = result
                });
            }
            catch (Exception ex)
            {
                return BadRequest(new ApiResponse<string>()
                {
                    Success = false,
                    Message = ex.Message,
                    Errors = ex.StackTrace
                });
            }
        }
    }
}
