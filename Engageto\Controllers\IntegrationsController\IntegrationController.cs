﻿using EngagetoContracts.Services;
using EngagetoEntities.Dtos.IntegrationDtos;
using EngagetoEntities.Enums;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using Newtonsoft.Json.Linq;

namespace Engageto.Controllers.IntegrationsController
{
    [Route("api/[controller]")]
    [ApiController]
    public class IntegrationController : BaseController
    {
        private readonly IIntegrationAccountService _integrationAccountService;
        public IntegrationController(IIntegrationAccountService integrationAccountService)
        {
            _integrationAccountService = integrationAccountService;
        }

        [HttpPost("account")]
        [Authorize]
        public async Task<IActionResult> AddAccount(IntegrationAccountDto accountDto)
        {
            try
            {
                return Ok(CreateSuccessResponse<Guid>(await _integrationAccountService.AddAccountAsync(accountDto)));
            }
            catch (Exception ex)
            {
                return BadRequest(CreateErrorResponse<string>(ex.Message, ex.StackTrace));
            }
        }

        [HttpPut("account")]
        [Authorize]
        public async Task<IActionResult> UpdateAccount(EditIntegrationAccountDto accountDto)
        {
            try
            {
                return Ok(CreateSuccessResponse<ViewIntegrationAccountDto>(await _integrationAccountService.UpdateAccountAsync(accountDto)));
            }
            catch (Exception ex)
            {
                return BadRequest(CreateErrorResponse<string>(ex.Message, ex.StackTrace));
            }
        }

        [HttpGet("account")]
        [Authorize]
        public async Task<IActionResult> GetAccount()
        {
            try
            {
                return Ok(CreateSuccessResponse<List<ViewIntegrationAccountDto>>(await _integrationAccountService.GetAccountAsync()));
            }
            catch (Exception ex)
            {
                return BadRequest(CreateErrorResponse<string>(ex.Message, ex.StackTrace));
            }
        }

        [HttpGet("account/id")]
        [Authorize]
        public async Task<IActionResult> GetAccount(Guid accountId)
        {
            try
            {
                return Ok(CreateSuccessResponse<List<ViewIntegrationAccountDto>>(await _integrationAccountService.GetAccountByIdAsync(accountId)));
            }
            catch (Exception ex)
            {
                return BadRequest(CreateErrorResponse<string>(ex.Message, ex.StackTrace));
            }
        }
        [HttpGet("payload-properties/accountName")]
        [Authorize]
        public async Task<IActionResult> GetPayloadProperty(string accountName)
        {
            try
            {
                return Ok(CreateSuccessResponse<IntegrationWebhookPayloadDto>(await _integrationAccountService.GetPayloadPropertyMappingAsync(accountName)));
            }
            catch (Exception ex)
            {
                return BadRequest(CreateErrorResponse<string>(ex.Message, ex.StackTrace));
            }
        }

        [HttpPost("process-failedlead-generation")]
        [Authorize]
        public async Task<IActionResult> ProcessLeadGeneration([FromQuery] Guid businessId, [FromQuery] SourceType source)
        {
            try
            {
                await _integrationAccountService.SyncLeadInBackgroundAsync(businessId, source);
                return Ok(CreateSuccessResponse<bool>(true));
            }
            catch (Exception ex)
            {
                return BadRequest(CreateErrorResponse<string>(ex.Message, ex.StackTrace));
            }
        }

        [HttpPost("external-api/lead")]
        [Authorize]
        public async Task<IActionResult> CallExternalApiForLead([FromQuery] string businessId, [FromBody] JObject leadData, [FromQuery] ModelMapping modelMapping, [FromQuery] IntegrationEvent @event)
        {
            try
            {
                bool result = await _integrationAccountService.CallExternalApiForLeadAsync(
                    businessId,
                    leadData,
                    modelMapping,
                    @event);
                if (result)
                {
                    return Ok(CreateSuccessResponse<bool>(result));
                }
                return BadRequest(result);
            }
            catch (Exception ex)
            {
                return BadRequest(CreateErrorResponse<string>(ex.Message, ex.StackTrace));
            }
        }
    }
}
