﻿using System;
using PhoneNumbers;

public static class PhoneNumberHelper
{
    private static readonly PhoneNumberUtil PhoneUtil = PhoneNumberUtil.GetInstance();

    /// <summary>
    /// Extracts the country code and national (local) phone number from a given phone number.
    /// </summary>
    /// <param name="phoneNumber">The phone number as a string.</param>
    /// <param name="defaultCountryCode">The default country code to use if the phone number lacks one.</param>
    /// <returns>A tuple containing the country code and the national number.</returns>
    public static (string CountryCode, string NationalNumber) GetCountryCodeAndPhoneNumber(string phoneNumber, string? defaultCountryCode = "+91")
    {
        try
        {
            // Remove any extra "+" symbols and parse the phone number
            string formattedNumber = phoneNumber.StartsWith("+") ? phoneNumber : $"+{phoneNumber}";
            var parsedNumber = PhoneUtil.Parse(formattedNumber, null);

            // Validate the parsed number
            if (!PhoneUtil.IsValidNumber(parsedNumber) && defaultCountryCode != null)
            {
                // If invalid, try parsing with the default country code
                string fallbackNumber = $"{defaultCountryCode}{phoneNumber}";
                parsedNumber = PhoneUtil.Parse(fallbackNumber, null);
            }

            if (!PhoneUtil.IsValidNumber(parsedNumber))
                throw new ArgumentException("Invalid phone number.");

            // Extract country code and national number
            string countryCode = parsedNumber.CountryCode.ToString();
            string nationalNumber = parsedNumber.NationalNumber.ToString();

            return (countryCode, nationalNumber);
        }
        catch (NumberParseException)
        {
            // Return the original input if parsing fails
            return (defaultCountryCode ?? "91", phoneNumber);
        }
    }
    /// <summary>
    /// Returns the ISO 2-letter country/region code (e.g., "IN", "US") from the given phone number.
    /// </summary>
    /// <param name="phoneNumber">Phone number to parse.</param>
    /// <param name="defaultCountryCode">Default country code (e.g., "+91") if input is incomplete.</param>
    /// <returns>ISO country code (e.g., "IN"), or null if invalid.</returns>
    public static string? GetRegionCodeFromPhoneNumber(string phoneNumber, string? defaultCountryCode = "+91")
    {
        try
        {
            string formattedNumber = phoneNumber.StartsWith("+") ? phoneNumber : $"+{phoneNumber}";

            var parsedNumber = PhoneUtil.Parse(formattedNumber, null);

            if (!PhoneUtil.IsValidNumber(parsedNumber) && defaultCountryCode != null)
            {
                // Retry with default country code
                string fallbackNumber = $"{defaultCountryCode}{phoneNumber}";
                parsedNumber = PhoneUtil.Parse(fallbackNumber, null);
            }

            if (!PhoneUtil.IsValidNumber(parsedNumber))
                return null;

            // This returns "IN", "US", "GB", etc.
            return PhoneUtil.GetRegionCodeForNumber(parsedNumber);
        }
        catch (NumberParseException)
        {
            return null;
        }
    }
}
