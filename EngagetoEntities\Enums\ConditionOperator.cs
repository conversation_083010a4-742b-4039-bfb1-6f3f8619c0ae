﻿using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace EngagetoEntities.Enums
{
    public enum ConditionOperator
    {

        [Description("Equals")]
        Equals,

        [Description("Not Equals")]
        NotEquals,

        [Description("Greater Than")]
        Greater<PERSON>han,

        [Description("Less Than")]
        <PERSON><PERSON><PERSON>,

        [Description("Contains")]
        Contains,

        [Description("Starts With")]
        StartsWith,

        [Description("Ends With")]
        EndsWith
    }

}

