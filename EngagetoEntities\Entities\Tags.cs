﻿using System.ComponentModel.DataAnnotations;

namespace EngagetoEntities.Entities
{
    public class Tags
    {
        [Key]
        public Guid Id { get; set; }
        public string Tag { get; set; }
        public Guid UserId { get; set; }
        public Guid BusinessId { get; set; }
        public bool IsActive { get; set; } = true;
        public DateTime? CreatedAt { get; set; }
        public DateTime? UpdatedAt { get; set; } = DateTime.Now;
        public DateTime? DeletedAt { get; set; }
        public string? CreatedBy { get; set; }
        public string? UpdatedBy { get; set; }
        public string? DeletedBy { get; set; }
    }
}
