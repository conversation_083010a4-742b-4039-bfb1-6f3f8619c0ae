﻿using Engageto.Attributes;
using EngagetoDapper.Data.Interfaces.IUserInterfaces.IUserServices;
using EngagetoEntities.Dtos.ApiResponseDtos;
using EngagetoEntities.Entities;
using EngagetoEntities.UserEntities.Dtos;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using System.Security.Claims;

namespace Engageto.Controllers.UserControllers
{
    [Route("api/[controller]")]
    [ApiController]
    public class WebhookController : ControllerBase
    {
        private readonly ILogger<WebhookController> _logger;
        private readonly IWebhookService _webhookService;
        public WebhookController(ILogger<WebhookController> logger,
            IWebhookService webhookService)
        {
            _logger = logger;
            _webhookService = webhookService;
        }

        [HttpPost]
        [Authorize]
        [ValidateModelState]
        public async Task<IActionResult> SaveWebhookEndpoint(WebhookEndpointRequestDto endpointRequestDto)
        {
            try
            {
                var currentUserIdClaim = User.Claims.FirstOrDefault(c => c.Type == ClaimTypes.Name);

                if (currentUserIdClaim == null || !Guid.TryParse(currentUserIdClaim.Value, out Guid currentUserId))
                    return BadRequest(new { Message = "Invalid current user." });

                endpointRequestDto.UserId = currentUserId;
                var result = await _webhookService.SaveWebhookEnpointAsync(endpointRequestDto);
                if (!result)
                    throw new Exception("An error occurred while saving the webhook.");
                return Ok(new ApiResponse<string>()
                {
                    Success = true,
                    Message = "Webhook endpoint saved successfully."
                });
            }
            catch (Exception ex)
            {
                _logger.LogError($"An error occurred while saving the webhook. error: {ex}");
                return BadRequest(new ApiResponse<string>()
                {
                    Success = false,
                    Message = ex.Message,
                    Error = ex.Message
                });
            }
        }

        [HttpGet]
        [Authorize]
        public async Task<IActionResult> GetWebhookEndpoint(string companyId)
        {
            try
            {
                var result = await _webhookService.GetWebhookEnpointAsync(companyId)
                    ?? throw new Exception("An error occurred while getting the Webhook.");

                return Ok(new ApiResponse<List<WebhookEndpointEntity>>()
                {
                    Success = true,
                    Message = "Webhook endpoint received successfully.",
                    Data = result
                });
            }
            catch (Exception ex)
            {
                _logger.LogError($"An error occurred while getting the Webhook. error: {ex}");
                return BadRequest(new ApiResponse<string>()
                {
                    Success = false,
                    Message = ex.Message,
                    Error = ex.Message
                });
            }
        }
    }
}
