﻿using Microsoft.Data.SqlClient;
using Microsoft.Extensions.Configuration;

namespace EngagetoDapper.Data.Connections
{
    public class SqlConnectionFactory : ISqlConnectionFactory
    {
        private readonly string _connectionString;

        public SqlConnectionFactory(IConfiguration configuration)
        {
            _connectionString = configuration.GetConnectionString("ConnStr") ?? configuration["ConnStr"]
                ?? throw new InvalidOperationException("Connection string 'ConnStr' not found.");
        }

        public SqlConnection CreateConnection()
        {
            return new SqlConnection(_connectionString); // NOT open
        }
    }
}
