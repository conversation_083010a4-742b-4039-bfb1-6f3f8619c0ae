﻿using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using EngagetoEntities.Enums;

namespace EngagetoEntities.UserEntities.Dtos
{
    public class RazorpayOderDto
    {
        [Required (ErrorMessage = "Amount is a Required Field")]
        public decimal Amount { get; set; }
        public Currency Currency { get; set; } = Currency.INR;
        public Guid? DiscountId { get; set; }
        public Guid? WalletId { get;set; }
        public Guid? UserId { get; set; }
        public string? Receipt { get; set; }
    }
}
