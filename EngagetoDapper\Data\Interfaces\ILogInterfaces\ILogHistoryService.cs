﻿using EngagetoEntities.Entities;
using EngagetoEntities.Enums;

namespace EngagetoDapper.Data.Interfaces.ILogInterfaces
{
    public interface ILogHistoryService
    {
        public Task<bool> SaveInformationLogHistoryAsyn(string? apiName, object? requestBody, object? response, string? notes);
        public Task<bool> SaveSuccessLogHistoryAsyn(string? apiName, object? requestBody, object? response, string? notes);
        public Task<bool> SaveErrorLogHistoryAsyn(string? apiName, object? requestBody, string? notes, string? errorMessage, object? stackTrace);
        Task<IEnumerable<LogHistoryEntitity>> GetLogHistoryAsync(LogType logType, DateTime? startDate, DateTime? EndDate);
    }
}
