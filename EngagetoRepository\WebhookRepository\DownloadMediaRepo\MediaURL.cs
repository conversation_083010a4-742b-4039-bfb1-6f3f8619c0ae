﻿using Amazon;
using Amazon.Runtime;
using Amazon.S3;
using Amazon.S3.Transfer;
using EngagetoContracts.WebhookContracts.DownloadMedia;
using EngagetoEntities.Dtos.WebhookDtos;
using Microsoft.AspNetCore.Http;
using Microsoft.Extensions.Configuration;
using Newtonsoft.Json;

namespace EngagetoRepository.WebhookRepository.DownloadMediaRepo
{

    public class MediaURL : IMediaURL
    {
        private readonly IDownloadMedia _downloadMedia;
        private readonly IConfiguration _configuration;
        public IHttpClientFactory _httpClientFactory { get; set; }

        public MediaURL(IDownloadMedia downloadMedia, IConfiguration configuration, IHttpClientFactory httpClientFactory)
        {
            _downloadMedia = downloadMedia;
            _configuration = configuration;
            _httpClientFactory = httpClientFactory;
        }
        private HttpClient _httpClient = new HttpClient();

        private readonly JsonSerializer _serializer = new JsonSerializer();
        
        public async Task<string> GetByMediaId(string mediaId, string phoneNumberId)
        {
            var accessKey = _configuration["AWS:AccessKey"];
            var secretKey = _configuration["AWS:SecretKey"];
            var bucketName = _configuration["AWS:BucketName"];
            var regionString = _configuration["AWS:Region"];

            string filename = string.Empty;

            if (!string.IsNullOrEmpty(mediaId))
            {
                // Get media URL by media ID
                var mediaUrlResponse = await _downloadMedia.GetMediaUrlAsync(mediaId, phoneNumberId);

                // Download the media file
                var mediaFileDownloaded = await _downloadMedia.DownloadMediaAsync(mediaUrlResponse.Url, phoneNumberId);

                // Determine the filename based on MimeType using a switch expression
                filename = mediaUrlResponse.MimeType switch
                {
                    "audio/mpeg" => $"{mediaUrlResponse.Id}.mp3",
                    "audio/ogg" => $"{mediaUrlResponse.Id}.ogg",
                    "audio/aac" => $"{mediaUrlResponse.Id}.aac",
                    "audio/mp4" => $"{mediaUrlResponse.Id}.mp4",
                    "audio/amr" => $"{mediaUrlResponse.Id}.amr",

                    "image/jpeg" => $"{mediaUrlResponse.Id}.jpeg",
                    "image/png" => $"{mediaUrlResponse.Id}.png",
                    "image/webp" => $"{mediaUrlResponse.Id}.webp",  // Sticker

                    "text/plain" => $"{mediaUrlResponse.Id}.plain",
                    "application/pdf" => $"{mediaUrlResponse.Id}.pdf",
                    "application/vnd.ms-powerpoint" => $"{mediaUrlResponse.Id}.ppt",
                    "application/msword" => $"{mediaUrlResponse.Id}.doc",
                    "application/vnd.ms-excel" => $"{mediaUrlResponse.Id}.xls",
                    "application/vnd.openxmlformats-officedocument.wordprocessingml.document" => $"{mediaUrlResponse.Id}.docx",
                    "application/vnd.openxmlformats-officedocument.presentationml.presentation" => $"{mediaUrlResponse.Id}.pptx",
                    "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet" => $"{mediaUrlResponse.Id}.xlsx",

                    "video/3gp" => $"{mediaUrlResponse.Id}.3gp",
                    "video/mp4" => $"{mediaUrlResponse.Id}.mp4",

                    _ => throw new NotSupportedException($"Unsupported MimeType: {mediaUrlResponse.MimeType}")
                };

                // Convert byte array to stream
                using var stream = new MemoryStream(mediaFileDownloaded);

                // Set up AWS S3 client and credentials
                var region = RegionEndpoint.GetBySystemName(regionString);
                var credentials = new BasicAWSCredentials(accessKey, secretKey);
                var s3Client = new AmazonS3Client(credentials, region);
                var utility = new TransferUtility(s3Client);

                // Perform the upload
                await utility.UploadAsync(stream, bucketName, filename);

                // Return the S3 file URL
                return $"https://{bucketName}.s3.{regionString}.amazonaws.com/{filename}";
            }

            return string.Empty;
        }

        public async Task<string> GetByFile(IFormFile media)
        {
            try
            {
                var accessKey = _configuration["AWS:AccessKey"];
                var secretKey = _configuration["AWS:SecretKey"];
                var bucketName = _configuration["AWS:BucketName"];
                var regionString = _configuration["AWS:Region"];

                // Generate a unique filename
                string filename = Guid.NewGuid().ToString() + Path.GetExtension(media.FileName);

                var region = RegionEndpoint.GetBySystemName(regionString);
                var credentials = new BasicAWSCredentials(accessKey, secretKey);
                var s3Client = new AmazonS3Client(credentials, region);
                var utility = new TransferUtility(s3Client);

                // Read file content from IFormFile into a MemoryStream
                using (var memoryStream = new MemoryStream())
                {
                    await media.CopyToAsync(memoryStream);
                    memoryStream.Position = 0;

                    // Perform the upload
                    await utility.UploadAsync(memoryStream, bucketName, filename);
                }

                // Make the uploaded file public
                #region comment code
                //var request = new PutACLRequest
                //{
                //    BucketName = bucketName,
                //    Key = filename,
                //    CannedACL = S3CannedACL.PublicRead // Set the ACL to make it publicly readable
                //};

                //await s3Client.PutACLAsync(request);
                #endregion

                return "https://" + bucketName + ".s3." + regionString + ".amazonaws.com/" + filename;
            }
            catch (Exception ex)
            {
                // Handle exceptions appropriately
                throw new Exception("Error uploading file to S3", ex);
            }
        }

        public async Task<string> UploadFileAsync(byte[] fileBytes, string fileName)
        {
            try
            {
                var accessKey = _configuration["AWS:AccessKey"];
                var secretKey = _configuration["AWS:SecretKey"];
                var bucketName = _configuration["AWS:BucketName"];
                var regionString = _configuration["AWS:Region"];
                fileName = fileName ?? Guid.NewGuid().ToString();

                var region = RegionEndpoint.GetBySystemName(regionString);
                var credentials = new BasicAWSCredentials(accessKey, secretKey);
                var s3Client = new AmazonS3Client(credentials, region);
                var utility = new TransferUtility(s3Client);

                using (var memoryStream = new MemoryStream(fileBytes))
                {
                    await utility.UploadAsync(memoryStream, bucketName, fileName);
                }
                #region comment code
                //var request = new PutACLRequest
                //{
                //    BucketName = bucketName,
                //    Key = fileName,
                //    CannedACL = S3CannedACL.PublicRead
                //};

                //await s3Client.PutACLAsync(request);
                #endregion

                return $"https://{bucketName}.s3.{regionString}.amazonaws.com/{fileName}";
            }
            catch (Exception ex)
            {
                throw new Exception("Error uploading file to S3", ex);
            }
        }

        public async Task<(bool IsValid, string ErrorMessage)> ValidateMediaFile(string mediaFileUrl)
        {
            var client = _httpClientFactory.CreateClient();
            var res = await client.GetAsync(mediaFileUrl);
            if (!res.IsSuccessStatusCode)
            {
                return (false, "Failed to fetch media file from the provided URL.");
            }
            var contentType = res.Content.Headers.ContentType?.MediaType;
            var fileSize = res.Content.Headers.ContentLength ?? 0;
            var fileName = mediaFileUrl.Split('/').Last();
            var fileType = contentType;

            byte[] fileBytes = await res.Content.ReadAsByteArrayAsync();
            bool isValidFile = false;

            if (fileSize > 0)
            {
                if (fileSize <= 5242880 && (fileType == "image/jpeg" || fileType == "image/png")) // 5MB for images
                {
                    isValidFile = true;
                }
                else if (fileSize <= 104857600 && (fileType.StartsWith("text/") || fileType == "application/pdf" || fileType.StartsWith("application/vnd"))) // 100MB for documents
                {
                    isValidFile = true;
                }
                else if (fileType.StartsWith("video/"))
                {
                    // Supported video types and size check
                    var supportedVideoTypes = new[] { "video/mp4", "video/3gp" };
                    var maxVideoFileSize = 16 * 1024 * 1024; // 16MB

                    if (supportedVideoTypes.Contains(fileType) && fileSize <= maxVideoFileSize)
                    {
                        isValidFile = true;
                    }
                    else
                    {
                        return (false, "Unsupported video file type or file size exceeded.");
                    }
                }
                else
                {
                    return (false, "Unsupported file type.");
                }
            }
            else
            {
                return (false, "Empty file.");
            }

            return (isValidFile, null);
        }

    }
}
