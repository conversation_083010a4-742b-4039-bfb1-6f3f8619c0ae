﻿using Microsoft.AspNetCore.Http;
using Microsoft.Identity.Client;
using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.Linq;
using System.Net.Http;
using System.Text;
using System.Threading.Tasks;
using static System.Net.Mime.MediaTypeNames;

namespace EngagetoEntities.Extensions
{



    public class AllowedExtensionsAttribute : ValidationAttribute
    {
        public override bool IsValid(object value)
        {
            var file = value as IFormFile;
            if (file == null)
                return true; // Not a file upload, so no validation

            // Check file size (16MB limit)


            string contenttype = file.ContentType.ToLower();
            string[] _AllowedAudio = { "audio/aac", "audio/mp4", "audio/mpeg", "audio/amr", "audio/ogg" };
            string[] _AllowedVideo = { "video/mp4", "video/3gp" };
            string[] _AllowedImage = { "image/png", "image/jpeg" };
            string[] _AllowedDocument = { "text/plain", "application/pdf", "application/vnd.ms-powerpoint", "application/msword", "application/vnd.ms-excel", "application/vnd.openxmlformats- officedocument.wordprocessingml.document", "application/vnd.openxmlformats-officedocument.presentationml.presentation", "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet" };
            // Check if the extension is allowed
            if (_AllowedAudio.Contains(contenttype))
            {
                if (file.Length > 16 * 1024 * 1024)
                    return false;
                if (contenttype == "audio/ogg")
                {
                    // Read the first few bytes to determine the codec
                    var buffer = new byte[8];
                    using (var stream = file.OpenReadStream())
                    {
                        stream.Read(buffer, 0, 8);
                    }

                    // Check if the audio is encoded with Opus codec
                    if (!IsOpusCodec(buffer))
                        return false;
                }
            }
            else if (_AllowedImage.Contains(contenttype))
            {
                if (file.Length > 5 * 1024 * 1024)
                    return false;
            }
            else if (_AllowedVideo.Contains(contenttype))
            {
                if (file.Length > 16 * 1024 * 1024)
                    return false;
            }
            else if (_AllowedDocument.Contains(contenttype))
            {
                if (file.Length > 100 * 1024 * 1024)
                    return false;
            }
            else
            {
                return false;
            }
            // Additional validation for audio/ogg with Opus codec

            return true;
        }
        private bool IsOpusCodec(byte[] buffer)
        {
            // Implement your logic to detect Opus codec based on the file content
            // For demonstration purpose, return true for all audio/ogg files
            return true;
        }
    }


}
