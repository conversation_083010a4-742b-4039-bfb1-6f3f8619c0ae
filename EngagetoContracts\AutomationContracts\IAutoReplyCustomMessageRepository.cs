﻿using EngagetoEntities.Entities;

namespace EngagetoContracts.AutomationContracts
{
    public interface IAutoReplyCustomMessageRepository
    {
        public Task<T?> SaveAutoReplyCustomMessageAsync<T>(AutoReplyCustomMessageEntity customMessageDto, Guid userId);
        public Task<IEnumerable<T>> GetAutoReplyCustomMessageAsync<T>(Guid companyId, Guid? autoReplyAutomationId);
        public Task<T?> SaveAutoReplyVeriablesAsync<T>(string xml);
    }
}
