﻿using Amazon;
using Amazon.Runtime;
using Amazon.S3;
using Amazon.S3.Model;
using Amazon.S3.Transfer;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Http.HttpResults;
using Microsoft.AspNetCore.Mvc;
using Microsoft.EntityFrameworkCore;
using Microsoft.IdentityModel.Tokens;
using System.Security.Claims;
using EngagetoContracts.UserContracts;
using EngagetoEntities.UserEntities.Models;
using static System.Runtime.InteropServices.JavaScript.JSType;

namespace Engageto.Controllers.UserControllers
{
    [Route("api/[controller]")]
    [ApiController]
    public class UserController : ControllerBase
    {
        private readonly IUserService _userService;
        private readonly IHttpContextAccessor _httpContextAccessor;
        private readonly IConfiguration _configuration;
        private readonly ApplicationDBContext _context;


        public UserController(IUserService userService, IHttpContextAccessor httpContextAccessor, IConfiguration configuration, ApplicationDBContext context)
        {
            _userService = userService;
            _httpContextAccessor = httpContextAccessor;
            _configuration = configuration;
            _context = context;
        }


        [HttpPut("update-assigned-role/{userId}/{newRoleId}")]
        [AuthorizeRoles(RoleConstants.PlatformAdmin, RoleConstants.PlatformOwner)]
        public async Task<IActionResult> UpdateAssignedRole([FromQuery] string email, string companyId, Guid newRoleId)
        {
            try
            {
                var currentUserIdClaim = User.Claims.FirstOrDefault(c => c.Type == ClaimTypes.Name);

                if (currentUserIdClaim == null || !Guid.TryParse(currentUserIdClaim.Value, out Guid currentUserId))
                {
                    return BadRequest(new { Message = "Invalid current user." });
                }

                var success = await _userService.UpdateAssignedRoleAsync(email, companyId, newRoleId);

                if (success)
                {
                    return Ok(new { Message = "Assigned role updated successfully." });
                }
                else
                {
                    return BadRequest(new { Message = "Failed to update assigned role." });
                }
            }
            catch (Exception ex)
            {
                return StatusCode(500, new { Message = $"Internal Server Error: {ex.Message}" });
            }
        }

        [HttpDelete("delete-assigned-roles/{targetUserId}/{roleId}")]
        [AuthorizeRoles(RoleConstants.PlatformAdmin, RoleConstants.PlatformOwner)]
        public async Task<IActionResult> DeleteAssignedRoles(Guid targetUserId, Guid roleId)
        {
            try
            {
                var currentUserIdClaim = User.Claims.FirstOrDefault(c => c.Type == ClaimTypes.Name);

                if (currentUserIdClaim == null || !Guid.TryParse(currentUserIdClaim.Value, out Guid currentUserId))
                {
                    return BadRequest(new { Message = "Invalid current user." });
                }

                var success = await _userService.DeleteAssignedRolesAsync(currentUserId, targetUserId, roleId);

                if (success)
                {
                    return Ok(new { Message = "Assigned role deleted successfully." });
                }
                else
                {
                    return BadRequest(new { Message = "Failed to delete assigned role." });
                }
            }
            catch (Exception ex)
            {
                return StatusCode(500, new { Message = $"Internal Server Error: {ex.Message}" });
            }
        }

        [HttpPut("update-password")]
        // [AuthorizeMenu("changePassword")]
        public async Task<IActionResult> UpdatePassword([FromBody] UpdatePasswordRequest request)
        {
            try
            {

                var manageAccountUser = await _context.Ahex_CRM_Users.FirstOrDefaultAsync(u => u.EmailAddress == request.Email);
                if (manageAccountUser == null)
                {
                    return NotFound(new { Message = "User not found." });
                }
                if (!VerifyPassword(request.TemporaryPassword, manageAccountUser.Password))
                {
                    return BadRequest(new { Message = "Invalid current password." });
                }
                if (VerifyPassword(request.NewPassword, manageAccountUser.Password))
                {
                    return BadRequest(new { Message = "New password cannot be the same as the current password." });
                }

                var result = await _userService.UpdateUserPasswordAsync(request.Email, request.NewPassword, request.ConfirmPassword, request.TemporaryPassword);

                if (result)
                {
                    return Ok(new { Message = "Password updated successfully." });
                }
                else
                {
                    return BadRequest(new { Message = "Failed to update password." });
                }
            }
            catch (Exception ex)
            {

                return StatusCode(500, new { Message = "Internal Server Error" });
            }
        }



        [HttpPost("assign-role")]
        public async Task<IActionResult> AssignRoleToUser([FromBody] AssignRoleRequest request)
        {
            try
            {
                var currentUserIdClaim = User.Claims.FirstOrDefault(c => c.Type == ClaimTypes.Name);

                if (currentUserIdClaim == null || !Guid.TryParse(currentUserIdClaim.Value, out Guid currentUserId))
                {
                    return BadRequest(new { Message = "Invalid current user." });
                }

                var result = await _userService.AssignRoleToUserAsync(currentUserId, request.UserId, request.RoleId);

                if (result.Success)
                {
                    return Ok(result);
                }
                else
                {
                    return BadRequest(result);
                }
            }
            catch (Exception ex)
            {
                return StatusCode(500, new { Message = $"Internal Server Error: {ex.Message}" });
            }
        }

        private bool VerifyPassword(string plainPassword, string hashedPassword)
        {
            return BCrypt.Net.BCrypt.Verify(plainPassword, hashedPassword);
        }

    }
}

