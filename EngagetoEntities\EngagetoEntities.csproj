﻿<Project Sdk="Microsoft.NET.Sdk">

  <PropertyGroup>
    <TargetFramework>net7.0</TargetFramework>
    <ImplicitUsings>enable</ImplicitUsings>
    <Nullable>enable</Nullable>
  </PropertyGroup>

  <ItemGroup>
    <PackageReference Include="AngleSharp" Version="1.1.2" />
    <PackageReference Include="AutoMapper" Version="13.0.1" />
    <PackageReference Include="AutoMapper.Extensions.Microsoft.DependencyInjection" Version="12.0.0" />
    <PackageReference Include="BCrypt.Net" Version="0.1.0" />
    <PackageReference Include="Concentus.OggFile.NetStandard" Version="1.0.0" />
    <PackageReference Include="Dapper" Version="2.1.35" />
    <PackageReference Include="DocumentFormat.OpenXml" Version="2.16.0" />
    <PackageReference Include="EPPlus" Version="7.5.2" />
    <PackageReference Include="iTextSharp" Version="********" />
    <PackageReference Include="libphonenumber-csharp" Version="8.13.42" />
    <PackageReference Include="Mapster" Version="7.4.0" />
    <PackageReference Include="Microsoft.AspNetCore.Authentication.JwtBearer" Version="7.0.14" />
    <PackageReference Include="Microsoft.AspNetCore.Hosting" Version="2.1.1" />
    <PackageReference Include="Microsoft.AspNetCore.Http" Version="2.1.34" />
    <PackageReference Include="Microsoft.AspNetCore.Http.Features" Version="3.1.30" />
    <PackageReference Include="Microsoft.AspNetCore.SignalR.Client.Core" Version="8.0.6" />
    <PackageReference Include="Microsoft.EntityFrameworkCore" Version="7.0.14" />
    <PackageReference Include="Microsoft.EntityFrameworkCore.Design" Version="7.0.14">
      <PrivateAssets>all</PrivateAssets>
      <IncludeAssets>runtime; build; native; contentfiles; analyzers; buildtransitive</IncludeAssets> 
    </PackageReference>
    <PackageReference Include="Microsoft.EntityFrameworkCore.SqlServer" Version="7.0.14" />
    <PackageReference Include="Microsoft.EntityFrameworkCore.Tools" Version="7.0.14">
      <PrivateAssets>all</PrivateAssets>
      <IncludeAssets>runtime; build; native; contentfiles; analyzers; buildtransitive</IncludeAssets>
    </PackageReference>
    <PackageReference Include="Newtonsoft.Json" Version="13.0.3" />
    <PackageReference Include="Quartz" Version="3.9.0" />
    <PackageReference Include="Razorpay" Version="3.1.2" />
    <PackageReference Include="StackExchange.Redis" Version="2.7.33" />
    <PackageReference Include="System.IdentityModel.Tokens.Jwt" Version="7.0.0" />
    <PackageReference Include="System.Net.Http" Version="4.3.4" />
  </ItemGroup>

</Project>
