﻿using EngagetoEntities.Entities;
using EngagetoEntities.Enums;
using iTextSharp.text.pdf;

namespace EngagetoEntities.Utilities
{
    public static class CommonHelper
    {
        static Dictionary<string, string> _allowedFiles = new Dictionary<string, string> {
            {"jpg",  "image/jpeg"},
            {"jpeg", "image/jpeg"},
            {"gif",  "image/gif"},
            {"png",  "image/png"},
            {"tif",  "image/tiff"},
            {"tiff", "image/tiff"},
            {"txt",  "text/plain"},
            {"doc",  "application/msword"},
            {"docx",  "application/msword"},
            {"ppt",  "application/vnd.ms-powerpoint"},
            {"pptx",  "application/vnd.ms-powerpoint"},
            {"pdf",  "application/pdf"},
            {"csv",  "text/csv"},
            {"zip",  "application/zip"},
            {"xls",  "application/vnd.ms-excel"},
            {"xlsx", "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet" },
            {"msg", "application/vnd.ms-outlook" },
            {"eml", "message/rfc822" }
        };
        public static decimal CurrencyConvertIntoINR(decimal amount, Currency currency)
        {
            switch (currency)
            {
                case Currency.INR:
                    return amount;
                case Currency.USD:
                    return amount * 83.30m; // Example exchange rate
                default:
                    return amount;
            }
        }

        public static void MapPaymentDetailsWithTransactionHistory(ref TransactionHistoryEntity transactionHistoryEntity, Razorpay.Api.Payment payment)
        {
            var paymentDetails = payment;

            transactionHistoryEntity.PaymentId = paymentDetails["id"];
            transactionHistoryEntity.PaymentMethod = paymentDetails["method"];
            transactionHistoryEntity.Email = paymentDetails["email"];
            transactionHistoryEntity.Contact = paymentDetails["contact"];
            transactionHistoryEntity.AccountId = paymentDetails["account_id"];

            if (payment["status"] == "captured")
            {
                //transactionHistoryEntity.Status = "paid";
                var method = (string)paymentDetails["method"];
                switch (method)
                {
                    case "netbanking":
                        transactionHistoryEntity.BankTransactionId = paymentDetails["acquirer_data"]["bank_transaction_id"];
                        break;
                    case "card":
                        transactionHistoryEntity.CardId = paymentDetails["card"]["id"];
                        transactionHistoryEntity.CardLast4Digit = paymentDetails["card"]["last4"];
                        transactionHistoryEntity.CardNetwork = paymentDetails["card"]["netword"];
                        transactionHistoryEntity.CardType = paymentDetails["card"]["type"];
                        break;
                    case "wallet":
                        transactionHistoryEntity.PaymentWallet = paymentDetails["wallet"];
                        break;
                    case "upi":
                        transactionHistoryEntity.PayerAccountType = paymentDetails["upi"]["payer_account_type"];
                        transactionHistoryEntity.PayerAccountType = paymentDetails["upi"]["vpa"];
                        break;
                }
            }

        }
        public static string GetMimeFileType(string fileName)
        {
            var fileParts = (fileName ?? string.Empty).Split('.');
            var extension = fileParts[fileParts.Length - 1];

            if (fileParts.Length < 2)
                return null;

            if (_allowedFiles.ContainsKey(extension))
                return _allowedFiles[extension];

            return null;
        }
        public static (string name, string extension) GetExtensionOfFile(string fileName)
        {
            if (string.IsNullOrWhiteSpace(fileName))
                throw new InvalidOperationException("File name cannot be null or empty.");

            var fileParts = fileName.Split('.');

            if (fileParts.Length < 2)
                throw new InvalidOperationException("Invalid file name. File must have an extension.");

            var extension = fileParts[^1]; // ^1 is the last element
            var name = string.Join('.', fileParts.Take(fileParts.Length - 1));

            return (name, extension);
        }

        public static byte[] CompressPdf(byte[] inputPdfBytes)
        {
            using (var inputStream = new MemoryStream(inputPdfBytes))
            {
                using (var outputStream = new MemoryStream())
                {
                    using (var reader = new PdfReader(inputStream))
                    {
                        using (var stamper = new PdfStamper(reader, outputStream, PdfWriter.VERSION_1_5))
                        {
                            stamper.SetFullCompression();
                            stamper.Reader.RemoveUnusedObjects();
                            stamper.Writer.CompressionLevel = PdfStream.BEST_COMPRESSION;
                            stamper.Close();
                        }
                    }
                    return outputStream.ToArray();
                }
            }
        }


    }
}
