﻿using System.ComponentModel.DataAnnotations.Schema;

namespace EngagetoEntities.Entities
{
    [Table("WidgetUrlFieldEntities")]
    public class WidgetUrlFieldEntity
    {
        public Guid Id { get; set; }
        public string SourceUrl { get; set; } // Not nullable
        public string PreFilledMessage { get; set; } // Not nullable
        public string OnScreenMessage { get; set; } // Not nullable
        public bool RemoveChecked { get; set; } // Not nullable
        public bool CapitalizeChecked { get; set; } // Not nullable
        public string BrandImageUrl { get; set; } // Not nullable

        // Navigation property to link back to WidgetEntity
        public virtual WidgetEntity WidgetEntity { get; set; }
    }
}
