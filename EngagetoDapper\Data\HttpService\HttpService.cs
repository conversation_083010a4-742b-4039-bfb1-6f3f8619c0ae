﻿using Microsoft.Extensions.Logging;
using <PERSON>;
using System.Text;
using System.Text.Json;

namespace EngagetoDapper.Data.HttpService
{
    internal class HttpService : IHttpService
    {
        private readonly ILogger<HttpService> _logger;
        public HttpService(ILogger<HttpService> logger)
        {
            _logger = logger;
        }

        public async Task<T?> GetAsync<T>(string url, Dictionary<string, string> headers)
        {
            try
            {
                HttpClient client = new HttpClient();
                client.BaseAddress = new Uri(url);
                client.DefaultRequestHeaders.Add("Accept", "application/json");
                foreach (var header in headers)
                    client.DefaultRequestHeaders.Add(header.Key, header.Value);

                var response = await client.GetAsync(url);
                if (response.StatusCode == System.Net.HttpStatusCode.OK)
                {
                    var j = await response.Content.ReadAsStringAsync();
                    return JsonSerializer.Deserialize<T>(await response.Content.ReadAsStringAsync());
                }
                else
                {
                    var j = await response.Content.ReadAsStringAsync();
                    _logger.LogError($"Getting exception while pulling record from Get API:satus code: {response.StatusCode},{response.Content}");
                    return default;
                }
            }
            catch (Exception ex)
            {
                _logger.LogError($"Getting exception while pulling record from Get API:{url},{ex}");
                return default;
            }
        }

        public async Task<object?> PostAsync<T>(string url, Dictionary<string, string> headers, T body)
        {
            try
            {
                using HttpClient client = new() { BaseAddress = new Uri(url) };
                client.DefaultRequestHeaders.Add("Accept", "application/json");

                foreach (var header in headers)
                {
                    client.DefaultRequestHeaders.Add(header.Key, header.Value);
                }

                var options = new JsonSerializerOptions
                {
                    PropertyNamingPolicy = JsonNamingPolicy.CamelCase,
                    WriteIndented = true
                };

                string json = JsonSerializer.Serialize(body, options);
                using StringContent jsonContent = new(json, Encoding.UTF8, "application/json");

                var response = await client.PostAsync(url, jsonContent);
                response.EnsureSuccessStatusCode();

                string content = await response.Content.ReadAsStringAsync();
                return JsonSerializer.Deserialize<object>(content, options);
            }
            catch (HttpRequestException ex)
            {
                _logger.LogError(ex, $"HttpRequestException occurred while making POST request to {url}.");
                return ex.Message;
            }
        }

    }
}
