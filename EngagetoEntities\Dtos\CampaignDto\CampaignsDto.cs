﻿using System.ComponentModel.DataAnnotations;
using EngagetoEntities.Dtos.CommonDtos.PaymentGatwayDtos;
using EngagetoEntities.Entities;
using EngagetoEntities.Enums;

namespace EngagetoEntities.Dtos.CampaignDto
{
    public class CreateCampaignDto : BaseCampaignsDto
    {
        public List<Guid>? Audiences { get; set; }
        public DateTime? ScheduleDate { get; set; }
        public CampaignTemplateDto? Template { get; set; }
        public string? Text { get; set; }
        public string? MediaUrl { get; set; }
        public int? UploadedFileId { get; set; }
    }
    public class BaseCampaignsDto
    {
        public Guid? Id { get; set; }
        public string BusinessId { get; set; }
        public string? Name { get; set; }

    }
    public class CampaignTemplateDto
    {
        public Guid Id { get; set; }
        public MediaType MediaType { get; set; }
        public List<VariableDto>? BodyValues { get; set; }
        public VariableDto? HeaderValue { get; set; }
        public List<CarouselVariablesDto>? CarouselVariables { get; set; }
    }

    public class CarouselVariablesDto
    {
        public List<VariableDto>? BodyCarouselVariableValues { get; set; }
        public List<VariableDto>? RedirectUrlVariableValues { get; set; }
        public string? MediaUrl { get; set; }
    }

    public class ViewCampignDto : Campaigns
    {
    }

    public class InputPayloadDto
    {
        public Guid CampaignId { get; set; }
        public Campaign CampaignJsonData { get; set; } = default!;
        public DateTime ScheduledTime { get; set; }
    }
}
