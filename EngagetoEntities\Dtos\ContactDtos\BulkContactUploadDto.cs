﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace EngagetoEntities.Dtos.ContactDtos
{
    public class BulkContactUploadDto
    {
        public string? S3BucketKey { get; set; }
        public string? SheetName { get; set; }
        public string? FileName { get; set; }
        public int UploadedFileId { get; set; } 

    }
    public class ContactBulkUploadData
    {
        public string BusinessId { get; set; } = string.Empty;
        public Guid UserId { get; set; } = Guid.Empty;
    }
}
