﻿using EngagetoContracts.AutomationContracts;
using EngagetoEntities.Dtos.AutomationDtos;
using EngagetoEntities.Entities;
using EngagetoEntities.UserEntities.Models;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using System.ComponentModel.DataAnnotations;
using System.Runtime.InteropServices;
using System.Text.Json;

namespace Engageto.Controllers.AutomationControllers
{
    [Route("api/[controller]")]
    [ApiController]
    public class WorkingHoursController : ControllerBase
    {
        private readonly IRepositoryBase _context;

        public WorkingHoursController(IRepositoryBase repository)
        {
            _context = repository;
        }

        /* [Route("get_working_hours")]
         [HttpGet]
         [Authorize]
         public ActionResult GetWorkingHours([FromQuery, Required] Guid UserId, [FromQuery, Required] Guid BusinessId)
         {
             try
             {
                 // Fetch working hours from the database
                 var workingHours = _context.GetWorkingHours(UserId, BusinessId);

                 // If working hours are not found, return an empty list
                 if (workingHours == null)
                 {
                     return Ok(new List<WorkingHoursDto>());
                 }

                 // Initialize the WorkingHoursDto and a list for DaysOfWeek
                 WorkingHoursDto workingHoursDto = new WorkingHoursDto();
                 List<DayOfWeekDto> list = new List<DayOfWeekDto>();

                 // Add each day only if 'From' and 'To' values are not null
                 AddDayOfWeekDto(workingHours.SundayFrom, workingHours.SundayTo, EngagetoEntities.AutomationEntities.Enum.DayOfWeek.Sunday, list);
                 AddDayOfWeekDto(workingHours.MondayFrom, workingHours.MondayTo, EngagetoEntities.AutomationEntities.Enum.DayOfWeek.Monday, list);
                 AddDayOfWeekDto(workingHours.TuesdayFrom, workingHours.TuesdayTo, EngagetoEntities.AutomationEntities.Enum.DayOfWeek.Tuesday, list);
                 AddDayOfWeekDto(workingHours.WednesdayFrom, workingHours.WednesdayTo, EngagetoEntities.AutomationEntities.Enum.DayOfWeek.Wednesday, list);
                 AddDayOfWeekDto(workingHours.ThursdayFrom, workingHours.ThursdayTo, EngagetoEntities.AutomationEntities.Enum.DayOfWeek.Thursday, list);
                 AddDayOfWeekDto(workingHours.FridayFrom, workingHours.FridayTo, EngagetoEntities.AutomationEntities.Enum.DayOfWeek.Friday, list);
                 AddDayOfWeekDto(workingHours.SaturdayFrom, workingHours.SaturdayTo, EngagetoEntities.AutomationEntities.Enum.DayOfWeek.Saturday, list);

                 // Set the DaysOfWeek and TimeZone in the DTO
                 workingHoursDto.DaysOfWeek = list;
                 workingHoursDto.TimeZone = workingHours.TimeZone;

                 // Return the WorkingHoursDto object as the response
                 return Ok(workingHoursDto);
             }
             catch (Exception ex)
             {
                 return StatusCode(500, new { Message = "An error occurred while fetching the working hours.", Error = ex.Message });
             }
         }
 */
        [Route("get_working_hours")]
        [HttpGet]
        [Authorize]
        public async Task<ActionResult> GetWorkingHours([FromQuery, Required] string UserId, [FromQuery, Required] string BusinessId)
        {
            try
            {
                // Decrypt the encrypted query parameters
                Guid userId = Guid.Parse(EncryptionHelper.Decrypt(UserId));
                Guid businessId = Guid.Parse(EncryptionHelper.Decrypt(BusinessId));

                // Fetch working hours from the database
                var workingHours = await _context.GetWorkingHours(userId, businessId);

                // If working hours are not found, return an empty list
                if (workingHours == null)
                {
                    return Ok(new List<WorkingHoursDto>());
                }

                // Initialize the WorkingHoursDto and a list for DaysOfWeek
                WorkingHoursDto workingHoursDto = new WorkingHoursDto();
                List<DayOfWeekDto> list = new List<DayOfWeekDto>();

                // Add each day only if 'From' and 'To' values are not null
                AddDayOfWeekDto(workingHours.SundayFrom, workingHours.SundayTo, EngagetoEntities.Enums.DayOfWeek.Sunday, list);
                AddDayOfWeekDto(workingHours.MondayFrom, workingHours.MondayTo, EngagetoEntities.Enums.DayOfWeek.Monday, list);
                AddDayOfWeekDto(workingHours.TuesdayFrom, workingHours.TuesdayTo, EngagetoEntities.Enums.DayOfWeek.Tuesday, list);
                AddDayOfWeekDto(workingHours.WednesdayFrom, workingHours.WednesdayTo, EngagetoEntities.Enums.DayOfWeek.Wednesday, list);
                AddDayOfWeekDto(workingHours.ThursdayFrom, workingHours.ThursdayTo, EngagetoEntities.Enums.DayOfWeek.Thursday, list);
                AddDayOfWeekDto(workingHours.FridayFrom, workingHours.FridayTo, EngagetoEntities.Enums.DayOfWeek.Friday, list);
                AddDayOfWeekDto(workingHours.SaturdayFrom, workingHours.SaturdayTo, EngagetoEntities.Enums.DayOfWeek.Saturday, list);

                // Set the DaysOfWeek and TimeZone in the DTO
                workingHoursDto.DaysOfWeek = list;
                workingHoursDto.TimeZone = workingHours.TimeZone;

                // Return the WorkingHoursDto object as the response

                var jsonResponse = JsonSerializer.Serialize(workingHoursDto);
                var encryptedResponse = EncryptionHelper.Encrypt(jsonResponse);

                // Return the encrypted response
                return Ok(encryptedResponse);
            }
            catch (Exception ex)
            {
                return StatusCode(500, new { Message = "An error occurred while fetching the working hours.", Error = ex.Message });
            }
        }

        // Helper method to add DayOfWeekDto to the list only if both From and To are not null
        private void AddDayOfWeekDto(TimeSpan? from, TimeSpan? to, EngagetoEntities.Enums.DayOfWeek dayOfWeek, List<DayOfWeekDto> list)
        {
            if (from != null && to != null)
            {
                list.Add(new DayOfWeekDto
                {
                    DayOfWeek = dayOfWeek,
                    From = from,
                    To = to
                });
            }
        }


        [Route("save_working_hours")]
        [HttpPost]
        [Authorize]
        public async Task<ActionResult> SaveWorkingHours([FromQuery, Required] Guid UserId, [FromQuery, Required] Guid BusinessId, [FromBody, Optional] WorkingHoursDto data)
        {
            try
            {
                // Validate the timezone
                if (!_context.IsValidTimeZone(data.TimeZone))
                {
                    return BadRequest(new { Message = "Invalid time zone provided." });
                }

                WorkingHours wh = new WorkingHours
                {
                    UserId = UserId,
                    BusinessId = BusinessId
                };

                // Loop through the working hours data
                foreach (var WHDto in data.DaysOfWeek)
                {
                    switch (WHDto.DayOfWeek.ToString())
                    {
                        case "Sunday":
                            wh.SundayFrom = WHDto.From;
                            wh.SundayTo = WHDto.To;
                            break;
                        case "Monday":
                            wh.MondayFrom = WHDto.From;
                            wh.MondayTo = WHDto.To;
                            break;
                        case "Tuesday":
                            wh.TuesdayFrom = WHDto.From;
                            wh.TuesdayTo = WHDto.To;
                            break;
                        case "Wednesday":
                            wh.WednesdayFrom = WHDto.From;
                            wh.WednesdayTo = WHDto.To;
                            break;
                        case "Thursday":
                            wh.ThursdayFrom = WHDto.From;
                            wh.ThursdayTo = WHDto.To;
                            break;
                        case "Friday":
                            wh.FridayFrom = WHDto.From;
                            wh.FridayTo = WHDto.To;
                            break;
                        case "Saturday":
                            wh.SaturdayFrom = WHDto.From;
                            wh.SaturdayTo = WHDto.To;
                            break;
                    }
                }

                // Retrieve existing working hours from the database
                var Wh = await _context.GetWorkingHours(UserId, BusinessId);
                if (Wh != null)
                {
                    Wh.UserId = wh.UserId;
                    Wh.BusinessId = wh.BusinessId;
                    Wh.SundayFrom = wh.SundayFrom;
                    Wh.SundayTo = wh.SundayTo;
                    Wh.MondayFrom = wh.MondayFrom;
                    Wh.MondayTo = wh.MondayTo;
                    Wh.TuesdayFrom = wh.TuesdayFrom;
                    Wh.TuesdayTo = wh.TuesdayTo;
                    Wh.WednesdayFrom = wh.WednesdayFrom;
                    Wh.WednesdayTo = wh.WednesdayTo;
                    Wh.ThursdayFrom = wh.ThursdayFrom;
                    Wh.ThursdayTo = wh.ThursdayTo;
                    Wh.FridayFrom = wh.FridayFrom;
                    Wh.FridayTo = wh.FridayTo;
                    Wh.SaturdayFrom = wh.SaturdayFrom;
                    Wh.SaturdayTo = wh.SaturdayTo;
                    Wh.TimeZone = data.TimeZone;

                    // Update the existing working hours
                    await _context.UpdateWorkingHours(Wh);
                    return Ok(new { Message = "Working hours have been successfully updated." });
                }

                // If no existing working hours, add new ones
                await _context.AddWorkingHours(wh);
                return Ok(new { Message = "Working hours have been successfully saved." });
            }
            catch (Exception ex)
            {
                return BadRequest(new { Message = ex.Message });
            }
        }
    }
}

