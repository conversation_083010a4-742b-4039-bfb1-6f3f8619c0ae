﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace EngagetoEntities.Dtos.WhatsAppChargeDtos
{
    public class MasterMetaPriceDtos
    {
        public Guid Id { get; set; }
        public string? Currency { get; set; }
        public decimal? Marketing { get; set; }
        public decimal? Utility { get; set; }
        public decimal? Service { get; set; }
        public decimal? Authentication { get; set; }
        public decimal? AuthenticationOutSide { get; set; }
        public string? CountryName { get; set; }
        public string? CountryCode { get; set; }
    }

    public  class CustomMetapPriceDtos
    {
        public string? Currency { get; set; }
        public decimal? MarketingFee { get; set; }
        public decimal? OutSideCountryFee { get; set; }
        public decimal? UtilityFee { get; set; }
        public decimal? ServiceFee { get; set; }
        public decimal? AuthenticationFee { get; set; }
        public decimal? AuthenticationOutSideFee { get; set; }
        public Guid? MasterMetaPriceId { get; set; }

    }

}
