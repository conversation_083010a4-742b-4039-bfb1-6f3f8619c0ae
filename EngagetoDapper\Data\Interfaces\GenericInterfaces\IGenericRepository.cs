﻿using EngagetoDapper.Data.Dtos;

namespace EngagetoDapper.Data.Interfaces.GenericInterfaces
{
    public interface IGenericRepository
    {
        Task<int> SaveAsync<T>(T entity);
        Task<List<T>> GetAllAsync<T>();
        Task<List<T>> GetByGuidIdAsync<T>(Dictionary<Guid, string> filterValues);
        Task<List<T>> GetByNameAsync<T>(Dictionary<string, string> filterValues);
        Task<T?> UpdateRecordAsync<T>(T entity, Dictionary<string, object>? filterValues = null);
        Task<List<T>> GetByObjectAsync<T>(Dictionary<string, object> filterValues, string? tableName = null);
        Task<List<T>> GetRecordByRequestFilter<T>(List<RequestFilterDto> requestFilters, string? tableName = null, int? page = 0, int? pageSize = 0, List<string>? columns = null);
        Task<bool> UpdateRecordAsync<T>(string tableName, List<string> columns, T entity, Dictionary<string, object> conditions);
        Task<bool> DeleteRecordAsync<T>(string tableName, List<RequestFilterDto> requestFilters);
        Task<bool> InsertRecordsAsync<T>(string tableName, List<string> columns, List<T> entities);
        Task<bool> IsExistAsync<T>(Dictionary<string, object> filterValues, string? tableName = null);
        Task<bool> FindAndUpdateAsync(string tableName, Dictionary<string, object> updatedValues, Dictionary<string, object> conditions);
        Task<int>  Count<T>(Dictionary<string, Object> filters, string? tableName = null);
        Task<List<T>> GetColumnValuesAsync<T>(List<RequestFilterDto> filters, string columnName, string tableName, string? orderByColumn = null, string? orderDirection = "ASC", int? page = 0, int? pageSize = 0);
        Task<bool> UpdateRecordsByIdsAsync<TId, TEntity>(
            string tableName,
            IEnumerable<string> columnsToUpdate,
            TEntity updateValues,
            string idColumn,
            IEnumerable<TId> ids);
    }
}
