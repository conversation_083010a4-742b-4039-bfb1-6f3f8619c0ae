﻿using AutoMapper;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Logging;
using EngagetoContracts.UserContracts;
using EngagetoEntities.UserEntities.Models;
using EngagetoEntities.Entities;

namespace EngagetoRepository.UserRepository
{
    public class DiscountService: IDiscountService
    {
        private readonly ILogger<DiscountService> _logger;
        private readonly ApplicationDBContext _dbContext;
        private readonly IMapper _mapper;
        public DiscountService(ApplicationDBContext dbContext, ILogger<DiscountService> logger, IMapper mapper)
        {
            _dbContext = dbContext;
            _logger = logger;
            _mapper = mapper;
        }

        public async Task<DiscountEntity> CreateDiscountAsyn(DiscountEntity entity)
        {
            var result = await _dbContext.DiscountEntities.AddAsync(entity);
            _dbContext.SaveChanges();
            return entity;
        }

        public async Task<DiscountEntity> GetDiscountEntity(string discountName)
        {
            var discountEntity = await _dbContext.DiscountEntities.FirstOrDefaultAsync(x=>x.DiscountCode.Equals(discountName));
            if (discountEntity == null)
            {
                _logger.LogInformation($"Not Found any discount for this code: {discountName}");
                throw new Exception("Not Found any discount for this code");
            }
            else
            {
                return discountEntity;
            }

        }

        public async Task<DiscountEntity> UpdateDiscountAsyn(DiscountEntity discountEntity)
        {
            var discount = await GetDiscountEntity(discountEntity.DiscountCode);
            _mapper.Map(discountEntity, discount);
            _dbContext.SaveChanges();
            return discount;
        }
    }
}
