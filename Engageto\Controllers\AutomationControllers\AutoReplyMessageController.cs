﻿using Engageto.Attributes;
using EngagetoContracts.AutomationContracts;
using EngagetoContracts.Services;
using EngagetoDapper.Data.Interfaces.AutomationInterfaces;
using EngagetoDapper.Data.Interfaces.CommonInterfaces;
using EngagetoEntities.Dtos.AutomationDtos;
using EngagetoEntities.Entities;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using Newtonsoft.Json;
using System.ComponentModel.DataAnnotations;
using System.Security.Claims;

namespace Engageto.Controllers.AutomationControllers
{
    [Route("api/[controller]")]
    [ApiController]
    public class AutoReplyMessageController : BaseController
    {

        private readonly ILogger<AutoReplyMessageController> _logger;
        private readonly IAutoReplyMessageService _autoReplyMessageService;
        private readonly IAutomationWorkflowService _automationWorkflowService;
        private readonly IRepositoryBase _repositoryBase;
        private readonly ICommonService _commonService;
        private readonly IUserIdentityService _userIdentityService;

        public AutoReplyMessageController(
            IAutoReplyMessageService autoReplyMessageService,
            IAutomationWorkflowService automationWorkflowService,
            ILogger<AutoReplyMessageController> logger,
            ICommonService commonService,
            IRepositoryBase repositoryBase,
            IUserIdentityService userIdentityService
            )
        {

            _logger = logger;
            _autoReplyMessageService = autoReplyMessageService;
            _automationWorkflowService = automationWorkflowService;
            _commonService = commonService;
            _repositoryBase = repositoryBase;
            _userIdentityService = userIdentityService;
        }


        [HttpPost]
        [HttpPut]
        [Route("CreateAutoReplyMessage/{isAutomation:bool?}")]
        [Authorize]
        public async Task<IActionResult> CreateAutoReplyMessage(AutoReplyMessageDto customMessageDto, bool isAutomation = false)
        {
            var currentUserIdClaim = User.Claims.FirstOrDefault(c => c.Type == ClaimTypes.Name);
            if (currentUserIdClaim == null || !Guid.TryParse(currentUserIdClaim.Value, out Guid currentUserId))
            {
                throw new Exception("Invalid current user.");
            }
            try
            {
                var result = await _autoReplyMessageService.SaveAutoReplyMessageAsync(customMessageDto, currentUserId,isAutomation);
                return Ok(CreateSuccessResponse<string>("Auto reply message saved successfully"));
            }
            catch (Exception ex)
            {
                _logger.LogError($"Error saving record for auto-reply message: {JsonConvert.SerializeObject(CreateErrorResponse<string>(ex.Message, ex.StackTrace))}");
                return StatusCode(500, CreateErrorResponse<string>(ex.Message, ex.StackTrace));
            }
        }


        [HttpGet]
        [Route("GetAutoReplyMessage")]
        [Authorize]
        public async Task<IActionResult> GetAutoReplyMessage([FromQuery, Required] Guid companyId, Guid? autoReplyAutomationId)
        {
            try
            {
                var result = await _autoReplyMessageService.GetAutoReplyMessageAsync(companyId, autoReplyAutomationId);
                return Ok(CreateSuccessResponse<List<AutoReplyMessageDto>>(result.ToList() ?? new List<AutoReplyMessageDto>(), "Auto reply custom message list"));
            }
            catch (Exception ex)
            {
                var error = CreateErrorResponse<string>(ex.Message, ex.StackTrace);
                _logger.LogError($"Error getting auto-reply message record: {JsonConvert.SerializeObject(error)}");
                return StatusCode(500, error);
            }
        }

        [HttpDelete("DeleteAutoReplyMessage")]
        [Authorize]
        public async Task<IActionResult> DeleteAutoReplyMessage([Required] Guid companyId, [Required] Guid autoReplyAutomationId)
        {
            try
            {
                var currentUserIdClaim = User.Claims.FirstOrDefault(c => c.Type == ClaimTypes.Name);

                if (currentUserIdClaim == null || !Guid.TryParse(currentUserIdClaim.Value, out Guid currentUserId))
                {
                    throw new Exception("Invalid current user.");
                }
                var result = await _autoReplyMessageService.DeleteAutoReplyMessageAsync(companyId, currentUserId, autoReplyAutomationId);
                return Ok(CreateSuccessResponse<string>("The Auto-reply  message deleted successfully."));
            }
            catch (Exception ex)
            {
                var error = CreateErrorResponse<string>(ex.Message, ex.StackTrace);
                _logger.LogError($"Error delete auto-reply  message record: {JsonConvert.SerializeObject(error)}");
                return StatusCode(500, error);
            }
        }

        [HttpGet("DownloadWorkflowResponseListByName")]
        [Authorize]
        public async Task<IActionResult> DownloadWorkflowListExcelByName(string WorkflowName, Guid CompanyId)
        {
            try
            {
                // Generate Excel file using the service and the provided workflow name and company ID
                var stream = await _commonService.GenerateWorkflowResponseListByName(WorkflowName, CompanyId);

                // Set the file name for the downloaded file
                var fileName = $"{WorkflowName}_WorkflowList.xlsx";

                // Return the Excel file as a download
                return File(stream, "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet", fileName);
            }
            catch (Exception ex)
            {
                var error = CreateErrorResponse<string>(ex.Message, ex.StackTrace);
                _logger.LogError($"Error generating Excel file for workflow list by name: {JsonConvert.SerializeObject(error)}");
                return StatusCode(500, error);
            }
        }

        [HttpPost]
        [Route("AddVeriableName")]
        [ValidateModelState]
        [Authorize]
        public async Task<IActionResult> AddVeriableName(VeriableNameEntity veriableName)
        {
            try
            {
                var currentUserIdClaim = User.Claims.FirstOrDefault(c => c.Type == ClaimTypes.Name);
                if (currentUserIdClaim == null || !Guid.TryParse(currentUserIdClaim.Value, out Guid currentUserId))
                {
                    throw new Exception("Invalid current user.");
                }
                veriableName.UserId = currentUserId;
                var response = await _commonService.SaveVeriableNameAsync(veriableName);
                return Ok(CreateSuccessResponse<VeriableNameEntity>(response, "Veriable name inserted"));

            }
            catch (Exception ex)
            {
                var error = CreateErrorResponse<string>(ex.Message, ex.StackTrace);
                _logger.LogError($"Error Save auto-reply AddVeriableName record: {JsonConvert.SerializeObject(error)}");
                return StatusCode(500, error);
            }
        }

        [HttpGet]
        [Route("GetVeriableNames")]
        [Authorize]
        public async Task<IActionResult> GetVeriableNames([FromQuery, Required] Guid companyId)
        {
            try
            {
                var currentUserIdClaim = User.Claims.FirstOrDefault(c => c.Type == ClaimTypes.Name);
                if (currentUserIdClaim == null || !Guid.TryParse(currentUserIdClaim.Value, out Guid currentUserId))
                {
                    throw new Exception("Invalid current user.");
                }

                var response = await _commonService.GetVeriableNameAsync(companyId, null);
                return Ok(CreateSuccessResponse<List<VeriableNameEntity>>(response, "Veriable Names List"));

            }
            catch (Exception ex)
            {
                var error = CreateErrorResponse<string>(ex.Message, ex.StackTrace);
                _logger.LogError($"Error getting auto-reply GetVeriableNames record: {JsonConvert.SerializeObject(error)}");
                return StatusCode(500, error);
            }
        }

        [HttpPost]
        [Route("AddWorkflowList")]
        [ValidateModelState]
        [Authorize]
        public async Task<IActionResult> AddWorkflowList(WorkflowListDto workflowListDto)
        {
            try
            {
                var currentUserIdClaim = User.Claims.FirstOrDefault(c => c.Type == ClaimTypes.Name);
                if (currentUserIdClaim == null || !Guid.TryParse(currentUserIdClaim.Value, out Guid currentUserId))
                {
                    throw new Exception("Invalid current user.");
                }
                workflowListDto.UserId = currentUserId;
                var response = await _commonService.SaveInputListAsync(workflowListDto);
                return Ok(CreateSuccessResponse<WorkflowListDto>(response, "Workflow list insterted"));

            }
            catch (Exception ex)
            {
                var error = CreateErrorResponse<string>(ex.Message, ex.StackTrace);
                _logger.LogError($"Error Save auto-reply AddWorkflowList record: {JsonConvert.SerializeObject(error)}");
                return StatusCode(500, error);
            }
        }

        [HttpGet]
        [Route("GetWorkflowList")]
        [Authorize]
        public async Task<IActionResult> GetWorkflowList([FromQuery, Required] Guid companyId)
        {
            try
            {
                var currentUserIdClaim = User.Claims.FirstOrDefault(c => c.Type == ClaimTypes.Name);
                if (currentUserIdClaim == null || !Guid.TryParse(currentUserIdClaim.Value, out Guid currentUserId))
                {
                    throw new Exception("Invalid current user.");
                }

                var response = await _commonService.GetInputListAsync(companyId, null);
                return Ok(CreateSuccessResponse<List<WorkflowListDto>>(response, "Work flow list fetched successfully."));

            }
            catch (Exception ex)
            {
                var error = CreateErrorResponse<string>(ex.Message, ex.StackTrace);
                _logger.LogError($"Error getting auto-reply GetWorkflowList record: {JsonConvert.SerializeObject(error)}");
                return StatusCode(500, error);
            }
        }

        [HttpPut]
        [Route("UpdateWorkflowList")]
        [Authorize]
        public async Task<IActionResult> UpdateWorkflowList(WorkflowListDto entity)
        {
            try
            {
                var currentUserIdClaim = User.Claims.FirstOrDefault(c => c.Type == ClaimTypes.Name);
                if (currentUserIdClaim == null || !Guid.TryParse(currentUserIdClaim.Value, out Guid currentUserId))
                {
                    throw new Exception("Invalid current user.");
                }
                var response = await _commonService.UpdateInputListAsync(entity);
                return Ok(CreateSuccessResponse<WorkflowListDto>(response, "Work flow list updated successfully."));

            }
            catch (Exception ex)
            {
                var error = CreateErrorResponse<string>(ex.Message, ex.StackTrace);
                _logger.LogError($"Error getting auto-reply UpdateWorkflowList record: {JsonConvert.SerializeObject(error)}");
                return StatusCode(500, error);
            }
        }

        [HttpPost]
        [Route("CreateWorkflow/{isNew}")]
        [Authorize]
        public async Task<IActionResult> CreateWorkflow(bool isNew, AutomationWorkflowDto automationWorkflowDto)
        {
            try
            {
                Dictionary<string, List<AutomationWorkflowResponseDto>> automationWorkflowResponses = new Dictionary<string, List<AutomationWorkflowResponseDto>>();
                var currentUserIdClaim = User.Claims.FirstOrDefault(c => c.Type == ClaimTypes.Name);
                if (currentUserIdClaim == null || !Guid.TryParse(currentUserIdClaim.Value, out Guid currentUserId))
                {
                    throw new Exception("Invalid current user.");
                }
                automationWorkflowDto.UserId = currentUserId;
                var response = await _automationWorkflowService.SaveAutomationWorkFlowAsync(automationWorkflowDto, isNew);
                if (response != Guid.Empty)
                {
                    automationWorkflowResponses = await _automationWorkflowService.GetWorkflowAsync(automationWorkflowDto.CompanyId, null, response);
                }
                return Ok(CreateSuccessResponse<Dictionary<string, List<AutomationWorkflowResponseDto>>>(automationWorkflowResponses, "Work flow created successfully."));
            }
            catch (Exception ex)
            {
                var error = CreateErrorResponse<string>(ex.Message, ex.StackTrace);
                _logger.LogError($"Error getting to create workflow: {JsonConvert.SerializeObject(error)}");
                return StatusCode(500, error);
            }
        }

        [HttpGet]
        [Route("GetWorkflow")]
        [Authorize]
        public async Task<IActionResult> GetWorkflow([FromQuery, Required] Guid companyId, string? workflowName = null)
        {
            try
            {
                var currentUserIdClaim = User.Claims.FirstOrDefault(c => c.Type == ClaimTypes.Name);
                if (currentUserIdClaim == null || !Guid.TryParse(currentUserIdClaim.Value, out Guid currentUserId))
                {
                    throw new Exception("Invalid current user.");
                }
                var response = await _automationWorkflowService.GetWorkflowAsync(companyId, null, null, workflowName);
                return Ok(CreateSuccessResponse(response, "Work flow fetched successfully."));
            }
            catch (Exception ex)
            {
                var error = CreateErrorResponse<string>(ex.Message, ex.StackTrace);
                _logger.LogError($"Error getting to get workflows: {JsonConvert.SerializeObject(error)}");
                return StatusCode(500, error);
            }
        }
        [HttpGet("GetWorkflowNames/{companyId}")]
        [Authorize]
        public async Task<IActionResult> GetWorkflowNames(Guid companyId)
        {
            try
            {
                var result = await _automationWorkflowService.GetWorkflowNamesAsync(companyId);
                return Ok(CreateSuccessResponse<List<WorkflowNamesDto>>(result, "Workflow Names List."));
            }
            catch (Exception ex)
            {
                var error = CreateErrorResponse<string>(ex.Message, ex.StackTrace);
                _logger.LogError($"Error getting to fetched workflow names: {JsonConvert.SerializeObject(error)}");
                return StatusCode(500, error);
            }
        }

        [HttpPut]
        [Route("UpdateWorkflow")]
        [Authorize]
        public async Task<IActionResult> UpdateWorkflow(AutomationWorkflowDto automationWorkflow)
        {
            try
            {
                Dictionary<string, List<AutomationWorkflowResponseDto>> automationWorkflowResponses = new Dictionary<string, List<AutomationWorkflowResponseDto>>();
                var currentUserIdClaim = User.Claims.FirstOrDefault(c => c.Type == ClaimTypes.Name);
                if (currentUserIdClaim == null || !Guid.TryParse(currentUserIdClaim.Value, out Guid currentUserId))
                {
                    throw new Exception("Invalid current user.");
                }
                automationWorkflow.UserId = currentUserId;
                var response = await _automationWorkflowService.UpdateWorkflowAsync(automationWorkflow);

                if (response)
                {
                    automationWorkflowResponses = await _automationWorkflowService.GetWorkflowAsync(automationWorkflow.CompanyId, null, automationWorkflow.Id);
                }
                return Ok(CreateSuccessResponse<Dictionary<string, List<AutomationWorkflowResponseDto>>>(automationWorkflowResponses, "Workflow Updated successsfully."));
            }
            catch (Exception ex)
            {
                var error = CreateErrorResponse<string>(ex.Message, ex.StackTrace);
                _logger.LogError($"Error getting to UpdateWorkflow workflows: {JsonConvert.SerializeObject(error)}");
                return StatusCode(500, error);
            }
        }

        [HttpPatch("UpdateWorkflowName")]
        [Authorize]
        public async Task<IActionResult> UpdateWorkflowName(WorkflowUpdatePayloadDto workflowUpdatePayload)
        {
            try
            {
                var currentUserIdClaim = User.Claims.FirstOrDefault(c => c.Type == ClaimTypes.Name);

                if (currentUserIdClaim == null || !Guid.TryParse(currentUserIdClaim.Value, out Guid currentUserId))
                {
                    throw new Exception("Invalid current user.");
                }
                var result = await _automationWorkflowService.UpdateWorkflowNameAsync(workflowUpdatePayload, currentUserId);
                return Ok(CreateSuccessResponse<string>("The Auto-reply workflow name updated successfully."));
            }
            catch (Exception ex)
            {
                var error = CreateErrorResponse<string>(ex.Message, ex.StackTrace);
                _logger.LogError($"An error occurred while updating a workflow name: {JsonConvert.SerializeObject(error)}");
                return StatusCode(500, error);
            }
        }



        // GET: AutomationSelectResponse
        [HttpGet]
        [Authorize]
        [Route("AutomationSelectResponse/{CompanyId}")]
        public async Task<ActionResult<List<AutomationSelectResponseEntity>>> GetAllSelectResponses(Guid CompanyId)
        {
            try
            {
                var currentUserIdClaim = User.Claims.FirstOrDefault(c => c.Type == ClaimTypes.Name);

                if (currentUserIdClaim == null || !Guid.TryParse(currentUserIdClaim.Value, out Guid currentUserId))
                {
                    throw new Exception("Invalid current user.");
                }

                var responses = await _repositoryBase.GetSelectResponseEntity();
                responses = responses.Where(m => m.CompanyId == CompanyId).ToList();
                return Ok(responses);
            }
            catch (Exception ex)
            {
                return BadRequest(new { Message = ex.Message });
            }
        }


        // POST: AutomationSelectResponse
        [HttpPost]
        [Authorize]
        [Route("AutomationSelectResponse/{CompanyId}")]
        public async Task<ActionResult> AddSelectResponse([FromBody] string SelectResponse, Guid CompanyId)
        {
            try
            {
                var currentUserIdClaim = User.Claims.FirstOrDefault(c => c.Type == ClaimTypes.Name);

                if (currentUserIdClaim == null || !Guid.TryParse(currentUserIdClaim.Value, out Guid currentUserId))
                {
                    throw new Exception("Invalid current user.");
                }

                AutomationSelectResponseEntity selectResponseEntity = new AutomationSelectResponseEntity
                {
                    SelectResponse = SelectResponse,
                    CreatedBy = currentUserId,
                    CreatedAt = DateTime.UtcNow,
                    UpdatedBy = currentUserId,
                    CompanyId = CompanyId
                };

                if (selectResponseEntity == null)
                {
                    return BadRequest(new { Message = "Invalid data." });
                }

                await _repositoryBase.AddSelectResponse(selectResponseEntity);
                return Ok(new { Message = "Select Response added successfully." });
            }
            catch (Exception ex)
            {
                return BadRequest(new { Message = ex.Message });
            }
        }

        // PUT: AutomationSelectResponse/{id}
        [HttpPut]
        [Authorize]
        [Route("AutomationSelectResponse/{Id}")]
        public async Task<ActionResult> UpdateSelectResponse(Guid Id, [FromBody] string SelectResponse)
        {
            try
            {
                var currentUserIdClaim = User.Claims.FirstOrDefault(c => c.Type == ClaimTypes.Name);

                if (currentUserIdClaim == null || !Guid.TryParse(currentUserIdClaim.Value, out Guid currentUserId))
                {
                    throw new Exception("Invalid current user.");
                }
                var selectResponseEntities = await _repositoryBase.GetSelectResponseEntity();
                var updatedResponse = selectResponseEntities.FirstOrDefault(m => m.Id == Id);


                if (updatedResponse == null)
                {
                    throw new Exception("SelectResponse not found.");
                }
                updatedResponse.UpdatedBy = currentUserId;
                updatedResponse.SelectResponse = SelectResponse;
                await _repositoryBase.UpdateSelectResponseEntity(updatedResponse);

                return Ok(new { Message = "Select Response updated successfully." });
            }
            catch (Exception ex)
            {
                return BadRequest(new { Message = ex.Message });
            }
        }

        // DELETE: AutomationSelectResponse/{id}
        [HttpDelete]
        [Authorize]
        [Route("AutomationSelectResponse/{Id}")]
        public async Task<IActionResult> DeleteSelectResponse(Guid Id)
        {
            try
            {
                var currentUserIdClaim = User.Claims.FirstOrDefault(c => c.Type == ClaimTypes.Name);

                if (currentUserIdClaim == null || !Guid.TryParse(currentUserIdClaim.Value, out Guid currentUserId))
                {
                    throw new Exception("Invalid current user.");
                }

                await _repositoryBase.DeleteSelectResponseAsync(Id, currentUserId);
                return Ok(CreateSuccessResponse<string>("Select Response deleted successfully."));
            }
            catch (Exception ex)
            {
                return BadRequest(new { Message = ex.Message });
            }
        }





        // GET: AutomationSelectResponse
        [HttpGet]
        [Authorize]
        [Route("AutomationWorkflowResponse")]
        public async Task<ActionResult<List<WorkflowResponseHistoryEntity>>> GetAllWorkflowResponses()
        {
            try
            {
                var currentUserIdClaim = User.Claims.FirstOrDefault(c => c.Type == ClaimTypes.Name);

                if (currentUserIdClaim == null || !Guid.TryParse(currentUserIdClaim.Value, out Guid currentUserId))
                {
                    throw new Exception("Invalid current user.");
                }

                var responses = await _repositoryBase.GetWorkflowResponseEntity();
                return Ok(responses);
            }
            catch (Exception ex)
            {
                return BadRequest(new { Message = ex.Message });
            }
        }

        // GET: AutomationSelectResponse/{id}
        [HttpGet]
        [Authorize]
        [Route("AutomationWorkflowResponse/{Id}")]
        public async Task<ActionResult<WorkflowResponseHistoryEntity>> GetWorkflowResponseById(Guid Id)
        {
            try
            {
                var currentUserIdClaim = User.Claims.FirstOrDefault(c => c.Type == ClaimTypes.Name);

                if (currentUserIdClaim == null || !Guid.TryParse(currentUserIdClaim.Value, out Guid currentUserId))
                {
                    throw new Exception("Invalid current user.");
                }

                var responseList = await _repositoryBase.GetSelectResponseEntity();
                var response = responseList.Find(r => r.Id == Id);

                if (response == null)
                {
                    return BadRequest(new { Message = "SelectResponse not found." });
                }

                return Ok(response);
            }
            catch (Exception ex)
            {
                return BadRequest(new { Message = ex.Message });
            }
        }

        // POST: AutomationSelectResponse
        [HttpPost]
        [Authorize]
        [Route("AutomationWorkflowResponse")]
        public async Task<ActionResult> AddWorkflowResponse([FromBody] string SelectResponse)
        {
            try
            {
                var currentUserIdClaim = User.Claims.FirstOrDefault(c => c.Type == ClaimTypes.Name);

                if (currentUserIdClaim == null || !Guid.TryParse(currentUserIdClaim.Value, out Guid currentUserId))
                {
                    throw new Exception("Invalid current user.");
                }

                WorkflowResponseHistoryEntity selectResponseEntity = new WorkflowResponseHistoryEntity
                {
                    WorkflowName = "",
                    CreatedBy = currentUserId,
                    CreatedAt = DateTime.UtcNow,
                    WorkflowId = Guid.NewGuid(),
                    ContactId = Guid.NewGuid(),
                    UpdatedBy = currentUserId
                };

                if (selectResponseEntity == null)
                {
                    return BadRequest(new { Message = "Invalid data." });
                }

                await _repositoryBase.AddWorkflowResponse(selectResponseEntity);
                return Ok(new { Message = "Workflow Response added successfully." });
            }
            catch (Exception ex)
            {
                return BadRequest(new { Message = ex.Message });
            }
        }

        // PUT: AutomationSelectResponse/{id}
        [HttpPut]
        [Authorize]
        [Route("AutomationWorkflowResponse/{Id}")]
        public async Task<ActionResult> UpdateWorkflowResponse(Guid Id, [FromBody] string Response)
        {
            try
            {
                var currentUserIdClaim = User.Claims.FirstOrDefault(c => c.Type == ClaimTypes.Name);

                if (currentUserIdClaim == null || !Guid.TryParse(currentUserIdClaim.Value, out Guid currentUserId))
                {
                    throw new Exception("Invalid current user.");
                }
                var selectResponseEntities = await _repositoryBase.GetWorkflowResponseEntity();
                var updatedResponse = selectResponseEntities.Find(m => m.Id == Id);


                if (updatedResponse == null)
                {
                    throw new Exception("SelectResponse not found.");
                }
                updatedResponse.UpdatedBy = currentUserId;
                updatedResponse.Response = Response;
                await _repositoryBase.UpdateWorkflowResponseEntity(updatedResponse);

                return Ok(new { Message = "Select Response updated successfully." });
            }
            catch (Exception ex)
            {
                return BadRequest(new { Message = ex.Message });
            }
        }

        // DELETE: AutomationSelectResponse/{id}
        [HttpDelete]
        [Authorize]
        [Route("AutomationWorkflowResponse/{Id}")]
        public async Task<IActionResult> DeleteWorkflowResponse(Guid Id)
        {
            try
            {
                var currentUserIdClaim = User.Claims.FirstOrDefault(c => c.Type == ClaimTypes.Name);

                if (currentUserIdClaim == null || !Guid.TryParse(currentUserIdClaim.Value, out Guid currentUserId))
                {
                    throw new Exception("Invalid current user.");
                }

                await _repositoryBase.DeleteWorkflowResponseAsync(Id, currentUserId);
                return Ok(CreateSuccessResponse<string>("Select Response deleted successfully."));
            }
            catch (Exception ex)
            {
                return BadRequest(new { Message = ex.Message });
            }
        }

        [HttpDelete("DeleteWorkflow")]
        [Authorize]
        public async Task<IActionResult> DeleteWorkflow([Required] Guid companyId, string? workflowName, Guid? workflowId)
        {
            try
            {
                if (string.IsNullOrEmpty(workflowName) && workflowId == null)
                    throw new Exception($"Workflow name and workflowid can not be null.");

                var currentUserIdClaim = User.Claims.FirstOrDefault(c => c.Type == ClaimTypes.Name);

                if (currentUserIdClaim == null || !Guid.TryParse(currentUserIdClaim.Value, out Guid currentUserId))
                {
                    throw new Exception("Invalid current user.");
                }
                var result = await _automationWorkflowService.DeleteWorkflowAsync(companyId, currentUserId, workflowId, workflowName);
                return Ok(CreateSuccessResponse<string>("The Auto-reply workflow deleted successfully."));
            }
            catch (Exception ex)
            {
                var error = CreateErrorResponse<string>(ex.Message, ex.StackTrace);
                _logger.LogError($"An error occurred while deleting a workflow: {JsonConvert.SerializeObject(error)}");
                return StatusCode(500, error);
            }
        }
        [HttpPost("set-automation-campaign")]
        [Authorize]
        public async Task<IActionResult> SetAutomationForCampaign(CampaignAutomationDto campaignAutomationDto)
        {
            try
            {
                var result = await _autoReplyMessageService.SetAutoReplyAutomationCampaignAsync(_userIdentityService.BusinessId, _userIdentityService.UserId, campaignAutomationDto);
                return Ok(CreateSuccessResponse<string>("The Auto-reply campaign set successfully."));
            }
            catch (Exception ex) 
            {
                var error = CreateErrorResponse<string>(ex.Message, ex.StackTrace);
                _logger.LogError($"An error occurred while setup a campaign: {JsonConvert.SerializeObject(error)}");
                return StatusCode(500, error);
            }
        }
    }
}

