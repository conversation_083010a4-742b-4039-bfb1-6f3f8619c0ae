﻿using System.ComponentModel.DataAnnotations;

namespace EngagetoEntities.Dtos.WebhookDtos
{
    public class SendTemplate
    {
        [Required(ErrorMessage = "BusinessId is required")]
        public string BusinessId { get; set; } = default!;

        [Required(ErrorMessage = "UserId is required")]
        public Guid UserId { get; set; }

        [Required(ErrorMessage = "TemplateId is required")]
        public Guid TemplateId { get; set; }
        public List<string> Contact { get; set; } = new();
        public string? HeaderVariableValue { get; set; }
        public string[]? BodyVariableValues { get; set; }
        public string[]? RedirectUrlVariableValues { get; set; }
        public string? MadiaUrl { get; set; }
        public string? MediaFile { get; set; }
        public List<CarouselCardVariableDto>? CarouselVariables { get; set; }

    }
}
