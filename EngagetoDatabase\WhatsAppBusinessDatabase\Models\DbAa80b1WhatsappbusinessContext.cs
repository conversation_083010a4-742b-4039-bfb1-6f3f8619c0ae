﻿using System;
using System.Collections.Generic;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Configuration;

namespace EngagetoDatabase.WhatsAppBusinessDatabase.Models;
public partial class DbAa80b1WhatsappbusinessContext : DbContext
{
    private readonly IConfiguration _configuration;

    public DbAa80b1WhatsappbusinessContext(IConfiguration configuration)
    {
        _configuration = configuration;
    }
    public DbAa80b1WhatsappbusinessContext(DbContextOptions<DbAa80b1WhatsappbusinessContext> options)
        : base(options)
    {
    }

    public virtual DbSet<BusinessDetailsMeta> BusinessDetailsMetas { get; set; }

    //public virtual DbSet<Button> ButtonDetails { get; set; }

    public virtual DbSet<Contact> Contacts { get; set; }

    public virtual DbSet<Conversation> Conversations { get; set; }

    public virtual DbSet<CountryDetail> CountryDetails { get; set; }

    public virtual DbSet<InboxSettingsVariable> InboxSettingsVariables { get; set; }

    public virtual DbSet<Note> Notes { get; set; }

    public virtual DbSet<Role> Roles { get; set; }

    public virtual DbSet<Tag> Tags { get; set; }

    //public virtual DbSet<Template> Templates { get; set; }

    public virtual DbSet<User> Users { get; set; }

    public virtual DbSet<UserRole> UserRoles { get; set; }

    public virtual DbSet<WorkingHour> WorkingHours { get; set; }


    protected override void OnConfiguring(DbContextOptionsBuilder optionsBuilder)
    {
        if (!optionsBuilder.IsConfigured)
        {
            var connectionString = _configuration.GetConnectionString("ConnStr");
            optionsBuilder.UseSqlServer(connectionString);
        }
    }
    protected override void OnModelCreating(ModelBuilder modelBuilder)
    {
        modelBuilder.Entity<BusinessDetailsMeta>(entity =>
        {
            entity.ToTable("BusinessDetails_Metas");

            entity.Property(e => e.Id).ValueGeneratedNever();
            entity.Property(e => e.PhoneNumberId).HasColumnName("PhoneNumberID");
            entity.Property(e => e.WhatsAppBusinessAccountId).HasColumnName("WhatsAppBusinessAccountID");
        });

        //modelBuilder.Entity<ButtonDetail>(entity =>
        //{
        //    entity.HasIndex(e => e.TemplateId, "IX_ButtonDetails_TemplateId");

        //    entity.HasOne(d => d.Template).WithMany(p => p.ButtonDetails).HasForeignKey(d => d.TemplateId);
        //});

        modelBuilder.Entity<Contact>(entity =>
        {
            entity.Property(e => e.ContactId).ValueGeneratedNever();
            entity.Property(e => e.Contact1).HasColumnName("Contact");
        });

        modelBuilder.Entity<Conversation>(entity =>
        {
            entity.Property(e => e.Id).ValueGeneratedNever();
        });

        modelBuilder.Entity<CountryDetail>(entity =>
        {
            entity.Property(e => e.Id).ValueGeneratedNever();
        });

        modelBuilder.Entity<InboxSettingsVariable>(entity =>
        {
            entity.Property(e => e.Id).ValueGeneratedNever();
        });

        modelBuilder.Entity<Note>(entity =>
        {
            entity.Property(e => e.Id).ValueGeneratedNever();
            entity.Property(e => e.Note1).HasColumnName("Note");
        });

        modelBuilder.Entity<Role>(entity =>
        {
            entity.HasKey(e => e.Sno);

            entity.ToTable("Role");

            entity.Property(e => e.Name).HasMaxLength(100);
        });

        modelBuilder.Entity<Tag>(entity =>
        {
            entity.Property(e => e.Id).ValueGeneratedNever();
            entity.Property(e => e.Tag1).HasColumnName("Tag");
        });

        //modelBuilder.Entity<Template>(entity =>
        //{
        //    entity.Property(e => e.TemplateId).ValueGeneratedNever();
        //});

        modelBuilder.Entity<User>(entity =>
        {
            entity.Property(e => e.Id).ValueGeneratedNever();
        });

        modelBuilder.Entity<UserRole>(entity =>
        {
            entity.HasKey(e => new { e.Id, e.RoleId });

            entity.ToTable("UserRole");

            entity.HasIndex(e => e.AhexCrmUsersId, "IX_UserRole_Ahex_CRM_UsersId");

            entity.HasIndex(e => e.RoleSno, "IX_UserRole_RoleSno");

            entity.Property(e => e.AhexCrmUsersId).HasColumnName("Ahex_CRM_UsersId");

            entity.HasOne(d => d.AhexCrmUsers).WithMany(p => p.UserRoles).HasForeignKey(d => d.AhexCrmUsersId);

            entity.HasOne(d => d.RoleSnoNavigation).WithMany(p => p.UserRoles).HasForeignKey(d => d.RoleSno);
        });

        modelBuilder.Entity<WorkingHour>(entity =>
        {
            entity.Property(e => e.Id).ValueGeneratedNever();
        });

        OnModelCreatingPartial(modelBuilder);
    }

    partial void OnModelCreatingPartial(ModelBuilder modelBuilder);
}
