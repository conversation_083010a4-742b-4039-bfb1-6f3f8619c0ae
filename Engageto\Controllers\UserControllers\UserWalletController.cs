﻿using AutoMapper;
using EngagetoContracts.UserContracts;
using EngagetoDapper.Data.Interfaces.CampaignInterfaces.ICampaignServices;
using EngagetoEntities.Dtos.WalletDtos;
using EngagetoEntities.Entities;
using EngagetoEntities.Enums;
using EngagetoEntities.Response;
using EngagetoEntities.UserEntities.Dtos;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using System.ComponentModel.DataAnnotations;
using System.Security.Claims;

namespace Engageto.Controllers.UserControllers
{
    [Route("api/[controller]")]
    [ApiController]
    public class UserWalletController : ControllerBase
    {
        private readonly ILogger<PaymentGatewayController> _logger;

        private readonly IWalletService _walletService;
        private readonly IWalletNotificationService _walletNotificationService;
        private readonly IMapper _mapper;
        private readonly IAccountDetailsService _accountDetailsService;
        private readonly IConversationsService _conversationsService;
        private readonly EngagetoDapper.Data.Interfaces.IUserInterfaces.IUserServices.IUserService _userService;
        private readonly EngagetoDapper.Data.Interfaces.IEmailInterfaces.IEmailService _emailService;

        public UserWalletController(IWalletService walletService,
            IWalletNotificationService walletNotificationService,
            IAccountDetailsService accountDetailsService,
            ILogger<PaymentGatewayController> logger,
            IMapper mapper,
            IConversationsService conversationsService,
            EngagetoDapper.Data.Interfaces.IUserInterfaces.IUserServices.IUserService userService,
            EngagetoDapper.Data.Interfaces.IEmailInterfaces.IEmailService emailService)
        {
            _walletService = walletService;
            _walletNotificationService = walletNotificationService;
            _accountDetailsService = accountDetailsService;
            _logger = logger;
            _mapper = mapper;
            _conversationsService = conversationsService;
            _userService = userService;
            _emailService = emailService;
        }

        [HttpGet]
        [Route("GetUserWalletDetails")]
        [Authorize]
        public async Task<IActionResult> GetUserWalletDetails()
        {
            try
            {
                var currentUserIdClaim = User.Claims.FirstOrDefault(c => c.Type == ClaimTypes.Name);

                if (currentUserIdClaim == null || !Guid.TryParse(currentUserIdClaim.Value, out Guid currentUserId))
                {
                    return BadRequest(new { Message = "Invalid current user." });
                }
                var userDetails = await _accountDetailsService.GetAccountDetails(currentUserId);
                var walletEntity = await _walletService.GetWalletAsync(userDetails.CompanyId);
                if (walletEntity?.WalletId == null)
                {
                    walletEntity = await _walletService.CreateWalletAsync(new UserWalletEntity()
                    {
                        UserId = currentUserId,
                        Balance = 0,
                        Currency = Currency.INR.ToString(),
                        CreatedAt = DateTime.UtcNow,
                        UpdatedAt = DateTime.UtcNow,
                        CreatedBy = currentUserId,
                        UpdatedBy = currentUserId,
                    });
                }
                var Notificatons = await _walletNotificationService.GetWalletNotificationAsync(walletEntity.WalletId);
                var NotificationDto = _mapper.Map<List<WalletNotificationDto>>(Notificatons);
                return Ok(new ApiResponse<UserWalletResponseDto>()
                {
                    Success = true,
                    Message = "Get Wallet Details successfully.",
                    Data = new UserWalletResponseDto()
                    {
                        Wallet = walletEntity,
                        Notification = NotificationDto,

                    }
                });
            }
            catch (Exception ex)
            {
                _logger.LogError($"Exception occurs  in UserWalletController/GetUserWalletDetails: {ex}");
                return StatusCode(StatusCodes.Status500InternalServerError, new ApiResponse<string>()
                {
                    Success = false,
                    Message = ex.Message,
                    Errors = ex.StackTrace
                });
            }
        }
        [HttpPost]
        [AllowAnonymous]
        public async Task<ActionResult> AddWalletAmout(string companyId, decimal amount)
        {
            try
            {
                var wallet = await _walletService.GetWalletAsync(companyId);
                if (wallet == null)
                {
                    throw new InvalidOperationException("Not Found wallet for this companyId");
                }
                wallet.Balance += amount;
                wallet.ExpectedWalletBallance += amount;
                var result = await _walletService.UpdateWalletAsync(wallet);
                return Ok(new ApiResponse<UserWalletEntity>()
                {
                    Success = true,
                    Data = result,
                    Message = "Wallet amount updated sucessfully."
                });
            }
            catch (InvalidDataException e)
            {
                return BadRequest(new ApiResponse<string>()
                {
                    Success = false,
                    Data = null,
                    Message = e.Message
                });
            }
            catch (Exception ex)
            {
                return StatusCode(StatusCodes.Status500InternalServerError, new ApiResponse<string>()
                {
                    Success = false,
                    Message = ex.Message,
                    Errors = ex.StackTrace
                });
            }
        }

        [HttpGet]
        [Route("GetWalletAndSubscription/{companyId}")]
        [Authorize]
        public async Task<IActionResult> GetWalletAndSubscription(string companyId)
        {
            try
            {
                var currentUserIdClaim = User.Claims.FirstOrDefault(c => c.Type == ClaimTypes.Name);

                if (currentUserIdClaim == null || !Guid.TryParse(currentUserIdClaim.Value, out Guid currentUserId))
                {
                    throw new Exception("Invalid current user.");
                }
                return Ok(new ApiResponse<WalletAndSubscription>()
                {
                    Success = true,
                    Message = "Get Wallet and subscription Data fetched Successfully.",
                    Data = await _walletService.GetWalletAndSubscriptionPlanAsync(companyId, currentUserId)
                });
            }
            catch (Exception ex)
            {
                _logger.LogError($"Exception occurs  in UserWalletController/GetWalletAndSubscription: {ex}");
                return StatusCode(StatusCodes.Status500InternalServerError, new ApiResponse<string>()
                {
                    Success = false,
                    Message = ex.Message,
                    Errors = ex.StackTrace
                });
            }
        }

        [HttpPost("SaveDiscount")]
        [Authorize]
        public async Task<IActionResult> SaveDiscountAsync(DiscountDto discount)
        {
            try
            {
                var currentUserIdClaim = User.Claims.FirstOrDefault(c => c.Type == ClaimTypes.Name);

                if (currentUserIdClaim == null || !Guid.TryParse(currentUserIdClaim.Value, out Guid currentUserId))
                {
                    return BadRequest(new { Message = "Invalid current user." });
                }
                return Ok(new ApiResponse<DiscountEntity>()
                {
                    Success = true,
                    Message = "Save Discount Successfully",
                    Data = await _userService.SaveDiscountAsync(discount, currentUserId)
                });
            }
            catch (Exception ex)
            {
                _logger.LogError($"Exception occurs  in UserWalletController/SaveDiscountAsync: {ex}");
                return StatusCode(StatusCodes.Status500InternalServerError, new ApiResponse<string>()
                {
                    Success = false,
                    Message = ex.Message,
                    Errors = ex.StackTrace
                });
            }
        }

        [HttpPut("UpdateDiscount")]
        [Authorize]
        public async Task<IActionResult> UpdateDiscountAsync(DiscountEntity discount)
        {
            try
            {
                var currentUserIdClaim = User.Claims.FirstOrDefault(c => c.Type == ClaimTypes.Name);

                if (currentUserIdClaim == null || !Guid.TryParse(currentUserIdClaim.Value, out Guid currentUserId))
                {
                    return BadRequest(new { Message = "Invalid current user." });
                }
                discount.UpdatedBy = currentUserId;
                return Ok(new ApiResponse<DiscountEntity>()
                {
                    Success = true,
                    Message = "Update Discount Successfully",
                    Data = await _userService.UpdateDiscount(discount)
                });
            }
            catch (Exception ex)
            {
                _logger.LogError($"Exception occurs  in UserWalletController/UpdateDiscount: {ex}");
                return StatusCode(StatusCodes.Status500InternalServerError, new ApiResponse<string>()
                {
                    Success = false,
                    Message = ex.Message,
                    Errors = ex.StackTrace
                });
            }
        }

        [HttpGet("GetDiscountByCode")]
        [AllowAnonymous]
        public async Task<IActionResult> GetDiscountAsync([Required] string code, [Required] string companyId)
        {
            try
            {
                var discount = await _userService.GetDiscountByDicountCode(code);
                if (discount == null)
                    throw new Exception("Disount Coupon is not found.");
                if (discount.ValidFrom >= DateTime.Now && discount.ValidTo <= DateTime.Now)
                    throw new Exception("Disount Coupon is expired.");

                return Ok(new ApiResponse<DiscountEntity>()
                {
                    Success = true,
                    Message = "Get Discount Coupon Successfully",
                    Data = await _userService.GetDiscountByDicountCode(code)
                });
            }
            catch (Exception ex)
            {
                _logger.LogError($"Exception occurs in UserWalletController/GetDiscountByCode: {ex}");
                return StatusCode(StatusCodes.Status500InternalServerError, new ApiResponse<string>()
                {
                    Success = false,
                    Message = ex.Message,
                    Errors = ex.StackTrace
                });
            }
        }

        [HttpGet("GetAllDiscountCoupons")]
        [AllowAnonymous]
        public async Task<IActionResult> GetAllDiscountCoupons()
        {
            try
            {
                return Ok(new ApiResponse<List<DiscountDto>>()
                {
                    Success = true,
                    Message = "Get Discount Coupon Successfully",
                    Data = await _userService.GetAllDiscountAsync()
                });
            }
            catch (Exception ex)
            {
                _logger.LogError($"Exception occurs in UserWalletController/GetDiscountByCode: {ex}");
                return StatusCode(StatusCodes.Status500InternalServerError, new ApiResponse<string>()
                {
                    Success = false,
                    Message = ex.Message,
                    Errors = ex.StackTrace
                });
            }
        }

        [HttpGet("GetExpectedWalletBalance/{companyId}")]
        [Authorize]
        public async Task<IActionResult> GetExpectedWalletBalance(string companyId, CancellationToken cancellationToken)
        {
            try
            {
                if (cancellationToken.IsCancellationRequested)
                {
                    return BadRequest(new ApiResponse<string>
                    {
                        Success = false,
                        Message = "Request was cancelled."
                    });
                }

                var balance = await _userService.GetExpectedWalletBalanceAsync(companyId);
                return Ok(new ApiResponse<ExpectedWalletBalanceDto>
                {
                    Success = true,
                    Data = balance ?? new ExpectedWalletBalanceDto() { CompanyId = companyId },
                    Message = "Get expected wallet balance successfully."
                });
            }
            catch (Exception ex)
            {
                // Log the exception (this assumes you have some logging mechanism in place)
                _logger.LogError(ex, "An error occurred while getting the expected wallet balance.");

                return BadRequest(new ApiResponse<string>
                {
                    Success = false,
                    Message = "An error occurred while processing your request.",
                    Errors = ex.StackTrace // Optionally, you might not want to expose stack trace in production.
                });
            }
        }
        [HttpGet("tenat-wallet/{tenantId}")]
        [AllowAnonymous]
        public async Task<IActionResult> GetWalletDetailsByTenant(string tenantId, DateTime fromDate, DateTime toDate)
        {
            try
            {
                var wallet = await _walletService.GetTenantWalletAsync(tenantId, fromDate, toDate);
                return Ok(new ApiResponse<TenantWalletDto>
                {
                    Success = true,
                    Data = wallet,
                    Message = "wallet balance details."
                });
            }
            catch (Exception ex)
            {
                // Log the exception (this assumes you have some logging mechanism in place)
                _logger.LogError(ex, "An error occurred while getting the expected wallet balance.");

                return BadRequest(new ApiResponse<string>
                {
                    Success = false,
                    Message = "An error occurred while processing your request.",
                    Errors = ex.StackTrace // Optionally, you might not want to expose stack trace in production.
                });
            }
        }

        //[HttpGet]
        //[Route("GetWalletAndSubscription1/{companyId}")]
        //[AllowAnonymous]
        //public async Task<IActionResult> GetWalletAndSubscription1(Guid companyId)
        //{
        //    try
        //    {
        //        return Ok(new ApiResponse<ConverstationDetailsDto>()
        //        {
        //            Success = true,
        //            Message = "Get Wallet and subscription Data fetched Successfully.",
        //            Data = await _conversationsService.GetConversationDetailsAsync(companyId)
        //        });
        //    }
        //    catch (Exception ex)
        //    {
        //        _logger.LogError($"Exception occurs  in UserWalletController/GetWalletAndSubscription: {ex}");
        //        return StatusCode(StatusCodes.Status500InternalServerError, new ApiResponse<string>()
        //        {
        //            Success = false,
        //            Message = ex.Message,
        //            Errors = ex.StackTrace
        //        });
        //    }
        //}

    }
}
