﻿using EngagetoContracts.UserContracts;
using EngagetoEntities.Entities;
using EngagetoEntities.UserEntities.Models;
using Microsoft.EntityFrameworkCore;


namespace EngagetoRepository.UserRepository
{
    public class PlanEntitiesService : IPlanEntitiesService
    {
        private readonly ApplicationDBContext _context;
        public PlanEntitiesService(ApplicationDBContext context)
        {
            _context = context;
        }

        public async Task<IEnumerable<PlanEntities>> GetAllPlansAsync(Guid currentUserId, string searchQuery, string sortBy, bool isSortAscending)
        {
            try
            {

                var query = _context.PlanEntities.AsQueryable();

                if (!string.IsNullOrEmpty(searchQuery))
                {
                    query = query.Where(plan =>
                                plan.Status == true &&
                                (plan.Price.ToString().Contains(searchQuery) ||
                                plan.PlanName.Contains(searchQuery))
                            );
                }
                switch (sortBy?.ToLower())
                {
                    case "planName":
                        query = isSortAscending ? query.OrderBy(plan => plan.PlanName) : query.OrderByDescending(plan => plan.PlanName);
                        break;
                    case "price":
                        query = isSortAscending ? query.OrderBy(plan => plan.Price) : query.OrderByDescending(plan => plan.Price);
                        break;

                    default:
                        query = query.OrderBy(plan => plan.PlanName);
                        break;
                }


                var subscriptionPlans = await query.Where(x => x.Status).ToListAsync();
                return subscriptionPlans;
            }
            catch (Exception)
            {
                return null;
            }
        }

        public async Task<List<PlanEntities>> GetAllPlansAsync()
        {
            try
            {
                return await _context.PlanEntities.Where(x => x.Status).ToListAsync();
            }
            catch (Exception)
            {
                throw;
            }
        }
        public async Task<PlanEntities> GetPlanById(int planId)
        {
            return await _context.PlanEntities.FindAsync(planId);
        }

        public async Task<bool> CreatePlan(Guid currentUserId, PlanEntitiesDto planEntitiesDto)
        {
            try
            {
                var currentuserId = await _context.Ahex_CRM_Users
                                                   .Where(user => user.Id == currentUserId)
                                                   .Select(user => user.Id)
                                                   .FirstOrDefaultAsync();
                var newPlan = new PlanEntities
                {
                    PlanName = planEntitiesDto.PlanName,
                    Price = planEntitiesDto.Price,
                    Status = true,
                    CreatedBy = currentuserId.ToString(),
                    CreatedAt = DateTime.UtcNow,
                };

                _context.PlanEntities.Add(newPlan);
                await _context.SaveChangesAsync();

                return true;
            }
            catch (Exception)
            {
                return false;
            }
        }

        public async Task<bool> UpdatePlan(Guid currentUserId, int planId, UpdatePlanEntitiesDto updatePlanEntitiesDto)
        {
            try
            {
                var currentUserName = await _context.Ahex_CRM_Users
                                                     .Where(user => user.Id == currentUserId)
                                                     .Select(user => user.Name)
                                                     .FirstOrDefaultAsync();

                var existingPlan = await _context.PlanEntities
                    .FirstOrDefaultAsync(sp => sp.Id == planId);

                if (existingPlan == null)
                {
                    return false;
                }
                existingPlan.Price = updatePlanEntitiesDto.Price > 0 ? updatePlanEntitiesDto.Price : existingPlan.Price;
                existingPlan.PlanName = string.IsNullOrEmpty(updatePlanEntitiesDto.PlanName) ? string.Empty : updatePlanEntitiesDto.PlanName;
                existingPlan.UpdatedBy = currentUserId.ToString();
                existingPlan.UpdatedAt = DateTime.UtcNow;

                await _context.SaveChangesAsync();

                return true;
            }
            catch (Exception)
            {
                return false;
            }
        }

        public async Task<bool> DeletePlan(Guid currentUserId, int planId)
        {
            try
            {


                var PlanDelete = await _context.PlanEntities.FindAsync(planId);

                if (PlanDelete == null)
                {
                    return false;
                }

                PlanDelete.Status = false;
                _context.PlanEntities.Update(PlanDelete);
                await _context.SaveChangesAsync();

                return true;
            }
            catch (Exception)
            {
                return false;
            }
        }
        public async Task<IEnumerable<MenuWithPlanDto>> GetActiveMenusByPlanAsync(int? planId, string? planName)
        {
            var query = from sp in _context.SubscriptionPermissions
                        join menu in _context.MenuDetails on sp.MenuId equals menu.MenuId
                        join plan in _context.PlanEntities on sp.PlanId equals plan.Id
                        where sp.isActive && plan.Status
                        select new MenuWithPlanDto
                        {
                            PlanId = plan.Id,
                            PlanName = plan.PlanName,
                            MenuName = menu.MenuName
                        };

            if (planId.HasValue)
            {
                query = query.Where(q => q.PlanId == planId.Value);
            }
            else if (!string.IsNullOrEmpty(planName))
            {
                query = query.Where(q => q.PlanName == planName);
            }

            return await query.ToListAsync();
        }
        public async Task<Subscriptions?> GetSubscriptionsAsync(string companyid)
        {
            return await _context.Subscriptions.FirstOrDefaultAsync(x => x.CompanyId == companyid);
        }
    }
}
