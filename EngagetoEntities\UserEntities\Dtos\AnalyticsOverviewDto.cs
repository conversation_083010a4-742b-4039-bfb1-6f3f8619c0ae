﻿namespace EngagetoEntities.UserEntities.Dtos
{
    public class AnalyticsOverviewDto
    {
        public long TotalConversation { get; set; }
        public long ConversationDuration { get; set; }
        public long AverageResponseTime { get; set;}
        public List<WaitTimeDataDto> ResolutionTimes { get; set; } = new();
        public List<WaitTimeDataDto> AgentResponseTimes { get; set; } = new();
        public List<WaitTimeDataDto> FirstAgentResponseTimes { get; set; } = new();
    }
    public class WaitTimeDataDto
    {
        public string Range { get; set; }
        public int Count { get; set; }
    }
}
