﻿using EngagetoContracts.WebhookContracts.DownloadMedia;
using EngagetoDatabase.WhatsAppBusinessDatabase.Models;
using EngagetoEntities.Dtos.MetaDto;
using EngagetoEntities.Extensions;
using EngagetoRepository.WebhookRepository.WhatsAppBusinessEndpointUrl;
using Microsoft.Extensions.Configuration;
using Newtonsoft.Json;
using System.Net.Http.Headers;

namespace EngagetoRepository.WebhookRepository.DownloadMediaRepo
{
    public class DownloadMeidaService : IDownloadMedia
    {
        private HttpClient _httpClient = new HttpClient();

        private readonly JsonSerializer _serializer = new JsonSerializer();
        private readonly DbAa80b1WhatsappbusinessContext apiContext;
        private readonly IConfiguration _configuration;

        public DownloadMeidaService(DbAa80b1WhatsappbusinessContext context, IConfiguration configuration)
        {
            apiContext = context;
            _httpClient.BaseAddress = new Uri(WhatsAppBusinessRequestEndpoint.BaseAddress.ToString());
            _configuration = configuration;
        }



        /// <summary>
        /// To retrieve your media’s URL, make a GET call to /{{Media-ID}}. Later, you can use this URL to download the media file.
        /// </summary>
        /// <param name="mediaId">ID for the media to send a media message or media template message to your customers.</param>
        /// <returns>MediaUrlResponse</returns>
        public async Task<MediaUrlResponse> GetMediaUrlAsync(string mediaId, string PhoneNumberId)
        {
            string formattedWhatsAppEndpoint;

            formattedWhatsAppEndpoint = WhatsAppBusinessRequestEndpoint.GetMediaUrl.Replace("{{Media-ID}}", mediaId);

            return await WhatsAppBusinessGetAsync<MediaUrlResponse>(formattedWhatsAppEndpoint, PhoneNumberId);
        }



        /// <summary>
        /// To perform WhatsAppBusiness Cloud API endpoint GET request 
        /// </summary>
        /// <typeparam name="T">Response Class</typeparam>
        /// <param name="whatsAppBusinessEndpoint">WhatsApp Business Cloud API endpoint</param>
        /// <returns>Response object</returns>
        /// <exception cref="WhatsappBusinessCloudAPIException"></exception>
        private async Task<T> WhatsAppBusinessGetAsync<T>(string whatsAppBusinessEndpoint, string PhoneNumberId) where T : new()
        {
            DbAa80b1WhatsappbusinessContext _apiContext = new DbAa80b1WhatsappbusinessContext(_configuration);
            var MetaDetails = _apiContext.BusinessDetailsMetas.FirstOrDefault(m => m.PhoneNumberId == PhoneNumberId.ToString())?.Token;
            string AccessToken = string.Empty;
            if (MetaDetails == null)
            {
                AccessToken = _configuration["Facebook:AccessToken"];
            }
            else
            {
                AccessToken = MetaDetails;
            }
            _httpClient.DefaultRequestHeaders.Authorization = new AuthenticationHeaderValue("Bearer", AccessToken);

            T result = new();

            HttpResponseMessage? response;


            HttpRequestMessage requestMessage = new HttpRequestMessage();
            requestMessage.Method = HttpMethod.Get;
            requestMessage.RequestUri = new Uri($"{_httpClient.BaseAddress}{whatsAppBusinessEndpoint}");

            response = await _httpClient.SendAsync(requestMessage).ConfigureAwait(false);


            if (response.IsSuccessStatusCode)
            {
#if NET5_0_OR_GREATER
                await response.Content.ReadAsStreamAsync().ContinueWith((Task<Stream> stream) =>
                {
                    using var reader = new StreamReader(stream.Result);
                    using var json = new JsonTextReader(reader);
                    result = _serializer.Deserialize<T>(json);
                });
#endif
#if NETSTANDARD2_0_OR_GREATER
                await response.Content.ReadAsStreamAsync().ContinueWith((Task<Stream> stream) =>
                {
                    using var reader = new StreamReader(stream.Result);
                    using var json = new JsonTextReader(reader);
                    result = _serializer.Deserialize<T>(json);
                }, cancellationToken);
#endif
            }
            else
            {
                WhatsAppErrorResponse whatsAppErrorResponse = new WhatsAppErrorResponse();
#if NET5_0_OR_GREATER
                await response.Content.ReadAsStreamAsync().ContinueWith((Task<Stream> stream) =>
                {
                    using var reader = new StreamReader(stream.Result);
                    using var json = new JsonTextReader(reader);
                    whatsAppErrorResponse = _serializer.Deserialize<WhatsAppErrorResponse>(json);
                });
                throw new WhatsappBusinessCloudAPIException(new HttpRequestException(whatsAppErrorResponse.Error.Message), response.StatusCode, whatsAppErrorResponse);
#endif
#if NETSTANDARD2_0_OR_GREATER
                await response.Content.ReadAsStreamAsync().ContinueWith((Task<Stream> stream) =>
                {
                    using var reader = new StreamReader(stream.Result);
                    using var json = new JsonTextReader(reader);
                    whatsAppErrorResponse = _serializer.Deserialize<WhatsAppErrorResponse>(json);
                }, cancellationToken);
                throw new WhatsappBusinessCloudAPIException(new HttpRequestException(whatsAppErrorResponse.Error.Message), response.StatusCode, whatsAppErrorResponse);
#endif
            }
            return result;
        }


        /// <summary>
        /// To download media uploaded from whatsapp
        /// </summary>
        /// <returns>byte[]</returns>
        public async Task<byte[]> DownloadMediaAsync(string mediaUrl, string PhoneNumberId)
        {
            string formattedWhatsAppEndpoint;
            formattedWhatsAppEndpoint = WhatsAppBusinessRequestEndpoint.DownloadMedia.Replace("{{Media-URL}}", mediaUrl);
            return await WhatsAppBusinessGetAsync(formattedWhatsAppEndpoint, PhoneNumberId);
        }

        private async Task<byte[]> WhatsAppBusinessGetAsync(string whatsAppBusinessEndpoint, string PhoneNumberId)
        {
            DbAa80b1WhatsappbusinessContext _apiContext = new DbAa80b1WhatsappbusinessContext(_configuration);
            var MetaDetails = _apiContext.BusinessDetailsMetas.FirstOrDefault(m => m.PhoneNumberId == PhoneNumberId.ToString());
            string AccessToken = string.Empty;
            string AppName = string.Empty;
            if (MetaDetails == null)
            {
                AccessToken = _configuration["Facebook:AccessToken"];
                AppName = _configuration["Facebook:AppName"];
            }
            else
            {
                AppName = MetaDetails.AppName;
                AccessToken = MetaDetails.Token;
            }
            _httpClient.DefaultRequestHeaders.Authorization = new AuthenticationHeaderValue("Bearer", AccessToken);

            var productValue = new ProductInfoHeaderValue(AppName, "v18.0");

            _httpClient.DefaultRequestHeaders.Authorization = new AuthenticationHeaderValue("Bearer", AccessToken);
            _httpClient.DefaultRequestHeaders.UserAgent.Add(productValue);

#if NET5_0_OR_GREATER
            var bytesDownloaded = await _httpClient.GetByteArrayAsync(whatsAppBusinessEndpoint).ConfigureAwait(false);
#endif

#if NETSTANDARD2_0_OR_GREATER
              var bytesDownloaded = await _httpClient.GetByteArrayAsync(whatsAppBusinessEndpoint).ConfigureAwait(false);
#endif

            return bytesDownloaded;
            return Array.Empty<byte>(); ;
        }
    }
}
