﻿using EngagetoEntities.Dtos.TemplateDtos;
using EngagetoEntities.Entities;
using EngagetoEntities.Enums;
using Newtonsoft.Json.Linq;

namespace EngagetoContracts.MetaContracts
{
    public interface IMetaPayloadService
    {
        JObject InitializeBasePayload(string contact, string templateName, string languageCode);
        JArray GetComponents();
        void AddBodyComponent(ref JArray components, string? body, List<string>? bodyVariableValues);
        void AddBodyComponent(ref JArray components, List<string>? bodyVariableValues);
        void AddHeaderComponent(ref JArray components, string? header, MediaType mediatype, string? headerValue);
        void AddHeaderComponent(ref JArray components, MediaType mediatype, string? headerValue);
        string AddButtonComponent(ref JArray components, List<Button>? buttons, List<ButtonDto>? buttonVeriableValues);
        JObject AddBodyComponentRequest(string text);
        Task<JObject> AddHeaderForCreateComponentRequest(CreateTemplateDto model, string handler);
        List<Button> AddButtonForCreateComponetRequest(CreateTemplateDto model, ref JArray components);
        void AddFooterForCreateComponentRequest(CreateTemplateDto model, ref JArray components);
    }
}
