﻿using EngagetoEntities.Dtos.AutomationDtos;
using EngagetoEntities.UserEntities.Dtos;

namespace EngagetoDapper.Data.Interfaces.AnalyticsInterfaces
{
    public interface IAnalyticsService
    {
        Task<AnalyticsOverviewDto> GetAnalyticsOverviewAsync(string companyId,AnalyticsOverviewFilterDto overviewFilterDto,CancellationToken cancellationToken);
        Task<List<AgentPerformanceDto>> GetAgentPerformanceAsync(string companyId,AnalyticsOverviewFilterDto overviewFilterDto, CancellationToken cancellationToken);
        Task<InboxAnalyticsDasboardDto> GetInboxAnalyticsAsync(string companyId,InboxAnalyticsFilterDto inboxAnalyticsFilter, CancellationToken cancellationToken);
        Task<MemoryStream> DownloadReportAsync(string companyId, DateTime fromDate, DateTime toDate, CancellationToken cancellationToken);
        Task<MemoryStream> DownalodAgentPerformanceReportAsync(string companyId, AnalyticsOverviewFilterDto filterDot, CancellationToken cancellationToken);
        Task<MemoryStream> DownloadInboxAnalyticsReportAsync(string companyId,InboxAnalyticsFilterDto inboxAnalyticsFilter, CancellationToken cancellationToken);
        Task<TenantCCostAnalyticsDto?> GetTenantCCostAnalyticAsync(string tenantId, DateTime fromDate, DateTime toDate, CancellationToken cancellationToken);
        Task<TemplateAnalyticsDto?> GetTemplateAnalyticsAsync(string tenantId, DateTime fromDate, DateTime toDate, CancellationToken cancellationToken);
    }
}
