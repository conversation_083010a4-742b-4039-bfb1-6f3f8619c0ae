﻿using EngagetoContracts.AutomationContracts;
using Dapper;
using System.Data;
using EngagetoEntities.Entities;


namespace EngagetoRepository.AutomationRepository
{
    public class AutoReplyCustomMessageRepository : IAutoReplyCustomMessageRepository
    {
        private readonly IUnitOfWork _unitOfWork;
        public AutoReplyCustomMessageRepository(IUnitOfWork unitOfWork)
        {
            _unitOfWork = unitOfWork;
        }

        public async Task<IEnumerable<T>> GetAutoReplyCustomMessageAsync<T>(Guid companyId, Guid? autoReplyAutomationId)
        {
            return await _unitOfWork.Connection.QueryAsync<T>("AutoReplyCustomMessageEntity_Get",
                 new
                 {
                     companyId,
                     @AutoReplyId = autoReplyAutomationId
                 },
                 _unitOfWork.Transaction,
                 commandType: CommandType.StoredProcedure);
        }
        public async Task<T?> SaveAutoReplyCustomMessageAsync<T>(AutoReplyCustomMessageEntity autoCustomMessage, Guid userId)
        {
            return await _unitOfWork.Connection.ExecuteScalarAsync<T>(
                               "AutoReplyCustomMessageEntity_Save",
                               new
                               {
                                   autoCustomMessage.Id,
                                   userId,
                                   autoCustomMessage.AutoReplyId,
                                   autoCustomMessage.BodyMessage,
                                   autoCustomMessage.Buttons,
                                   autoCustomMessage.IsDeleted
                               },
                               _unitOfWork.Transaction,
                               commandType: CommandType.StoredProcedure);
        }

        public async Task<T?> SaveAutoReplyVeriablesAsync<T>(string veriablesXml)
        {
            return await _unitOfWork.Connection.ExecuteScalarAsync<T>(
                            "AutoReplyVeriableEntity_SaveBulk",
                            new 
                            { 
                                xmlData = veriablesXml 
                            },
                            _unitOfWork.Transaction,
                            commandType: CommandType.StoredProcedure);
        }

    }

}
