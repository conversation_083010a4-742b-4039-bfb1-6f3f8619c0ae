﻿using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations.Schema;
using System.ComponentModel.DataAnnotations;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace EngagetoEntities.Entities
{
    public class MenuDetails
    {
        [Key]
        [DatabaseGenerated(DatabaseGeneratedOption.None)]
        public int MenuId { get; set; }
        public string? MenuName { get; set; }
        public int ParentMenuId { get; set; }
    }
    public class UpdateMenuStatusDto
    {
        public bool Status { get; set; }
        public string? menuname { get; set; }
    }
    public class MenuItem
    {
        public int MenuId { get; set; }
        public int ParentMenuId { get; set; }
        public string MenuName { get; set; }
        public string RoleName { get; set; }
        public bool Status { get; set; }
        public bool Access { get; set; }

    }

    public class ActionItem
    {
        public string MenuName { get; set; }
        public string RoleName { get; set; }
        public int Status { get; set; }
    }

    public class MenuNode
    {
        public string MenuName { get; set; }
        public int Status { get; set; }
        public string RoleName { get; set; }
        public int? ParentId { get; set; }
        public List<MenuNode> ChildNames { get; set; }
    }
    public class MainMenu
    {
        public string MenuName { get; set; }
        public bool Status { get; set; }
        public string RoleName { get; set; }
        public int Access { get; set; }
        public List<SubMenu> SubMenus { get; set; }
    }

    public class SubMenu
    {
        public string MenuName { get; set; }
        public List<ActionMenu> Actions { get; set; }
        public bool Status { get; set; }
        public string RoleName { get; set; }
        public int Access { get; set; }
    }

    public class ActionMenu
    {
        public string ActionName { get; set; }
        public bool Status { get; set; }
        public string RoleName { get; set; }
        public int Access { get; set; }
    }

    public class MenuItemInfo
    {
        public string ItemName { get; set; }
        public bool Status { get; set; }
        public string RoleName { get; set; }
        public int Access { get; set; }
    }
}
