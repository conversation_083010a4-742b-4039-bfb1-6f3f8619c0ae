﻿using EngagetoEntities.Utilities;
using Newtonsoft.Json.Linq;

namespace EngagetoEntities.Dtos.MetaDto
{
    public static class CreateMetaPayloadJsonDto
    {
        // Base payload template
        private static readonly string BasePayloadTemplate = @"
        {{
            ""name"": ""{0}"",
            ""language"": ""{1}"",
            ""category"": ""{2}"",
            ""components"": []
        }}";

        private static readonly string AuthBasePayloadTemplate = @"
        {{
            ""name"": ""{0}"",
            ""languages"": [],
            ""category"": ""{1}"",
            ""components"": []
        }}";
        // Templates for different components
        private static readonly string TextHeaderTemplate = @"
        {{
            ""type"": ""HEADER"",
            ""format"": ""TEXT"",
            ""text"": ""{0}""
        }}";

        private static readonly string TextWithVariablesHeaderTemplate = @"
        {{
            ""type"": ""HEADER"",
            ""format"": ""TEXT"",
            ""text"": ""{0}"",
            ""example"": {{
                ""header_text"": {1}
            }}
        }}";

        private static readonly string MediaHeaderTemplate = @"
        {{
            ""type"": ""HEADER"",
            ""format"": ""{0}"",
            ""example"": {{
                ""header_handle"": {1}
            }}
        }}";

        private static readonly string BodyTemplate = @"
        {{
            ""type"": ""BODY"",
            ""text"": ""{0}""
        }}";
        private static readonly string AuthBodyTemplate = @"
        {{
            ""type"": ""BODY"",
            ""add_security_recommendation"": ""{0}""
        }}";
        private static readonly string BodyWithVariablesTemplate = @"
        {{
            ""type"": ""BODY"",
            ""text"": ""{0}"",
            ""example"": {{
                ""body_text"": {1}
            }}
        }}";

        private static readonly string ButtonPayloadTemplate = @"
        {{
            ""type"": ""BUTTONS"",
            ""buttons"": {0}
        }}";

        private static readonly string PhoneNumberButtonTemplate = @"
        {{
            ""type"": ""PHONE_NUMBER"",
            ""text"": ""{0}"",
            ""phone_number"": ""{1}""
        }}";

        private static readonly string UrlButtonTemplate = @"
        {{
            ""type"": ""URL"",
            ""text"": ""{0}"",
            ""url"": ""{1}""
        }}";

        private static readonly string UrlButtonWithVariable = @"
        {{
            ""type"": ""URL"",
            ""text"": ""{0}"",
            ""url"": ""{1}""
            ""example"" :  [ ""{2}"" ]
        }}";

        private static readonly string QuickReplyTemplate = @"
        {{
            ""type"": ""QUICK_REPLY"",
            ""text"": ""{0}""
        }}";
        private static readonly string AuthButtonTemplate = @"
        {{
            ""type"": ""BUTTONS"",
            ""buttons"": [
                {{
                    ""type"": ""OTP"",
                    ""otp_type"": ""{0}"",
                    ""supported_apps"": []
                }}
            ]
        }}";
        private static readonly string FooterTemplate = @"
        {{
            ""type"": ""FOOTER"",
            ""text"": ""{0}""
        }}";
        private static readonly string AuthFooterTemplate = @"
        {{
            ""type"": ""FOOTER"",
            ""code_expiration_minutes"": ""{0}""
        }}";
        private static readonly string SupportedApps = @"
        {{
            ""package_name"": ""{0}"",
            ""signature_hash"": ""{1}""
        }}";
        // Methods to create payloads
        public static string CreateBasePayload(string templateName, string language, string category)
        {
            return string.Format(BasePayloadTemplate, templateName, language, category);
        }
        public static JObject CreateAuthBasePayload(string name, List<string> languages, string category, int ttlSeconds)
        {
            // Parse the base JSON template
            var jObject = JObject.Parse(string.Format(AuthBasePayloadTemplate, name, category));

            // Add languages if provided
            if (languages != null && languages.Any())
            {
                jObject["languages"] = new JArray(languages);
            }

            // Add TTL seconds if greater than 0
            if (ttlSeconds > 0)
            {
                jObject["message_send_ttl_seconds"] = ttlSeconds;
            }

            return jObject;
        }

        public static string CreateTextHeaderPayload(string text)
        {
            return string.Format(TextHeaderTemplate, text.Replace("\"", "\\\"").Replace("\n", "\\n"));
        }

        public static string CreateHeaderWithVariablesPayload(string text, List<string> exampleHeaderText)
        {
            string exampleHeaderTextJson = JArray.FromObject(exampleHeaderText).ToString();
            return string.Format(TextWithVariablesHeaderTemplate, text.Replace("\"", "\\\"").Replace("\n", "\\n"), exampleHeaderTextJson);
        }

        public static string CreateMediaHeaderPayload(string format, List<string> headerHandles)
        {
            string headerHandlesJson = JArray.FromObject(headerHandles).ToString();
            return string.Format(MediaHeaderTemplate, format, headerHandlesJson);
        }

        public static string CreateBodyPayload(string text)
        {
            return string.Format(BodyTemplate, text.Replace("\"", "\\\"").Replace("\n", "\\n"));
        }
        public static string CreateAuthBodyPayload(bool isSecurityRecommendation = false)
        {
            return string.Format(AuthBodyTemplate,isSecurityRecommendation);
        }
        public static string CreateBodyWithVariablesPayload(string text, List<string> bodyTextExamples)
        {
            string bodyTextExamplesJson = JArray.FromObject(new List<List<string>> { bodyTextExamples }).ToString();
            return string.Format(BodyWithVariablesTemplate, text.Replace("\"", "\\\"").Replace("\n", "\\n"), bodyTextExamplesJson);
        }
        public static string CreateButtonPayload(JArray buttons)
        {
            return string.Format(ButtonPayloadTemplate, buttons.ToString());
        }
        public static string CreatePhoneNumberButton(string text, string phoneNumber)
        {
            return string.Format(PhoneNumberButtonTemplate, text, phoneNumber);
        }

        public static string CreateUrlButton(string text, string url)
        {
            if (StringHelper.GetVariableRegexs().IsMatch(url))
            {
                return string.Format(UrlButtonWithVariable, text , url);
            }
            return string.Format(UrlButtonTemplate, text, url);
        }

        public static string CreateQuickReply(string text)
        {            
            return string.Format(QuickReplyTemplate, text);
        }

        public static string CreateFooter(string text)
        {
            return string.Format(FooterTemplate, text.Replace("\"", "\\\"").Replace("\n", "\\n"));
        }
        public static string CreateAuthFooter(int codeExpirationMinutes)
        {
            return string.Format(AuthFooterTemplate, codeExpirationMinutes);
        }
        public static JObject CreateAuthButton(string type, JArray? supportedApps)
        {
            var jObject = JObject.Parse(string.Format(AuthButtonTemplate, type)); // Correctly passing the type
            if (supportedApps?.Any() == true)
            {
                jObject["buttons"][0]["supported_apps"] = supportedApps;
            }
            return jObject;
        }
        // Method to add a button to the buttons array
        public static void AddButtons(string buttonPayload, ref JArray buttons)
        {
            var button = JObject.Parse(buttonPayload);
            buttons.Add(button);
        }
    }
}
