﻿namespace EngagetoEntities.Dtos.AutomationDtos
{
    public class AutoReplyCustomMessageDto
    {
        public AutoReplyAutomationDto AutoReplyAutomation { get; set; }
        public string? BodyMessage { get; set; }
        public List<VeriableDto>? Veriables { get; set; }
        public List<ButtonValueDto>? ButtonValue { get; set; }

    }

    public class ButtonValueDto
    {
        public string Key { get; set; }
        public string Value { get; set; }
    }

    public class AutomationVeriableDto : VeriableDto
    {
        public Guid? AutoReplyId { get; set; }
    }
}
