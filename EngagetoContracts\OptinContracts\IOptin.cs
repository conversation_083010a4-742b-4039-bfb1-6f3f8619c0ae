﻿
using EngagetoContracts.Services;
using EngagetoEntities.Dtos.OptInManagementDto;
using EngagetoEntities.Dtos.WebhookDtos;

namespace EngagetoContracts.OptinContracts
{
    public interface IOptin
    {
        Task<string> AddKeyword(AddKeywordRequestDto model, string currentUserId, string currentUserNameClaim);
        Task<AddKeywordRequestDto> GetOptOutKeywordsByIdAsync(string BusinessId, string currentUserId);
        Task<AddKeywordRequestDto> GetOptInKeywordsByIdAsync(string BusinessId, string currentUserId);
        Task<bool> RemoveKeywordAsync(Guid id, string BusinessId, string keywordToRemove);
        Task<bool> OptInAndOutKeywordProcessAsync(WAWebhookDto wAWebhook,string businessId);
    }
}
