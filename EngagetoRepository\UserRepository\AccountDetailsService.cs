﻿using Amazon.S3;
using Amazon.S3.Transfer;
using EngagetoContracts.GeneralContracts;
using EngagetoContracts.MetaContracts;
using EngagetoContracts.UserContracts;
using EngagetoEntities.DdContext;
using EngagetoEntities.Entities;
using EngagetoEntities.Enums;
using EngagetoEntities.UserEntities.Dtos;
using EngagetoEntities.UserEntities.Models;
using Microsoft.AspNetCore.Http;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Configuration;

namespace EngagetoRepository.UserRepository
{
    public class AccountDetailsService : IAccountDetailsService
    {
        private readonly ApplicationDbContext _context;
        private readonly IEmailService _emailService;
        private readonly IConfiguration _configuration;
        private readonly string _websiteLink;
        IEnvironmentService _environmentService;
        private readonly IMetaApiService _metaApiService;

        public AccountDetailsService(IConfiguration config,
            ApplicationDbContext context,
            IEmailService emailService,
            IEnvironmentService environmentService,
            IMetaApiService metaApiService)
        {
            _context = context;
            _emailService = emailService;
            _configuration = config;
            _environmentService = environmentService;
            _metaApiService = metaApiService;
            if (_environmentService.IsDevelopment)
                _websiteLink = _environmentService.GetDevWebsiteLink();
            else
                _websiteLink = _environmentService.GetProdWebsiteLink();
        }


        public async Task<Guid> UpdateManageAccountAsync(Guid currentUserId, Ahex_CRM_UsersDto updateRequest)
        {
            var existingManageAccount = await _context.Users
                                                      .Where(m => m.Id == currentUserId)
                                                      .FirstOrDefaultAsync();

            if (existingManageAccount == null)
            {
                throw new InvalidOperationException("Manage account not found or unauthorized to update.");
            }
            if (!existingManageAccount.Status)
            {
                throw new InvalidOperationException("Account is inactive and cannot be updated.");
            }

            existingManageAccount.Name = string.IsNullOrEmpty(updateRequest.Name) ? string.Empty : updateRequest.Name;

            if (!string.IsNullOrEmpty(updateRequest.CountryCode))
            {
                var countryDetails = await _context.CountryDetails.FirstOrDefaultAsync(cd => cd.CountryCode == updateRequest.CountryCode);
                if (countryDetails == null)
                {
                    throw new InvalidOperationException($"Country code '{updateRequest.CountryCode}' not found.");
                }

                //  existingManageAccount.CountryCode = countryDetails.CountryCode;
                existingManageAccount.CountryCode = updateRequest.CountryCode;
            }
            else
            {
                existingManageAccount.CountryCode = string.Empty;

            }
            existingManageAccount.PhoneNumber = string.IsNullOrEmpty(updateRequest.PhoneNumber) ? string.Empty : updateRequest.PhoneNumber;
            existingManageAccount.FacebookBusinessManagerId = string.IsNullOrEmpty(updateRequest.FacebookBusinessManagerId) ? string.Empty : updateRequest.FacebookBusinessManagerId;
            existingManageAccount.WhatsAppBusinessId = string.IsNullOrEmpty(updateRequest.WhatsAppBusinessId) ? string.Empty : updateRequest.WhatsAppBusinessId;
            existingManageAccount.Address = string.IsNullOrEmpty(updateRequest.Address) ? string.Empty : updateRequest.Address;
            existingManageAccount.About = string.IsNullOrEmpty(updateRequest.About) ? string.Empty : updateRequest.About;
            existingManageAccount.Designation = string.IsNullOrEmpty(updateRequest.Designation) ? string.Empty : updateRequest.Designation;

            if (!string.IsNullOrEmpty(updateRequest.CountryName))
            {
                var countryDetails = await _context.CountryDetails.FirstOrDefaultAsync(cd => cd.CountryName == updateRequest.CountryName);
                if (countryDetails == null)
                {
                    throw new InvalidOperationException($"Country name '{updateRequest.CountryName}' not found.");
                }
                existingManageAccount.CountryName = countryDetails.CountryName;
            }
            else
            {
                existingManageAccount.CountryName = string.Empty;
            }

            if (updateRequest.Image != null)
            {
                string logoLink = await UploadLogoAsync(existingManageAccount.Id, updateRequest.Image);
                if (logoLink != null)
                {
                    existingManageAccount.Image = logoLink;

                }
            }
            await _context.SaveChangesAsync();
            string notificationName = "Get notified when account details are created or updated";
            int? notificationId = await GetNotificationIdAsync(notificationName);

            var userNotification = await _context.UserNotificationEntities
                                                .FirstOrDefaultAsync(un => un.UserId.ToLower() == existingManageAccount.Id.ToString().ToLower() &&
                                                                             un.CompanyId.ToLower() == existingManageAccount.CompanyId &&
                                                                             un.isActive == true &&
                                                                             un.NotificationId == notificationId);
            if (userNotification != null)
            {
                string emailSubject = "Your account has been updated";
                string emailBody = $"Dear {existingManageAccount.Name},<br/><br/>Your account details have been successfully updated.<br/>";
                string logoUrl = _configuration["SmtpSettings:LogoUrl"];
                string loginLink = _websiteLink;

                await _emailService.SendEmailAsync(existingManageAccount.EmailAddress, emailSubject, emailBody, logoUrl, loginLink);
            }

            return existingManageAccount.Id;
        }

        public async Task<Ahex_CRM_UserDisplay> GetManageAccountByIdAsync(Guid currentUserId, Guid Id)
        {
            var manageAccount = await _context.Users
                .AsNoTracking()
                .FirstOrDefaultAsync(m => m.Id == currentUserId);

            if (manageAccount == null)
            {
                throw new InvalidOperationException("ManageAccount not found.");
            }
            bool isPlatform = RoleConstants.CompanyId == manageAccount.CompanyId;
            var manageAccountDto = new Ahex_CRM_UserDisplay
            {
                Id = manageAccount.Id,
                Name = manageAccount.Name,
                Designation = manageAccount.Designation,
                CountryName = manageAccount.CountryName,
                EmailAddress = manageAccount.EmailAddress,
                CountryCode = manageAccount.CountryCode,
                PhoneNumber = manageAccount.PhoneNumber,
                FacebookBusinessManagerId = manageAccount.FacebookBusinessManagerId,
                WhatsAppBusinessId = manageAccount.WhatsAppBusinessId,
                Address = manageAccount.Address,
                About = manageAccount.About,
                Image = manageAccount.Image,
                CreationDate = manageAccount.CreationDate ?? DateTime.MinValue,
                Status = manageAccount.Status,
            };
            var healtStatus = await _metaApiService.GetHealthStatusAsync(manageAccount.CompanyId);
            manageAccountDto.IsMetaEnabled = healtStatus?.OverAllStatus == MetaAccountStatus.AVAILABLE.ToString();
            var userRole = await GetUserRoleAsync(currentUserId);
            if (isPlatform)
            {
                manageAccountDto.companyVerificationStatus = true;
            }
            else
            {
                var companyId = manageAccount.CompanyId;
                if (companyId != null)
                {
                    var companyExists = await _context.BusinessDetailsMetas
                        .AsNoTracking()
                        .AnyAsync(b => b.BusinessId == companyId);

                    manageAccountDto.companyVerificationStatus = companyExists;
                }
                else
                {
                    manageAccountDto.companyVerificationStatus = false;
                }

            }
            return manageAccountDto;
        }
        private async Task<string> GetUserRoleAsync(Guid userId)
        {
            var userRole = await (from ur in _context.UserRoles
                                  join r in _context.Roles on ur.RoleId equals r.Id
                                  where ur.Id == userId
                                  select r.Name).FirstOrDefaultAsync();

            return userRole;
        }
        public async Task<IEnumerable<Ahex_CRM_Users>> GetAllManageAccountsAsync(Guid currentUserId, string searchQuery, bool includeInactive, string sortBy, bool isSortAscending)
        {
            try
            {


                var query = _context.Users.AsQueryable();

                if (!string.IsNullOrEmpty(searchQuery))
                {

                    if (Guid.TryParse(searchQuery, out Guid accountId))
                    {
                        query = query.Where(account => account.Id == accountId); ;
                    }
                    else
                    {
                        if (searchQuery.Length == 3 && searchQuery.All(char.IsDigit))
                        {

                            query = query.Where(company => company.PhoneNumber.StartsWith(searchQuery));
                        }
                        else
                        {
                            query = query.Where(account =>
                                 account.Name.Contains(searchQuery) ||
                                 account.Designation.Contains(searchQuery) ||
                                 account.EmailAddress.Contains(searchQuery) ||
                                 account.PhoneNumber.Substring(3).Contains(searchQuery) ||
                                 account.FacebookBusinessManagerId.Contains(searchQuery) ||
                                 account.WhatsAppBusinessId.Contains(searchQuery)
                            );
                        }
                    }
                }
                if (!includeInactive)
                {
                    query = query.Where(account => account.Status == includeInactive);
                }

                if (!string.IsNullOrEmpty(sortBy))
                {
                    query = isSortAscending ? query.OrderBy(account => EF.Property<object>(account, sortBy)) : query.OrderByDescending(account => EF.Property<object>(account, sortBy));
                }

                var accounts = await query.ToListAsync();
                return accounts;
            }
            catch (Exception)
            {
                return null;
            }
        }
        public async Task<bool> DeleteManageAccountAsync(Guid currentUserId, Guid Id)
        {
            try
            {


                var manageAccountToDelete = await _context.Users
                    .Where(m => m.Id == currentUserId)
                    .FirstOrDefaultAsync();

                if (manageAccountToDelete == null)
                {
                    return false;
                }

                manageAccountToDelete.Status = false;

                await _context.SaveChangesAsync();

                return true;
            }
            catch (Exception)
            {
                return false;
            }
        }

        public async Task<string> UploadLogoAsync(Guid Id, IFormFile? file)
        {
            var manageClient = await _context.Users.FindAsync(Id);

            if (manageClient == null)
            {

                return null;
            }

            var fileLink = await HandleFileUploadAsync(file);

            manageClient.Image = fileLink;

            await _context.SaveChangesAsync();

            return fileLink;
        }

        private async Task<string> HandleFileUploadAsync(IFormFile? file)
        {
            try
            {
                var accessKey = _configuration["Aws:AccessKey"];
                var secretKey = _configuration["Aws:SecretKey"];
                var bucketName = _configuration["Aws:BucketName"];
                var regionString = _configuration["Aws:Region"];
                var region = Amazon.RegionEndpoint.GetBySystemName(regionString);

                using (var client = new AmazonS3Client(accessKey, secretKey, region))
                {
                    var key = $"{Guid.NewGuid()}.{file.FileName.Split('.')[1]}";

                    using (var newMemoryStream = new MemoryStream())
                    {

                        await file.CopyToAsync(newMemoryStream);
                        //await utility.UploadAsync(filepath, bucketName, fileName);
                        var uploadRequest = new TransferUtilityUploadRequest
                        {
                            InputStream = newMemoryStream,
                            Key = key,
                            BucketName = bucketName
                            //CannedACL = S3CannedACL.PublicRead
                        };

                        using (var fileTransferUtility = new TransferUtility(client))
                        {
                            await fileTransferUtility.UploadAsync(uploadRequest);
                        }
                    }
                    var fileLink = $"https://{bucketName}.s3.{region.SystemName}.amazonaws.com/{key}";

                    return fileLink;
                }
            }
            catch (Exception ex)
            {

                Console.WriteLine($"Error uploading file to S3: {ex.Message}");
                throw;
            }
        }
        public async Task<int?> GetNotificationIdAsync(string notificationName)
        {

            var notification = await _context.NotificationEntities
                .FirstOrDefaultAsync(n => n.Name == notificationName);


            return notification?.Id;
        }
        public async Task<Ahex_CRM_Users> GetAccountDetails(Guid currentUserId)
        {
            return await _context.Users.FirstOrDefaultAsync(x => x.Id == currentUserId) ?? throw new Exception("Not Found User");
        }
    }
}
