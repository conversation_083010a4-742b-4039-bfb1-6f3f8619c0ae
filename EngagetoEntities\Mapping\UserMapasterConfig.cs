﻿using EngagetoEntities.Dtos.ContactDtos;
using EngagetoEntities.Dtos.ConversationDtos;
using EngagetoEntities.Entities;
using EngagetoEntities.UserEntities.Dtos;
using EngagetoEntities.Utilities;
using Mapster;
using Newtonsoft.Json;

namespace EngagetoEntities.Mapping
{
    public static class UserMapasterConfig
    {
        public static void RegisterMappings()
        {
            TypeAdapterConfig<ConversationAnalyticsPriceEntity, ConversationAnalyticsPriceDto>
                .NewConfig()
                .TwoWays();

            TypeAdapterConfig<PaymentWalletDetail, PaymentDetailsDto>
                .NewConfig();

            TypeAdapterConfig<DiscountEntity, DiscountDto>
                .NewConfig()
                .TwoWays();
            TypeAdapterConfig<ConversationAnalyticsJobRequest, ConversationAnalyticsJobRequestDto>
                .NewConfig()
                .TwoWays();
            TypeAdapterConfig<ApiKeyEntity, ApiKeyDto>
                .NewConfig()
                .TwoWays();
            TypeAdapterConfig<Conversations, ConversationDto>
               .NewConfig()
               .TwoWays();

            TypeAdapterConfig<ResourcePermissionEntity, ResourcePermissionEntity>
                .NewConfig()
                .Map(dest => dest.FreeAnalyticsConversationCountsJson,
                    src => JsonConvert.SerializeObject(src.FreeAnalyticsConversationCounts))

                .Map(dest => dest.FreeAnalyticsConversationCounts,
                    src => string.IsNullOrEmpty(src.FreeAnalyticsConversationCountsJson)
                        ? new List<FreeConversationCountDetail>()
                        : JsonConvert.DeserializeObject<List<FreeConversationCountDetail>>(src.FreeAnalyticsConversationCountsJson))
                .Compile();

            TypeAdapterConfig<RequestResourcePermissionDto, RequestResourcePermissionDto>
                .NewConfig()
                .TwoWays();
            TypeAdapterConfig<PlanDiscountDto, PlanDiscountEntity>
                .NewConfig()
                .TwoWays();

            TypeAdapterConfig<Dtos.ContactDtos.ContactDto, InboxContactDto>
                .NewConfig()
                .Map(dest => dest.Contact, src => src.Contact)
                .Map(dest => dest.Name, src => src.Name.Trim())
                .Map(dest => dest.ContactId, src => src.ContactId)
                .Map(dest => dest.ChatStatus, src => src.ChatStatus.ToString().ToLowerInvariant())
                .Map(dest => dest.Assignee, src => src.Assignee)
                .Map(dest => dest.Tags, src => StringHelper.SplitString(src.Tags))
                .Map(dest => dest.LastMessageAt, src => src.LastMessageAt.HasValue ? (TimeSpan?)(DateTime.Now - StringHelper.ConvertIntoIndianDateTime(src.LastMessageAt.Value)) : null)
                .Map(dest => dest.LastMessage, src => src.LastMessage)
                .Map(dest => dest.UnRead, src => src.Unread)
                .Map(dest => dest.IsSpam, src => src.IsSpam);

            TypeAdapterConfig<Contacts, ContactResponseDto>
                .NewConfig()
                .Ignore(dest => dest.Tags); // Ignore Tags property during mapping

        }
    }
}
