﻿using System.ComponentModel.DataAnnotations;


namespace EngagetoEntities.UserEntities.Models
{
    public class AssignRoleRequest
    {
        [Required(ErrorMessage = "UserId is required.")]
        public Guid UserId { get; set; }

        [Required(ErrorMessage = "RoleId is required.")]
        public Guid RoleId { get; set; }
    }
    public class UpdateAssignedRoleRequest
    {
        [Required(ErrorMessage = "UserId is required.")]
        public Guid UserId { get; set; }

        [Required(ErrorMessage = "NewRoleId is required.")]
        public string NewRoleId { get; set; }
    }
    public class AssignRoleResult
    {
        public bool Success { get; set; }
        public string Message { get; set; }
    }
    public class UpdatePasswordRequest
    {
        [Required(ErrorMessage = "Email is required.")]
        [EmailAddress(ErrorMessage = "Invalid email address.")]
        [RegularExpression(@"^\S+@\S+\.\S+$", ErrorMessage = "Email must be in a valid format.")]
        public string Email { get; set; }
        [Required(ErrorMessage = "NewPassword is required.")]
        [StringLength(100, MinimumLength = 8, ErrorMessage = "NewPassword length must be between 8 and 100 characters.")]
        public string NewPassword { get; set; }
        [Required]

        [Compare("NewPassword", ErrorMessage = "Passwords do not match.")]
        public string ConfirmPassword { get; set; }
        [Required]
        public string TemporaryPassword { get; set; }
    }
}
