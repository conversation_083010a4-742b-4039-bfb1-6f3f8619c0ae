﻿using EngagetoDapper.Data.Interfaces.GenericInterfaces;
using EngagetoDapper.Data.Interfaces.IConversationAnalyticsInterfaces;
using EngagetoDapper.Data.Interfaces.IUserInterfaces.IUserRepositories;
using EngagetoDapper.Data.Interfaces.IUserInterfaces.IUserServices;
using EngagetoEntities.Entities;
using EngagetoEntities.UserEntities.Dtos;
using Mapster;
using EngagetoEntities.Dtos.ContactDtos;
using EngagetoEntities.Dtos;

namespace EngagetoDapper.Data.Dapper.Services.UserServices
{
    public class UserService : IUserService
    {
        private readonly IUserRepository _userRepository;
        private readonly IGenericRepository _genericRepository;
        private readonly IConversationAnalyticsService _conversationAnalyticsService;
        public UserService(IUserRepository userRepository,
            IGenericRepository genericRepository,
            IConversationAnalyticsService conversationAnalyticsService)
        {
            _userRepository = userRepository;
            _genericRepository = genericRepository;
            _conversationAnalyticsService = conversationAnalyticsService;
        }
        public async Task<Ahex_CRM_BusinessDetails?> GetBusinessDetailsAsync(string companyId)
        {
            Guid id;
            Guid.TryParse(companyId, out id);
            var filter = new Dictionary<string, object>()
                    {
                        { "Id",id },
                    };
            return (await _genericRepository.GetByObjectAsync<Ahex_CRM_BusinessDetails>(filter))?.FirstOrDefault();
        }
        public async Task<Dictionary<string, List<Dictionary<string, List<string>>>>> GetMenuDetailsAsync(string companyId, string roleId)
        {
            try
            {
                var menuDetails = await _userRepository.GetMenuDetailsAsync(companyId, roleId);
                var groupedMenuDetails = menuDetails.Where(x => (x.Status ?? false) == true).GroupBy(m => m.ParentMenuId);
                var menuHierarchy = new Dictionary<string, List<Dictionary<string, List<string>>>>();
                var rootMenus = groupedMenuDetails.FirstOrDefault(g => g.Key == 0);

                if (rootMenus != null)
                {
                    foreach (var mainMenu in rootMenus)
                    {
                        var childMenus = groupedMenuDetails.FirstOrDefault(g => g.Key == mainMenu.MenuId);
                        var childMenuList = new List<Dictionary<string, List<string>>>();
                        if (childMenus != null)
                        {
                            foreach (var childMenu in childMenus)
                            {
                                var subMenus = groupedMenuDetails.FirstOrDefault(g => g.Key == childMenu.MenuId);
                                var subMenuNames = subMenus?.Select(m => m.MenuName).ToList() ?? new List<string>();

                                var submenuDict = new Dictionary<string, List<string>>()
                                    {
                                        {
                                            childMenu.MenuName,
                                            subMenuNames
                                        }
                                    };

                                childMenuList.Add(submenuDict);
                            }
                        }
                        menuHierarchy[mainMenu.MenuName] = childMenuList;
                    }
                }
                return menuHierarchy;
            }
            catch (Exception ex)
            {
                throw;
            }
        }
        public async Task<DiscountEntity> SaveDiscountAsync(DiscountDto discountDto, Guid userId)
        {
            try
            {
                var discount = discountDto.Adapt<DiscountEntity>();
                discount.DiscountId = Guid.NewGuid();
                discount.IsActive = true;
                discount.CreatedAt = DateTime.Now;
                discount.UpdatedAt = DateTime.Now;
                discount.CreatedBy = userId;
                discount.UpdatedBy = userId;
                var result = await _genericRepository.SaveAsync(discount);
                if (result > 0)
                {
                    var filter = new Dictionary<Guid, string>()
                    {
                        {
                            discount.DiscountId, "DiscountId"
                        }
                    };
                    return (await _genericRepository.GetByGuidIdAsync<DiscountEntity>(filter))?.FirstOrDefault() ?? new();
                }
                else
                    throw new Exception("There is an issue with saving discount data.");

            }
            catch (Exception ex)
            {
                throw new Exception("There is an issue with saving discount data.", ex);
            }
        }
        public async Task<DiscountEntity> UpdateDiscount(DiscountEntity discount)
        {
            try
            {
                var filter = new Dictionary<string, object>()
                    {
                        {
                            "DiscountId", discount.DiscountId
                        }
                    };
                var result = await _genericRepository.UpdateRecordAsync<DiscountEntity>(discount, filter);
                if (result != null)
                {
                    return result;
                }
                else
                    throw new Exception("There is an issue with saving discount data.");
            }
            catch (Exception ex)
            {
                throw new Exception("There is an issue with saving discount data.", ex);
            }
        }

        public async Task<Dictionary<string, List<MainMenu>>> GetPermissionMenuAsync(string companyId, string roleId)
        {
            try
            {
                Dictionary<string, List<MainMenu>> menuHierarchy = new Dictionary<string, List<MainMenu>>();

                var permissionMenuDetails = await _userRepository.GetPermissionMenuAsync(companyId, roleId);

                var menuDict = permissionMenuDetails.ToDictionary(m => m.MenuId);
                var parentMenuLookup = permissionMenuDetails
                                        .Where(m => m.ParentMenuId == 0)
                                        .ToLookup(m => m.MenuId);

                var childMenuLookup = permissionMenuDetails
                                        .Where(m => m.ParentMenuId != 0)
                                        .ToLookup(m => m.ParentMenuId);

                foreach (var mainMenu in parentMenuLookup.SelectMany(g => g))
                {
                    MainMenu mainMenuInfo = new MainMenu
                    {
                        SubMenus = new List<SubMenu>(),
                        MenuName = mainMenu.MenuName,
                        RoleName = mainMenu.RoleName,
                        Access = mainMenu.Access ? 1 : 0,
                        Status = mainMenu.Status
                    };

                    foreach (var childMenu in childMenuLookup[mainMenu.MenuId])
                    {
                        SubMenu childMenuInfo = new SubMenu
                        {
                            Actions = new List<ActionMenu>(),
                            MenuName = childMenu.MenuName,
                            RoleName = childMenu.RoleName,
                            Access = childMenu.Access ? 1 : 0,
                            Status = childMenu.Status
                        };

                        foreach (var subMenu in childMenuLookup[childMenu.MenuId])
                        {
                            ActionMenu actionMenu = new ActionMenu
                            {
                                ActionName = subMenu.MenuName,
                                RoleName = subMenu.RoleName,
                                Access = subMenu.Access ? 1 : 0,
                                Status = subMenu.Status
                            };

                            childMenuInfo.Actions.Add(actionMenu);
                        }

                        mainMenuInfo.SubMenus.Add(childMenuInfo);
                    }

                    if (!menuHierarchy.ContainsKey(mainMenuInfo.MenuName))
                    {
                        menuHierarchy[mainMenuInfo.MenuName] = new List<MainMenu>();
                    }
                    menuHierarchy[mainMenuInfo.MenuName].Add(mainMenuInfo);
                }

                return menuHierarchy;
            }
            catch (Exception ex)
            {

                throw;
            }
        }

        public async Task<DiscountEntity> GetDiscountByDicountCode(string code)
        {
            try
            {
                var filter = new Dictionary<string, object>()
                    {
                        { "DiscountCode",code },
                        { "IsActive", true }
                    };
                var result = await _genericRepository.GetByObjectAsync<DiscountEntity>(filter);
                if (result != null && result?.Count > 0)
                {
                    var discount = result?.FirstOrDefault();
                    if (discount != null)
                    {
                        if (discount.ValidFrom >= DateTime.Now || discount.ValidTo <= DateTime.Now)
                            throw new Exception("Discount Coupon Expired.");
                        return discount;
                    }
                }
                throw new Exception("Invalid Coupon Code.");
            }
            catch (Exception ex)
            {
                throw new Exception(ex.Message);
            }
        }
        public async Task<DiscountEntity?> GetDiscountById(Guid id)
        {
            try
            {
                var filter = new Dictionary<string, object>()
                {
                    {
                            "DiscountId",id
                    },
                    {
                            "IsActive", true
                    }
                };
                var result = await _genericRepository.GetByObjectAsync<DiscountEntity>(filter);
                return result?.FirstOrDefault();
            }
            catch (Exception ex)
            {
                throw new Exception(ex.Message);
            }
        }

        public async Task<List<DiscountDto>> GetAllDiscountAsync()
        {
            try
            {
                var result = await _genericRepository.GetAllAsync<DiscountEntity>();
                return result?.Adapt<List<DiscountDto>>() ?? new List<DiscountDto>();
            }
            catch (Exception ex)
            {
                throw new Exception(ex.Message);
            }
        }

        public async Task<List<TransactionHistoryEntity>?> GetCompanyWalletTransactionsHistoryAsync(string companyId)
        {
            try
            {
                var transactions = await _userRepository.GetCompanyWalletTransactionsHistoryAsync<TransactionHistoryEntity>(companyId);
                return transactions?.ToList();
            }
            catch (Exception ex)
            {
                throw new Exception(ex.Message);
            }
        }

        public async Task<BusinessDetailsMeta?> IsValidateMetaAccount(string companyId, Guid? usingId = null)
        {
            try
            {
                var metaAccounts = await _userRepository.GetMetaAccountAsync<BusinessDetailsMeta>(companyId, usingId);
                var metaAccount = metaAccounts.FirstOrDefault();

                if (metaAccount == null)
                    throw new UnauthorizedAccessException("Account is not valid. Please contact the support team.");

                return metaAccount;
            }
            catch (Exception ex)
            {
                throw new Exception("An error occurred while validating the meta account.", ex);
            }
        }
        public async Task<ExpectedWalletBalanceDto?> GetExpectedWalletBalanceAsync(string companyId)
        {
            try
            {
                IEnumerable<ExpectedWalletBalanceDto> walletBalanceDto = await _userRepository.GetExpectedWalletBalanceAsync<ExpectedWalletBalanceDto>(companyId);
                if (walletBalanceDto.Any())
                {
                    var expectedWalletBalance = walletBalanceDto.FirstOrDefault();
                    var campaignCost = await GetCampaignCostAsync(companyId);
                    expectedWalletBalance.ExpectedWalletBalance = expectedWalletBalance.ExpectedWalletBalance - campaignCost;

                    return expectedWalletBalance;
                }
                return null;
            }
            catch (Exception ex)
            {
                throw new Exception(ex.Message);
            }
        }
        public async Task<decimal> GetCampaignCostAsync(string companyId)
        {
            try
            {
                decimal campaignPrice = 0;
                var subscriptionDetails = await _userRepository.GetSubscriptionDetailsAsync<SubscriptionDetailsDto>(companyId);
                var futureCampaigns = await _genericRepository.GetRecordByRequestFilter<Campaign>(new List<Dtos.RequestFilterDto>
                {
                    new Dtos.RequestFilterDto
                    {
                        Key = "BusinessId",
                        Value = companyId,
                        Operator = "="
                    },
                    new Dtos.RequestFilterDto
                    {
                        Key = "DateSetLive",
                        Value = DateTime.UtcNow,
                        Operator = ">"
                    },
                }, "Campaigns");

                if (futureCampaigns.Count == 0)
                    return 0;

                var cost = await GetCosts(companyId);

                // Get all template IDs for future campaigns that have a valid TemplateId
                var templateIds = futureCampaigns
                    .Where(x => x.TemplateId.HasValue && x.TemplateId != Guid.Empty)
                    .Select(x => x.TemplateId.Value)
                    .Distinct()
                    .ToList();

                // Fetch all templates in one go
                //var templates1 = string.Join(",", templateIds);
                var templates = await _genericRepository.GetRecordByRequestFilter<EngagetoEntities.Entities.Template>(
                    new List<Dtos.RequestFilterDto>
                {
                    new Dtos.RequestFilterDto
                    {
                        Key = "TemplateId",
                        Value = templateIds,
                        Operator = "in"
                    }
                }, "Templates");

                var templatesDict = templates.ToDictionary(t => t.TemplateId);

                foreach (var campaign in futureCampaigns)
                {
                    if (!templatesDict.TryGetValue(campaign.TemplateId.Value, out var template))
                        continue;

                    var price = cost
                        .FirstOrDefault(x => x.ConversationCategory.Equals(template?.Category?.ToString(), StringComparison.OrdinalIgnoreCase)
                            && subscriptionDetails != null && x.PlanId == subscriptionDetails?.PlanId);

                    var audienceCount = campaign?.Audiance?.Split(",")?.Length ?? 0;
                    campaignPrice += (price?.Cost ?? 0) * audienceCount;
                }

                return campaignPrice;
            }
            catch (Exception ex)
            {
                // Log exception
                return 0;
            }
        }

        public async Task<IEnumerable<InboxContactDto>> GetInboxContactsAsync(string companyId,
            Guid userId,
            string businessPhoneNumber,
            CancellationToken cancellationToken)
        {
            try
            {
                var result = await _userRepository.GetInboxContactsAsync<EngagetoEntities.Dtos.ContactDtos.ContactDto>(companyId, userId, businessPhoneNumber, cancellationToken);
                if (result == null)
                    return Enumerable.Empty<InboxContactDto>();
                else
                    return result.Adapt<List<InboxContactDto>>();
            }
            catch (Exception ex)
            {
                throw new Exception("An error occurred while Getting the inbox contact.", ex);
            }
        }

        public async Task<IEnumerable<UserDto>> GetUsersByBusinessIdAync(string? businessId, string? waPhoneNumberId)
        {
            try
            {
                var users = await _userRepository.GetUsersByBusinessIdAync<UserDto>(businessId, waPhoneNumberId);
                return users;
            }
            catch (Exception ex) 
            { 
                return new List<UserDto>();
            }
        }

        #region Helper Methods
        private async Task<List<ConversationAnalyticsPriceEntity>> GetCosts(string businessId)
        {
            var costs = await _conversationAnalyticsService.GetConversationAnalyticsPriceByYear(businessId, DateTime.Now.Year);
            if (!costs.Any())
            {
                costs = await _conversationAnalyticsService.GetDefaultConversationAnalyticsPrice();
            }
            return costs;
        }

        public async Task<IEnumerable<ExpiringOrExpiredSubscriptionsDto>> GetExpiringOrExpiredSubscriptionsAsync()
        {
            try
            {
                var expirignOrExpiredSubscriptions = await _userRepository
                    .GetExpiringOrExpiredSubscriptionsAsync<ExpiringOrExpiredSubscriptionsDto>();
                return expirignOrExpiredSubscriptions;
            }
            catch (Exception ex)
            {
                return Enumerable.Empty<ExpiringOrExpiredSubscriptionsDto>();
            }
        }

        #endregion
    }
}
