﻿using EngagetoEntities.Entities;
using EngagetoEntities.UserEntities.Dtos;

namespace EngagetoContracts.UserContracts
{
    public interface ITeamDetailsService
    {
        Task<Guid?> GetRoleIdByRoleNameAsync(string roleName);
        Task<bool> AddTeamMemberAsync(Guid adminUserId, User_TeamMember teamMemberRequest);
        Task<bool> AddClientTeamMemberAsync(Guid adminUserId, User_ClientTeamMember teamMemberRequest);
        Task<IEnumerable<Ahex_CRM_Users>> GetTeamMembersAsync(Guid currentUserId, string searchQuery, bool isActive, string sortBy, bool isSortAscending);
        Task<bool> UpdateTeamMemberAsync(Guid adminUserId, Guid teamMemberId, User_TeamMemberUpdate teamMemberRequest);
        Task<bool> DeleteTeamMemberAsync(Guid adminUserId, Guid teamMemberId);
        Task<IEnumerable<Ahex_CRM_Users>> GetTeamDetailsByCompanyIdAsync(string companyId, bool? isActive, string searchQuery = null, string sortBy = null, bool ascending = true);
    }
}
