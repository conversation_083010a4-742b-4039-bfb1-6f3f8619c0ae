﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace EngagetoEntities.Dtos.WebhookReceiveNotification
{
    internal class TemplateStatusReceived
    {
        public class Rootobject
        {
            public Entry[] entry { get; set; }
        }

        public class Entry
        {
            public string id { get; set; }
            public int time { get; set; }
            public Change[] changes { get; set; }
        }

        public class Change
        {
            public Value value { get; set; }
            public string field { get; set; }
        }

        public class Value
        {
            public string _event { get; set; }
            public long message_template_id { get; set; }
            public string message_template_name { get; set; }
            public string message_template_language { get; set; }
            public string reason { get; set; }
        }

    }
}
