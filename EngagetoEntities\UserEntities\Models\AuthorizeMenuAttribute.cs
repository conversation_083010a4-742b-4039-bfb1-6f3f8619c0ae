﻿using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Mvc.Filters;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.DependencyInjection;
using System.Security.Claims;
using EngagetoEntities.UserEntities.Models;

public class AuthorizeMenuAttribute : Attribute, IAsyncAuthorizationFilter
{
    private readonly string[] _menuNames;

    public AuthorizeMenuAttribute(params string[] menuNames)
    {
        _menuNames = menuNames;
    }

    public async Task OnAuthorizationAsync(AuthorizationFilterContext context)
    {
        var dbContext = context.HttpContext.RequestServices.GetRequiredService<ApplicationDBContext>();

        var requestingUserId = context.HttpContext.User.FindFirstValue(ClaimTypes.Name);
        if (string.IsNullOrEmpty(requestingUserId) || !Guid.TryParse(requestingUserId, out Guid userId))
        {
            context.Result = new UnauthorizedResult();
            return;
        }

        var user = await dbContext.Ahex_CRM_Users.FirstOrDefaultAsync(u => u.Id == userId);
        if (user == null)
        {
            context.Result = new UnauthorizedResult();
            return;
        }

        var userRole = user.RoleId.ToLower();
        var userCompany = user.CompanyId;

        if (string.IsNullOrEmpty(userRole))
        {
            context.Result = new UnauthorizedResult();
            return;
        }

        var isAuthorized = await IsAnyMenuAuthorized(dbContext, _menuNames, userRole, userCompany);
        if (!isAuthorized)
        {
            context.Result = new UnauthorizedResult();
        }
    }

    private async Task<bool> IsAnyMenuAuthorized(ApplicationDBContext dbContext, string[] menuNames, string userRole, string userCompany)
    {
        foreach (var menuName in menuNames)
        {
            var menu = await dbContext.MenuDetails.FirstOrDefaultAsync(m => m.MenuName == menuName);
            if (menu != null)
            {
                var menuStatus = await dbContext.MenuwithRoleRelationDetails
                    .AnyAsync(mrr => mrr.MenuId == menu.MenuId && mrr.RoleId == userRole && mrr.CompanyId == userCompany && mrr.Status);

                if (menuStatus)
                {
                    return true;
                }
            }
        }
        return false; 
    }
}
