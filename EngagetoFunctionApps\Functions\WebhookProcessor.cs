using EngagetoEntities.Dtos.WebhookDtos;
using Microsoft.Azure.Functions.Worker;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Logging;
using Newtonsoft.Json;
using System.Text;

namespace EngagetoFunctionApps.Functions
{
    public class WebhookProcessor
    {
        private readonly ILogger<WebhookProcessor> _logger;
        private string engagetoApiUrl;
        private readonly IConfiguration _configuration;
        private readonly HttpClient _httpClient;

        public WebhookProcessor(ILogger<WebhookProcessor> logger, IConfiguration configuration, HttpClient httpClient)
        {
            _logger = logger;
            _configuration = configuration;
            engagetoApiUrl = configuration["EngagetoApiUrl"];
            _httpClient = httpClient;
        }

        [Function("ProcessWebhookMessage")]
        public async Task ProcessWebhookMessage(
            [ServiceBusTrigger("engageto-meta-background-jobs", Connection = "ServiceBusConnection")]
            string messageBody,
            FunctionContext context)
        {
            var correlationId = Guid.NewGuid().ToString();
            try
            {
                _logger.LogInformation("📥 Message received from queue: engageto-meta-background-jobs | CorrelationId: {CorrelationId} | WebhookDto: {WebhookDto}", correlationId, messageBody);

                var stopwatch = System.Diagnostics.Stopwatch.StartNew();
                //Console.WriteLine(JObject.Parse(messageBody).ToString(Formatting.Indented));
                // Deserialize the webhook message
                var webhookData = JsonConvert.DeserializeObject<WAWebhookDto>(messageBody);
                _logger.LogInformation("✅ Message Deserialize successfully |  WebhookDto: {WebhookDto}",
                    webhookData);

                if (webhookData == null)
                {
                    _logger.LogWarning("⚠️  Received null webhook data | CorrelationId: {CorrelationId}", correlationId);
                    return;
                }
                _logger.LogInformation("🔄 Processing WAWebhookDto message | Type: {MessageType} | CorrelationId: {CorrelationId}",
                    webhookData.Object, correlationId);
                // Process each entry in the webhook
                await ProcessWebhookEntry(webhookData, correlationId);


                stopwatch.Stop();
                _logger.LogInformation("✅ Message processed successfully | CorrelationId: {CorrelationId} | Duration: {Duration}ms",
                    correlationId, stopwatch.ElapsedMilliseconds);
            }
            catch (JsonException jsonEx)
            {
                _logger.LogError(jsonEx, "❌ JSON deserialization error | CorrelationId: {CorrelationId} | Message: {Message}",
                    correlationId, jsonEx.Message);
                throw; // This will send message to dead letter queue
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "❌ Error processing webhook message | CorrelationId: {CorrelationId} | Error: {Error}",
                    correlationId, ex.Message);
                throw; // This will send message to dead letter queue
            }
        }

        private async Task ProcessWebhookEntry(WAWebhookDto entry, string correlationId)
        {
            _logger.LogInformation("📋 Processing entry | BusinessId: {BusinessId} | Time: {Time} | CorrelationId: {CorrelationId} | WebhookDto: {WebhookDto}",
                entry?.Entry[0].Id, DateTime.UtcNow.ToString(), correlationId, JsonConvert.SerializeObject(entry));

            try
            {
                // Convert to JSON
                var jsonContent = JsonConvert.SerializeObject(entry);
                var content = new StringContent(jsonContent, Encoding.UTF8, "application/json");

                // Make the API call
                var response = await _httpClient.PostAsync(engagetoApiUrl, content);

                if (response.IsSuccessStatusCode)
                {
                    var responseContent = await response.Content.ReadAsStringAsync();
                    _logger.LogInformation("✅ API call successful | BusinessId: {BusinessId} | Response: {Response} | CorrelationId: {CorrelationId}",
                        entry?.Entry[0].Id, responseContent, correlationId);
                }
                else
                {
                    _logger.LogError("❌ API call failed | BusinessId: {BusinessId} | StatusCode: {StatusCode} | CorrelationId: {CorrelationId}",
                        entry?.Entry[0].Id, response.StatusCode, correlationId);
                }
            }
            catch (HttpRequestException httpEx)
            {
                _logger.LogError(httpEx, "🌐 HTTP error during API call | BusinessId: {BusinessId} | CorrelationId: {CorrelationId}",
                    entry?.Entry[0].Id, correlationId);
                throw;
            }
            catch (TaskCanceledException tcEx) when (tcEx.InnerException is TimeoutException)
            {
                _logger.LogError(tcEx, "⏰ Timeout during API call | BusinessId: {BusinessId} | CorrelationId: {CorrelationId}",
                    entry?.Entry[0].Id, correlationId);
                throw;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "💥 Unexpected error during API call | BusinessId: {BusinessId} | CorrelationId: {CorrelationId}",
                    entry?.Entry[0].Id, correlationId);
                throw;
            }
        }
    }
}