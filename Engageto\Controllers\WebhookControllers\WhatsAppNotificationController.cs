﻿using EngagetoContracts.ContactContracts;
using EngagetoContracts.GeneralContracts;
using EngagetoContracts.MetaContracts;
using EngagetoContracts.UserContracts;
using EngagetoContracts.WebhookContracts.Client;
using EngagetoContracts.WebhookContracts.ReceivedNotification;
using EngagetoContracts.WebhookContracts.SentMessage;
using EngagetoDapper.Data.Interfaces.GenericInterfaces;
using EngagetoDapper.Data.Interfaces.ILogInterfaces;
using EngagetoDapper.Data.Interfaces.IUserInterfaces.IUserServices;
using EngagetoEntities.DdContext;
using EngagetoEntities.Dtos.WebhookDtos;
using EngagetoEntities.Dtos.WebhookReceiveNotification;
using EngagetoEntities.Entities;
using EngagetoEntities.Enums;
using EngagetoEntities.Utilities;
using Microsoft.AspNetCore.Mvc;
using Newtonsoft.Json;
using System.Text;

namespace Engageto.Controllers.WebhookControllers
{
    [Route("api")]
    [ApiController]
    public class WhatsAppNotificationController : ControllerBase
    {
        private string VerifyToken;
        private readonly IWhatsAppBusinessNotificarion _whatsAppBusinessNotificarion;
        private readonly IWhatsAppBusinessClient _whatsAppBusinessClient;
        private readonly IWhatsAppReceiveNotification _whatsAppReceiveNotification;
        private IContactRepositoryBase _contactRepositoryBase;
        private readonly IReceivedContacts _receivedContacts;
        private readonly ApplicationDbContext _context;
        private List<TextMessage> textMessage;
        private List<ImageMessage> imageMessage;
        private List<AudioMessage> audioMessage;
        private List<DocumentMessage> documentMessage;
        private List<StickerMessage> stickerMessage;
        private List<VideoMessage> videoMessage;
        private TextMessageMetadata textMessageMetadata;
        private ImageMessageMetadata imageMessageMetadata;
        private AudioMessageMetadata audioMessageMetadata;
        private DocumentMessageMetadata documentMessageMetadata;
        private VideoMessageMetadata videoMessageMetadata;
        private Image image;
        private Audio audio;
        private Document document;
        private Video video;
        private TemplateStatus templateStatus;
        private IConfiguration _configuration;
        private readonly IEnvironmentService _environmentService;
        private readonly IGenericRepository _genericRepository;
        private readonly IServiceScopeFactory _serviceScopeFactory;
        private readonly ILogHistoryService _historyService;
        private readonly IClientDetailsService _clientDetailsService;
        public WhatsAppNotificationController(IConfiguration configuration,
            IContactRepositoryBase contactRepositoryBase,
            ApplicationDbContext context,
            IReceivedContacts recevedContacts, IWhatsAppBusinessNotificarion whatsAppBusinessNotificarion,
            IWhatsAppBusinessClient whatsAppBusinessClient, IWhatsAppReceiveNotification whatsAppReceiveNotification,
            TemplateStatus templateStatus, IEnvironmentService environmentService,
            IGenericRepository genericRepository, ILogHistoryService historyService, IServiceScopeFactory serviceScopeFactory,
            IClientDetailsService clientDetailsService)
        {
            _configuration = configuration;
            _contactRepositoryBase = contactRepositoryBase;
            this._whatsAppBusinessNotificarion = whatsAppBusinessNotificarion;
            this._whatsAppBusinessClient = whatsAppBusinessClient;
            this._whatsAppReceiveNotification = whatsAppReceiveNotification;
            this.templateStatus = templateStatus;
            this._receivedContacts = recevedContacts;
            _environmentService = environmentService;
            _genericRepository = genericRepository;
            _historyService = historyService;
            _serviceScopeFactory = serviceScopeFactory;
            _clientDetailsService= clientDetailsService;
            _context = context;
        }
        // Required step for configuring webhook to WhatsApp Cloud API
        // Make sure the verifytoken matches with the hubverifytoken returned from whatsapp.
        [HttpGet("receive/Message")]
        public ActionResult<string> ConfigureWhatsAppMessageWebhook([FromQuery(Name = "hub.mode")] string hubMode,
                                                                    [FromQuery(Name = "hub.challenge")] int hubChallenge,
                                                                    [FromQuery(Name = "hub.verify_token")] string hubVerifyToken)
        {
            VerifyToken = _configuration["Webhooks:VerifyToken"];
            if (!hubVerifyToken.Equals(VerifyToken))
            {
                return Forbid("VerifyToken doesn't match");
            }
            return Ok(hubChallenge);
        }


        [HttpPost("receive/Message")]
        public async Task<IActionResult> ReceiveWhatsAppTextMessage([FromBody] dynamic messageReceived)
        {
            try
            {
                if (messageReceived is null)
                {
                    return BadRequest(new
                    {
                        Message = "Message not received"
                    });
                }
                #region Send webhook message to other domain
                _ = Task.Run(() =>
                {
                    SendWebhookMessage(messageReceived);
                });
                #endregion
                var changesResult = messageReceived["entry"][0]["changes"][0]["value"];

                var field = messageReceived["entry"][0]["changes"][0]["field"];
                if (messageReceived["entry"][0]["changes"][0]["value"]["contacts"] != null)
                {
                    await _receivedContacts.ContactAdd(changesResult);
                }
                if (field == "message_template_status_update")
                {
                    var entry = messageReceived["entry"][0];
                    await templateStatus.UpdateCreateTemplateStatus(entry);

                    return Ok("Success");
                }
                else if (field == "business_capability_update")
                {
                    var waAccountId = Convert.ToString(messageReceived["entry"][0]["id"]);
                    var messageLimit = Convert.ToInt64(changesResult["max_daily_conversation_per_phone"]);
                    await _clientDetailsService.UpdateMetaAccountByWebhookAsync(waAccountId, messageLimit, null, null, null);
                    return Ok("Success");
                }
                else if (field == "phone_number_quality_update")
                {   
                    string waAccountId = Convert.ToString(messageReceived["entry"]?[0]?["id"]);
                    long messageLimit = 0;
                    string businessStatus = Convert.ToString(changesResult["event"]);
                    string tier = Convert.ToString(changesResult["current_limit"]);
                    string displayNumber = Convert.ToString(changesResult["display_phone_number"]);

                    await _clientDetailsService.UpdateMetaAccountByWebhookAsync(waAccountId, messageLimit, businessStatus, tier,displayNumber);
                    return Ok("Success");
                }
                else if (field == "message_template_quality_update")
                {
                    var entry = messageReceived["entry"][0];
                    await templateStatus.TemplateQualityUpdate(entry);
                    return Ok("Success");
                }
                else if (changesResult["statuses"] != null && field == "messages")
                {

                    var status = changesResult["statuses"][0];
                    // _ = Task.Run(() => MetaCostHandlerAsync(messageReceived));
                    await _whatsAppBusinessNotificarion.UpdateSentMessageStatus(status);
                    return Ok("Success");
                }
                else
                {
                    var messageType = Convert.ToString(messageReceived["entry"][0]["changes"][0]["value"]["messages"][0]["type"]);
                    List<string> MetaVerifiedBusinessIds = _context.BusinessDetailsMetas.Select(m => m.BusinessId.ToLower()).ToList();

                    if (messageType.Equals("text"))
                    {

                        var textMessageReceived = JsonConvert.DeserializeObject<TextMessageReceived>(Convert.ToString(messageReceived)) as TextMessageReceived;
                        textMessage = new List<TextMessage>(textMessageReceived.Entry.SelectMany(x => x.Changes).SelectMany(x => x.Value.Messages));
                        textMessageMetadata = textMessageReceived.Entry.SelectMany(x => x.Changes).Select(x => x.Value.Metadata).FirstOrDefault(metadata => metadata != null);

                        //saving received messages into database
                        await _whatsAppReceiveNotification.ReceiveNotification(textMessage[0], textMessageMetadata);
                        var DemoMessage = _configuration["DemoContactSaving:Message"];
                        if (textMessage[0].Text.Body.StartsWith(DemoMessage))
                        {
                            int prefixLength = DemoMessage.Length;

                            // Extract the remaining part of the message
                            string remainingText = textMessage[0].Text.Body.Substring(prefixLength);
                            if ((string)changesResult["metadata"]["phone_number_id"] == _configuration["Facebook:PhoneNumberId"])
                            {
                                if (Guid.TryParse(remainingText, out Guid parsedGuid))
                                {
                                    _receivedContacts.DemoContacts(changesResult, parsedGuid);

                                }
                            }
                        }
                        var PhoneNumberId = _configuration["Facebook:PhoneNumberId"];
                        string Contact = (string)messageReceived["entry"][0]["changes"][0]["value"]["contacts"][0]["wa_id"] ?? string.Empty;
                        if ((string)changesResult["metadata"]["phone_number_id"] == PhoneNumberId)
                        {

                            var Data = _context.Contacts.Where(m => (m.CountryCode + m.Contact).Replace("+", "") == Contact && m.IsActive == true).OrderByDescending(m => m.CreatedDate);
                            textMessageMetadata.PhoneNumberId = Data?.OrderByDescending(m => m.CreatedDate).FirstOrDefault()?.BusinessId.ToString() ?? string.Empty;
                            if (!MetaVerifiedBusinessIds.Contains(textMessageMetadata.PhoneNumberId.ToLower()))
                            {
                                if (textMessage[0].From != string.Empty)
                                {
                                    await _whatsAppReceiveNotification.ReceiveNotification(textMessage[0], textMessageMetadata);
                                }

                            }

                        }

                        return new JsonResult(new
                        {
                            Message = "Text Message received"
                        });
                    }
                    if (messageType.Equals("button"))
                    {
                        var textMessageReceived = JsonConvert.DeserializeObject<TextMessageReceived>(Convert.ToString(messageReceived)) as TextMessageReceived;
                        textMessage = new List<TextMessage>(textMessageReceived.Entry.SelectMany(x => x.Changes).SelectMany(x => x.Value.Messages));
                        textMessageMetadata = textMessageReceived.Entry.SelectMany(x => x.Changes).Select(x => x.Value.Metadata).FirstOrDefault(metadata => metadata != null);

                        //saving received messages into database
                        await _whatsAppReceiveNotification.ReceiveNotification(textMessage[0], textMessageMetadata);

                        var PhoneNumberId = _configuration["Facebook:PhoneNumberId"];
                        string Contact = (string)messageReceived["entry"][0]["changes"][0]["value"]["contacts"][0]["wa_id"] ?? string.Empty;
                        if ((string)changesResult["metadata"]["phone_number_id"] == PhoneNumberId)
                        {
                            var Data = _context.Contacts.Where(m => (m.CountryCode + m.Contact).Replace("+", "") == Contact && m.IsActive == true).OrderByDescending(m => m.CreatedDate);
                            textMessageMetadata.PhoneNumberId = Data?.OrderByDescending(m => m.CreatedDate).FirstOrDefault()?.BusinessId.ToString() ?? string.Empty;
                            if (!MetaVerifiedBusinessIds.Contains(textMessageMetadata.PhoneNumberId.ToLower()))
                            {
                                if (textMessage[0].From != string.Empty)
                                {
                                    await _whatsAppReceiveNotification.ReceiveNotification(textMessage[0], textMessageMetadata);
                                }
                            }

                        }

                        return new JsonResult(new
                        {
                            Message = "Text Message received"
                        });
                    }
                    if (messageType.Equals("interactive"))
                    {
                        var textMessageReceived = JsonConvert.DeserializeObject<TextMessageReceived>(Convert.ToString(messageReceived)) as TextMessageReceived;
                        textMessage = new List<TextMessage>(textMessageReceived.Entry.SelectMany(x => x.Changes).SelectMany(x => x.Value.Messages));
                        textMessageMetadata = textMessageReceived.Entry.SelectMany(x => x.Changes).Select(x => x.Value.Metadata).FirstOrDefault(metadata => metadata != null);

                        //saving received messages into database
                        await _whatsAppReceiveNotification.ReceiveNotification(textMessage[0], textMessageMetadata);

                        var PhoneNumberId = _configuration["Facebook:PhoneNumberId"];
                        string Contact = (string)messageReceived["entry"][0]["changes"][0]["value"]["contacts"][0]["wa_id"] ?? string.Empty;
                        if ((string)changesResult["metadata"]["phone_number_id"] == PhoneNumberId)
                        {
                            var Data = _context.Contacts.Where(m => (m.CountryCode + m.Contact).Replace("+", "") == Contact && m.IsActive == true).OrderByDescending(m => m.CreatedDate);
                            textMessageMetadata.PhoneNumberId = Data?.OrderByDescending(m => m.CreatedDate).FirstOrDefault()?.BusinessId.ToString() ?? string.Empty;
                            if (!MetaVerifiedBusinessIds.Contains(textMessageMetadata.PhoneNumberId.ToLower()))
                            {
                                if (textMessage[0].From != string.Empty)
                                {
                                    await _whatsAppReceiveNotification.ReceiveNotification(textMessage[0], textMessageMetadata);
                                }
                            }

                        }

                        return new JsonResult(new
                        {
                            Message = "Text Message received"
                        });
                    }
                    if (messageType.Equals("image"))
                    {
                        var imageMessageReceived = JsonConvert.DeserializeObject<ImageMessageReceived>(Convert.ToString(messageReceived)) as ImageMessageReceived;
                        imageMessage = new List<ImageMessage>(imageMessageReceived.Entry.SelectMany(x => x.Changes).SelectMany(x => x.Value.Messages));
                        image = imageMessage.Select(x => x.Image).FirstOrDefault(metadata => metadata != null);
                        imageMessageMetadata = imageMessageReceived.Entry.SelectMany(x => x.Changes).Select(x => x.Value.Metadata).FirstOrDefault(metadata => metadata != null);
                        //Received Image data into database
                        await _whatsAppReceiveNotification.ReceivedImageMessage(imageMessage[0], image, imageMessageMetadata);
                        var PhoneNumberId = _configuration["Facebook:PhoneNumberId"];
                        string Contact = (string)messageReceived["entry"][0]["changes"][0]["value"]["contacts"][0]["wa_id"] ?? string.Empty;
                        if ((string)changesResult["metadata"]["phone_number_id"] == PhoneNumberId)
                        {
                            var Data = _context.Contacts.Where(m => (m.CountryCode + m.Contact).Replace("+", "") == Contact && m.IsActive == true).OrderByDescending(m => m.CreatedDate);
                            imageMessageMetadata.PhoneNumberId = Data?.OrderByDescending(m => m.CreatedDate).FirstOrDefault()?.BusinessId.ToString() ?? string.Empty;
                            if (!MetaVerifiedBusinessIds.Contains(imageMessageMetadata?.PhoneNumberId?.ToLower()))
                            {
                                if (imageMessage[0].From != string.Empty)
                                {
                                    await _whatsAppReceiveNotification.ReceivedImageMessage(imageMessage[0], image, imageMessageMetadata);
                                }
                            }

                        }

                        return new JsonResult(new
                        {
                            Message = "Image Message received"
                        });
                    }

                    if (messageType.Equals("audio"))
                    {
                        var audioMessageReceived = JsonConvert.DeserializeObject<AudioMessageReceived>(Convert.ToString(messageReceived)) as AudioMessageReceived;
                        audioMessage = new List<AudioMessage>(audioMessageReceived.Entry.SelectMany(x => x.Changes).SelectMany(x => x.Value.Messages));
                        audio = audioMessage.Select(x => x.Audio).FirstOrDefault(metadata => metadata != null);
                        audioMessageMetadata = audioMessageReceived.Entry.SelectMany(x => x.Changes).Select(x => x.Value.Metadata).FirstOrDefault(metadata => metadata != null);

                        //saving audio data into database
                        await _whatsAppReceiveNotification.ReceivedAudioMessage(audioMessage[0], audio, audioMessageMetadata);
                        var PhoneNumberId = _configuration["Facebook:PhoneNumberId"];
                        string Contact = (string)messageReceived["entry"][0]["changes"][0]["value"]["contacts"][0]["wa_id"] ?? string.Empty;
                        if ((string)changesResult["metadata"]["phone_number_id"] == PhoneNumberId)
                        {
                            var Data = _context.Contacts.Where(m => (m.CountryCode + m.Contact).Replace("+", "") == Contact && m.IsActive == true).OrderByDescending(m => m.CreatedDate);
                            audioMessageMetadata.PhoneNumberId = Data?.OrderByDescending(m => m.CreatedDate).FirstOrDefault()?.BusinessId.ToString() ?? string.Empty;
                            if (!MetaVerifiedBusinessIds.Contains(audioMessageMetadata?.PhoneNumberId?.ToLower()))
                            {
                                if (audioMessage[0].From != string.Empty)
                                {
                                    await _whatsAppReceiveNotification.ReceivedAudioMessage(audioMessage[0], audio, audioMessageMetadata);
                                }
                            }
                        }
                        return new JsonResult(new
                        {
                            Message = "Audio Message received"
                        });
                    }
                    if (messageType.Equals("video"))
                    {
                        var videoMessageReceived = JsonConvert.DeserializeObject<VideoMessageReceived>(Convert.ToString(messageReceived)) as VideoMessageReceived;
                        videoMessage = new List<VideoMessage>(videoMessageReceived.Entry.SelectMany(x => x.Changes).SelectMany(x => x.Value.Messages));
                        video = videoMessage.Select(x => x.Video).FirstOrDefault(metadata => metadata != null);
                        videoMessageMetadata = videoMessageReceived.Entry.SelectMany(x => x.Changes).Select(x => x.Value.Metadata).FirstOrDefault(metadata => metadata != null);

                        //saving document message into databse 
                        await _whatsAppReceiveNotification.ReceivedVideoMessage(videoMessage[0], video, videoMessageMetadata);
                        var PhoneNumberId = _configuration["Facebook:PhoneNumberId"];
                        string Contact = (string)messageReceived["entry"][0]["changes"][0]["value"]["contacts"][0]["wa_id"] ?? string.Empty;
                        if ((string)changesResult["metadata"]["phone_number_id"] == PhoneNumberId)
                        {
                            var Data = _context.Contacts.Where(m => (m.CountryCode + m.Contact).Replace("+", "") == Contact && m.IsActive == true).OrderByDescending(m => m.CreatedDate);
                            videoMessageMetadata.PhoneNumberId = Data?.OrderByDescending(m => m.CreatedDate).FirstOrDefault()?.BusinessId.ToString() ?? string.Empty;
                            if (!MetaVerifiedBusinessIds.Contains(videoMessageMetadata?.PhoneNumberId?.ToLower()))
                            {
                                if (videoMessage[0].From != string.Empty)
                                {
                                    await _whatsAppReceiveNotification.ReceivedVideoMessage(videoMessage[0], video, videoMessageMetadata);
                                }
                            }

                        }
                        return new JsonResult(new
                        {
                            Message = "Video Message received"
                        });
                    }
                    if (messageType.Equals("document"))
                    {
                        var documentMessageReceived = JsonConvert.DeserializeObject<DocumentMessageReceived>(Convert.ToString(messageReceived)) as DocumentMessageReceived;
                        documentMessage = new List<DocumentMessage>(documentMessageReceived.Entry.SelectMany(x => x.Changes).SelectMany(x => x.Value.Messages));
                        document = documentMessage.Select(x => x.Document).FirstOrDefault(metadata => metadata != null);
                        documentMessageMetadata = documentMessageReceived.Entry.SelectMany(x => x.Changes).Select(x => x.Value.Metadata).FirstOrDefault(metadata => metadata != null);

                        //saving document message into databse 
                        await _whatsAppReceiveNotification.ReceivedDocumentMessage(documentMessage[0], document, documentMessageMetadata);
                        var PhoneNumberId = _configuration["Facebook:PhoneNumberId"];
                        string Contact = (string)messageReceived["entry"][0]["changes"][0]["value"]["contacts"][0]["wa_id"] ?? string.Empty;
                        if ((string)changesResult["metadata"]["phone_number_id"] == PhoneNumberId)
                        {

                            var Data = _context.Contacts.Where(m => (m.CountryCode + m.Contact).Replace("+", "") == Contact && m.IsActive == true).OrderByDescending(m => m.CreatedDate);
                            documentMessageMetadata.PhoneNumberId = Data?.OrderByDescending(m => m.CreatedDate).FirstOrDefault()?.BusinessId.ToString() ?? string.Empty;
                            if (!MetaVerifiedBusinessIds.Contains(documentMessageMetadata?.PhoneNumberId?.ToLower()))
                            {
                                if (documentMessage[0].From != string.Empty)
                                {
                                    await _whatsAppReceiveNotification.ReceivedDocumentMessage(documentMessage[0], document, documentMessageMetadata);
                                }
                            }
                        }

                        return new JsonResult(new
                        {
                            Message = "Document Message received"
                        });

                    }

                }
            }
            catch (Exception ex)
            {
                return BadRequest(ex);
            }

            return Ok();
        }

        #region Send the webhook WhatsApp message to the other environment instead of production.
        private async Task SendWebhookMessage(dynamic message)
        {
            try
            {
                List<string> developmentBusinessIds = new() { "297817036758193", "537494702780895", "470271766163991", "285898731273254" };
                WAWebhookDto webhookDto = JsonConvert.DeserializeObject<WAWebhookDto>(message.ToString());
                var waId = webhookDto?.Entry?[0]?.Id ?? string.Empty;
                var isDevelopment = _environmentService.IsDevelopment;
                await _historyService.SaveInformationLogHistoryAsyn($"Pre:WebhookMessage WAWebhooksend", JsonConvert.SerializeObject(message), isDevelopment, $"Information{_environmentService.RequestHost}");
                if (!isDevelopment && developmentBusinessIds.Contains(waId))
                {
                    Console.WriteLine("1");
                    var environments = await _genericRepository.GetByObjectAsync<EnvironmentUrlEntity>(new Dictionary<string, object> { { "Environment", "dev" } });
                    foreach (var environment in environments.Where(x => x.IsActive))
                    {
                        var httpClient = new HttpClient();
                        var content = new StringContent(JsonConvert.SerializeObject(message), Encoding.UTF8, "application/json");

                        // Call the POST API
                        var response = await httpClient.PostAsync(environment.Url, content);
                        if (response.IsSuccessStatusCode)
                        {
                            Console.WriteLine($"2:{environment.Url}:{response}:{message}");
                            var responseString = await response.Content.ReadAsStringAsync();
                            await _historyService.SaveSuccessLogHistoryAsyn($"WebhookMessage WAWebhooksend", JsonConvert.SerializeObject(message), responseString, "Success");
                        }
                        else
                        {
                            Console.WriteLine("3");
                            var responseString = await response.Content.ReadAsStringAsync();
                            await _historyService.SaveInformationLogHistoryAsyn($"WebhookMessage WAWebhooksend", JsonConvert.SerializeObject(message), responseString, "Information");
                        }
                    }
                }
            }
            catch (Exception ex)
            {
                Console.WriteLine("4");
                Console.WriteLine(ex.Message);
                await _historyService.SaveErrorLogHistoryAsyn($"WebhookMessage WAWebhooksend", JsonConvert.SerializeObject(message), "Error", ex.Message, ex.StackTrace);
            }
        }
        #endregion

        #region Meta conversation cost
        private async Task MetaCostHandlerAsync(dynamic receivedMessage)
        {
            try
            {
                var webhookDto = JsonConvert.DeserializeObject<WAWebhookDto>(receivedMessage.ToString());
                var status = webhookDto.Entry?[0].Changes?[0]?.Value?.Statuses?[0];
                if (status?.Status == "delivered" && status?.Pricing?.Billable == true)
                {
                    var pricing = webhookDto.Entry?[0].Changes?[0]?.Value?.Statuses?[0].Pricing;
                    var metaData = webhookDto.Entry?[0].Changes?[0].Value?.Metadata;
                    using (var scope = _serviceScopeFactory.CreateScope())
                    {
                        var metaCostInfoService = scope.ServiceProvider.GetRequiredService<IMetaCostInfoService>();
                        var genericRepository = scope.ServiceProvider.GetRequiredService<IGenericRepository>();
                        var inboxService = scope.ServiceProvider.GetRequiredService<IInboxService>();
                        var conversation = (await genericRepository.GetByObjectAsync<Conversations>(new Dictionary<string, object> { { "WhatsAppMessageId", status.Id } }))?.FirstOrDefault();
                        if (conversation != null)
                        {
                            string category = string.Empty;
                            if (string.IsNullOrEmpty(conversation.TemplateBody) || conversation.ReferenceId == null)
                            {
                                category = "SERVICE";
                            }
                            else
                            {
                                var waCategory = await inboxService.GetTemplateCategoryAsync(conversation.ReferenceId);
                                if (waCategory == WATemplateCategory.None)
                                    category = "SERVICE";
                                else
                                    category = waCategory.ToString();
                            }
                            var metaBusinessDetail = (await genericRepository.GetByObjectAsync<BusinessDetailsMeta>(new Dictionary<string, object> { { "PhoneNumberID", metaData.PhoneNumberId } }))?
                                .FirstOrDefault();
                            var cost = await metaCostInfoService.GetCostAsync(metaBusinessDetail.BusinessId, status.RecipientId, category);
                            var wallet = (await _genericRepository.GetByObjectAsync<UserWalletEntity>(new() { { "CompanyId", metaBusinessDetail.BusinessId } }))?.FirstOrDefault();
                            ConversationAnalyticsEntity entity = new ConversationAnalyticsEntity()
                            {
                                WAMessageId = status.Id,
                                CompanyId = metaBusinessDetail.BusinessId,
                                Conversation = 1,
                                ConversationCategory = category,
                                ConversationType = "REGULAR",
                                Cost = cost,
                                CreatedAt = DateTime.UtcNow,
                                UpdatedAt = DateTime.UtcNow,
                                StartDate = DateTime.UtcNow,
                                EndDate = DateTime.UtcNow,
                                MobileNumber = status.RecipientId,
                                CurrentBalance = (wallet?.Balance ?? 0 - cost)
                            };
                            var isInsert = await inboxService.SaveConversationCostAsync(entity);
                            if (isInsert && wallet != null)
                            {
                                wallet.Balance = wallet.Balance - cost;
                                wallet.ExpectedWalletBallance -= cost;
                                await _genericRepository.UpdateRecordAsync("UserWalletEntities", StringHelper.GetPropertyNames<UserWalletEntity>(false), wallet, new() { { "CompanyId", wallet.CompanyId } });
                            }
                        }
                    }
                }
            }
            catch (Exception ex)
            {

            }
        }
        #endregion

    }
}
