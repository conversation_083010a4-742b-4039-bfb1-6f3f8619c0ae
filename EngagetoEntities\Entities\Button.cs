﻿using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace EngagetoEntities.Entities
{
    [Table("ButtonDetails")]
    public class Button
    {
        [Key]
        public int Id { get; set; }
        public Guid TemplateId { get; set; }
        [ForeignKey("TemplateId")]
        public Template? Template { get; set; }
        public string? ButtonType { get; set; }
        public string? ButtonName { get; set; }
        public string? ButtonValue { get; set; }
    }
}
