﻿using EngagetoEntities.Enums;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace EngagetoDapper.Data.Dtos
{
    public class CampaignTrackerStatus
    {
        public string ContactId { get; set; }
        public string? WhatsAppMessageId { get; set; }
        public ConvStatus? ConvStatus { get; set; }
        public string? ErrorMessage { get; set; }
    }
}
