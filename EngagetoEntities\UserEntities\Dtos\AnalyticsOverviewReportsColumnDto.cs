﻿using Microsoft.EntityFrameworkCore.Query.Internal;
//using OfficeOpenXml;
//using OfficeOpenXml.Style;
using System.Drawing;
using System.Reflection.Metadata.Ecma335;
//using LicenseContext = OfficeOpenXml.LicenseContext;

namespace EngagetoEntities.UserEntities.Dtos
{
    public static class AnalyticsReportsColumnDto
    {
        private static readonly string[] overViewColumns = new string[]
        {
            "Customer Country Code",
            "Customer Phone Number",
            "First Message Date",
            "First Message By",
            "Agent First Message Date",
            "Number of Customer Messages",
            "Number of Agent Messages",
            "Conversation Resolved Date",
            "Agent First Response Time (In Minutes)",
            "Average Response Time (in Minutes)",
            "Resoultion Response Time (In Minutes)",
            "Conversation Resolved Agent Eamil",
          //  "Conversation Identifier",
            "Conversation's Current Status"
        };
        /*  private static readonly string[] agentPerformanceColumns = new string[]
          {
              "Customer Country Code",
              "Customer Phone Number",
              "Assigned Agent",
              "First Customer Message Datetime",
              "Assigned Datetime",
              "Agent 1st Respnse Datetime",
              "Reassigned Datetime",
              "Resolved Datetime",
              "Number of Customer Messages",
              "Number of Agent Messages",
              "Agent First Response Time",
              "Average Response Time",
              "Resolved Time"
          };*/
        private static readonly string[] agentPerformanceColumns = new string[]
        {
            "Agent Name",
            "Assigned Count",
            "Reassigned Count",
            "Responded Count",
            "Resolved Count",
            "Resolution Time",
            "Average Response Time"
        };


        private readonly static Dictionary<string, string[]> inboxAnlayticsColumns = new Dictionary<string, string[]>()
        {
            { "Chat",new string[]{ "Open chats","Pending","Resolved","Expired"/*,"Unassigned"*/} },
            { "Ticket Total",new string[]{ "Status", "Total messages" } },
            { "Top Users",new string[]{ "User Name","No. of Charts"} },
            { "Ticket Status",new string[]{ "Months", "Open Tickets","Pending Tickets", "Solved Tickets","Expired Tickets"} }
        };

        public static string GetOverViewColumnValue(int index) => overViewColumns[index];
        public static int GetOverViewColumnCount() => overViewColumns.Length;
        public static string GetAgentPerformanceColumnValue(int index) => agentPerformanceColumns[index];
        public static int GetAgentPerformanceColumnCount() => agentPerformanceColumns.Length;
        public static Dictionary<string, string[]> GetInboxAnalyticsColumns() => inboxAnlayticsColumns;
    }
}
