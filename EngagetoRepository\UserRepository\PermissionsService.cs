﻿using Amazon.Runtime.Internal.Transform;
using iTextSharp.text;
using Microsoft.EntityFrameworkCore;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using EngagetoContracts.UserContracts;
using EngagetoEntities.UserEntities.Models;
using EngagetoEntities.Entities;

namespace EngagetoRepository.UserRepository
{
    public class PermissionsService : IPermissionsService
    {
        private readonly ApplicationDBContext _context;

        public PermissionsService(ApplicationDBContext context)
        {
            _context = context;
        }

        public async Task<IEnumerable<ViewPermissionDto>> GetAllPermissionsAsync(Guid UserId, string searchKeyword = null)
        {
            IQueryable<ViewPermissionDto> query = _context.Permissions
                .Select(p => new ViewPermissionDto
                {
                    Id = p.Id,
                    Name = p.Name,

                });

            if (!string.IsNullOrEmpty(searchKeyword))
            {

                query = query.Where(p => p.Id.ToString().Contains(searchKeyword) || p.Name.Contains(searchKeyword));
            }

            return await query.ToListAsync();
        }

        public async Task<Guid> CreatePermissionAsync(Guid currentUserId, PermissionDto newPermission)
        {
            var currentUserRoles = await _context.UserRoles
                .Where(ur => ur.Id == currentUserId)
                .Select(ur => ur.Role.Name)
                .ToListAsync();

            if (!currentUserRoles.Contains(RoleConstants.PlatformOwner) && !currentUserRoles.Contains(RoleConstants.PlatformAdmin))
            {
                throw new UnauthorizedAccessException("Only admin or owner can create permissions.");
            }

            var permission = new Permissions
            {
                Id = Guid.NewGuid(),
                Name = newPermission.Name
            };

            _context.Permissions.Add(permission);
            await _context.SaveChangesAsync();

            var adminRoleId = await _context.Roles.Where(role => role.Name == RoleConstants.PlatformAdmin).Select(role => role.Id).FirstOrDefaultAsync();
            var ownerRoleId = await _context.Roles.Where(role => role.Name == RoleConstants.PlatformOwner).Select(role => role.Id).FirstOrDefaultAsync();

            if (currentUserRoles.Contains(RoleConstants.PlatformAdmin))
            {
                var adminPermission = new AssignPermissions
                {
                    RoleId = adminRoleId,
                    PermissionId = permission.Id,
                    Status = true
                };
                _context.AssignPermissionsToRoleIds.Add(adminPermission);
            }

            if (currentUserRoles.Contains(RoleConstants.PlatformOwner))
            {
                var ownerPermission = new AssignPermissions
                {
                    RoleId = ownerRoleId,
                    PermissionId = permission.Id,
                    Status = true
                };
                _context.AssignPermissionsToRoleIds.Add(ownerPermission);
            }

            await _context.SaveChangesAsync();

            return permission.Id;
        }

        public async Task<bool> UpdatePermissionAsync(Guid currentUserId, Guid permissionId, PermissionDto updatedPermission)
        {

            var currentUserRoles = await _context.UserRoles.Where(ur => ur.Id == currentUserId).Select(ur => ur.Role.Id.ToString()).ToListAsync();
            if (!currentUserRoles.Contains(RoleConstants.PlatformAdmin) && !currentUserRoles.Contains(RoleConstants.PlatformOwner))
            {
                throw new UnauthorizedAccessException("Only admin or owner can update permissions.");
            }


            var permission = await _context.Permissions.FindAsync(permissionId);
            if (permission == null)
            {
                return false;
            }


            permission.Name = updatedPermission.Name;


            await _context.SaveChangesAsync();
            return true;
        }

        public async Task<bool> DeletePermissionAsync(Guid currentUserId, Guid permissionId)
        {

            var currentUserRoles = await _context.UserRoles.Where(ur => ur.Id == currentUserId).Select(ur => ur.Role.Id.ToString()).ToListAsync();
            if (!currentUserRoles.Contains(RoleConstants.PlatformOwner) && !currentUserRoles.Contains(RoleConstants.PlatformAdmin))
            {
                throw new UnauthorizedAccessException("Only admin or owner can delete permissions.");
            }


            var permission = await _context.Permissions.FindAsync(permissionId);
            if (permission == null)
            {
                return false;
            }


            _context.Permissions.Remove(permission);
            await _context.SaveChangesAsync();
            return true;
        }


        public async Task<bool> CreateAssignPermissionAsync(Guid currentUserId, AssignPermissionsToRoleIdDto assignmentDto)
        {
            var currentUserRoles = await _context.UserRoles
                .Where(ur => ur.Id == currentUserId)
                .Select(ur => ur.Role.Id.ToString())
                .ToListAsync();

            if (!currentUserRoles.Contains(RoleConstants.PlatformAdmin) && !currentUserRoles.Contains(RoleConstants.PlatformOwner))
            {
                throw new UnauthorizedAccessException("Only admin or owner can assign permissions.");
            }

            if (!Guid.TryParse(assignmentDto.RoleId, out Guid roleId))
            {
                throw new InvalidOperationException($"Invalid RoleId: '{assignmentDto.RoleId}'");
            }

            var roleExists = await _context.Roles.AnyAsync(r => r.Id == roleId);
            if (!roleExists)
            {
                throw new InvalidOperationException($"Role with ID '{roleId}' not found.");
            }

            var userInRoleExists = await _context.UserRoles
                .AnyAsync(ur => ur.RoleId == roleId);

            if (!userInRoleExists)
            {
                throw new InvalidOperationException($"User with ID  is not associated with the specified role.");
            }

            var permissionExists = await _context.Permissions.AnyAsync(p => p.Id == assignmentDto.PermissionId);
            if (!permissionExists)
            {
                throw new InvalidOperationException($"Permission with ID '{assignmentDto.PermissionId}' not found.");
            }

            var assignPermission = new AssignPermissions
            {
                RoleId = roleId,
                PermissionId = assignmentDto.PermissionId,
                Status = assignmentDto.Status
            };

            _context.AssignPermissionsToRoleIds.Add(assignPermission);
            await _context.SaveChangesAsync();

            return true;
        }
        public async Task<bool> UpdatePermissionsAndStatusAsync(Guid currentUserId, Guid roleId, string companyId, List<AssignPermissionsDto> updatedAssignPermissions)
        {
            try
            {
                var currentUserCompanyId = await _context.Ahex_CRM_Users
                        .Where(u => u.Id == currentUserId)
                        .Select(u => u.CompanyId)
                        .FirstOrDefaultAsync();

                if (currentUserCompanyId != "99969011-7B1D-4C2D-92A6-FBA9CA31A261".ToLower())
                {
                    if (currentUserCompanyId.ToString() != companyId.ToLower())
                    {
                        throw new UnauthorizedAccessException("Current user does not have permission to update permissions for the provided company.");
                    }
                }

                var currentUserRoles = await _context.UserRoles
                    .Where(ur => ur.Id == currentUserId)
                    .Select(ur => ur.Role.Id)
                    .ToListAsync();


                if (!HasPermissionToUpdate(currentUserRoles, roleId, currentUserCompanyId))
                {
                    throw new UnauthorizedAccessException("Insufficient permissions to update permissions.");
                }


                var assignPermissions = await _context.AssignPermissionsToRoleIds
                    .Where(ap => ap.RoleId == roleId && ap.CompanyId == companyId)
                    .ToListAsync();

                Dictionary<string, List<string>> rolePermissions = GetRolePermissionsFromDatabase(roleId.ToString(), companyId);
                foreach (var role in rolePermissions[roleId.ToString()])
                {
                    roleId = Guid.Parse(role);

                    foreach (var updatedPermission in updatedAssignPermissions)
                    {

                        var permission = await _context.Permissions.FirstOrDefaultAsync(p => p.Name == updatedPermission.Name);

                        if (permission != null)
                        {
                            var assignPermission = await _context.AssignPermissionsToRoleIds
                                .FirstOrDefaultAsync(ap => ap.PermissionId == permission.Id && ap.RoleId == roleId && ap.CompanyId == companyId);


                            if (assignPermission != null)
                            {
                                assignPermission.PermissionId = permission.Id;
                                assignPermission.Status = updatedPermission.Status;

                            }
                            else
                            {
                                assignPermission = new AssignPermissions
                                {
                                    PermissionId = permission.Id,
                                    RoleId = roleId,
                                    CompanyId = companyId,
                                    Status = updatedPermission.Status,

                                    Access = updatedPermission.Status ? "enable" : "disable"
                                };
                                _context.AssignPermissionsToRoleIds.Add(assignPermission);
                            }
                        }
                        else
                        {
                            throw new InvalidOperationException($"Permission with name '{updatedPermission.Name}' not found.");
                        }
                    }
                }

                await _context.SaveChangesAsync();
                return true;
            }
            catch (Exception)
            {

                return false;
            }
        }

        private bool HasPermissionToUpdate(List<Guid> currentUserRoles, Guid roleId, string companyId)
        {
            Dictionary<string, List<string>> rolePermissions = GetRolePermissionsFromDatabase(roleId.ToString(), companyId);

            foreach (var role in currentUserRoles)
            {
                var isRolePresent = rolePermissions.ContainsKey(role.ToString());
                if (isRolePresent) { return true; }
            }
            return false;
        }

        private Dictionary<string, List<string>> GetRolePermissionsFromDatabase(string roleId, string companyId)
        {
            Dictionary<string, List<string>> rolePermissions = new Dictionary<string, List<string>>();

            var permissionsFromDatabase = _context.RolePermissions
                .Where(rp => rp.RoleId == roleId && rp.CompanyId == companyId)
                .ToList();

            foreach (var permission in permissionsFromDatabase)
            {
                string roleId1 = permission.RoleId.ToString().ToLower();
                string accessToRoleId = permission.AccessToRoleId.ToString().ToLower();

                if (!rolePermissions.ContainsKey(roleId1))
                {
                    rolePermissions[roleId1] = new List<string>();
                }

                rolePermissions[roleId1].Add(accessToRoleId);
            }

            return rolePermissions;
        }

        private string GetRoleNameById(Guid roleId)
        {

            var role = _context.Roles.FirstOrDefault(r => r.Id == roleId);
            return role?.Name;
        }

        public async Task<bool> DeleteAssignPermissionAsync(Guid currentUserId, AssignPermissionsToRoleIdDto assignmentDto)
        {
            var currentUserRoles = await _context.UserRoles
                .Where(ur => ur.Id == currentUserId)
                .Select(ur => ur.Role.Name)
                .ToListAsync();

            if (!currentUserRoles.Contains(RoleConstants.PlatformOwner) && !currentUserRoles.Contains(RoleConstants.PlatformAdmin))
            {
                throw new UnauthorizedAccessException("Only admin or owner can delete permissions.");
            }

            var assignPermission = await _context.AssignPermissionsToRoleIds
                .FirstOrDefaultAsync(ap => ap.RoleId.ToString() == assignmentDto.RoleId
                    && ap.PermissionId == assignmentDto.PermissionId);

            if (assignPermission == null)
            {
                return false;
            }

            assignPermission.Status = false;
            await _context.SaveChangesAsync();

            return true;
        }

        public async Task<List<AssignPermissionsToRoleIdDto>> GetAssignPermissionsAsync(Guid currentUserId)
        {
            var currentUserRoles = await _context.UserRoles
                .Where(ur => ur.Id == currentUserId)
                .Select(ur => ur.Role.Name)
                .ToListAsync();

            if (!currentUserRoles.Contains(RoleConstants.PlatformAdmin) && !currentUserRoles.Contains(RoleConstants.PlatformOwner))
            {
                throw new UnauthorizedAccessException("Only admin or owner can get permissions.");
            }

            var assignPermissions = await _context.AssignPermissionsToRoleIds
                .Where(ap => currentUserRoles.Contains(ap.RoleId.ToString()))
                .ToListAsync();

            var assignPermissionsDto = assignPermissions.Select(ap => new AssignPermissionsToRoleIdDto
            {
                RoleId = ap.RoleId.ToString(),
                PermissionId = ap.PermissionId,
                Status = ap.Status
            }).ToList();

            return assignPermissionsDto;
        }

        public async Task<object> GetPermissionsAndAssignPermissionsAsync(Guid roleId, string companyId)
        {
            var result = await (
                from p in _context.Permissions
                join ap in _context.AssignPermissionsToRoleIds on p.Id equals ap.PermissionId
                join r in _context.Roles on ap.RoleId equals r.Id
                join l in _context.Roles on r.Level equals (l.Level + 1) into lj
                from l in lj.DefaultIfEmpty()
                where ap.RoleId == roleId && ap.CompanyId == companyId
                orderby p.Name
                select new
                {
                    RoleName = r.Name,
                    PermissionName = p.Name,
                    Status = ap.Status ? 1 : 0,
                    Section = p.Section,
                    Access = ap.RoleId == roleId ? ap.Status : (
                        from rl in _context.AssignPermissionsToRoleIds
                        where rl.RoleId == roleId && rl.PermissionId == ap.PermissionId
                        select rl.Status
                    ).FirstOrDefault()
                }
            ).ToListAsync();
            return result;
        }

        public async Task<object> GetMenuAndRoleRelationshipAsync(Guid roleId, string companyId)
        {
            var roleIdString = roleId.ToString();

            var result = await (
                from m in _context.MenuDetails
                join mr in _context.MenuwithRoleRelationDetails on m.MenuId equals mr.MenuId
                join r in _context.Roles on mr.RoleId equals r.Id.ToString()
                join l in _context.Roles on r.Level equals (l.Level + 1) into lj
                from l in lj.DefaultIfEmpty()
                join parentMenu in _context.MenuDetails on m.ParentMenuId equals parentMenu.MenuId into parentMenuGroup
                from parentMenu in parentMenuGroup.DefaultIfEmpty()
                join grandParentMenu in _context.MenuDetails on parentMenu.ParentMenuId equals grandParentMenu.MenuId into grandParentMenuGroup
                from grandParentMenu in grandParentMenuGroup.DefaultIfEmpty()
                where mr.RoleId == roleIdString && mr.CompanyId == companyId

                select new
                {
                    RoleName = r.Name,
                    MenuName = m.MenuName,
                    ParentMenuName = parentMenu.MenuName,
                    GrandParentMenuName = grandParentMenu.MenuName,
                    Status = mr.Status ? 1 : 0,
                    Access = mr.RoleId == roleIdString ? mr.Status : (
                        from rm in _context.MenuwithRoleRelationDetails
                        where rm.RoleId == roleIdString && rm.MenuId == mr.MenuId
                        select rm.Status
                    ).FirstOrDefault()
                }
            ).ToListAsync();
            return result;
        }





        public async Task<bool> AssignPermissionsToClientAsync(Guid currentUserId, Guid roleId, string companyId, List<AssignPermissionsDto> updatedAssignPermissions)
        {
            try
            {
                var currentUserCompanyId = await _context.Ahex_CRM_Users
                    .Where(u => u.Id == currentUserId)
                    .Select(u => u.CompanyId)
                    .FirstOrDefaultAsync();

                var currentUserRoles = await _context.UserRoles
                    .Where(ur => ur.Id == currentUserId)
                    .Select(ur => ur.RoleId)
                    .ToListAsync();

                var currentUserRolesNames = await _context.UserRoles
                    .Where(ur => ur.Id == currentUserId)
                    .Select(ur => ur.Role.Name)
                    .ToListAsync();

                bool isOwner = currentUserRolesNames.Contains(RoleConstants.Owner);
                if (isOwner && currentUserCompanyId != RoleConstants.CompanyId)
                {
                    return false;
                }

                if (!HasPermissionToUpdate(currentUserRoles, roleId, currentUserCompanyId))
                {
                    throw new UnauthorizedAccessException("Insufficient permissions to update permissions.");
                }


                var role = await _context.Roles.FirstOrDefaultAsync(r => r.Id == roleId);
                if (role == null || role.Name != RoleConstants.Owner || role.Id.ToString() == currentUserCompanyId)
                {
                    return false;
                }

                var assignPermissions = await _context.AssignPermissionsToRoleIds
                    .Where(ap => ap.RoleId == roleId && ap.CompanyId == companyId)
                    .ToListAsync();

                foreach (var updatedPermission in updatedAssignPermissions)
                {
                    var permission = await _context.Permissions.FirstOrDefaultAsync(p => p.Name == updatedPermission.Name);

                    if (permission != null)
                    {
                        var assignPermission = assignPermissions.FirstOrDefault(ap => ap.PermissionId == permission.Id);

                        if (assignPermission != null)
                        {
                            assignPermission.Status = updatedPermission.Status;
                            assignPermission.ModifiedBy = roleId.ToString();
                            assignPermission.Access = updatedPermission.Status ? "enable" : "disable";
                        }
                        else
                        {
                            assignPermission = new AssignPermissions
                            {
                                PermissionId = permission.Id,
                                RoleId = roleId,
                                CompanyId = companyId,
                                Status = updatedPermission.Status,
                                ModifiedBy = roleId.ToString(),
                                Access = updatedPermission.Status ? "enable" : "disable"
                            };
                            _context.AssignPermissionsToRoleIds.Add(assignPermission);
                        }
                    }
                    else
                    {
                        throw new InvalidOperationException($"Permission with name '{updatedPermission.Name}' not found.");
                    }
                }

                await _context.SaveChangesAsync();
                return true;
            }
            catch (Exception ex)
            {
                return false;
            }
        }


        public Dictionary<string, List<Dictionary<string, List<string>>>> GetMenuHierarchy(string roleId, string companyId)
        {
            Dictionary<string, List<Dictionary<string, List<string>>>> menuHierarchy = new Dictionary<string, List<Dictionary<string, List<string>>>>();

            var menuIds = _context.MenuwithRoleRelationDetails
                                                            .Where(m => m.RoleId == roleId && m.Status && m.CompanyId == companyId)
                                                            .Select(m => m.MenuId)
                                                            .ToList();

            foreach (var menuId in menuIds)
            {
                var mainMenu = _context.MenuDetails.FirstOrDefault(m => m.MenuId == menuId && m.ParentMenuId == 0);

                if (mainMenu != null)
                {
                    List<Dictionary<string, List<string>>> childMenuList = new List<Dictionary<string, List<string>>>();

                    var childMenuIds = _context.MenuDetails
                                                        .Where(m => m.ParentMenuId == mainMenu.MenuId && menuIds.Contains(m.MenuId))
                                                        .Select(m => m.MenuId)
                                                        .ToList();

                    foreach (var childMenuId in childMenuIds)
                    {
                        Dictionary<string, List<string>> submenuDict = new Dictionary<string, List<string>>();

                        var childMenu = _context.MenuDetails.FirstOrDefault(m => m.MenuId == childMenuId);

                        if (childMenu != null)
                        {
                            var submenuNames = _context.MenuDetails
                                                                .Where(m => m.ParentMenuId == childMenuId && menuIds.Contains(m.MenuId))
                                                                .Select(m => m.MenuName)
                                                                .ToList();

                            submenuDict[childMenu.MenuName] = submenuNames;

                            childMenuList.Add(submenuDict);
                        }
                    }

                    menuHierarchy[mainMenu.MenuName] = childMenuList;
                }
            }

            return menuHierarchy;
        }


        public Dictionary<string, List<MainMenu>> GetMenuHierarchy1(string roleId, string companyId)
        {
            Dictionary<string, List<MainMenu>> menuHierarchy = new Dictionary<string, List<MainMenu>>();

            var roleInfo = _context.Roles.FirstOrDefault(r => r.Id.ToString() == roleId && r.CompanyId == companyId);

            var menuInfos = _context.MenuwithRoleRelationDetails
                                    .Where(m => m.RoleId == roleId && m.CompanyId == companyId)
                                    .Select(m => new { m.MenuId, m.Status })
                                    .Distinct()
                                    .ToList();

            foreach (var menuInfo in menuInfos)
            {
                var mainMenu = _context.MenuDetails.FirstOrDefault(m => m.MenuId == menuInfo.MenuId && m.ParentMenuId == 0);

                if (mainMenu != null)
                {
                    MainMenu mainMenuInfo = new MainMenu
                    {
                        SubMenus = new List<SubMenu>(),
                        MenuName = mainMenu.MenuName,
                        RoleName = roleInfo != null ? roleInfo.Name : "",
                        Access = GetAccessForMenu(menuInfo.MenuId, roleId, companyId)
                    };


                    mainMenuInfo.Status = GetMenuStatus(menuInfo.MenuId, roleId, companyId);

                    var childMenuIds = _context.MenuDetails
                                                .Where(m => m.ParentMenuId == mainMenu.MenuId)
                                                .Select(m => m.MenuId)
                                                .ToList();

                    foreach (var childMenuId in childMenuIds)
                    {
                        var childmenu = _context.MenuDetails.FirstOrDefault(m => m.MenuId == childMenuId);
                        SubMenu childMenuInfo = new SubMenu
                        {
                            Actions = new List<ActionMenu>(),
                            MenuName = childmenu.MenuName,
                            RoleName = roleInfo != null ? roleInfo.Name : "",
                            Access = GetAccessForMenu(childMenuId, roleId, companyId)
                        };


                        childMenuInfo.Status = GetMenuStatus(childMenuId, roleId, companyId);

                        var submenuIds = _context.MenuDetails
                                                 .Where(m => m.ParentMenuId == childMenuId)
                                                 .Select(m => m.MenuId)
                                                 .ToList();

                        foreach (var submenuId in submenuIds)
                        {
                            ActionMenu actionMenu = new ActionMenu();
                            var submenu = _context.MenuDetails.FirstOrDefault(m => m.MenuId == submenuId);

                            if (submenu != null)
                            {
                                actionMenu.ActionName = submenu.MenuName;
                                actionMenu.RoleName = roleInfo != null ? roleInfo.Name : "";
                                actionMenu.Access = GetAccessForMenu(submenuId, roleId, companyId);


                                actionMenu.Status = GetMenuStatus(submenuId, roleId, companyId);

                                childMenuInfo.Actions.Add(actionMenu);
                            }
                        }

                        mainMenuInfo.SubMenus.Add(childMenuInfo);
                    }

                    if (!menuHierarchy.ContainsKey(mainMenuInfo.MenuName))
                    {
                        menuHierarchy[mainMenuInfo.MenuName] = new List<MainMenu>();
                    }
                    menuHierarchy[mainMenuInfo.MenuName].Add(mainMenuInfo);
                }
            }

            return menuHierarchy;
        }

        private int GetAccessForMenu(int menuId, string roleId, string companyId)
        {
            var roleIdString = roleId.ToString();

            var access = _context.MenuwithRoleRelationDetails
                .Where(mr => mr.RoleId == roleIdString && mr.MenuId == menuId && mr.CompanyId == companyId)
                .Select(mr => mr.Status)
                .FirstOrDefault();

            if (access)
            {
                var currentRoleLevel = _context.Roles
                    .Where(r => r.Id.ToString() == roleIdString && r.CompanyId == companyId)
                    .Select(r => r.Level)
                    .FirstOrDefault();

                var higherLevelRoleId = _context.Roles
                    .Where(r => r.Level == currentRoleLevel + 1 && r.CompanyId == companyId)
                    .Select(r => r.Id)
                    .FirstOrDefault();

                if (higherLevelRoleId != default)
                {
                    var higherLevelRoleStatus = _context.MenuwithRoleRelationDetails
                        .Where(mr => mr.RoleId == higherLevelRoleId.ToString() && mr.CompanyId == companyId && mr.MenuId == menuId)
                        .Select(mr => mr.Status)
                        .FirstOrDefault();

                    return higherLevelRoleStatus ? 1 : 0;
                }
            }
            return 0;
        }


        private bool GetMenuStatus(int menuId, string roleId, string companyId)
        {
            var menuStatus = _context.MenuwithRoleRelationDetails
                                    .FirstOrDefault(m => m.MenuId == menuId && m.RoleId == roleId && m.CompanyId == companyId)?
                                    .Status;

            return menuStatus ?? false;
        }



        /* public async Task<bool> UpdateMenuStatusAsync(string roleId, string companyId, string menuName, bool status, Guid currentUserId)
         {
             try
             {   
                 var currentUserCompanyId = await _context.Ahex_CRM_Users
                     .Where(u => u.Id == currentUserId)
                     .Select(u => u.CompanyId)
                     .FirstOrDefaultAsync();

                 if (currentUserCompanyId != RoleConstants.CompanyId)
                 {
                     if (currentUserCompanyId != companyId.ToLower())
                     {

                         throw new UnauthorizedAccessException("Current user does not have permission to update menus for the provided company.");
                     }
                 }


              *//*   var currentUserRoles = await _context.UserRoles
                     .Where(ur => ur.Id == currentUserId)
                     .Select(ur => ur.RoleId)
                     .ToListAsync();*//*

                 var passedRoleId = roleId;
                 if (!HasPermissionToUpdate(roleId, currentUserCompanyId))
                 {
                     throw new UnauthorizedAccessException("Insufficient permissions to update menus.");
                 }


                 var menuId = await GetMenuIdByNameAsync(menuName);
                 if (!menuId.HasValue)
                 {
                     return false;
                 }


                 if (IsMainMenu(menuId.Value))
                 {

                     bool updated = await UpdateMenuStatusForRole(menuId.Value, roleId, companyId, status);
                     if (updated && !status)
                     {

                         await UpdateSubmenusAndSubsubmenusStatusAsync(menuId.Value, roleId, companyId, status);
                     }
                     return updated;
                 }
                 else if (await IsSubMenu(menuId.Value))
                 {

                     var mainMenuId = await GetMainMenuIdAsync(menuId.Value);
                     var mainMenuStatus = await GetMenuStatusAsync(mainMenuId.Value, roleId, companyId);
                     if (mainMenuStatus.HasValue && mainMenuStatus.Value)
                     {
                         bool updated = await UpdateMenuStatusForRole(menuId.Value, roleId, companyId, status);
                         if (updated && !status)
                         {

                             await UpdateSubsubmenusStatusAsync(menuId.Value, roleId, companyId, status);
                         }
                         return updated;
                     }
                     else
                     {
                         return false;
                     }
                 }
                 else 
                 {

                     var subMenuId = await GetParentMenuIdAsync(menuId.Value);
                     var mainMenuId = await GetMainMenuIdAsync(subMenuId.Value);

                     var subMenuStatus = await GetMenuStatusAsync(subMenuId.Value, roleId, companyId);
                     var mainMenuStatus = await GetMenuStatusAsync(mainMenuId.Value, roleId, companyId);

                     if (subMenuStatus.HasValue && subMenuStatus.Value && mainMenuStatus.HasValue && mainMenuStatus.Value)
                     {
                         bool updated = await UpdateMenuStatusForRole(menuId.Value, roleId, companyId, status);
                         return updated;
                     }
                     else
                     {
                         return false;
                     }
                 }
             }
             catch (Exception ex)
             {
                 Console.WriteLine($"Exception: {ex.Message}");
                 return false;
             }
         }

         private async Task<bool> UpdateMenuStatusForRole(int menuId, string roleId, string companyId, bool status)
         {
             try
             {
                 if (IsMainMenu(menuId))
                 {

                     var accessToRoleIds = await _context.RolePermissions
                          .Where(rp => rp.RoleId == roleId && rp.CompanyId == companyId)
                         .Select(rp => rp.AccessToRoleId)
                         .ToListAsync();

                     foreach (var accessToRoleId in accessToRoleIds)
                     {
                         var menuRoleRelationship = await _context.MenuwithRoleRelationDetails
                             .FirstOrDefaultAsync(mrr => mrr.MenuId == menuId && mrr.RoleId == accessToRoleId.ToString() && mrr.CompanyId == companyId);

                         if (menuRoleRelationship != null)
                         {
                             menuRoleRelationship.Status = status;
                         }
                         else
                         {
                             return false;
                         }
                     }

                     await _context.SaveChangesAsync();

                     if (!status)
                     {
                         await UpdateSubmenusAndSubsubmenusStatusAsync(menuId, roleId, companyId, status);
                     }

                     return true;
                 }
                 else if (await IsSubMenu(menuId))
                 {
                         var accessToRoleIds = await _context.RolePermissions
                             .Where(rp => rp.RoleId== roleId && rp.CompanyId  == companyId)
                             .Select(rp => rp.AccessToRoleId)
                             .ToListAsync();

                         foreach (var accessToRoleId in accessToRoleIds)
                     {
                                 var mainMenuId = await GetMainMenuIdAsync(menuId);
                                 var mainMenuStatus = await GetMenuStatusAsync(mainMenuId.Value, roleId, companyId);
                                 var mainMenuRoleStatus = await GetMenuStatusAsync(mainMenuId.Value, accessToRoleId, companyId);
                             if (mainMenuRoleStatus.HasValue && mainMenuRoleStatus.Value)
                             {
                                 var menuRoleRelationship = await _context.MenuwithRoleRelationDetails
                                     .FirstOrDefaultAsync(mrr => mrr.MenuId == menuId && mrr.RoleId == accessToRoleId.ToString() && mrr.CompanyId == companyId);

                                 if (menuRoleRelationship != null)
                                 {
                                     menuRoleRelationship.Status = status;
                                 }
                                 else
                                 {
                                     return false;
                                 }
                             }
                             else
                             {
                                 return false;
                             }
                         }

                         await _context.SaveChangesAsync();

                         if (!status)
                         {
                             await UpdateSubsubmenusStatusAsync(menuId, roleId, companyId, status);
                         }

                         return true;

                 }
                 else 
                 {

                         var accessToRoleIds = await _context.RolePermissions
                             .Where(rp => rp.RoleId == roleId && rp.CompanyId == companyId)
                             .Select(rp => rp.AccessToRoleId)
                             .ToListAsync();

                         foreach (var accessToRoleId in accessToRoleIds)
                     {
                         var subMenuId = await GetParentMenuIdAsync(menuId);
                         var mainMenuId = await GetMainMenuIdAsync(subMenuId.Value);

                         var mainMenuStatus = await GetMenuStatusAsync(mainMenuId.Value, roleId, companyId);
                         var subMenuStatus = await GetMenuStatusAsync(subMenuId.Value, roleId, companyId);
                         var mainMenuRoleStatus = await GetMenuStatusAsync(mainMenuId.Value, accessToRoleId.ToString(), companyId);
                             var subMenuRoleStatus = await GetMenuStatusAsync(subMenuId.Value, accessToRoleId.ToString(), companyId);

                             if (mainMenuRoleStatus.HasValue && mainMenuRoleStatus.Value && subMenuRoleStatus.HasValue && subMenuRoleStatus.Value)
                             {
                                 var menuRoleRelationship = await _context.MenuwithRoleRelationDetails
                                     .FirstOrDefaultAsync(mrr => mrr.MenuId == menuId && mrr.RoleId == accessToRoleId.ToString() && mrr.CompanyId == companyId);

                                 if (menuRoleRelationship != null)
                                 {
                                     menuRoleRelationship.Status = status;
                                 }
                                 else
                                 {
                                     return false;
                                 }
                             }
                             else
                             {
                                 return false;
                             }
                         }

                         await _context.SaveChangesAsync();

                         return true;

                 }
             }
             catch (Exception ex)
             {
                 Console.WriteLine($"Exception: {ex.Message}");
                 return false;
             }
         }
 */

        public async Task<bool> UpdateMenuStatusAsync(string roleId, string companyId, string menuName, bool status, Guid currentUserId)
        {
            try
            {
                var currentUserCompanyId = await _context.Ahex_CRM_Users
                    .Where(u => u.Id == currentUserId)
                    .Select(u => u.CompanyId)
                    .FirstOrDefaultAsync();

                if (currentUserCompanyId != RoleConstants.CompanyId && currentUserCompanyId.ToLower() != companyId.ToLower())
                {
                    throw new UnauthorizedAccessException("Current user does not have permission to update menus for the provided company.");
                }

             
                var menuId = await GetMenuIdByNameAsync(menuName);
                if (!menuId.HasValue)
                {
                    return false;
                }

                if (IsMainMenu(menuId.Value))
                {
                    
                    bool updated = await UpdateMenuStatusForRole(menuId.Value, roleId, companyId, status);
                    if (updated && !status)
                    {
                        await UpdateSubmenusAndSubsubmenusStatusAsync(menuId.Value, roleId, companyId, status);
                    }
                    return updated;
                }
                else if (await IsSubMenu(menuId.Value))
                {
                    var mainMenuId = await GetMainMenuIdAsync(menuId.Value);
                    var mainMenuStatus = await GetMenuStatusAsync(mainMenuId.Value, roleId, companyId);
                    
                    bool updated = await UpdateMenuStatusForRole(menuId.Value, roleId, companyId, status);
                    if (updated && status)
                    {
                        await UpdateParentMenuStatusesAsync(menuId.Value, roleId, companyId, status);
                    }
                    if (updated && !status)
                    {
                        await UpdateSubsubmenusStatusAsync(menuId.Value, roleId, companyId, status);
                    }
                    return updated;
                   
                }
                else
                {
                    var subMenuId = await GetParentMenuIdAsync(menuId.Value);
                    var mainMenuId = await GetMainMenuIdAsync(subMenuId.Value);

                    var subMenuStatus = await GetMenuStatusAsync(subMenuId.Value, roleId, companyId);
                    var mainMenuStatus = await GetMenuStatusAsync(mainMenuId.Value, roleId, companyId);

                    bool updated = await UpdateMenuStatusForRole(menuId.Value, roleId, companyId, status);
                    if (updated && status)
                    {
                        await UpdateParentMenuStatusesAsync(menuId.Value, roleId, companyId, status);
                    }
                    return updated;


                }
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Exception: {ex.Message}");
                return false;
            }
        }

        private async Task<bool> UpdateMenuStatusForRole(int menuId, string roleId, string companyId, bool status)
        {
            try
            {
                if (IsMainMenu(menuId))
                {
                    var menuRoleRelationship = await _context.MenuwithRoleRelationDetails
                    .FirstOrDefaultAsync(mrr => mrr.MenuId == menuId && mrr.RoleId == roleId && mrr.CompanyId == companyId);

                    if (menuRoleRelationship != null)
                    {
                        menuRoleRelationship.Status = status;
                        if (!status)
                        {
                            await UpdateSubmenusAndSubsubmenusStatusAsync(menuId, roleId, companyId, status);
                        }
                    }
                    else
                    {
                        return false;
                    }
                    await _context.SaveChangesAsync();



                    return true;
                }
                else if (await IsSubMenu(menuId))
                {
                    var menuRoleRelationship = await _context.MenuwithRoleRelationDetails
                    .FirstOrDefaultAsync(mrr => mrr.MenuId == menuId && mrr.RoleId == roleId && mrr.CompanyId == companyId);

                    if (menuRoleRelationship != null)
                    {
                        menuRoleRelationship.Status = status;
                        if (!status)
                        {
                            await UpdateSubsubmenusStatusAsync(menuId, roleId, companyId, status);
                        }
                    }
                    else
                    {
                        return false;
                    }

                    await _context.SaveChangesAsync();



                    return true;
                }
                else
                {
                    var menuRoleRelationship = await _context.MenuwithRoleRelationDetails
                   .FirstOrDefaultAsync(mrr => mrr.MenuId == menuId && mrr.RoleId == roleId && mrr.CompanyId == companyId);

                    if (menuRoleRelationship != null)
                    {
                        menuRoleRelationship.Status = status;

                    }
                    else
                    {
                        return false;
                    }

                    await _context.SaveChangesAsync();



                    return true;
                }
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Exception: {ex.Message}");
                return false;
            }
        }
        private async Task UpdateParentMenuStatusesAsync(int menuId, string roleId, string companyId, bool status)
        {
            var parentMenuId = await GetParentMenuIdAsync(menuId);
            if (parentMenuId.HasValue)
            {
                await UpdateMenuStatusForRole(parentMenuId.Value, roleId, companyId, status);

              

                var grandParentMenuId = await GetParentMenuIdAsync(parentMenuId.Value);
                if (grandParentMenuId.HasValue)
                {
                    await UpdateMenuStatusForRole(grandParentMenuId.Value, roleId, companyId, status);
                }
            }
        }

        private bool HasPermissionToUpdate(string roleId, string companyId)
        {
            string roleIdLower = roleId.ToLower();
            Dictionary<string, List<string>> rolePermissions = GetRolePermissionsFromDatabase(roleIdLower, companyId);

            string currentRoleLower = roleId.ToString().ToLower();

            if (rolePermissions.ContainsKey(currentRoleLower))
            {
                return true;
            }

            return false;
        }

        public async Task<int?> GetMenuIdByNameAsync(string menuName)
        {
            try
            {
                var menu = await _context.MenuDetails
                    .FirstOrDefaultAsync(m => m.MenuName == menuName);

                return menu?.MenuId;
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Exception: {ex.Message}");
                return null;
            }
        }

        private async Task UpdateSubmenusAndSubsubmenusStatusAsync(int menuId, string roleId, string companyId, bool status)
        {
            var subMenuIdss = await GetSubMenuIdsAsync(menuId);
            foreach (var subMenuId in subMenuIdss)
            {
                var subMenuRoleRelationship = await _context.MenuwithRoleRelationDetails
                    .FirstOrDefaultAsync(mrr => mrr.MenuId == subMenuId && mrr.RoleId == roleId && mrr.CompanyId == companyId);

                if (subMenuRoleRelationship != null)
                {
                    subMenuRoleRelationship.Status = status;
                }

                var subSubMenuIds = await GetSubSubMenuIdsAsync(subMenuId);
                foreach (var subSubMenuId in subSubMenuIds)
                {
                    var subSubMenuRoleRelationship = await _context.MenuwithRoleRelationDetails
                        .FirstOrDefaultAsync(mrr => mrr.MenuId == subSubMenuId && mrr.RoleId == roleId && mrr.CompanyId == companyId);

                    if (subSubMenuRoleRelationship != null)
                    {
                        subSubMenuRoleRelationship.Status = status;
                    }
                }
            }
            await _context.SaveChangesAsync();
          //  await _context.SaveChangesAsync();

        }

        private async Task<List<int>> GetSubMenuIdsAsync(int mainMenuId)
        {

            return await _context.MenuDetails
                .Where(m => m.ParentMenuId == mainMenuId)
                .Select(m => m.MenuId)
                .ToListAsync();
        }


        private async Task UpdateSubsubmenusStatusAsync(int menuId, string roleId, string companyId, bool status)
        {
            var subSubMenuIdss = await GetSubSubMenuIdsAsync(menuId);
            foreach (var subSubMenuId in subSubMenuIdss)
            {
                var subSubMenuRoleRelationship = await _context.MenuwithRoleRelationDetails
                    .FirstOrDefaultAsync(mrr => mrr.MenuId == subSubMenuId && mrr.RoleId == roleId && mrr.CompanyId == companyId);

                if (subSubMenuRoleRelationship != null)
                {
                    subSubMenuRoleRelationship.Status = status;
                }
            }
            await _context.SaveChangesAsync();
          //  await _context.SaveChangesAsync();
        }

        private async Task<List<int>> GetSubSubMenuIdsAsync(int menuId)
        {

            return await _context.MenuDetails
                .Where(ssm => ssm.ParentMenuId == menuId)
                .Select(ssm => ssm.MenuId)
                .ToListAsync();
        }

        private async Task<List<int>> GetSubmenusAsync(int menuId)
        {
            var submenus = new List<int>();

            var menus = await _context.MenuDetails
                                    .Where(m => m.ParentMenuId == menuId)
                                    .Select(m => m.MenuId)
                                    .ToListAsync();
            submenus.AddRange(menus);
            return submenus;
        }
        private async Task<bool> IsSubMenu(int menuId)
        {
            var menu = await _context.MenuDetails.FirstOrDefaultAsync(m => m.MenuId == menuId);

            if (menu != null && menu.ParentMenuId != 0)
            {

                var parentMenu = await _context.MenuDetails.FirstOrDefaultAsync(m => m.MenuId == menu.ParentMenuId);


                if (parentMenu != null && parentMenu.ParentMenuId == 0)
                {
                    return true;
                }
            }

            return false;
        }

        private async Task<int?> GetMainMenuIdAsync(int menuId)
        {

            var subMenu = await _context.MenuDetails.FirstOrDefaultAsync(m => m.MenuId == menuId);
            if (subMenu != null && subMenu.ParentMenuId != 0)
            {
                var mainMenu = await _context.MenuDetails.FirstOrDefaultAsync(m => m.MenuId == subMenu.ParentMenuId);
                return mainMenu?.MenuId;
            }
            return null;
        }
        private bool IsMainMenu(int menuId)
        {
            var menuItem = _context.MenuDetails.FirstOrDefault(m => m.MenuId == menuId);
            return menuItem != null && menuItem.ParentMenuId == 0;
        }

        private async Task<int?> GetParentMenuIdAsync(int menuId)
        {

            var parentMenu = await _context.MenuDetails.FirstOrDefaultAsync(m => m.MenuId == menuId);


            return parentMenu?.ParentMenuId;
        }
        private async Task<bool?> GetMenuStatusAsync(int menuId, string roleId, string companyId)
        {
            try
            {
                var menu = await _context.MenuwithRoleRelationDetails
                                          .FirstOrDefaultAsync(m => m.MenuId == menuId && m.RoleId == roleId && m.CompanyId == companyId);

                if (menu != null)
                {
                    return menu.Status;
                }
                else
                {

                    return null;
                }
            }
            catch (Exception ex)
            {

                Console.WriteLine($"Exception: {ex.Message}");
                return null;
            }
        }


        public async Task<bool> UpdateClientMenuStatusAsync(string roleId, string companyId, string menuName, bool status, Guid currentUserId)
        {
            try
            {

                var currentUserCompanyId = await _context.Ahex_CRM_Users
                    .Where(u => u.Id == currentUserId)
                    .Select(u => u.CompanyId)
                    .FirstOrDefaultAsync();


                if (currentUserCompanyId != RoleConstants.CompanyId && currentUserCompanyId != companyId.ToLower())
                {
                    throw new UnauthorizedAccessException("Current user does not have permission to update menus for the provided company.");
                }


                var currentUserRoles = await _context.UserRoles
                    .Where(ur => ur.Id == currentUserId)
                    .Select(ur => ur.RoleId)
                    .ToListAsync();



                var menuId = await GetMenuIdByNameAsync(menuName);
                if (!menuId.HasValue)
                {
                    return false;
                }


                if (IsMainMenu(menuId.Value))
                {
                    bool updated = await UpdateMenuStatusForRole(menuId.Value, roleId, companyId, status);
                    if (updated && !status)
                    {
                        await UpdateSubmenusAndSubsubmenusStatusAsync(menuId.Value, roleId, companyId, status);
                    }
                    return updated;
                }
                else if (await IsSubMenu(menuId.Value))
                {
                    var mainMenuId = await GetMainMenuIdAsync(menuId.Value);
                    var mainMenuStatus = await GetMenuStatusAsync(mainMenuId.Value, roleId, companyId);
                  
                    bool updated = await UpdateMenuStatusForRole(menuId.Value, roleId, companyId, status);
                    if (updated && status)
                    {
                        await UpdateParentMenuStatusesAsync(menuId.Value, roleId, companyId, status);
                    }
                    if (updated && !status)
                    {
                        await UpdateSubsubmenusStatusAsync(menuId.Value, roleId, companyId, status);
                    }
                    return updated;
                   
                }
                else
                {
                    var subMenuId = await GetParentMenuIdAsync(menuId.Value);
                    var mainMenuId = await GetMainMenuIdAsync(subMenuId.Value);

                    var subMenuStatus = await GetMenuStatusAsync(subMenuId.Value, roleId, companyId);
                    var mainMenuStatus = await GetMenuStatusAsync(mainMenuId.Value, roleId, companyId);

                    bool updated = await UpdateMenuStatusForRole(menuId.Value, roleId, companyId, status);
                    if (updated && status)
                    {
                        await UpdateParentMenuStatusesAsync(menuId.Value, roleId, companyId, status);
                    }
                    return updated;
                }
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Exception: {ex.Message}");
                return false;
            }
        }



        private async Task<bool> UpdateClientMenuStatusForRole(int menuId, string roleId, string companyId, bool status)
        {
            try
            {
                if (IsMainMenu(menuId))
                {
                    var menuRoleRelationship = await _context.MenuwithRoleRelationDetails
                        .FirstOrDefaultAsync(mrr => mrr.MenuId == menuId && mrr.RoleId == roleId && mrr.CompanyId == companyId);

                    if (menuRoleRelationship != null)
                    {
                        menuRoleRelationship.Status = status;
                        await _context.SaveChangesAsync();

                        if (!status)
                        {
                            await UpdateSubmenusAndSubSubmenusStatusAsync(menuId, roleId, companyId, status);
                        }

                        return true;
                    }
                    else
                    {
                        return false;
                    }
                }
                else if (await IsSubMenu(menuId))
                {
                    var mainMenuId = await GetMainMenuIdAsync(menuId);
                    var mainMenuStatus = await GetMenuStatusAsync(mainMenuId.Value, roleId, companyId);

                    if (mainMenuStatus.HasValue && mainMenuStatus.Value)
                    {
                        var menuRoleRelationship = await _context.MenuwithRoleRelationDetails
                            .FirstOrDefaultAsync(mrr => mrr.MenuId == menuId && mrr.RoleId == roleId && mrr.CompanyId == companyId);

                        if (menuRoleRelationship != null)
                        {
                            menuRoleRelationship.Status = status;
                            await _context.SaveChangesAsync();

                            if (!status)
                            {
                                await UpdateSubSubmenusStatusAsync(menuId, roleId, companyId, status);
                            }

                            return true;
                        }
                        else
                        {
                            return false;
                        }
                    }
                    else
                    {
                        return false;
                    }
                }
                else
                {
                    var subMenuId = await GetParentMenuIdAsync(menuId);
                    var mainMenuId = await GetMainMenuIdAsync(subMenuId.Value);

                    var mainMenuStatus = await GetMenuStatusAsync(mainMenuId.Value, roleId, companyId);
                    var subMenuStatus = await GetMenuStatusAsync(subMenuId.Value, roleId, companyId);

                    if (mainMenuStatus.HasValue && mainMenuStatus.Value && subMenuStatus.HasValue && subMenuStatus.Value)
                    {
                        var menuRoleRelationship = await _context.MenuwithRoleRelationDetails
                            .FirstOrDefaultAsync(mrr => mrr.MenuId == menuId && mrr.RoleId == roleId && mrr.CompanyId == companyId);

                        if (menuRoleRelationship != null)
                        {
                            menuRoleRelationship.Status = status;
                            await _context.SaveChangesAsync();
                            return true;
                        }
                        else
                        {
                            return false;
                        }
                    }
                    else
                    {
                        return false;
                    }
                }
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Exception: {ex.Message}");
                return false;
            }
        }

        private async Task UpdateSubmenusAndSubSubmenusStatusAsync(int menuId, string roleId, string companyId, bool status)
        {

            var submenus = await GetSubmenusAsync(menuId);
            foreach (var submenuId in submenus)
            {
                await UpdateClientMenuStatusForRole(submenuId, roleId, companyId, status);

                await UpdateSubsubmenusStatusAsync(submenuId, roleId, companyId, status);
            }
        }
        private async Task UpdateSubSubmenusStatusAsync(int menuId, string roleId, string companyId, bool status)
        {

            var subsubmenus = await GetSubmenusAsync(menuId);
            foreach (var subsubmenuId in subsubmenus)
            {
                await UpdateClientMenuStatusForRole(subsubmenuId, roleId, companyId, status);
            }
        }

    }
}
