﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using EngagetoEntities.Entities;

namespace EngagetoContracts.UserContracts
{
    public interface INotificationPreferenceService
    {
        /* Task<NotificationDetails> SetNotificationPreferences(Guid userId, NotificationDetailsDto preferences);
         Task<NotificationDetails> GetNotificationPreferencesAsync(Guid userId);*/
        Task<IEnumerable<NotificationDto>> GetNotificationHierarchyAsync(string userId, string companyId);
        Task<bool> UpdateNotificationStatusAsync(string userId, string companyId, string notificationName, bool isActive);
    }
}
