﻿using Dapper;
using EngagetoDapper.Data.Connections;
using EngagetoDapper.Data.Interfaces.IConversationAnalyticsInterfaces;
using EngagetoEntities.Entities;
using System.Data;

namespace EngagetoDapper.Data.Dapper.Repositories.ConversationRepositories
{
    public class ConversationAnalyticsRepository : IConversationAnalyticsRepository
    {
        private readonly IUnitOfWork _unitOfWork;
        public ConversationAnalyticsRepository(IUnitOfWork unitOfWork)
        {
            _unitOfWork = unitOfWork;
        }

        public async Task<List<ConversationAnalyticsEntity>> GetAnalyticsDetailsByStartAndEndDateAsync(string companyId, DateTime startDate, DateTime endDate, string? conversationCategory = null)
        {
            var sqlQuery = @"
                            SELECT * 
                            FROM ConversationAnalyticsEntities
                            WHERE CompanyId = @CompanyId
                            AND CAST(StartDate AS DATE) >= @StartDate 
                            AND CAST(StartDate AS DATE) < @EndDate
                            AND (@conversationCategory IS NULL OR ConversationCategory = @conversationCategory)";

            var parameters = new
            {
                CompanyId = companyId,
                StartDate = startDate.Date,
                EndDate = endDate.Date,
                ConversationCategory = conversationCategory
            };

            return (await _unitOfWork.Connection.QueryAsync<ConversationAnalyticsEntity>(sqlQuery, parameters))?.ToList() ?? new List<ConversationAnalyticsEntity>();

        }

        public async Task<bool> SaveConversationAnalyticsAsync<T>(string tableName, List<string> columns, IEnumerable<T> entities, IDbTransaction transaction)
        {
            var sqlValues = columns.Select(s => "@" + s).ToArray();
            var sqlQuery = @$"INSERT INTO {tableName} 
                     (
                         {string.Join(",", columns)}
                     )
                     VALUES
                     (
                         {string.Join(",", sqlValues)}
                     )";

            try
            {
                var result = await _unitOfWork.Connection.ExecuteAsync(sqlQuery, entities);
                return result > 0;
            }
            catch (Exception)
            {
                throw;
            }
        }

        public async Task<IEnumerable<T>> GetDefaultConversationAnalyticPriceAsync<T>(int? year = null)
        {

            string sqlQuery = @"SELECT ca.* 
                        FROM ConversationAnalyticsPriceEntities ca 
                        LEFT JOIN PlanEntities p ON
                            p.Id = ca.PlanId
							AND p.[Status] = 1
                        WHERE CompanyId IS NULL
                        AND (@year IS NULL OR Year = @year)";

            var parameters = new
            {
                Year = year,
            };
            return await _unitOfWork.Connection.QueryAsync<T>(sqlQuery, parameters);
        }

        public async Task<bool> SaveConversationAnalyticsJobRequestAsync<T>(string tableName, List<string> columns, T entities)
        {
            var sqlValues = columns.Select(s => "@" + s).ToArray();
            var sqlQuery = @$"INSERT INTO {tableName} 
                     (
                         {string.Join(",", columns)}
                     )
                     VALUES
                     (
                         {string.Join(",", sqlValues)}
                     )";

            try
            {
                var result = await _unitOfWork.Connection.ExecuteAsync(sqlQuery, entities);
                return result > 0;
            }
            catch (Exception)
            {
                throw;
            }
        }

        public async Task<bool> UpdateConversationAnalyticsJobRequestAsync<T>(string tableName, List<string> columns, T entity, Dictionary<string, object> conditions)
        {
            var setClauses = columns.Select(col => $"{col} = @{col}").ToList();
            var setClauseString = string.Join(", ", setClauses);

            var whereClauses = conditions.Select(condition => $"{condition.Key} = @{condition.Key}").ToList();
            var whereClauseString = "WHERE " + string.Join(" AND ", whereClauses);

            var sqlQuery = $@"
                UPDATE {tableName}
                SET {setClauseString}
                {whereClauseString}";

            var parameters = new DynamicParameters(entity);
            foreach (var condition in conditions)
            {
                parameters.Add("@" + condition.Key, condition.Value);
            }
            try
            {
                var result = await _unitOfWork.Connection.ExecuteAsync(sqlQuery, parameters);
                return result > 0;
            }
            catch (Exception ex)
            {
                throw new ApplicationException("Error updating database.", ex);
            }
        }

        public async Task<T?> GetConversationAnalyticsJobRequestAsync<T>(string companyId)
        {
            string sqlQuery = @"SELECT TOP(1) *
                    FROM ConversationAnalyticsJobRequest 
                    WHERE CompanyId = @CompanyId
                    ORDER BY StartDate DESC";
            var parameters = new
            {
                CompanyId = companyId,
            };
            return (await _unitOfWork.Connection.QueryAsync<T>(sqlQuery, parameters)).FirstOrDefault();
        }

        public async Task<bool> IsSentWelcomeMessageAsync(string companyId, string phoneNumber)
        {
            string sqlQuery = @"SELECT TOP(2)
                            [From],
                            [To]
                        FROM Conversations
                        WHERE 
                            [From] = @PhoneNumber
                            AND [To] = @BusinessId;";

            var parameters = new
            {
                BusinessId = companyId,
                PhoneNumber = phoneNumber,
            };

            var result = await _unitOfWork.Connection.QueryAsync(sqlQuery, parameters);

            return result.Count() < 2;
        }
    }
}
