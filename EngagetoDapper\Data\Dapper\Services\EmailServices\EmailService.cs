﻿using EngagetoDapper.Data.Interfaces.IEmailInterfaces;
using EngagetoEntities.ServiceModels;
using MailKit.Security;
using Microsoft.Extensions.Options;
using MimeKit;

namespace EngagetoDapper.Data.Dapper.Services.EmailServices
{
    public class EmailService : IEmailService
    {
        private readonly SmtpSettings _smtpSettings;
        public EmailService(IOptions<SmtpSettings> smtpSettings) 
        {  
            _smtpSettings = smtpSettings.Value; 
        }
        public async Task SendEmailAsync(EmailMessage emailMessage)
        {
            var message = new MimeMessage();
            message.From.Add(new MailboxAddress("Engageto", _smtpSettings.SmtpUsername));

            emailMessage.To.ForEach(to => message.To.Add(new MailboxAddress(to, to)));
            emailMessage.Cc.ForEach(cc => message.Cc.Add(new MailboxAddress(cc, cc)));
            emailMessage.Bcc.ForEach(bcc => message.Bcc.Add(new MailboxAddress(bcc, bcc)));

            message.Subject = emailMessage.Subject;

            var bodyBuilder = new BodyBuilder
            {
                HtmlBody = emailMessage.Body.Trim()
            };

            // Attach files
            foreach (var attachment in emailMessage.Attachments)
            {
                using var ms = new MemoryStream();
                await attachment.CopyToAsync(ms);
                ms.Position = 0;
                bodyBuilder.Attachments.Add(attachment.FileName, ms.ToArray(), ContentType.Parse(attachment.ContentType));
            }

            message.Body = bodyBuilder.ToMessageBody();

            using var client = new MailKit.Net.Smtp.SmtpClient();
            await client.ConnectAsync(_smtpSettings.SmtpServer, _smtpSettings.SmtpPort, SecureSocketOptions.StartTls);
            await client.AuthenticateAsync(_smtpSettings.SmtpUsername, _smtpSettings.SmtpPassword);
            await client.SendAsync(message);
            await client.DisconnectAsync(true);
        }
    }
    
}
