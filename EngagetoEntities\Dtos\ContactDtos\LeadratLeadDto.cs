﻿using EngagetoEntities.Enums;

namespace EngagetoEntities.Dtos.ContactDtos
{
    public class LeadratLeadDto
    {
        public string Name { get; set; } = default!;
        public string PhoneNumber { get; set; } = default!;
        public string? Email { get; set; } = default!;
        public string? Notes { get; set; }
        public LeadSource SubSource { get; set; }
        public string? AssignTo { get; set; }
        public string? Project { get; set; }
        public string? Status { get; set; }
        public string? SubStatus {  get; set; }
        public DateTime? ScheduledAt { get; set; }
    }
}
