﻿using Microsoft.IdentityModel.Tokens;

namespace EngagetoEntities.Dtos.MetaDto
{
    public static class CreateTemplatePayloads
    {
        public static string GetCreateTemplatePayload(string name, string language, string category, string headerText, string headerMediaType,
                   string headerMediaUrl, string bodyText, string buttons, string footerText, string? headerExample, string bodyExample)
        {
            var validFormats = new[] { "TEXT", "IMAGE", "DOCUMENT", "VIDEO", "LOCATION", "AUDIO" };
            var format = category != "AUTHENTICATION" && !string.IsNullOrEmpty(headerMediaType) && validFormats.Contains(headerMediaType.ToUpper())
                ? headerMediaType.ToUpper()
                : "TEXT";

            var header = format == "TEXT"
              ? (!string.IsNullOrEmpty(headerText)
                  ? (string.IsNullOrEmpty(headerExample)
                      ? $@"{{ ""type"": ""HEADER"", 
                              ""format"": ""TEXT"",
                              ""text"": ""{headerText}"" }}"
                      : $@"{{ ""type"": ""HEADER"", ""format"": ""TEXT"", ""text"": ""{headerText}"", ""example"": {{ ""header_text"": {headerExample} }} }}"
                  )
                  : string.Empty)
              : (!string.IsNullOrEmpty(headerMediaUrl)
                  ? $@"{{ ""type"": ""HEADER"",
                          ""format"": ""{format}"",
                          ""example"": {{ ""header_handle"": [ ""{headerMediaUrl}"" ] 
                                       }}
                       }}"
                  : string.Empty);

            var body = "";
            var buttonsPayload = "";
            var footer = "";
            if (category == "AUTHENTICATION")
            {
                body = $@"{{ ""type"": ""BODY"", ""add_security_recommendation"": ""{bodyText}"" }}";
                footer = footerText != null && footerText.Any() ? $@"{{ ""type"": ""FOOTER"", ""code_expiration_minutes"": {footerText} }}" : string.Empty;
                buttonsPayload = buttons != null && buttons.Any()
                   ? $@"{buttons}"
                   : string.Empty;
            }
            else
            {
                body = !string.IsNullOrEmpty(bodyText)
                ? string.IsNullOrEmpty(bodyExample)
                    ? $@"{{ ""type"": ""BODY"", ""text"": ""{bodyText}"" }}"
                    : $@"{{ ""type"": ""BODY"", ""text"": ""{bodyText}"", 
                           ""example"": {{
                                           ""body_text"": [
                                                             [{bodyExample}]
                                                          ] }} 
                        }}"
                : string.Empty;

                buttonsPayload = buttons != null && buttons.Any()
                   ? $@"{buttons}"
                   : string.Empty;

                footer = !string.IsNullOrEmpty(footerText)
                ? $@"{{ ""type"": ""FOOTER"", ""text"": ""{footerText}"" }}"
                : string.Empty;
            }

            if (language == "0") language = "en_US";

            return $@"
           {{
              ""name"": ""{name}"",
              ""language"": ""{language}"",
              ""category"": ""{category}"",
              ""components"": [
                           {header}{(string.IsNullOrEmpty(header) ? "" : ",")}
                           {body}{(string.IsNullOrEmpty(body) ? "" : ",")}
                           {buttonsPayload}{(string.IsNullOrEmpty(buttonsPayload) ? "" : ",")}
                           {footer}
                           ]
           }}".Replace(",\n\n", ",\n").Replace(",\n]", "\n]");
        }

        public static string CarouselTemplatePayload(string templateName, string bodyTextVariables, string language, string category, string header, string body, string mediaType, string headerHandle,
                                     string CarouselCardBody, string bodyExample)
        {
            var bodypayload = !string.IsNullOrEmpty(body)
              ? string.IsNullOrEmpty(bodyExample)
                  ? $@"{{ ""type"": ""BODY"",
                           ""text"": ""{body}"" }}"
                  : $@"{{ ""type"": ""BODY"",
                            ""text"": ""{body}"", 
                            ""example"": {{
                                           ""body_text"": [
                                                             [{bodyExample}]
                                                          ] 
                                         }} 
                        }}"
              : string.Empty;

            return $@"
            {{
               ""name"": ""{templateName}"",
               ""language"": ""{language}"",
               ""category"": ""{category}"",
               ""components"": [ 
                                 {bodypayload}{(string.IsNullOrEmpty(bodypayload) ? "" : ",")}
                                 {CarouselCardBody}                        
                               ]
            }}".Replace(",\n\n", ",\n").Replace(",\n]", "\n]");
        }

        public static string SendCarouselTemplatePayload(string templateName, string phoneNumberId, List<string> bodyTextVariables, string language, string CarouselCardBody, string carouselBodiesPayload, string? campaignId = null, string? campaignName = null)
        {
            string bodyTextVariablesParameters = "";
            if (!bodyTextVariables.IsNullOrEmpty())
            {
                bodyTextVariablesParameters = string.Join(",", bodyTextVariables
                    .Where(x => !string.IsNullOrWhiteSpace(x))
                    .Select(variable => $@"{{ ""type"": ""text"", ""text"": ""{variable}"" }}"));
            }

            string bodyComponent = !string.IsNullOrWhiteSpace(bodyTextVariablesParameters)
                ? $@"{{ 
                         ""type"": ""body"",
                         ""parameters"": [ {bodyTextVariablesParameters} ]
                      }}"
                        : string.Empty;

            string components = !string.IsNullOrWhiteSpace(bodyComponent) && !string.IsNullOrWhiteSpace(CarouselCardBody)
                ? $"{bodyComponent},\n{CarouselCardBody}"
                : $"{bodyComponent}{CarouselCardBody}";

            // ✅ Only add biz_opaque_callback_data if both values are provided
            string callbackData = (!string.IsNullOrWhiteSpace(campaignId) && !string.IsNullOrWhiteSpace(campaignName))
                ? $@",
                       ""biz_opaque_callback_data"": {{
                                ""data"": ""campaignId={campaignId};campaignName={campaignName}""
                }}"
                : "";

            return $@"{{ 
                           ""messaging_product"": ""whatsapp"",
                           ""recipient_type"": ""individual"",
                           ""to"": ""{phoneNumberId}"",
                           ""type"": ""template"",
                           ""template"": {{
                           ""name"": ""{templateName}"",
                           ""language"": {{ ""code"": ""{language}"" }},
                           ""components"": [ {components} ]
                      }}{callbackData}
                    }}";
        }
        public static string SendAuthenticationTemplatePayload(string phoneNumberId, string templateName, int bodyVariableCount, string language, string category, string oneTimePassword)
        {
            string bodyTextVariablesParameters = "";

            if (bodyVariableCount > 0)
            {
                bodyTextVariablesParameters = $@"{{ ""type"": ""text"", ""text"": ""{oneTimePassword}"" }}";
            }
            return $@"{{ ""messaging_product"": ""whatsapp"",
                         ""recipient_type"": ""individual"",
                         ""to"": ""{phoneNumberId}"",
                         ""type"": ""template"",
                         ""template"": {{ ""name"": ""{templateName}"",
                                          ""language"":{{ ""code"": ""{language}""}},
                                          ""components"": [ {(!string.IsNullOrWhiteSpace(bodyTextVariablesParameters)
                                                             ? $@"{{ ""type"": ""body"",
                                                                      ""parameters"": [ {bodyTextVariablesParameters} ]
                                                                   }},"
                                                             : string.Empty)}
                                                            {{
                                                               ""type"":""button"",
                                                               ""sub_type"": ""url"",
                                                               ""index"":""0"",
                                                               ""parameters"": [ {{
                                                                                    ""type"": ""text"",
                                                                                    ""text"": ""{oneTimePassword}""
                                                                                 }}
                                                                               ]
                                                            }}
                                                          ]
                                       }}
                       }}";
        }


    }
}
