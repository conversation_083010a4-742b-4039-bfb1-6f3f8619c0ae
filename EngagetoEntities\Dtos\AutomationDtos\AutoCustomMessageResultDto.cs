﻿using EngagetoEntities.Enums;

namespace EngagetoEntities.Dtos.AutomationDtos
{
    public class AutoCustomMessageResultDto
    {
        public Guid Id { get; set; }
        public Guid CompanyId { get; set; }
        public Guid UserId { get; set; }
        public string Input { get; set; }
        public string? InputVariation { get; set; }
        public int AutoReplyType { get; set; }
        public Guid AutoReplyCustomMessageId { get; set; }
        public Guid? VeriableId { get; set; }
        public string BodyMessage { get; set; }
        public string? Buttons { get; set; }
        public int? Index { get; set; }
        public string? Veriable { get; set; }
        public string? Value { get; set; }
        public string? FallbackValue { get; set; }
        public ReferenceTableType ReferenceTableType { get; set; }

    }
}



