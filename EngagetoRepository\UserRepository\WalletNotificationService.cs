﻿using Amazon.Runtime.Internal.Util;
using AutoMapper;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Logging;
using Razorpay.Api;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using EngagetoContracts.UserContracts;
using EngagetoEntities.UserEntities.Models;
using EngagetoEntities.Entities;

namespace EngagetoRepository.UserRepository
{
    public class WalletNotificationService : IWalletNotificationService
    {
        private readonly ILogger<WalletNotificationService> _logger;
        private readonly ApplicationDBContext _dbContext;
        private readonly IMapper _mapper;
        public WalletNotificationService(ApplicationDBContext dbContext, ILogger<WalletNotificationService> logger, IMapper mapper) 
        {
            _dbContext = dbContext;
            _logger = logger;
            _mapper = mapper;
        }
        public async Task<WalletNotification> CreateWalletNotificationAsync(WalletNotification WalletNotification)
        {
            await _dbContext.WalletNotificationEntities.AddAsync(WalletNotification);
            await _dbContext.SaveChangesAsync();
            return WalletNotification;
        }

        public async Task<List<WalletNotification>> GetWalletNotificationAsync(Guid walletId)
        {
           return _dbContext.WalletNotificationEntities.Where(x => x.WalletId == walletId && !x.IsDeleted)?.OrderByDescending(x => x.CreatedAt)?.ToList() ?? new List<WalletNotification>();
        }
    }
}
