﻿using EngagetoEntities.Enums;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace EngagetoEntities.Entities
{
    [Table("WorkflowEntities")]
    public class WorkflowEntity : BaseEntity
    {
        [Key]
        [Required]
        public Guid Id { get; set; }
        public int? Step { get; set; }
        public int? StepType { get; set; }
        public string WorkflowName { get; set; }
        [Required(ErrorMessage = "CompanyId is a required Field")]
        public Guid CompanyId { get; set; }
        public Guid? UserId { get; set; }
        public string? Title { get; set; }
        public WorkflowResponseType? FlowResponseType { get; set; }
        public string? WorkflowButtons { get; set; }
        public Guid? WorkflowListId { get; set; }
        public string? WebhookTriggerUrl { get; set; }
        public string? WebhookTriggerHeader { get; set; }
        public string? WebhookTriggerBody { get; set; }
        public string? DefaultErrorResponse { get; set; }
        public string? WebhookTriggerHttpMethod { get; set; }

        public WorkflowEntity(Guid userId, bool IsUpdate = false)
        {
            CreatedBy = userId;
            UpdatedBy = userId;
            IsDeleted = false;
            if (!IsUpdate)
            {
                CreatedAt = DateTime.UtcNow;
            }
        }
        public WorkflowEntity() { }


    }
}
