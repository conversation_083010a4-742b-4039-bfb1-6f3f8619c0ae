﻿using Newtonsoft.Json;

namespace EngagetoEntities.Dtos.MetaDto
{
    public class AudioMessageRequest
    {
        [JsonProperty("messaging_product")]
        public string MessagingProduct { get; private set; } = "whatsapp";

        [JsonProperty("recipient_type")]
        public string RecipientType { get; private set; } = "individual";

        [JsonProperty("to")]
        public string To { get; set; }

        [JsonProperty("type")]
        public string Type { get; private set; } = "audio";

        [JsonProperty("audio")]
        public WhatsAppAudio Audio { get; set; }
        [JsonProperty("context")]
        public MessageContext Context { get; set; }
    }

    public class WhatsAppAudio
    {
        [JsonProperty("link")]
        public string Link { get; set; }
    }
}
