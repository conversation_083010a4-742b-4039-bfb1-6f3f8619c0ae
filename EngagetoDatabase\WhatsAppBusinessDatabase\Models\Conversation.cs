﻿using EngagetoEntities.Enums;
using Newtonsoft.Json;

namespace EngagetoDatabase.WhatsAppBusinessDatabase.Models;

public partial class Conversation
{
    public Guid Id { get; set; }

    public string WhatsAppMessageId { get; set; } = null!;

    public string From { get; set; } = null!;

    public string To { get; set; } = null!;

    public string? ReplyId { get; set; }

    public ConvStatus Status { get; set; }

    public DateTime CreatedAt { get; set; }

    public string? TextMessage { get; set; }

    public string? MediaFileName { get; set; }

    public string? MediaMimeType { get; set; }

    public string? MediaUrl { get; set; }

    public string? MediaCaption { get; set; }

    public MediaType? TemplateMediaType { get; set; }

    public string? TemplateMediaUrl { get; set; }

    public string? TemplateHeader { get; set; }

    public string? TemplateBody { get; set; }

    public string? TemplateFooter { get; set; }

    public string? CallButtonName { get; set; }

    public string? PhoneNumber { get; set; }

    public string? UrlButtonNames { get; set; }

    public string? RedirectUrls { get; set; }

    public string? QuickReplies { get; set; }
    
    public class SendMessageResponse
    {
        public string MessagingProduct { get; set; }

        [JsonProperty("contacts")]
        public List<Contact> Contacts { get; set; }

        [JsonProperty("messages")]
        public List<Messages> Messages { get; set; }
    }
    public class Messages
    {
        [JsonProperty("id")]
        public string WhatsAppMessageId { get; set; }

        [JsonProperty("message_status")]
        public SendStatus Status { get; set; }
    }
    public class Contact
    {
        [JsonProperty("input")]
        public string To { get; set; }

        //[JsonProperty("wa_id")]
        //public string From { get; set; }
    }
}
