﻿using EngagetoContracts.Services;

namespace Engageto.Middleware
{
    public class ClaimsMiddleware
    {
        private readonly RequestDelegate _next;

        public ClaimsMiddleware(RequestDelegate next)
        {
            _next = next;
        }

        public async Task InvokeAsync(HttpContext context, IUserIdentityService userService)
        {
            if (context.User.Identity?.IsAuthenticated == true)
            {
                var claims = context.User.Claims;
                userService.SetClaims(claims);
            }

            await _next(context);
        }
    }
}
