﻿using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace EngagetoEntities.Entities
{
    [Table("AssignPermissions")]
    public class AssignPermissions
    {
        [Key]
        [DatabaseGenerated(DatabaseGeneratedOption.Identity)]
        public Guid Id { get; set; }
        public Guid RoleId { get; set; }
        public Guid PermissionId { get; set; }
        public bool Status { get; set; }
        public string? CompanyId { get; set; }
        public string? ModifiedBy { get; set; }
        public string? Access { get; set; }

        [ForeignKey("PermissionId")]
        public Permissions Permissions { get; set; }
        /*  [ForeignKey("RoleId")]
          public Role Role { get; set; }*/


    }
    public class AssignPermissionsToRoleIdDto
    {
        public string RoleId { get; set; }

        public Guid PermissionId { get; set; }
        public bool Status { get; set; }
    }
    public class AssignPermissionsDto
    {
        public string Name { get; set; }
        public bool Status { get; set; }
        // public string Section {  get; set; }
    }
}
