﻿using EngagetoEntities.Extensions;
using System;
using System.Collections.Concurrent;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace EngagetoEntities.Utilities
{
    public static class ApiRateLimit
    {
        private static readonly ConcurrentDictionary<string, ConcurrentDictionary<DateTime, int>> _endpointCallCounter = new();
        public static bool HasHitRateLimit(string path, int thresholdPer5Seconds = 500)
        {
            var currentTime = DateTime.UtcNow.TrimToSecond();
            var endpointCounter = _endpointCallCounter.GetOrAdd(path, _ => new ConcurrentDictionary<DateTime, int>());

            // Increment count for the current second
            endpointCounter.AddOrUpdate(currentTime, 1, (_, old) => old + 1);

            // Clean up entries older than 5 seconds
            foreach (var key in endpointCounter.Keys)
            {
                if ((currentTime - key).TotalSeconds > 5)
                {
                    endpointCounter.TryRemove(key, out _);
                    Console.WriteLine($"Removed old request: {key:O}");
                }
            }

            // Count all requests in the last 5 seconds (inclusive of current second)
            int recentCount = endpointCounter
                .Where(kvp => (currentTime - kvp.Key).TotalSeconds < 5)
                .Sum(kvp => kvp.Value);

            Console.WriteLine($"[{currentTime:O}] - Requests in last 5s: {recentCount}");

            return recentCount >= thresholdPer5Seconds;
        }
    }
}
