﻿namespace EngagetoEntities.UserEntities.Dtos
{
    public class WebhookEndpointRequestDto
    {
        public string CompanyId { get; set; }
        public Guid? UserId { get; set; } = Guid.Empty;
        public string WebhookUrl { get; set; } = default!;
        public string? ApiKeyName { get; set; } = "API-Key";
        public string? ApiKey { get; set; }
        public string? Description { get; set; }
    }
}
