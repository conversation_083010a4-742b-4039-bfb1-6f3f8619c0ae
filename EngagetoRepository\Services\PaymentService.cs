﻿using cashfree_pg.Client;
using cashfree_pg.Model;
using EngagetoContracts.GeneralContracts;
using EngagetoContracts.Services;
using EngagetoDapper.Data.Interfaces.GenericInterfaces;
using EngagetoEntities.Dtos.CommonDtos.PaymentGatwayDtos;
using EngagetoEntities.Entities;
using EngagetoEntities.Enums;
using EngagetoEntities.Settings;
using EngagetoEntities.UserEntities.Models;
using EngagetoEntities.Utilities;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Options;



namespace EngagetoRepository.Services
{
    public class PaymentService : IPaymentService
    {
        private readonly IUserIdentityService _identityService;
        private readonly IGenericRepository _genericRepository;
        private readonly PaymentGatewayOptions _options;
        private readonly IConfiguration _configuration;
        private readonly ApplicationDBContext _dbContext;
        private readonly IEnvironmentService _environmentService;
        private int igstPercentage = 18;
        private string url = string.Empty;
        Cashfree cashfree = new Cashfree();
        public PaymentService(IUserIdentityService identityService, IGenericRepository genericRepository,
            IOptions<PaymentGatewayOptions> options, IConfiguration configuration,
            IEnvironmentService environmentService,
            ApplicationDBContext dbContext)
        {
            _identityService = identityService;
            _genericRepository = genericRepository;
            _options = options.Value;
            _configuration = configuration;
            _dbContext = dbContext;
            _environmentService = environmentService;

            _ = int.TryParse(_configuration.GetSection("GstDetails:IGSTPercentage").Value,
                            out var igstPercentage);

            Cashfree.XClientId = _options.CashfreeOption.ApiKey;
            Cashfree.XClientSecret = _options.CashfreeOption.Secret;
            Cashfree.XEnvironment = Cashfree.PRODUCTION;
            url = $"{_environmentService.RequestScheme}://{_environmentService.RequestHost}/wallet";
        }
        public async Task<CashfreeOrderDetailsDto> CreateOrderAsyn(OrderDto orderDto)
        {
            try
            {
                if (orderDto.Amount <= 0)
                {
                    throw new InvalidOperationException("Amount can not less than 1");
                }
                var companyId = _identityService.BusinessId;
                var apiVersion = _options.CashfreeOption.ApiVersion;
                Guid orderId = Guid.NewGuid();
                _ = Guid.TryParse(companyId, out var businessId);
                var userId = _identityService.UserId;
                var businessDetails = (await _genericRepository.GetByObjectAsync<Ahex_CRM_BusinessDetails>(new() { { "Id", businessId } })).FirstOrDefault();
                var customerDetails = new CustomerDetails(companyId, businessDetails?.BusinessEmail, orderDto.PhoneNumber, businessDetails?.BusinessName);
                var calculateAmount = await DiscountAmountAsync(orderDto.DiscountId, orderDto.Amount);
                var totalAmount = (calculateAmount.TotalAmount * (100 + igstPercentage)) / 100;

                if (totalAmount != orderDto.TotalAmount)
                    throw new Exception("somthing went worng with total amount please connect with support team.");

                url = url + "?orderId=" + orderId.ToString();
                //OrderMeta meta = new OrderMeta() { return_url = url,notify_url = url};
                var createOrdersRequest = new CreateOrderRequest(orderId.ToString(), totalAmount, orderDto.Currency.ToString(), customerDetails);
                var result = cashfree.PGCreateOrder(_options.CashfreeOption.ApiVersion, createOrdersRequest, null, null, null);
                CashfreeOrderDetailsDto data = new();
                if (result.StatusCode == System.Net.HttpStatusCode.OK)
                {
                    data = MapCashfreeOrder(result.Data);
                    TransactionHistoryEntity transactionHistory = new()
                    {
                        Transactionid = Guid.NewGuid(),
                        Amount = (decimal)orderDto.Amount,
                        AmountPaid = 0,
                        Attempts = 0,
                        CompanyId = companyId,
                        UserID = userId,
                        Contact = result.Data?.customer_details?.customer_phone ?? orderDto.PhoneNumber,
                        Currency = data.OrderCurrency ?? orderDto.Currency.ToString(),
                        DiscountAmount = 0,
                        OrderId = orderId.ToString(),
                        Status = data.OrderStatus,
                        DiscountId = orderDto.DiscountId,
                        Email = result?.Data?.customer_details?.customer_email,
                        WalletId = orderDto.WalletId,
                        TransactionDate = null,
                        Entity = data.Entity,
                        CreatedAt = DateTime.UtcNow,
                        UpdatedAt = DateTime.UtcNow,
                        AmountDue = 0,
                        CreatedBy = userId,
                        UpdatedBy = userId,
                    };
                    await _genericRepository.InsertRecordsAsync("TransactionHistoryEntities",
                        StringHelper.GetPropertyNames<TransactionHistoryEntity>(false),
                        new List<TransactionHistoryEntity>() { transactionHistory });
                    return data;
                }

                throw new InvalidOperationException(result.ErrorText);
            }
            catch (Exception ex)
            {
                throw;
            }
        }

        public async Task<UserWalletEntity> FetchOrderAsync(string orderId)
        {
            var results = cashfree.PGOrderFetchPayments(_options.CashfreeOption.ApiVersion, orderId, null, null);

            if (results.StatusCode == System.Net.HttpStatusCode.OK)
            {
                var result = results.Data.FirstOrDefault(x => x.payment_status == PaymentEntity.PaymentStatusEnum.SUCCESS) ?? results.Data.OrderByDescending(x => x.payment_time)?.FirstOrDefault();
                var transaction = (await _genericRepository.GetByObjectAsync<TransactionHistoryEntity>(new Dictionary<string, object> { { "OrderId", orderId }, { "CompanyId", _identityService.BusinessId } }))?.FirstOrDefault();
                var wallet = await _dbContext.UserWalletEntities.FirstOrDefaultAsync(x => x.CompanyId == _identityService.BusinessId);
                if (transaction != null && transaction.Status != "Paid")
                {
                    bool isSucess = false;
                    if (wallet != null && PaymentEntity.PaymentStatusEnum.SUCCESS == result?.payment_status)
                    {
                        wallet.Balance += transaction?.Amount ?? 0;
                        wallet.ExpectedWalletBallance += transaction?.Amount ?? 0;
                        wallet.UpdatedBy = _identityService.UserId;
                        wallet.UpdatedAt = DateTime.UtcNow;
                        _dbContext.Update(wallet);
                        isSucess = true;
                    }

                    transaction.UpdatedAt = DateTime.UtcNow;
                    transaction.Status = isSucess ? "Paid" : result?.payment_status.ToString();
                    transaction.PaymentId = result?.cf_payment_id;
                    transaction.Amount = result?.order_amount ?? 0;
                    transaction.Currency = result?.payment_currency;
                    transaction.AmountPaid = isSucess ? result?.order_amount ?? 0 : 0;
                    transaction.PaymentMethod = result?.payment_group?.ToString();
                    transaction.UpdatedBy = _identityService.UserId;
                    _dbContext.TransactionHistoryEntities.Update(transaction);
                    await _dbContext.SaveChangesAsync();
                }
                if (PaymentEntity.PaymentStatusEnum.SUCCESS != result?.payment_status)
                {
                    throw new Exception(result?.error_details?.error_description ?? "Payment faild.");
                }
                return wallet;
            }
            throw new InvalidOperationException(results.ErrorText);
        }
        private async Task<(double TotalAmount, double DiscountAmount)> DiscountAmountAsync(Guid? discountId, double amount)
        {
            double totalAmount = amount;
            double discountAmount = 0;
            if (discountId != null && discountId != Guid.Empty)
            {
                var discount = await _dbContext.DiscountEntities.FirstOrDefaultAsync(x => x.DiscountId == discountId);
                if (discount == null) throw new InvalidOperationException("Invalid discount");
                if (discount.ValidFrom > DateTime.UtcNow || discount.ValidTo < DateTime.UtcNow) throw new InvalidOperationException("This coupon code is not ");

                switch (discount.DiscountType)
                {
                    case DiscountType.Percentage:
                        totalAmount = ((amount * (double)(100 - discount.DiscountValue)) / 100);
                        discountAmount = (amount * (double)discount.DiscountValue) / 100;
                        break;
                    case DiscountType.Amount:
                        totalAmount = amount - (double)discount.DiscountValue;
                        discountAmount = (double)discount.DiscountValue;
                        break;
                }
            }
            return (totalAmount, discountAmount);
        }
        private CashfreeOrderDetailsDto MapCashfreeOrder(OrderEntity order)
        {
            return new CashfreeOrderDetailsDto()
            {
                CfOrderId = order.cf_order_id,
                OrderId = order.order_id,
                Entity = order.entity,
                OrderCurrency = order.order_currency,
                OrderAmount = order.order_amount,
                OrderStatus = order.order_status,
                PaymentSessionId = order.payment_session_id,
                OrderExpiryTime = order.order_expiry_time
            };
        }
    }
}
