﻿using PhoneNumbers;

namespace EngagetoEntities.Validations
{

    public class PhoneNumberValidator
    {
        private static readonly PhoneNumberUtil phoneNumberUtil = PhoneNumberUtil.GetInstance();

        public static (string, bool) ValidatePhoneNumber(string countryCode, string contact)
        {
            try
            {
                string trimmedCountryCode = countryCode.Trim().TrimStart('+');

                string fullNumber = "+" + trimmedCountryCode + contact;

                var parsedNumber = phoneNumberUtil.Parse(fullNumber, null);

                bool isValid = phoneNumberUtil.IsValidNumber(parsedNumber);

                if (isValid)
                {
                    string formattedNumber = phoneNumberUtil.Format(parsedNumber, PhoneNumberFormat.INTERNATIONAL);
                    return (formattedNumber, true);
                }
                else
                {
                    throw new Exception("This phone number format is not recognized. Please check the country code and phone number.");
                }
            }
            catch (NumberParseException ex)
            {
                throw new Exception("Error parsing phone number: " + ex.Message);
            }
            catch (FormatException ex)
            {
                throw new Exception("Invalid country code format: " + ex.Message);
            }
        }
    }
}


















