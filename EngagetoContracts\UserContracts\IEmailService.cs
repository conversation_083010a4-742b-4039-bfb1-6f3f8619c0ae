﻿using DocumentFormat.OpenXml.Wordprocessing;
using Microsoft.AspNetCore.Http;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace EngagetoContracts.UserContracts
{
    public interface IEmailService
    {
        Task<bool> SendEmailAsync(string to, string subject, string body, string logoUrl, string loginLink);
        Task<bool> SendClientEmailAsync(string to, string subject, string clientName, string email, string temporaryPassword, string logoUrl, string loginLink);
        Task<bool> SendEmailViaUtility(string subject , string body , List<IFormFile> attachment, List<string> to , List<string> cc, List<string> bcc);
        Task<bool> SendQualityRatingEmail(List<string> UserEmail, string previous_quality_score, string new_quality_score, List<string> clientName);




    }
}
