﻿using Microsoft.EntityFrameworkCore;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using EngagetoContracts.UserContracts;
using EngagetoEntities.UserEntities.Models;

namespace EngagetoRepository.UserRepository
{
    public class LanguageService : ILanguageService
    {
        private readonly ApplicationDBContext _dbContext;

        public LanguageService(ApplicationDBContext dbContext)
        {
            _dbContext = dbContext;
        }

        public async Task<Guid> CreateLanguageAsync(string languageCode, string languageName)
        {
            var language = new Language
            {
                LanguageId = Guid.NewGuid(),
                LanguageCode = languageCode,
                LanguageName = languageName
            };

            _dbContext.Languages.Add(language);
            await _dbContext.SaveChangesAsync();

            return language.LanguageId;
        }
        public async Task<bool> UpdateLanguageAsync(Guid languageId, LanguageUpdateDto languageUpdateDto)
        {
            var existingLanguage = await _dbContext.Languages.FindAsync(languageId);
            if (existingLanguage == null)
            {
                return false;
            }
            if (!string.IsNullOrEmpty(languageUpdateDto.LanguageCode))
            {
                existingLanguage.LanguageCode = languageUpdateDto.LanguageCode;
            }
            if (!string.IsNullOrEmpty(languageUpdateDto.LanguageName))
            {
                existingLanguage.LanguageName = languageUpdateDto.LanguageName;
            }

            await _dbContext.SaveChangesAsync();
            return true;
        }

        public async Task<bool> DeleteLanguageAsync(Guid languageId)
        {
            var existingLanguage = await _dbContext.Languages.FindAsync(languageId);
            if (existingLanguage == null)
            {
                return false;
            }

            _dbContext.Languages.Remove(existingLanguage);
            await _dbContext.SaveChangesAsync();
            return true;
        }
        public async Task<IEnumerable<LanguageDto>> GetAllLanguagesAsync(string searchKeyword = null)
        {
            IQueryable<Language> query = _dbContext.Languages;

            if (!string.IsNullOrEmpty(searchKeyword))
            {

                query = query.Where(l => l.LanguageCode.Contains(searchKeyword) || l.LanguageName.Contains(searchKeyword));
            }

            var languages = await query
                .Select(l => new LanguageDto
                {
                    LanguageId = l.LanguageId,
                    LanguageCode = l.LanguageCode,
                    LanguageName = l.LanguageName
                })
                .ToListAsync();

            return languages;
        }
        public async Task<Guid> SetUserPreferredLanguageAsync(Guid userId, string languageName)
        {
            var userLanguage = await _dbContext.UserLanguagePreferences
                .FirstOrDefaultAsync(ul => ul.UserId == userId);

            if (userLanguage == null)
            {
                var defaultLanguageId = await GetLanguageIdByNameAsync(languageName);

                userLanguage = new LanguagePreference
                {
                    Id = Guid.NewGuid(),
                    UserId = userId,
                    LanguageId = defaultLanguageId,
                };

                _dbContext.UserLanguagePreferences.Add(userLanguage);
            }
            else
            {
                var languageId = await GetLanguageIdByNameAsync(languageName);

                if (languageId != Guid.Empty)
                {
                    userLanguage.LanguageId = languageId;
                }
                else
                {

                    return Guid.Empty;
                }
            }
            try
            {
                await _dbContext.SaveChangesAsync();
            }
            catch (Exception ex)
            {
                Console.WriteLine(ex.ToString());
            }

            return userLanguage.Id;
        }

        private async Task<Guid> GetLanguageIdByNameAsync(string languageName)
        {
            var language = await _dbContext.Languages
                .FirstOrDefaultAsync(l => l.LanguageName == languageName);

            return language?.LanguageId ?? Guid.Empty;
        }
    }
}
