﻿using EngagetoEntities.Enums;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace EngagetoEntities.Entities
{
    [Table("UserWalletEntities")]
    public class UserWalletEntity : BaseEntity
    {

        [Key]
        [DatabaseGenerated(DatabaseGeneratedOption.Identity)]
        public Guid WalletId { get; set; }
        public Guid UserId { get; set; }
        public string? CompanyId { get; set; }
        public decimal Balance { get; set; }
        public decimal? ExpectedWalletBallance { get; set; } = 0;
        public string? Currency
        {
            get => _currency.ToString();
            set
            {
                if (Enum.TryParse(value, true, out Currency parsedCurrency))
                {
                    _currency = parsedCurrency;
                }
                else
                {
                    throw new ArgumentException($"Invalid currency value: {value}");
                }
            }
        }
        [NotMapped]
        private Currency _currency;
    }
}
