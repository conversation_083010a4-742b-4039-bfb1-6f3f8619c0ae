﻿using EngagetoEntities.Entities;

namespace EngagetoEntities.UserEntities.Dtos
{
    public class PlanDetailsDto
    {
        public string Type { get; set; }
        public string PlanDiscription { get; set; }
        public PlanEntities PlanDetails { get; set; }
        public List<ConversationAnalyticsPriceDto>? AnalyticsPrice { get; set; }
        public RequestResourcePermissionDto? ResourcePermission { get; set; }
    }
    public class PlanDto
    {
        public int Id { get; set; }
        public string Type { get; set; }
        public string PlanName { get; set; }
        public decimal PlanAmount { get; set; } 
        public string IGST { get; set; } = "IGST";
        public decimal IGSTAmount { get; set; }
        public decimal TotalAmount { get; set; }
        public decimal RemainingUpgradePlanAmount { get; set; } = 0;
        public DateTime ExpiryDate { get; set; }
    }
}
