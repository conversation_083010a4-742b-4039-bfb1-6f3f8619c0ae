﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace EngagetoEntities.UserEntities.Dtos
{
    public class ApiKeyDto
    {
        public string CompanyId { get; set; }
        public string Key { get; set; }
        public string? SecretKey { get; set; }
        public string? Owner { get; set; }
        public DateTime CreatedAt { get; set; }
        public bool IsActive { get; set; } = true;
        public ApiKeyDto() 
        { 
        }
        public ApiKeyDto(string companyId, string key, string? secretKey,string? owner, DateTime createdAt, bool isActive = true)
        {
            CompanyId = companyId;
            Key = key;
            Owner = owner;
            CreatedAt = createdAt;
            IsActive = isActive;
            SecretKey = secretKey;
        }
    }
}
