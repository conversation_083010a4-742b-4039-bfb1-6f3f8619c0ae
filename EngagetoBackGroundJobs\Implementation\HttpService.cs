﻿using EngagetoBackGroundJobs.Interfaces;
using Microsoft.Extensions.Logging;
using Newtonsoft.Json;
using System.Text;

namespace EngagetoBackGroundJobs.Implementation
{
    internal class HttpService : IHttpService
    {
        private readonly ILogger<HttpService> _logger;
        public HttpService(ILogger<HttpService> logger)
        {
            _logger = logger;
        }

        public async Task<T?> GetAsync<T>(string url, Dictionary<string, string> headers)
        {
            try
            {
                HttpClient client = new HttpClient();
                client.BaseAddress = new Uri(url);
                client.DefaultRequestHeaders.Add("Accept", "application/json");
                foreach (var header in headers)
                    client.DefaultRequestHeaders.Add(header.Key, header.Value);

                var response = await client.GetAsync(url);
                if (response.StatusCode == System.Net.HttpStatusCode.OK)
                {
                    var j = await response.Content.ReadAsStringAsync();
                    return JsonConvert.DeserializeObject<T>(await response.Content.ReadAsStringAsync());
                }
                else
                {
                    var j = await response.Content.ReadAsStringAsync();
                    _logger.LogError($"Getting exception while pulling record from Get API:satus code: {response.StatusCode},{response.Content}");
                    return default;
                }
            }
            catch (Exception ex)
            {
                _logger.LogError($"Getting exception while pulling record from Get API:{url},{ex}");
                return default;
            }
        }

        public async Task<T> PostAsync<T>(string url, Dictionary<string, string> headers, object body)
        {
            try
            {
                HttpClient client = new HttpClient();
                client.BaseAddress = new Uri(url);
                client.DefaultRequestHeaders.Add("Accept", "application/json");
                foreach (var header in headers)
                    client.DefaultRequestHeaders.Add(header.Key, header.Value);

                using StringContent jsonContent = new(JsonConvert.SerializeObject(body), Encoding.UTF8, "application/json");
                var response = await client.PostAsync(url, jsonContent);
                if (response.StatusCode == System.Net.HttpStatusCode.OK)
                {
                    return JsonConvert.DeserializeObject<T>(await response.Content.ReadAsStringAsync());
                }
                else
                    throw new Exception($"Getting exception while pulling record from API:satus code: {response.StatusCode},{response.Content}");
            }
            catch (Exception ex)
            {
                _logger.LogError($"Getting exception while pulling record from Post API:{url},{ex}");
                throw;
            }
        }
    }
}
