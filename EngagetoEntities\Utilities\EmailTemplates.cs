﻿using System;

namespace EngagetoEntities.Utilities
{
    public static class EmailTemplates
    {
        private static string Welcome { get; set; } = @"
<!DOCTYPE html>
<html lang=""en"">
<head>
    <meta charset=""UTF-8"">
    <meta name=""viewport"" content=""width=device-width, initial-scale=1.0"">
    <title>Welcome to Engageto</title>
    <style>
        body {
            margin: 0;
            padding: 0;
            font-family: Arial, sans-serif;
            background: linear-gradient(to bottom right, #d1f7d6, #ffe6e9, #e6f7ff); /* Subtle gradient background */
            color: #333;
        }
        .container {
            max-width: 600px;
            margin: 20px auto;
            background-color: #fff;
            border-radius: 8px;
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
            overflow: hidden;
        }
        .header {
            background-color: #ffffff; /* White to match your app's card theme */
            text-align: center;
            padding: 20px;
            border-bottom: 1px solid #eee;
        }
        .header img {
            max-width: 150px;
        }
        .content {
            padding: 20px;
        }
        .content h1 {
            color: #009933; /* Green color for headings */
            font-size: 24px;
            margin-top: 0;
        }
        .content p {
            line-height: 1.6;
            color: #555;
        }
        .content .cta-button {
            display: inline-block;
            margin: 20px 0;
            padding: 12px 20px;
            background-color: #009933; /* Green button matching your app */
            color: white;
            text-decoration: none;
            border-radius: 5px;
            font-weight: bold;
            text-align: center;
        }
        .footer {
            text-align: center;
            background-color: #ffffff; /* White footer like your app */
            padding: 10px;
            border-top: 1px solid #eee;
            font-size: 12px;
            color: #888;
        }
        .footer a {
            color: #009933;
            text-decoration: none;
        }
    </style>
</head>
<body>
    <div class=""container"">
        <!-- Header Section -->
        <div class=""header"">
            <img src=""https://dev-engageto.s3.ap-south-1.amazonaws.com/cropped-Engageto-logo-01-2048x684%20(1)_1735632797.png"" alt=""Engageto Logo"">
        </div>

        <!-- Main Content Section -->
        <div class=""content"">
            <h1>Welcome to Engageto!</h1>
            <p>Hi #UserName#,</p>
            <p>We’re excited to have you onboard! Your registration with <strong>Engageto</strong> has been successfully completed, and you’re now ready to supercharge your marketing efforts.</p>
            
            <h3>Your Account Details:</h3>
            <p><strong>Username:</strong> #UserEmail#</p>
            <p><strong>Password:</strong> #Password#</p>
            <a href=""https://app.engageto.in"" class=""cta-button"">Log In to Your Dashboard</a>

            <h3>Next Steps:</h3>
            <p>1. Log in to your dashboard using the credentials provided above.</p>
            <p>2. Complete the embedded signup by connecting your <strong>Facebook account</strong>, adding <strong>company information</strong>, and registering <strong>WhatsApp numbers</strong>.</p>
            <p>3. Start exploring our powerful WhatsApp marketing and automation tools.</p>

            <p>If you have any questions, feel free to reach out to us. Our team is here to ensure your success!</p>
        </div>

        <!-- Footer Section -->
        <div class=""footer"">
    	   <p>&copy; 2025 Engageto. All Rights Reserved.</p>
    	   <p>Thanks & Regards,</p>
    	   <p>The Engageto Team</p>
    	   <p> 
              <a href=""https://engageto.com/"">Contact Us</a>
    	   </p>
	</div>
    </div>
</body>
</html>
";
        public static string GetWelcomeEmail(string userName, string userEmail, string password)
        {
            string emailTemplate = EmailTemplates.Welcome;
            emailTemplate = emailTemplate.Replace("#UserName#", userName)
                                         .Replace("#UserEmail#", userEmail)
                                         .Replace("#Password#", password);

            return emailTemplate;
        }

    }
}
