﻿using Newtonsoft.Json;

namespace EngagetoEntities.Dtos.CommonDtos.PaymentGatwayDtos
{
    public class CashfreeOrderDetailsDto
    {

        public string? CfOrderId { get; set; }

        public string? OrderId { get; set; }

        public string? Entity { get; set; }

        public string? OrderCurrency { get; set; }

        public decimal? OrderAmount { get; set; }

        public string? OrderStatus { get; set; }

        public string? PaymentSessionId { get; set; }

        public DateTime? OrderExpiryTime { get; set; }
    }
}
