﻿using EngagetoContracts.UserContracts;
using EngagetoEntities.DdContext;
using EngagetoEntities.Entities;
using EngagetoEntities.Enums;
using Microsoft.EntityFrameworkCore;

namespace EngagetoRepository.UserRepository
{
    public class ConversationRepository : IConversationService
    {
        private readonly ApplicationDbContext _conversationContext;

        public ConversationRepository(ApplicationDbContext conversationContext)
        {
            _conversationContext = conversationContext;
        }

        public async Task<Conversations?> GetMessageByIdAsync(Guid id)
        {
            return await _conversationContext.Conversations.FirstOrDefaultAsync(c => c.Id == id);
        }

        public async Task<List<Conversations>> SaveConversationsAsync(List<Conversations> conversations)
        {
            await _conversationContext.Conversations.AddRangeAsync(conversations);
            await _conversationContext.SaveChangesAsync();
            return conversations;
        }

        public async Task<Conversations> SaveTextMediaConversationAsync(string? textMessage, string from, string to, string? mediaType, string? caption, string? mediaMimeType, string? mediaUrl, string waMessageId, string? replyId)
        {
            Guid contactId = Guid.Empty;
            Guid businessId = Guid.TryParse(from, out var bid) ? bid : Guid.TryParse(to, out bid) ? bid : Guid.Empty;
            string number = businessId == (Guid.TryParse(from, out _) ? bid : Guid.Empty) ? to.Replace("+", "") : from.Replace("+", "");

            if (businessId != Guid.Empty)
            {
                contactId = _conversationContext.Contacts
                .FirstOrDefault(c => c.BusinessId == businessId && (c.CountryCode + c.Contact).Replace("+", "") == number)
                ?.ContactId ?? Guid.Empty;
            }

            Conversations conv = new Conversations
            {
                Id = Guid.NewGuid(),
                CreatedAt = DateTime.UtcNow,
                WhatsAppMessageId = waMessageId,
                TextMessage = textMessage ?? string.Empty,
                MediaCaption = caption ?? string.Empty,
                ReplyId = replyId,
                To = to.ToLowerInvariant().Replace("+", ""),
                From = from.ToString().ToLowerInvariant().Replace("+", ""),
                BusinessId = businessId,
                ContactId = contactId,
                MediaUrl = mediaUrl,
                Status = ConvStatus.sent,
                MediaMimeType = mediaMimeType,
                MessageType = MessageType.Normal,
            };
            await _conversationContext.Conversations.AddAsync(conv);
            await _conversationContext.SaveChangesAsync();
            return conv;
        }
    }
}
