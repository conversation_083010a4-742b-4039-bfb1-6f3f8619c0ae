﻿using Newtonsoft.Json.Linq;
using System.ComponentModel.DataAnnotations.Schema;
using System.Reflection;
using System.Text.RegularExpressions;

namespace EngagetoEntities.Utilities
{
    public static class StringHelper
    {
        private static readonly Regex TemplateNameRegex = new Regex("^[a-z0-9_]+$", RegexOptions.Compiled);
        private static readonly Regex VariableRegex = new Regex("\\{\\{\\d+\\}\\}", RegexOptions.Compiled);
        private static readonly Regex PlaceholderWithWordsBetweenRegex = new Regex(@"\s*\{\{\d+\}\}\s+.*?(?=\b\w+\b).*?\s\{\{\d+\}\}\s*", RegexOptions.Singleline);
        private static readonly Regex PhoneRegex = new Regex(@"^\+\d{1,3}\s?\(\d{1,4}\)\s?\d{6,}$");
        private static readonly Regex UrlRegex = new Regex("^(https?://)?([\\w-]+\\.)+\\w{2,}(:\\d+)?(/[\\w-./?%&=]*)?(\\{\\{\\d+\\}})?$", RegexOptions.Compiled);
        private static readonly Regex VariableRegexs = new Regex(@"\{\{\d+\}\}");
        private static readonly Regex NameRegex = new Regex(@"^[A-Za-z\s'-]{2,50}$", RegexOptions.Compiled);
        private static readonly Regex EmailRegex = new Regex(@"^[A-Za-z0-9._%+-]+@[A-Za-z0-9.-]+\.[A-Za-z]{2,}$", RegexOptions.Compiled);
        private static readonly Regex LeadratVariableRegex = new Regex(@"#([^\s#][\w\s]*[^\s#])#");
        public static string FormateTemplateComponents(string text) => text.Replace("\"", "\\\"").Replace("\n", "\\n");


        public static DateTime GetIndianDateTime()
        {
            TimeZoneInfo indiaZone = TimeZoneInfo.FindSystemTimeZoneById("India Standard Time");
            DateTime utcNow = DateTime.UtcNow;
            return TimeZoneInfo.ConvertTimeFromUtc(utcNow, indiaZone);
        }

        public static DateTime ConvertIntoIndianDateTime(DateTime dateTime)
        {
            TimeZoneInfo indiaZone = TimeZoneInfo.FindSystemTimeZoneById("India Standard Time");
            return TimeZoneInfo.ConvertTimeFromUtc(dateTime, indiaZone);
        }
        public static DateTimeOffset ConvertToDateTimeOffset(DateTime? dateTime)
        {
            if (dateTime.HasValue)
            {
                var dt = dateTime.Value;
                TimeSpan offset;

                if (dt.Kind == DateTimeKind.Utc)
                    offset = TimeSpan.Zero;
                else if (dt.Kind == DateTimeKind.Local)
                    offset = TimeZoneInfo.Local.GetUtcOffset(dt);
                else
                    offset = TimeZoneInfo.Local.GetUtcOffset(dt);

                return new DateTimeOffset(dt, offset);
            }
            else
            {
                return DateTimeOffset.MinValue;
            }
        }
        public static List<string> GetPropertyNames<T>(bool isNotMappedAttributeColumn = true)
        {
            if (isNotMappedAttributeColumn)
            {
                return typeof(T).GetProperties(BindingFlags.Public | BindingFlags.Instance)
                                .Select(prop => prop.Name)
                                .ToList();
            }
            return typeof(T).GetProperties(BindingFlags.Public | BindingFlags.Instance)
                        .Where(prop => !prop.GetCustomAttributes<NotMappedAttribute>().Any())
                        .Select(prop => prop.Name)
                        .ToList();
        }
        public static string GetTableName<T>()
        {
            var tableAttribute = (TableAttribute)Attribute.GetCustomAttribute(typeof(T), typeof(TableAttribute));
            if (tableAttribute != null)
            {
                return tableAttribute.Name;
            }
            return typeof(T).Name;
        }

        public static string[] SplitString(string tags) => !string.IsNullOrEmpty(tags) ? tags.Split(',') : Array.Empty<string>();
        public static string JoinString(string[] tags) => tags != null && tags.Length > 0 ? string.Join(",", tags) : null;
        public static bool IsValidTemplateName(string name) => TemplateNameRegex.IsMatch(name);
        public static bool IsValidVariable(string name) => VariableRegex.IsMatch(name);
        public static int GetVariableCount(string name)
        {
            MatchCollection matches = VariableRegex.Matches(name);
            return matches.Count;
        }
        public static Regex GetVariableRegex() => VariableRegex;
        public static Regex GetVariableRegexs() => VariableRegexs;
        public static Regex GetPlaceholderWithWordsBetweenRegex() => PlaceholderWithWordsBetweenRegex;
        public static bool IsValidPhoneNumber(string phoneNumber) => PhoneRegex.IsMatch(phoneNumber);
        public static bool IsValidUrl(string url) => UrlRegex.IsMatch(url);
        public static Dictionary<string, object> GetPropertyNamesAndValues(object obj)
        {
            Dictionary<string, object> keyValuePairs = new();
            Type type = obj.GetType();
            PropertyInfo[] properties = type.GetProperties();

            foreach (var property in properties)
            {
                string propertyName = property.Name;

                object propertyValue = property.GetValue(obj);

                keyValuePairs.Add(propertyName, propertyValue);
            }
            return keyValuePairs;
        }

        public static DateTime GetLocalDateTime()
        {
            TimeZoneInfo localZone = TimeZoneInfo.Local;
            DateTime utcNow = DateTime.UtcNow;
            return TimeZoneInfo.ConvertTimeFromUtc(utcNow, localZone);
        }
        public static DateTime GetLocalDateTime(DateTime utcDate)
        {
            TimeZoneInfo localZone = TimeZoneInfo.Local;
            return TimeZoneInfo.ConvertTimeFromUtc(utcDate, localZone);
        }
        public static string FormateString(string text) => text.Replace("\\\"", "\"").Replace("\\n", "\n");

        public static bool IsValidName(string name) => !string.IsNullOrEmpty(name) && NameRegex.IsMatch(name);
        public static bool IsValidEmail(string email) => !string.IsNullOrEmpty(email) && EmailRegex.IsMatch(email);
        public static bool HasProperty(dynamic obj, string propertyName)
        {
            return ((JObject)obj).ContainsKey(propertyName);
        }
        public static string FormateEscapeSequences(string input)
        {
            // Replace newline characters with \n
            string escapedString = input.Replace("\r\n", "\\n").Replace("\n", "\\n");

            // Escape double quotes
            escapedString = escapedString.Replace("\"", "\\\"");

            return escapedString;
        }
        public static (string UpdatedMessage, List<string> Variables) ReplaceAndExtractVariables(string message)
        {
            var variables = new List<string>();
            int index = 1;
            // Replace variables with placeholders and collect them in a list
            string updatedMessage = LeadratVariableRegex.Replace(message, match =>
            {
                string variable = match.Value;
                if (!variables.Contains(variable))
                {
                    variables.Add(variable); // Add to the list if not already added
                }
                return $"{{{{{index++}}}}}"; // Replace with placeholder and increment index
            });

            return (updatedMessage, variables);
        }
        public static bool IsLeadratVariable(string text)
        {
            if (string.IsNullOrWhiteSpace(text))
            {
                return false;
            }
            return LeadratVariableRegex.IsMatch(text);
        }

        public static string ReplacePlaceholders(string message, List<string> values)
        {
            for (int i = 0; i < values.Count; i++)
            {
                // Replace placeholders like {{1}}, {{2}}, etc., with corresponding values
                string placeholder = $"{{{{{i + 1}}}}}"; // Builds {{1}}, {{2}}, etc.
                message = message.Replace(placeholder, values[i]);
            }
            return message;
        }
        public static List<string> GetLeadratVariables(string text)
        {
            var variables = new List<string>();

            // Regular expression to match variables like #name#, #phone#
            string pattern = @"#(.*?)#";

            MatchCollection matches = Regex.Matches(text, pattern);

            foreach (Match match in matches)
            {
                variables.Add(match.Value); // Adds #name#, #phone#, etc.
            }

            return variables;
        }
        public static string? GetValueFromResponse(dynamic result, string path)
        {
            try
            {
                // Split the path into parts (e.g., "messages[0].id" -> ["messages", "0", "id"])
                var parts = path.Split('.');
                dynamic current = result;

                foreach (var part in parts)
                {
                    if (current == null) return null;

                    // Handle array indexing (e.g., "messages[0]")
                    if (part.Contains("[") && part.Contains("]"))
                    {
                        var propertyName = part.Substring(0, part.IndexOf('['));
                        var index = int.Parse(part.Substring(part.IndexOf('[') + 1, part.IndexOf(']') - part.IndexOf('[') - 1));

                        current = current[propertyName]?[index];
                    }
                    else
                    {
                        current = current[part];
                    }
                }

                return current?.ToString();
            }
            catch
            {
                // Return null if any part of the traversal fails
                return null;
            }
        }

        public static string ExtractVariables(string body)
        {
            var matches = StringHelper.GetVariableRegexs().Matches(body);
            var placeholders = new List<string>();

            foreach (Match match in matches)
            {
                placeholders.Add(match.Value.Trim('{', '}'));
            }
            return string.Join(",", placeholders.Select(p => $"\"{p}\""));
        }

        public static string ReplaceVariable(string text, string[]? values)
        {
            for (int i = 0; i < values.Length; i++)
            {
                text = text.Replace($"{{{{{i + 1}}}}}", values[i]); // Assuming each example value is an array of one element
            }
            return text;
        }

    }
}
