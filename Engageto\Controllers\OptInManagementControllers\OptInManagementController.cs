﻿using EngagetoContracts.OptinContracts;
using Microsoft.AspNetCore.Mvc;
using System.Security.Claims;
using Microsoft.AspNetCore.Authorization;
using EngagetoEntities.DdContext;
using EngagetoEntities.Dtos.OptInManagementDto;

namespace Engageto.Controllers.OptInManagementControllers
{
    [Route("api/[controller]")]
    [ApiController]
    public class OptInManagementController : ControllerBase
    {
        private readonly IOptin _iOptinService;
        private readonly IConfiguration _configuration;
        private readonly ApplicationDbContext _dbContext;
        private readonly EngagetoDapper.Data.Interfaces.IUserInterfaces.IUserServices.IUserService _userService;


        public OptInManagementController(EngagetoDapper.Data.Interfaces.IUserInterfaces.IUserServices.IUserService userService,
                                          ApplicationDbContext dbContext, IConfiguration configuration, IOptin iOptinService)
        {
            _userService = userService;
            _dbContext = dbContext;
            _configuration = configuration;
            _iOptinService = iOptinService;
        }

        [HttpPost("OptInManagement")]
        [Authorize]
        public async Task<IActionResult> AddKeywords([FromBody] AddKeywordRequestDto model)
        {
            try
            {
                var currentUserIdClaim = User.Claims.FirstOrDefault(c => c.Type == ClaimTypes.Name);
               
                if (currentUserIdClaim == null || !Guid.TryParse(currentUserIdClaim.Value, out Guid currentUserId))
                {
                    return BadRequest(new { Message = "Invalid current user." });
                }

                var currentUserNameClaim = User.Claims.FirstOrDefault(c => c.Type == "UserName")?.Value;

                var results = await _userService.IsValidateMetaAccount(model.BusinessId.ToString(), currentUserId);

                var resultId = await _iOptinService.AddKeyword(model, currentUserId.ToString(),currentUserNameClaim.ToString());

                return Ok(new { Message = resultId });
            }
            catch (Exception ex)
            {
                return BadRequest(new { Message = ex.Message });
            }
        }
        [HttpGet("OptOutKeyword/{BusinessId}")]
        [Authorize]
        public async Task<IActionResult> GetOptOutKeywords(string BusinessId)
        {
            var currentUserIdClaim = User.Claims.FirstOrDefault(c => c.Type == ClaimTypes.Name);
            if (currentUserIdClaim == null || !Guid.TryParse(currentUserIdClaim.Value, out Guid currentUserId))
            {
                return BadRequest(new { Message = "Invalid current user." });
            }
            // Handle media meta validation in all case.
            var results = await _userService.IsValidateMetaAccount(BusinessId.ToString(), currentUserId);
            var result = await _iOptinService.GetOptOutKeywordsByIdAsync(BusinessId, currentUserId.ToString());

            return Ok(result);
        }
        [HttpGet("OptInKeyword/{BusinessId}")]
        [Authorize]
        public async Task<IActionResult> GetOptInKeywords(string BusinessId)
        {
            var currentUserIdClaim = User.Claims.FirstOrDefault(c => c.Type == ClaimTypes.Name);
            if (currentUserIdClaim == null || !Guid.TryParse(currentUserIdClaim.Value, out Guid currentUserId))
            {
                return BadRequest(new { Message = "Invalid current user." });
            }
            var results = await _userService.IsValidateMetaAccount(BusinessId.ToString(), currentUserId);
            var result = await _iOptinService.GetOptInKeywordsByIdAsync( BusinessId, currentUserId.ToString());
          
            return Ok(result);
        }

        [HttpDelete("{id}/{BusinessId}/{keyword}")]
        [Authorize]
        public async Task<IActionResult> RemoveOptOutKeyword(Guid id, string BusinessId, string keyword)
        {
            var currentUserIdClaim = User.Claims.FirstOrDefault(c => c.Type == ClaimTypes.Name);
            if (currentUserIdClaim == null || !Guid.TryParse(currentUserIdClaim.Value, out Guid currentUserId))
            {
                return BadRequest(new { Message = "Invalid current user." });
            }
            // Handle media meta validation in all case.
            var results = await _userService.IsValidateMetaAccount(BusinessId.ToString(), currentUserId);
            var result = await _iOptinService.RemoveKeywordAsync(id, BusinessId, keyword);

            if (result)
            {
                return Ok(new { Message = "Keyword removed successfully.", Result = result });
            }
            else
            {
                return NotFound(new { Message = "Keyword not found or error occurred." , Result = result });
            }
        }
    }
}
