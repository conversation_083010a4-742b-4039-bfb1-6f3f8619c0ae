﻿/*using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using System.Security.Claims;
using UserContracts;
using UserEntities.Models;
using UserRepository;

namespace UserManagementService.Controllers
{
    [Route("api/[controller]")]
    [ApiController]
    public class Ahex_CRM_UserController : ControllerBase
    {
        private readonly IAhex_CRM_UserService _ahex_CRM_UserService;

        public Ahex_CRM_UserController(IAhex_CRM_UserService ahex_CRM_UserService)
        {
            _ahex_CRM_UserService = ahex_CRM_UserService;
        }
        [HttpPut("update-account/{currentUserId}")]
        [Authorize]
        public async Task<IActionResult> UpdateAccounts([FromForm] Ahex_CRM_UsersDto updateRequest)
        {
            try
            {

                var currentUserIdClaim = User.Claims.FirstOrDefault(c => c.Type == ClaimTypes.Name);

                if (currentUserIdClaim == null || !Guid.TryParse(currentUserIdClaim.Value, out Guid currentUserId))
                {
                    return BadRequest(new { Message = "Invalid current user." });
                }


                var updatedManageAccountId = await _ahex_CRM_UserService.UpdateManageAccountAsync(currentUserId, updateRequest);

                return Ok(new { ManageAccountId = updatedManageAccountId, Message = "ManageAccount updated successfully." });
            }
            catch (InvalidOperationException ex)
            {
                return BadRequest(new { Message = ex.Message });
            }
            catch (Exception ex)
            {
                return StatusCode(500, new { Message = $"Internal Server Error: {ex.Message}" });
            }
        }
        [HttpPost("add-teammember")]
        [Authorize]
        public async Task<IActionResult> AddTeamMember([FromBody] User_TeamMember teamMemberRequest)
        {
            try
            {

                var currentUserIdClaim = User.Claims.FirstOrDefault(c => c.Type == ClaimTypes.Name);

                if (currentUserIdClaim == null || !Guid.TryParse(currentUserIdClaim.Value, out Guid currentUserId))
                {
                    return BadRequest(new { Message = "Invalid current user." });
                }

                var result = await _ahex_CRM_UserService.AddTeamMemberAsync(currentUserId, teamMemberRequest);

                if (result)
                    return Ok(new { Message = "Team member added successfully." });
                else
                    return BadRequest(new { Message = "Failed to add team member." });
            }
            catch (InvalidOperationException ex)
            {
                return BadRequest(new { Message = ex.Message });
            }
            catch (Exception ex)
            {
                return StatusCode(500, new { Message = "Internal Server Error" });
            }
        }
        [HttpPut("update-company/{companyId}")]
        //[AuthorizePermission("Update/Delete-BusinessDetails")]
        [Authorize]
        public async Task<IActionResult> UpdateCompany(Guid companyId, [FromForm] Ahex_CRM_BusinessDetailsDto updatedCompany)
        {
            try
            {
                var currentUserIdClaim = User.Claims.FirstOrDefault(c => c.Type == ClaimTypes.Name);

                if (currentUserIdClaim == null || !Guid.TryParse(currentUserIdClaim.Value, out Guid currentUserId))
                {
                    return BadRequest(new { Message = "Invalid current user." });
                }


                updatedCompany ??= new Ahex_CRM_BusinessDetailsDto();

                var result = await _ahex_CRM_UserService.UpdateCompanyFieldsAsync(currentUserId, companyId, updatedCompany);

                if (result)
                {
                    return Ok(new { Message = "Company updated successfully." });
                }
                else
                {
                    return BadRequest(new { Message = "Failed to update company." });

                }
            }

            catch (Exception ex)
            {
                return StatusCode(500, new { Message = $"Internal Server Error: {ex.Message}" });
            }
        }
        [HttpPost("add-client")]
        //[AuthorizePermission("Add/Update/Delete-Client")]
        [Authorize]
        public async Task<IActionResult> AddClient([FromForm] Ahex_CRMClientDetailsDto clientDetailsDto)
        {
            try
            {
                var currentUserIdClaim = User.Claims.FirstOrDefault(c => c.Type == ClaimTypes.Name);

                if (currentUserIdClaim == null || !Guid.TryParse(currentUserIdClaim.Value, out Guid currentUserId))
                {
                    return BadRequest(new { Message = "Invalid current user." });
                }

                var clientid = await _ahex_CRM_UserService.AddClientAsync(currentUserId, clientDetailsDto);

                return Ok(new { Id = clientid });
            }
            catch (InvalidOperationException ex)
            {
                return BadRequest(new { Message = ex.Message });
            }
            catch (Exception ex)
            {
                return StatusCode(500, new { Message = $"Internal Server Error: {ex.Message}" });
            }
        }
    }
}
*/