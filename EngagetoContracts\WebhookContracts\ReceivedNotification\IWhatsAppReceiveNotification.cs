﻿using EngagetoEntities.Dtos.WebhookReceiveNotification;

namespace EngagetoContracts.WebhookContracts.ReceivedNotification
{
    public interface IWhatsAppReceiveNotification
    {
        /// <summary>
        /// to store text message data into database
        /// </summary>
        /// <param name="textMessage">received text message data</param>
        /// <param name="textMessageMetadata">to whom they sent message for example phone number id</param>
        /// <returns>void</returns>
        Task ReceiveNotification(TextMessage textMessage,TextMessageMetadata textMessageMetadata);
        /// <summary>
        /// to store Image message data into database
        /// </summary>
        /// <param name="imageMessage">information about image sender</param>
        /// <param name="image">information about image details</param>
        /// <param name="imageMessageMetadata">to whom they sent message for example phone number id</param>
        /// <returns>void</returns>
        Task ReceivedImageMessage(ImageMessage imageMessage,Image image,ImageMessageMetadata imageMessageMetadata);
        /// <summary>
        /// to store audio message data into database
        /// </summary>
        /// <param name="audioMessage">information about audio sender</param>
        /// <param name="audio">information about audio details</param>
        /// <param name="audioMessageMetadata">to whom they sent message for example phone number id</param>
        /// <returns>void</returns>
        Task ReceivedAudioMessage(AudioMessage audioMessage,Audio audio,AudioMessageMetadata audioMessageMetadata);
        /// <summary>
        /// to store document message data into database
        /// </summary>
        /// <param name="documentMessage">information about document sender</param>
        /// <param name="document">information about documment details</param>
        /// <param name="documentMessageMetadata">to whom they sent message for example phone number id</param>
        /// <returns>void</returns>
        Task ReceivedDocumentMessage(DocumentMessage documentMessage,Document document,DocumentMessageMetadata documentMessageMetadata);
        /// <summary>
        /// to store video message data into database
        /// </summary>
        /// <param name="videoMessage">information about document sender</param>
        /// <param name="video">information about documment details</param>
        /// <param name="videoMessageMetadata">to whom they sent message for example phone number id</param>
        /// <returns>void</returns>
        Task ReceivedVideoMessage(VideoMessage videoMessage, Video video, VideoMessageMetadata videoMessageMetadata);

    
    }
}
