﻿namespace EngagetoEntities.UserEntities.Dtos
{
    public class ExpectedWalletBalanceDto
    {
        public string CompanyId { get; set; }
        public string? SubscriptionPlanName { get; set; }
        public int? PlanId { get; set; }
        public bool? IsActive { get; set; } = false;
        public decimal? WalletBalance { get; set; } = 0;
        public int CampaignCount { get; set; } = 0;
        public int ConversationCount { get; set; } = 0;
        public int TotalAudianceCampaignCount { get; set; } = 0;
        public decimal? ExpectedWalletBalance { get; set; } = 0;
    }
}
