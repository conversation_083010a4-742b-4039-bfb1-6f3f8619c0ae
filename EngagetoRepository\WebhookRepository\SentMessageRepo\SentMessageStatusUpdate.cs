﻿using Engageto.Hubs;
using EngagetoContracts.WebhookContracts.SentMessage;
using EngagetoDapper.Data.Interfaces.IUserInterfaces.IUserServices;
using EngagetoEntities.DdContext;
using EngagetoEntities.Dtos.ConversationDtos;
using EngagetoEntities.Dtos.WebhookDtos;
using EngagetoEntities.Entities;
using EngagetoEntities.Utilities;
using Humanizer;
using Microsoft.AspNetCore.SignalR;
using Newtonsoft.Json.Linq;


namespace EngagetoRepository.WebhookRepository.SentMessageRepo
{
    public class SentMessageStatusUpdate : IWhatsAppBusinessNotificarion
    {

        private readonly ApplicationDbContext _appDbContext;
        private IHubContext<MessageHub, IMessageHubClient> messageHub;
        private readonly IWebhookService _webhookService;
        private readonly EngagetoDapper.Data.Interfaces.IConversationAnalyticsInterfaces.IConversationAnalyticsService _conversationAnalyticsService;


        public SentMessageStatusUpdate(ApplicationDbContext appDbContext,
            IHubContext<MessageHub, IMessageHubClient> messageHub,
            IWebhookService webhookService,
            EngagetoDapper.Data.Interfaces.IConversationAnalyticsInterfaces.IConversationAnalyticsService conversationAnalyticsService)
        {
            this._appDbContext = appDbContext;
            this.messageHub = messageHub;
            _webhookService = webhookService;
            _conversationAnalyticsService = conversationAnalyticsService;
        }
        public object? GetReplyItems(IEnumerable<Conversations>? query, string? replyId)
        {
            if (replyId != null)
            {
                return query?.Where(m => m.WhatsAppMessageId == replyId)?.Select(i => new
                {
                    Id = i.Id,
                    WhatsAppMessageId = i.WhatsAppMessageId,
                    From = i.From,
                    To = i.To,
                    BusinessId = i.BusinessId,
                    ContactId = i.ContactId,
                    Status = i.Status.ToString(),
                    CreatedAt = i.CreatedAt,
                    TextMessage = i.TextMessage,
                    MediaFileName = i.MediaFileName,
                    MediaMimeType = i.MediaMimeType,
                    MediaUrl = i.MediaUrl,
                    MediaCaption = i.MediaCaption,
                    TemplateMediaType = i.TemplateMediaType,
                    TemplateMediaFile = i.TemplateMediaUrl,
                    TemplateHeader = i.TemplateHeader,
                    TemplateBody = i.TemplateBody,
                    TemplateFooter = i.TemplateFooter,
                    CallButtonName = i.CallButtonName,
                    PhoneNumber = i.PhoneNumber,
                    UrlButtonNames = (i.UrlButtonNames ?? string.Empty).Split(new[] { ',' }, StringSplitOptions.RemoveEmptyEntries)
                    .Select(name => name.Trim())
                    .Where(name => !string.IsNullOrEmpty(name))
                    .ToArray(),
                    RedirectUrls = (i.RedirectUrls ?? string.Empty).Split(new[] { ',' }, StringSplitOptions.RemoveEmptyEntries)
                    .Select(name => name.Trim())
                    .Where(name => !string.IsNullOrEmpty(name))
                    .ToArray(),
                    QuickReplies = (i.QuickReplies ?? string.Empty).Split(new[] { ',' }, StringSplitOptions.RemoveEmptyEntries)
                    .Select(name => name.Trim())
                    .Where(name => !string.IsNullOrEmpty(name))
                    .ToArray()
                }).FirstOrDefault();
            }
            return null;
        }
        public async Task UpdateSentMessageStatus(dynamic Status)
        {
            DateTimeOffset dateTimeOffset = DateTimeOffset.FromUnixTimeSeconds((int)Status["timestamp"]);
            DateTime dateTime = dateTimeOffset.LocalDateTime;
            var status = Convert.ToString(Status["status"]);
            string messageId = Convert.ToString(Status["id"]);
            
            string? error = null;
            string? errorMessaage = null;
            JArray? errors = null;
            if (StringHelper.HasProperty(Status, "errors"))
            {
                errors = Status["errors"];
                error = errors?[0]?.ToString();
                var errorData = errors?[0]?["error_data"] as JObject;
                errorMessaage = errorData?["details"]?.ToString();
            }
            //  Messages sentMessage = _appDbContext.Messages.Where(m => m.WhatsAppMessageId ==messageId).FirstOrDefault();
            // Assuming _appDbContext is your DbContext instance
            EngagetoEntities.Entities.Conversations sentMessage = _appDbContext.Conversations.FirstOrDefault(m => m.WhatsAppMessageId == messageId);
            if (sentMessage == null)
                return;


            Guid contactId = Guid.Empty;
            Guid businessId = Guid.TryParse(sentMessage.From, out var bid) ? bid : Guid.TryParse(sentMessage.To, out bid) ? bid : Guid.Empty;
            string number = businessId == (Guid.TryParse(sentMessage.From, out _) ? bid : Guid.Empty) ? sentMessage.To.Replace("+", "") : sentMessage.From.Replace("+", "");

            if (businessId != Guid.Empty)
            {
                contactId = _appDbContext.Contacts
                .FirstOrDefault(c => c.BusinessId == businessId && (c.CountryCode + c.Contact).Replace("+", "") == number)
                ?.ContactId ?? Guid.Empty;
            }

            var BusinessId = _appDbContext.BusinessDetailsMetas.FirstOrDefault(m => m.PhoneNumberID == sentMessage.From)?.BusinessId; // Retrieve the second item or null if not found
            if (BusinessId == null)
            {
                BusinessId = sentMessage.From;
            }
            List<EngagetoEntities.Entities.Conversations> query = new List<EngagetoEntities.Entities.Conversations>();
            if (sentMessage.ReplyId != null)
            {
                query = _appDbContext.Conversations?.Where(m => m.WhatsAppMessageId == sentMessage.ReplyId)?.ToList();
            }

            if (sentMessage != null)
            {
                if (status == "delivered")
                {
                    sentMessage.ErrorMessage = null;
                    sentMessage.Status = EngagetoEntities.Enums.ConvStatus.delivered;
                    EngagetoEntities.Dtos.WebhookDtos.Messages messages = new();
                    var obj = messages.Message(sentMessage, query);
                    List<EngagetoEntities.Dtos.ConversationDtos.ConversationDto> list = new List<EngagetoEntities.Dtos.ConversationDtos.ConversationDto>();
                    list.Add(obj);
                    // adding this code to detuct the cost of conversation
                    await _conversationAnalyticsService.SaveConversationCostDetuctionHistoryAsync(new List<EngagetoEntities.Entities.Conversations> { sentMessage }, BusinessId, sentMessage.UserId);
                    if (BusinessId != null)
                    {
                        var data = _appDbContext.Users.Where(m => m.CompanyId == BusinessId);
                        if (data != null)
                        {
                            var UserIds = data.Select(m => m.Id).ToList();
                            foreach (var UserId in UserIds)
                            {
                                await messageHub.Clients.Groups(BusinessId.ToString().ToLower() + UserId.ToString().ToLower()).ReceiveMessageFromServer(list);
                            }
                        }
                    }
                    _appDbContext.Update(sentMessage);
                    _appDbContext.SaveChanges();
                    await _webhookService.SaveWebhookEventsAsync(BusinessId, "UpdatedConversationDeliveredMessage", sentMessage);
                }
                if(status == "failed")
                {
                    sentMessage.ErrorMessage = errorMessaage;
                    sentMessage.ErrorDetails = error;
                    sentMessage.Status = EngagetoEntities.Enums.ConvStatus.failed;
                    Messages messages = new Messages();
                    var obj = messages.Message(sentMessage, query);
                    List<ConversationDto> list = new List<ConversationDto>();
                    list.Add(obj);
                    // adding this code to detuct the cost of conversation
                    await _conversationAnalyticsService.SaveConversationCostDetuctionHistoryAsync(new List<Conversations> { sentMessage }, BusinessId, sentMessage.UserId);
                    if (BusinessId != null)
                    {
                        var data = _appDbContext.Users.Where(m => m.CompanyId == BusinessId);
                        if (data != null)
                        {
                            var UserIds = data.Select(m => m.Id).ToList();
                            foreach (var UserId in UserIds)
                            {
                                await messageHub.Clients.Groups(BusinessId.ToString().ToLower() + UserId.ToString().ToLower()).ReceiveMessageFromServer(list);
                            }
                        }
                    }
                    _appDbContext.Update(sentMessage);
                    _appDbContext.SaveChanges();
                    await _webhookService.SaveWebhookEventsAsync(BusinessId, "UpdatedConversationErrorMessage", sentMessage);
                }
                if (status == "read")
                {
                    sentMessage.ErrorMessage = null;
                    sentMessage.Status = EngagetoEntities.Enums.ConvStatus.read;
                    EngagetoEntities.Dtos.WebhookDtos.Messages messages = new();
                    var obj = messages.Message(sentMessage, query);
                    List<EngagetoEntities.Dtos.ConversationDtos.ConversationDto> list = new List<EngagetoEntities.Dtos.ConversationDtos.ConversationDto>();
                    list.Add(obj);
                    //await _conversationAnalyticsService.SaveConversationCostDetuctionHistoryAsync(new List<Conversations> { sentMessage }, BusinessId, sentMessage.UserId);
                    if (BusinessId != null)
                        if (BusinessId != null)
                        {
                            var data = _appDbContext.Users.Where(m => m.CompanyId == BusinessId);
                            if (data != null)
                            {
                                var UserIds = data.Select(m => m.Id).ToList();
                                foreach (var UserId in UserIds)
                                {
                                    await messageHub.Clients.Groups(BusinessId.ToString().ToLower() + UserId.ToString().ToLower()).ReceiveMessageFromServer(list);

                                }

                            }
                        }
                    _appDbContext.Update(sentMessage);
                    _appDbContext.SaveChanges();
                    await _webhookService.SaveWebhookEventsAsync(BusinessId, "UpdatedConversationReadMessage", sentMessage);

                }

            }
        }

        public async Task RenderMessagesBySignalR(string companyId, List<EngagetoEntities.Dtos.ConversationDtos.ConversationDto> conversations)
        {
            var userIds = _appDbContext.Users.Where(m => m.CompanyId == companyId).Select(x => x.Id);
            foreach (var UserId in userIds)
            {
                await messageHub.Clients.Groups(companyId.ToString().ToLower() + UserId.ToString().ToLower()).ReceiveMessageFromServer(conversations);
                await messageHub.Clients.Groups(companyId.ToString().ToLower() + UserId.ToString().ToLower()).RenderContacts();
            }
        }
        public async Task RenderErrorMessagesBySignalR(string companyId, List<EngagetoEntities.Dtos.ConversationDtos.ConversationDto> conversations)
        {
            var userIds = _appDbContext.Users.Where(m => m.CompanyId == companyId).Select(x => x.Id);
            foreach (var UserId in userIds)
            {
                await messageHub.Clients.Groups(companyId.ToString().ToLower() + UserId.ToString().ToLower()).ReceiveErrorMessageFromServer(conversations);
                //await messageHub.Clients.Groups(companyId.ToString().ToLower() + UserId.ToString().ToLower()).RenderContacts();
            }
        }
    }
}
