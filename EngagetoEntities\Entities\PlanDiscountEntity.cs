﻿using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations.Schema;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace EngagetoEntities.Entities
{
    [Table("PlanDiscountEntities")]
    public class PlanDiscountEntity
    {
        public int Id { get; set; }
        public int PlanId { get; set; }
        public int? Year { get; set; }
        public string PlanType { get; set; } = default!;
        public string DiscountType { get; set; } = default!;
        public decimal? WeeklyDiscount { get; set; }
        public decimal? MonthlyDiscount { get; set; }
        public decimal? QuarterlyDiscount { get; set; }
        public decimal? AnnuallyDiscount { get; set; }
        public bool IsDelete { get; set; } = false;

        public PlanDiscountEntity() { }
        public PlanDiscountEntity(int planId,
            int? year,
            string planType,
            string discountType,
            decimal? weeklyDiscount,
            decimal? monthlyDiscount,
            decimal? quarterlyDiscount,
            decimal? annuallyDiscount, bool isDelete = false)
        {
            PlanId = planId;
            Year = year;
            PlanType = planType;
            DiscountType = discountType;
            WeeklyDiscount = weeklyDiscount ?? 0;
            MonthlyDiscount = monthlyDiscount ?? 0;
            QuarterlyDiscount = quarterlyDiscount ?? 0;
            AnnuallyDiscount = annuallyDiscount ?? 0;
            IsDelete = isDelete;
        }
    }
}
