﻿using EngagetoEntities.Enums;

namespace EngagetoEntities.Dtos.MetaDto
{
    public class AuthTemplateDto
    {
        public Guid? Id { get; set; }
        public string Name { get; set; }
        public string Body { get; set; }
        public string? Footer { get; set; }
        public List<Language> Languages { get; set; } = new();
        public WATemplateCategory Category { get; set; } = WATemplateCategory.AUTHENTICATION;
        public string? SubCategory { get; set; }
        public int MessageSendTtlSeconds { get; set; }
        public bool AddSecurityRecommendation { get; set; } = false;
        public int CodeExpirationMinutes { get; set; } = 0;
        public OtpType OtpType { get; set; }
        public List<SupportedApps>? SupportedApps { get; set; }
        public AuthButtonDto? Button { get; set; } = new();
    }
    public class SupportedApps
    {
        public string PackageName { get; set; }
        public string SignatureHash { get; set; }
    }

    public class SendAuthenticationTemplateDto
    {
        public string BusinessId { get; set; }
        public Guid UserId { get; set; }
        public Guid TemplateId { get; set; }
        public OtpType OtpType { get; set; }
        public string Contact { get; set; }
    }

}
