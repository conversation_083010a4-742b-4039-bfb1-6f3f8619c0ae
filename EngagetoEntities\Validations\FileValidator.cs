﻿using Microsoft.Extensions.Configuration;

namespace EngagetoEntities.Validations
{
    public class FileValidator
    {
        private readonly long MaxImageFileSize;
        private readonly long MaxDocumentFileSize;
        private readonly long MaxVideoFileSize;
        private readonly long MaxAudioFileSize;

        private static readonly string[] SupportedImageTypes = { "image/jpeg", "image/png" };
        private static readonly string[] SupportedDocumentTypes = { "application/pdf", "application/vnd", "text/" };
        private static readonly string[] SupportedVideoTypes = { "video/mp4", "video/3gp" };
        private static readonly string[] SupportedAudioTypes = { "audio/mpeg", "audio/wav" };

        public FileValidator(IConfiguration configuration)
        {
            var mediaFileSizeSection = configuration.GetSection("MetaMediaFileSize");
            MaxImageFileSize = mediaFileSizeSection.GetValue<long>("Image");
            MaxDocumentFileSize = mediaFileSizeSection.GetValue<long>("Document");
            MaxVideoFileSize = mediaFileSizeSection.GetValue<long>("Video");
            MaxAudioFileSize = mediaFileSizeSection.GetValue<long>("Audio");
        }

        public async Task<bool> ValidateFileAsync(HttpClient client, string fileUrl)
        {
            var res = await client.GetAsync(fileUrl);
            if (!res.IsSuccessStatusCode)
            {
                throw new HttpRequestException($"Failed to retrieve the file from the URL: {fileUrl}. Status Code: {res.StatusCode}");
            }

            var contentType = res.Content.Headers.ContentType?.MediaType;
            var fileSize = res.Content.Headers.ContentLength ?? 0;

            if (fileSize > 0)
            {
                if (IsValidImage(contentType, fileSize) ||
                    IsValidDocument(contentType, fileSize) ||
                    IsValidVideo(contentType, fileSize) ||
                    IsValidAudio(contentType, fileSize))
                {
                    return true;
                }
            }
            throw new InvalidOperationException(GetErrorMessage(contentType, fileSize));

        }

        private bool IsValidImage(string fileType, long fileSize)
        {
            return fileSize <= MaxImageFileSize && SupportedImageTypes.Contains(fileType);
        }

        private bool IsValidDocument(string fileType, long fileSize)
        {
            return fileSize <= MaxDocumentFileSize && SupportedDocumentTypes.Any(type => fileType.StartsWith(type));
        }

        private bool IsValidVideo(string fileType, long fileSize)
        {
            return SupportedVideoTypes.Contains(fileType) && fileSize <= MaxVideoFileSize;
        }

        private bool IsValidAudio(string fileType, long fileSize)
        {
            return SupportedAudioTypes.Contains(fileType) && fileSize <= MaxAudioFileSize;
        }

        private string GetErrorMessage(string fileType, long fileSize)
        {
            if (IsValidImage(fileType, fileSize))
                return $"Image file size exceeds the maximum allowed size of {MaxImageFileSize / (5 * 1024 * 1024)} 5MB.";

            if (IsValidDocument(fileType, fileSize))
                return $"Document file size exceeds the maximum allowed size of {MaxDocumentFileSize / (100 * 1024 * 1024)} 100MB.";

            if (IsValidVideo(fileType, fileSize))
                return $"Video file size exceeds the maximum allowed size of {MaxVideoFileSize / (16 * 1024 * 1024)} 16MB.";

            if (IsValidAudio(fileType, fileSize))
                return $"Audio file size exceeds the maximum allowed size of {MaxAudioFileSize / (16 * 1024 * 1024)} 16MB.";

            return "The file type or size is not valid.";
        }
    }
}
