﻿using EngagetoEntities.Dtos.ConversationDtos;
using EngagetoEntities.Entities;
using Mapster;

namespace EngagetoEntities.Mapping
{
    public static class ConversationMapping
    {
        public static void RegisterMappings()
        {
            TypeAdapterConfig<Conversations, ConversationDto>
               .NewConfig()
                .Map(dest => dest.Status, src => src.Status.ToString());
        }
    }
}
