﻿using EngagetoEntities.Enums;

namespace EngagetoEntities.Dtos.ContactDtos
{
    public class ContactDto
    {
        public Guid ContactId { get; set; }
        public string Name { get; set; } = default!;
        public string? BusinessId { get; set; }
        public Guid? UserId { get; set; }
        public string? Contact { get; set; }
        public ChatStatus ChatStatus { get; set; }
        public string? Assignee { get; set; }
        public string? Tags { get; set; }
        public string? LastMessage { get; set; }
        public DateTime? LastMessageAt { get; }
        public bool? IsSpam { get; set; }
        public int? Unread { get; set; }
    }
}
