﻿using Newtonsoft.Json;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace EngagetoEntities.UserEntities.Dtos
{
    public class OrderDto
    {
        public decimal Amount { get; set; }
        public decimal AmountDue { get; set; }
        public decimal AmountPaid { get; set; }
        public int Attempts { get; set; }
        [JsonProperty("created_at")]
        public long CreatedAt { get; set; } // This will store the Unix timestamp
        public string Currency { get; set; }
        public string Entity { get; set; }
        public string Id { get; set; }
        public Dictionary<string, string> Notes { get; set; }
        [JsonProperty("offer_id")]
        public string OfferId { get; set; }
        public string Receipt { get; set; }
        public string Status { get; set; }
    }
}
