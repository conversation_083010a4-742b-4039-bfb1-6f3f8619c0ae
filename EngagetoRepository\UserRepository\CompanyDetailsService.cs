﻿
using Amazon.S3;
using Amazon.S3.Transfer;
using EngagetoContracts.UserContracts;
using EngagetoEntities.DdContext;
using EngagetoEntities.Dtos.BusinessDetailsDtos;
using EngagetoEntities.Entities;
using EngagetoEntities.UserEntities.Dtos;
using Mapster;
using Microsoft.AspNetCore.Http;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Configuration;
using Newtonsoft.Json;
using System.Net.Http.Headers;
using System.Net.Http.Json;
using System.Text;
using System.Text.Json;

namespace EngagetoRepository.Repository
{
    public class CompanyDetailsService : ICompanyDetailsService
    {
        private readonly ApplicationDbContext _context;
        private readonly HttpClient _httpClient;
        private readonly IConfiguration _configuration;

        public CompanyDetailsService(ApplicationDbContext context, IConfiguration configuration, HttpClient httpClient)
        {
            _httpClient = httpClient;
            _context = context;
            _configuration = configuration;
        }



        public async Task<bool> UpdateCompanyFieldsAsync(Guid currentUserId, Guid companyId, Ahex_CRM_BusinessDetailsDto updatedCompany)
        {
            try
            {
                var existingCompany = await _context.BusinessDetails.FindAsync(companyId);
                if (existingCompany == null)
                {
                    return false;
                }


                var metaDetails = await _context.BusinessDetailsMetas.FirstOrDefaultAsync(md => md.BusinessId == companyId.ToString());
                if (metaDetails == null)
                {
                    throw new InvalidOperationException($"Meta details not found for company ID '{companyId}'");
                }

                var accessToken = metaDetails.Token;
                var businessAccountId = metaDetails.WhatsAppBusinessAccountID;
                var appId = metaDetails.AppId;

                if (string.IsNullOrEmpty(updatedCompany.BusinessName) ||
                   string.IsNullOrEmpty(updatedCompany.BusinessWebsite) ||
                   string.IsNullOrEmpty(updatedCompany.BusinessEmail) ||
                   string.IsNullOrEmpty(updatedCompany.CountryCode) ||
                   string.IsNullOrEmpty(updatedCompany.PhoneNumber))
                {
                    return false;
                }

                existingCompany.BusinessName = updatedCompany.BusinessName;
                existingCompany.BusinessWebsite = updatedCompany.BusinessWebsite;
                existingCompany.BusinessEmail = updatedCompany.BusinessEmail;
                existingCompany.CountryCode = updatedCompany.CountryCode;
                existingCompany.PhoneNumber = updatedCompany.PhoneNumber;

                if (Enum.TryParse(updatedCompany.BusinessCategory.ToString(), out BusinessCategory category))
                {
                    existingCompany.BusinessCategory = category.ToString();
                }
                else
                {
                    existingCompany.BusinessCategory = string.Empty;

                }

                if (!string.IsNullOrEmpty(updatedCompany.CountryName))
                {
                    var countryDetails = await _context.CountryDetails.FirstOrDefaultAsync(cd => cd.CountryName == updatedCompany.CountryName);
                    if (countryDetails == null)
                    {
                        throw new InvalidOperationException($"Country name '{updatedCompany.CountryName}' not found.");
                    }
                    existingCompany.CountryName = updatedCompany.CountryName;
                }
                else
                {
                    existingCompany.CountryName = string.Empty;
                }

                existingCompany.GSTNumber = string.IsNullOrEmpty(updatedCompany.GSTNumber) ? string.Empty : updatedCompany.GSTNumber;
                existingCompany.CompanyAddress = string.IsNullOrEmpty(updatedCompany.CompanyAddress) ? string.Empty : updatedCompany.CompanyAddress;
                existingCompany.Description = string.IsNullOrEmpty(updatedCompany.Description) ? string.Empty : updatedCompany.Description;
                existingCompany.CompanyLegalName = string.IsNullOrEmpty(updatedCompany.CompanyLegalName) ? string.Empty : updatedCompany.CompanyLegalName;

                if (updatedCompany.CompanyRegistered != null)
                {
                    existingCompany.CompanyRegistered = updatedCompany.CompanyRegistered.Value;
                }


                if (Enum.TryParse(updatedCompany.CompanyType.ToString(), out CompanyType company))
                {
                    existingCompany.CompanyType = company.ToString();
                }
                existingCompany.CustomerCareEmail = string.IsNullOrEmpty(updatedCompany.CustomerCareEmail) ? string.Empty : updatedCompany.CustomerCareEmail;

                if (!string.IsNullOrEmpty(updatedCompany.CustomerCareCountryCode))
                {
                    var countryDetails = await _context.CountryDetails.FirstOrDefaultAsync(cd => cd.CountryCode == updatedCompany.CustomerCareCountryCode);
                    if (countryDetails == null)
                    {
                        throw new InvalidOperationException($"Country code '{updatedCompany.CustomerCareCountryCode}' not found.");
                    }
                    existingCompany.CustomerCareCountryCode = updatedCompany.CustomerCareCountryCode;
                }
                else
                {
                    existingCompany.CustomerCareCountryCode = string.Empty;

                }

                existingCompany.CustomerCarePhone = string.IsNullOrEmpty(updatedCompany.CustomerCarePhone) ? string.Empty : updatedCompany.CustomerCarePhone;
                existingCompany.GrievanceOfficerName = string.IsNullOrEmpty(updatedCompany.GrievanceOfficerName) ? string.Empty : updatedCompany.GrievanceOfficerName;
                existingCompany.GrievanceOfficerEmail = string.IsNullOrEmpty(updatedCompany.GrievanceOfficerEmail) ? string.Empty : updatedCompany.GrievanceOfficerEmail;

                if (!string.IsNullOrEmpty(updatedCompany.GrievanceOfficerCountryCode))
                {
                    var countryDetails = await _context.CountryDetails.FirstOrDefaultAsync(cd => cd.CountryCode == updatedCompany.GrievanceOfficerCountryCode);
                    if (countryDetails == null)
                    {
                        throw new InvalidOperationException($"Country code '{updatedCompany.GrievanceOfficerCountryCode}' not found.");
                    }
                    existingCompany.GrievanceOfficerCountryCode = updatedCompany.GrievanceOfficerCountryCode;
                }
                else
                {
                    existingCompany.GrievanceOfficerCountryCode = string.Empty;
                }
                existingCompany.GrievanceOfficerPhone = string.IsNullOrEmpty(updatedCompany.GrievanceOfficerPhone) ? string.Empty : updatedCompany.GrievanceOfficerPhone;

                string profilePictureHandle = null;
                if (updatedCompany.Image != null)
                {

                    string logoLink = await UploadLogoAsync(existingCompany.Id, updatedCompany.Image);
                    if (logoLink != null)
                    {
                        existingCompany.CompanyLogoLink = logoLink;

                        profilePictureHandle = await UploadProfilePictureAsync(updatedCompany.Image, accessToken, appId);
                    }
                }

                bool updateResult = await UpdateBusinessProfileAsync(updatedCompany, profilePictureHandle, accessToken, businessAccountId);

                if (updateResult)
                {
                    await _context.SaveChangesAsync();
                    return true;
                }
                else
                {
                    throw new Exception("Company is disabled from meta.");
                }
            }
            catch (Exception ex)
            {

                var exems = ex.Message;
                return false;
            }
        }


        private async Task<string> UploadProfilePictureAsync(IFormFile imageFile, string accessToken, string appId)
        {
            try
            {
                var version = _configuration["MetaApi:Version"];
                var chunkSizeLimit = 1024 * 1024; // 1 MB for non-chunked single upload

                // Step 1: Initialize the upload session
                var initUrl = $"https://graph.facebook.com/{version}/{appId}/uploads?file_length={imageFile.Length}&file_type={imageFile.ContentType}&access_token={accessToken}";
                var initResponse = await _httpClient.PostAsync(initUrl, null);
                initResponse.EnsureSuccessStatusCode();

                var initResponseData = await initResponse.Content.ReadFromJsonAsync<Dictionary<string, string>>();
                if (initResponseData == null || !initResponseData.ContainsKey("id"))
                {
                    throw new InvalidOperationException("Unable to initialize the upload session.");
                }

                var uploadSessionId = initResponseData["id"];

                // Step 2: Prepare the upload URL using the upload session ID
                var uploadUrl = $"https://graph.facebook.com/{version}/{uploadSessionId}";

                // Open the file stream for reading the file
                var fileStream = imageFile.OpenReadStream();

                // Convert file to byte array
                var fileContent = new ByteArrayContent(await ConvertToByteArrayAsync(imageFile));

                // Set content type and other headers
                fileContent.Headers.ContentType = new MediaTypeHeaderValue(imageFile.ContentType);
                fileContent.Headers.ContentLength = imageFile.Length;

                // Create the request to upload the file
                var request = new HttpRequestMessage(HttpMethod.Post, uploadUrl)
                {
                    Content = fileContent
                };
                request.Headers.Authorization = new AuthenticationHeaderValue("OAuth", accessToken);

                // Send the request to upload the file
                var response = await _httpClient.SendAsync(request);
                response.EnsureSuccessStatusCode();

                // Parse the response content
                var responseData = await response.Content.ReadFromJsonAsync<Dictionary<string, string>>();
                if (responseData == null || !responseData.ContainsKey("h"))
                {
                    throw new InvalidOperationException("Unable to change the profile.");
                }

                // Step 3: Return the response ID or final status
                return responseData["h"];
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Error: {ex.Message}");
                throw;
            }
        }

        // Helper method to convert IFormFile to byte array
        private async Task<byte[]> ConvertToByteArrayAsync(IFormFile file)
        {
            using (var memoryStream = new MemoryStream())
            {
                await file.CopyToAsync(memoryStream);
                return memoryStream.ToArray();
            }
        }

        private async Task<bool> UpdateBusinessProfileAsync(Ahex_CRM_BusinessDetailsDto businessDetails, string profilePictureHandle, string accessToken, string businessAccountId)
        {
            try
            {
                var version = _configuration["MetaApi:Version"];


                var phoneNumberId = await GetPhoneNumberIdAsync(accessToken, businessAccountId);

                var profileData = new
                {
                    messaging_product = "whatsapp",
                    address = businessDetails.CompanyAddress,
                    description = businessDetails.Description,
                    vertical = businessDetails.BusinessCategory.ToString(),
                    about = businessDetails.BusinessName,
                    email = businessDetails.BusinessEmail,
                    websites = new[] { businessDetails.BusinessWebsite },
                    profile_picture_handle = profilePictureHandle
                };

                var content = new StringContent(JsonConvert.SerializeObject(profileData), Encoding.UTF8, "application/json");

                var response = await _httpClient.PostAsync($"https://graph.facebook.com/{version}/{phoneNumberId}/whatsapp_business_profile?access_token={accessToken}", content);
                response.EnsureSuccessStatusCode();

                return true;
            }
            catch (Exception ex)
            {
                Console.WriteLine(ex.Message);
                return false;
            }
        }


        private async Task<string> GetPhoneNumberIdAsync(string accessToken, string businessAccountId)
        {
            var version = _configuration["MetaApi:Version"];

            var response = await _httpClient.GetAsync($"https://graph.facebook.com/{version}/{businessAccountId}/phone_numbers?access_token={accessToken}");
            response.EnsureSuccessStatusCode();

            using (var responseStream = await response.Content.ReadAsStreamAsync())
            {
                using (var jsonDocument = await JsonDocument.ParseAsync(responseStream))
                {

                    var root = jsonDocument.RootElement;


                    if (root.TryGetProperty("data", out var dataArray))
                    {

                        if (dataArray.ValueKind == JsonValueKind.Array)
                        {

                            foreach (var item in dataArray.EnumerateArray())
                            {

                                var id = item.GetProperty("id").GetString();
                                if (!string.IsNullOrEmpty(id))
                                {
                                    return id;
                                }
                            }
                        }
                    }
                    else
                    {
                        throw new InvalidOperationException("Invalid response from Meta API: 'data' array is empty or null");
                    }
                }
            }
            return "dfv";
        }

        public async Task<bool> DeleteCompanyAsync(Guid currentUserId, Guid companyId)
        {
            try
            {

                var companyToDelete = await _context.BusinessDetails.FindAsync(companyId);

                if (companyToDelete == null)
                {
                    return false;
                }

                companyToDelete.Status = false;

                await _context.SaveChangesAsync();

                return true;
            }
            catch (Exception)
            {
                return false;
            }
        }

        public async Task<Ahex_CRM_BusinessDetails> GetCompanyByIdAsync(Guid companyId)
        {
            try
            {


                return await _context.BusinessDetails.FindAsync(companyId);
            }
            catch (Exception)
            {
                return null;
            }
        }

        public async Task<IEnumerable<Ahex_CRM_BusinessDetails>> GetAllCompaniesAsync(Guid currentUserId, string searchQuery, bool includeInactive, string sortBy, bool isSortAscending)
        {
            try
            {


                var query = _context.BusinessDetails.AsQueryable();

                if (!string.IsNullOrEmpty(searchQuery))
                {

                    if (Guid.TryParse(searchQuery, out Guid companyId))
                    {
                        query = query.Where(company => company.Id == companyId);
                    }
                    else
                    {

                        if (searchQuery.Length == 3 && searchQuery.All(char.IsDigit))
                        {

                            query = query.Where(company => company.PhoneNumber.StartsWith(searchQuery));
                        }
                        else
                        {

                            query = query.Where(company =>
                                company.PhoneNumber.Contains(searchQuery) ||
                                company.PhoneNumber.Substring(3).Contains(searchQuery) ||
                                company.GSTNumber.Contains(searchQuery) ||
                                company.BusinessName.Contains(searchQuery) ||
                                company.BusinessEmail.Contains(searchQuery));
                        }
                    }
                }

                if (!includeInactive)
                {
                    query = query.Where(company => company.Status == includeInactive);
                }

                switch (sortBy?.ToLower())
                {
                    case "companyname":
                        query = isSortAscending ? query.OrderBy(company => company.BusinessName) : query.OrderByDescending(company => company.BusinessName);
                        break;
                    case "clientname":
                        query = isSortAscending ? query.OrderBy(company => company.BusinessCategory) : query.OrderByDescending(company => company.BusinessCategory);
                        break;
                    default:
                        query = query.OrderBy(company => company.BusinessName);
                        break;
                }

                return await query.ToListAsync();
            }
            catch (Exception)
            {
                return null;
            }
        }

        public async Task<string> UploadLogoAsync(Guid id, IFormFile file)
        {
            var manageClient = await _context.BusinessDetails.FindAsync(id);

            if (manageClient == null)
            {

                return null;
            }

            var fileLink = await HandleFileUploadAsync(file);

            manageClient.CompanyLogoLink = fileLink;

            await _context.SaveChangesAsync();

            return fileLink;
        }

        private async Task<string> HandleFileUploadAsync(IFormFile file)
        {
            try
            {
                var accessKey = _configuration["Aws:AccessKey"];
                var secretKey = _configuration["Aws:SecretKey"];
                var bucketName = _configuration["Aws:BucketName"];
                var regionString = _configuration["Aws:Region"];
                var region = Amazon.RegionEndpoint.GetBySystemName(regionString);

                using (var client = new AmazonS3Client(accessKey, secretKey, region))
                {
                    var key = $"{Guid.NewGuid()}.{file.FileName.Split('.')[1]}";

                    using (var newMemoryStream = new MemoryStream())
                    {

                        await file.CopyToAsync(newMemoryStream);

                        var uploadRequest = new TransferUtilityUploadRequest
                        {
                            InputStream = newMemoryStream,
                            Key = key,
                            BucketName = bucketName
                            //CannedACL = S3CannedACL.PublicRead
                        };

                        using (var fileTransferUtility = new TransferUtility(client))
                        {
                            await fileTransferUtility.UploadAsync(uploadRequest);
                        }
                    }


                    var fileLink = $"https://{bucketName}.s3.{region.SystemName}.amazonaws.com/{key}";

                    return fileLink;
                }
            }
            catch (Exception ex)
            {

                Console.WriteLine($"Error uploading file to S3: {ex.Message}");
                throw;
            }
        }

        public async Task<Ahex_CRM_BusinessDetails> UpdateBillingDetailsAsyc(BillingDetailsDto detailsDto)
        {
            var businessDetails = await GetCompanyByIdAsync((detailsDto.Id ?? Guid.Empty));
            if (businessDetails == null)
            {
                throw new Exception("Company Details not found");
            }

            businessDetails.GSTNumber = detailsDto.GSTNumber;
            businessDetails.BusinessEmail = detailsDto.BusinessEmail;
            businessDetails.CompanyAddress = detailsDto.CompanyAddress;
            businessDetails.CompanyLegalName = detailsDto.CompanyLegalName;
            await _context.SaveChangesAsync();
            return businessDetails;
        }

        public async Task<string?> GetCompanyCountryCode(Guid companyId)
        {
            return (await _context.BusinessDetails.FirstOrDefaultAsync(x => x.Id == companyId))?.CountryCode;
        }

        public async Task<List<TenantDtos>> GetTenantsAsync()
        {
            return (await _context.BusinessDetails.Where(x => !string.IsNullOrEmpty(x.TenantId)).ToListAsync()).Adapt<List<TenantDtos>>();
        }
    }
}
