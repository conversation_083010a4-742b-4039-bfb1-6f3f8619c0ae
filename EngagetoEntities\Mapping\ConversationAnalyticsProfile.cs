﻿using AutoMapper;
using EngagetoEntities.Entities;
using EngagetoEntities.UserEntities.Dtos;

namespace EngagetoEntities.Mapping
{
    public class ConversationAnalyticsProfile : Profile
    {
        public ConversationAnalyticsProfile()
        {
            var indianTimeZone = TimeZoneInfo.FindSystemTimeZoneById("India Standard Time");
            CreateMap<ConversationAnalyticsDtatPointDto, ConversationAnalyticsEntity>()
                .ForMember(dest => dest.StartDate, opt => opt.MapFrom(src =>
                    TimeZoneInfo.ConvertTime(DateTimeOffset.FromUnixTimeSeconds(src.Start).UtcDateTime, indianTimeZone)))
                .ForMember(dest => dest.EndDate, opt => opt.MapFrom(src =>
                    TimeZoneInfo.ConvertTime(DateTimeOffset.FromUnixTimeSeconds(src.End).UtcDateTime, indianTimeZone)))
                .ForMember(dest => dest.Cost, opt => opt.MapFrom(src => src.Cost))
                .ForMember(dest => dest.Conversation, opt => opt.MapFrom(src => src.Conversation))
                .ForMember(dest => dest.ConversationCategory, opt => opt.MapFrom(src => src.ConversationCategory))
                .ForMember(dest => dest.ConversationType, opt => opt.MapFrom(src => src.ConversationType))
                .ForMember(dest => dest.MobileNumber, opt => opt.MapFrom(src => src.PhoneNumber))
                .ForMember(dest => dest.Id, opt => opt.Ignore())
                .ForMember(dest => dest.CompanyId, opt => opt.Ignore())
                .ForMember(dest => dest.DeletedAt, opt => opt.Ignore())
                .ForMember(dest => dest.DeletedBy, opt => opt.Ignore())
                .ForMember(dest => dest.UpdatedBy, opt => opt.Ignore())
                .ForMember(dest => dest.UpdatedAt, opt => opt.Ignore())
                .ForMember(dest => dest.CreatedBy, opt => opt.Ignore())
                .ForMember(dest => dest.CreatedAt, opt => opt.Ignore())
                .ForMember(dest => dest.CurrentBalance, opt => opt.Ignore())
                .ForMember(dest => dest.WAMessageId, opt => opt.Ignore())
                .ForMember(dest => dest.IsDeleted, opt => opt.Ignore());
        }
    }

}
