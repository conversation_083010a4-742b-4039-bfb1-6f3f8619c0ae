﻿using EngagetoEntities.Dtos.AutomationDtos;
using EngagetoEntities.Entities;
using EngagetoEntities.Enums;
using Mapster;
using Newtonsoft.Json;

namespace EngagetoEntities.Mapping
{
    public class AutomationMapping
    {
        public static void RegisterMappings()
        {
            TypeAdapterConfig<AutoReplyMessageDto, AutoReplyAutomationEntity>
                .NewConfig()
                .Map(dest => dest.Id, src => src.AutoReplyAutomation.Id)
                .Map(dest => dest.Input, src => src.AutoReplyAutomation.Input.Trim())
                .Map(dest => dest.CompanyId, src => src.AutoReplyAutomation.CompanyId)
                .Map(dest => dest.UserId, src => src.AutoReplyAutomation.UserId)
                .Map(dest => dest.InputVariation, src => (src.AutoReplyAutomation.InputVariations != null && src.AutoReplyAutomation.InputVariations.Count() > 0)
                    ? JsonConvert.SerializeObject(src.AutoReplyAutomation.InputVariations) : null)
                .Map(dest => dest.AutoReplyType, src => src.AutoReplyAutomation.AutomationResponseType)
                .Map(dest => dest.WorkflowName, src => src.AutoReplyAutomation.WorkflowName);

            TypeAdapterConfig<AutoReplyCustomMessageDto, AutoReplyCustomMessageEntity>
                .NewConfig()
                .Map(dest => dest.AutoReplyAutomationEntity.Input, src => src.AutoReplyAutomation.Input)
                .Map(dest => dest.AutoReplyAutomationEntity.InputVariation, src => (src.AutoReplyAutomation.InputVariations != null && src.AutoReplyAutomation.InputVariations.Count() > 0)
                    ? JsonConvert.SerializeObject(src.AutoReplyAutomation.InputVariations) : null)
                .Map(dest => dest.AutoReplyAutomationEntity.AutoReplyType, src => src.AutoReplyAutomation.AutomationResponseType)
                .Map(dest => dest.Buttons, src => src.ButtonValue != null && src.ButtonValue.Any() ? JsonConvert.SerializeObject(src.ButtonValue) : null)
                .Map(dest => dest.BodyMessage, src => src.BodyMessage);

            TypeAdapterConfig<AutomationVeriableDto, AutoReplyVeriableEntity>
                .NewConfig();

            TypeAdapterConfig<AutoReplyMessageDto, AutoReplyCustomMessageEntity>
                .NewConfig()
                .Map(dest => dest.Buttons, src => src.ButtonValue != null && src.ButtonValue.Any() ? JsonConvert.SerializeObject(src.ButtonValue) : null);


            TypeAdapterConfig<AutoReplyAutomationEntity, AutoReplyMessageDto>
             .NewConfig()
             .Map(dest => dest.BodyMessage,
                  src => src.AutoReplyCustomMessageEntities != null && src.AutoReplyCustomMessageEntities.Any()
                      ? src.AutoReplyCustomMessageEntities.FirstOrDefault().BodyMessage
                      : "")
             .Map(dest => dest.ButtonValue,
                  src => src.AutoReplyCustomMessageEntities != null && src.AutoReplyCustomMessageEntities.Any()
                      ? JsonConvert.DeserializeObject<List<ButtonValueDto>>(
                          src.AutoReplyCustomMessageEntities.FirstOrDefault().Buttons ?? "[]")
                      : new List<ButtonValueDto>())
             .Map(dest => dest.AutoReplyAutomation,
              src => new AutoReplyAutomationDto
              {
                  Id = src.Id,
                  CompanyId = src.CompanyId,
                  UserId = src.UserId,
                  Input = src.Input,
                  AutomationResponseType = (ResponseType)src.AutoReplyType,

                  // Handle InputVariations as an array of strings
                  InputVariations = !string.IsNullOrEmpty(src.InputVariation)
                      ? JsonConvert.DeserializeObject<List<string>>(src.InputVariation)
                      : new List<string> { },

                  // Ensure WorkflowName is not null
                  WorkflowName = src.WorkflowName ?? string.Empty
              });

            TypeAdapterConfig<VeriableEntity, VeriableDto>.NewConfig();

            TypeAdapterConfig<AutoCustomMessageResultDto, AutoReplyCustomMessageDto>
                .NewConfig()
                .Map(dest => dest.AutoReplyAutomation.Id, src => src.Id)
                .Map(dest => dest.AutoReplyAutomation.CompanyId, src => src.CompanyId)
                .Map(dest => dest.AutoReplyAutomation.UserId, src => src.UserId)
                .Map(dest => dest.AutoReplyAutomation.InputVariations, src => src.InputVariation != null ? JsonConvert.DeserializeObject(src.InputVariation) : null)
                .Map(dest => dest.AutoReplyAutomation.AutomationResponseType, src => src.AutoReplyType)
                .Map(dest => dest.AutoReplyAutomation.Input, src => src.Input)
                .Map(dest => dest.BodyMessage, src => src.BodyMessage)
                .Map(dest => dest.ButtonValue, src =>
                    !string.IsNullOrEmpty(src.Buttons) ? JsonConvert.DeserializeObject<List<ButtonValueDto>>(src.Buttons) : null);

            TypeAdapterConfig<AutoCustomMessageResultDto, VeriableDto>
                .NewConfig()
                .Map(dest => dest.ReferenceId, src => src.AutoReplyCustomMessageId)
                .Map(dest => dest.Index, src => src.Index)
                .Map(dest => dest.Veriable, src => src.Veriable)
                .Map(dest => dest.Value, src => src.Value)
                .Map(dest => dest.FallbackValue, src => src.FallbackValue)
                .Map(dest => dest.Id, src => src.VeriableId)
                .Map(dest => dest.ReferenceTableType, src => src.ReferenceTableType);


            TypeAdapterConfig<WorkflowListDto, InputListEntity>
                .NewConfig()
                .Map(dest => dest.Inputs, src => JsonConvert.SerializeObject(src.Inputs));

            TypeAdapterConfig<InputListEntity, WorkflowListDto>
                .NewConfig()
                .Map(dest => dest.Inputs, src => !string.IsNullOrEmpty(src.Inputs) ? JsonConvert.DeserializeObject<List<Inputs>>(src.Inputs) : null);

            TypeAdapterConfig<AutomationWorkflowDto, WorkflowEntity>
                .NewConfig()
                .Map(dest => dest.WorkflowButtons, src => src.AutoReplyWorkflowButtons != null ? JsonConvert.SerializeObject(src.AutoReplyWorkflowButtons) : null)
                .Map(dest => dest.WebhookTriggerHeader, src => src.WebhookTriggerHeader != null ? JsonConvert.SerializeObject(src.WebhookTriggerHeader) : null)
                .Map(dest => dest.WorkflowListId, src => src.WorkflowListId)
                .Map(dest => dest.IsDeleted, src => false);

            TypeAdapterConfig<AutoReplyWorkflowCustomerRequestDto, AutomationCustomerResponseEntity>
                .NewConfig();

            TypeAdapterConfig<VeriableDto, VeriableEntity>
                .NewConfig();



            TypeAdapterConfig<WorkflowResultDto, AutomationWorkflowResponseDto>
                .NewConfig()
                .Map(dest => dest.Step, src => src.Step)
                .Map(dest => dest.AutoReplyWorkflowButtons, src => src.FlowResponseType == WorkflowResponseType.Button ?
                    JsonConvert.DeserializeObject<List<string>>(src.WorkflowButtons)
                                    : null)
                .Map(dest => dest.WebhookTriggerHeader, src => src.WebhookTriggerHeader != null ?
                    JsonConvert.DeserializeObject<List<WebhookHeader>>(src.WebhookTriggerHeader)
                                    : null);
            TypeAdapterConfig<WorkflowResultDto, WorkflowListDto>
                .NewConfig()
                .Map(dest => dest.Id, src => src.WorkflowListId)
                .Map(dest => dest.Inputs, src => JsonConvert.DeserializeObject<List<Inputs>>(src.Inputs));

            TypeAdapterConfig<WorkflowResultDto, VeriableDto>
                .NewConfig()
                .Map(dest => dest.ReferenceId, src => src.Id);

            TypeAdapterConfig<WorkflowResultDto, AutoReplyWorkflowCustomerResponseDto>
                .NewConfig()
                .Map(dest => dest.Id, src => src.CustomerResponseId)
                .Map(dest => dest.VeriableName, src => new VeriableNameDto
                {
                    Id = src.VeriableNameEntityId,
                    UserId = src.UserId,
                    CompanyId = src.CompanyId,
                    VeriableName = src.VeriableName

                })
                .Map(dest => dest.ResponseMessage, src => src.ResponseMessage);

            TypeAdapterConfig<AutomationWorkflowResponseDto, WorkflowEntity>
                .NewConfig()
                .Map(dest => dest.Id, src => src.Id)
                .Map(dest => dest.WorkflowButtons, src => src.AutoReplyWorkflowButtons != null ? JsonConvert.SerializeObject(src.AutoReplyWorkflowButtons) : null)
                .Map(dest => dest.WebhookTriggerHeader, src => src.WebhookTriggerHeader != null ? JsonConvert.SerializeObject(src.WebhookTriggerHeader) : null)
                .Map(dest => dest.WorkflowListId, src => src.AutoReplyWorkflowList);
        }
    }
}
