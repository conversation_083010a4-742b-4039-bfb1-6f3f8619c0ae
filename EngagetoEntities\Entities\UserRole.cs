﻿using Microsoft.EntityFrameworkCore;
using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace EngagetoEntities.Entities
{
    [Table("UserRole")]
    public class UserRole
    {
        [Key]
        public Guid Id { get; set; }

        [NotMapped]
        public Ahex_CRM_Users? AccountDetails { get; set; }
        public Guid RoleId { get; set; }
        [NotMapped]
        public Role? Role { get; set; }
    }
}
