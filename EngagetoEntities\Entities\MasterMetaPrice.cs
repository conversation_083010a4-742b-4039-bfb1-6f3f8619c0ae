﻿using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace EngagetoEntities.Entities
{
    public class MasterMetaPrice :  BaseEntity
    {
        [Key]
        public Guid Id { get; set; } = Guid.NewGuid();
        public string? Currency { get; set; }
        public decimal? Marketing { get; set; }
        public decimal? Utility { get; set; }
        public decimal? Service { get; set; }
        public decimal? Authentication { get; set; }
        public decimal? AuthenticationOutSide { get; set; }
        public string? CountryName { get; set; }
        public string? CountryCode { get; set; }

    }
}
