﻿using AutoMapper;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Logging;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using EngagetoContracts.UserContracts;
using EngagetoEntities.UserEntities.Models;
using EngagetoEntities.Entities;

namespace EngagetoRepository.UserRepository
{
    public class TransactionHistoryService: ITransactionHistoryService
    {
        private readonly ILogger<TransactionHistoryService> _logger;
        private readonly ApplicationDBContext _dbContext;
        private readonly IMapper _mapper;
        public TransactionHistoryService(ApplicationDBContext dbContext, ILogger<TransactionHistoryService> logger, IMapper mapper)
        {
            _dbContext = dbContext;
            _logger = logger;
            _mapper = mapper;
        }

        public async Task<TransactionHistoryEntity> CreateTransactionHistoryAsync(TransactionHistoryEntity transactionHistoryEntity)
        {
            var userInformation = await _dbContext.Ahex_CRM_Users.FirstAsync(x => x.Id == transactionHistoryEntity.UserID);
            if (userInformation != null)
            {
                transactionHistoryEntity.CompanyId = userInformation.CompanyId;
                await _dbContext.TransactionHistoryEntities.AddAsync(transactionHistoryEntity);
                await _dbContext.SaveChangesAsync();
                return transactionHistoryEntity;
            }
            throw new ArgumentNullException("Not Found the companyId for this user");
        }

        public async Task<List<TransactionHistoryEntity>> GetTransactionHistoryByUserIdAsync(Guid userId)
        {
            return _dbContext.TransactionHistoryEntities.Where(x=>x.UserID == userId && !x.IsDeleted)?.ToList() ?? new List<TransactionHistoryEntity>();
        }
        
        public async Task<TransactionHistoryEntity> GetTransactionHistoryByOrderIdAsync(string orderId)
        {
           
            var transaction =  await _dbContext.TransactionHistoryEntities.FirstOrDefaultAsync(x => x.OrderId == orderId && !x.IsDeleted);
            if(transaction == null)
            {
                _logger.LogInformation($"Not Found any Transaction for this orderid: {orderId}");
                throw new Exception("Not Found any Transaction for this orderid");
            }
            else
            {
                return transaction;
            }
        }

        public async Task<TransactionHistoryEntity> UpdateTransactionHistoryAsync(TransactionHistoryEntity transactionHistoryEntity)
        {
            var transaction = await GetTransactionHistoryByOrderIdAsync(transactionHistoryEntity.OrderId);
            _mapper.Map(transactionHistoryEntity, transaction);
            await _dbContext.SaveChangesAsync();
            return transaction;
        }

        public async Task<List<TransactionHistoryEntity>> GetTrasactionHistoryAsync()
        {
            return await _dbContext.TransactionHistoryEntities.ToListAsync();
        }
    }
}
