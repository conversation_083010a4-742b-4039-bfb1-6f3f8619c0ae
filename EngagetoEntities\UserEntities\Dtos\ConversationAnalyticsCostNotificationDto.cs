﻿using EngagetoEntities.Entities;
using EngagetoEntities.Enums;

namespace EngagetoEntities.UserEntities.Dtos
{
    public class ConversationAnalyticsCostNotificationDto
    {
        public int TotalCount { get; set; }
        public List<WalletTransactionDto> Transactions { get; set; } = new List<WalletTransactionDto>();
        public List<TransactionHistoryEntity>? TransactionsHistory { get; set; }
    }
    public class WalletTransactionDto
    {
        public Guid? Id { get; set; }
        public decimal Cost { get; set; }
        public string Currency { get; set; } = "INR";
        public decimal CurrentBalance { get; set; }
        public DateTime? StartDate { get; set; }
        public DateTime? EndDate { get; set; }
        public string? Description { get; set; }
        public int ConversationCount { get; set; } = 0;
        public string? ConversationType { get; set; }
        public string? ConversationCategory { get; set; }
        public string? TransactionType { get; set; } = NotificationType.Debit.ToString();

    }
}
