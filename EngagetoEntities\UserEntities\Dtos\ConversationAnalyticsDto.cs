﻿using Newtonsoft.Json;

namespace EngagetoEntities.UserEntities.Dtos
{

    public class ConversationAnalyticsResponseDto
    {
        public string Id { get; set; }
        [JsonProperty("conversation_analytics")]
        public ConversationAnalyticsDto ConversationAnalytics { get; set; } = new();

    }
    public class ConversationAnalyticsDtatPointDto
    {
        public long Start { get; set; }
        public long End { get; set; }
        public int Conversation { get; set; }
        [JsonProperty("conversation_type")]
        public string? ConversationType { get; set; }
        [JsonProperty("conversation_category")]
        public string? ConversationCategory { get; set; }
        public decimal Cost { get; set; }
        [JsonProperty("phone_number")]
        public string? PhoneNumber { get; set; }
        [JsonProperty("country")]
        public string? Country { get; set; }
    }
    public class ConversationAnalyticsDtatDto
    {
        [JsonProperty("data_points")]
        public List<ConversationAnalyticsDtatPointDto>? DataPoints { get; set; } = new();
    }
    public class ConversationAnalyticsDto
    {
        public List<ConversationAnalyticsDtatDto>? Data { get; set; } = new();
    }
}
