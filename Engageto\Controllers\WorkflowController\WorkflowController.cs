using EngagetoContracts.Services;
using EngagetoContracts.Workflow;
using EngagetoEntities.Dtos.ContactDtos;
using EngagetoEntities.Dtos.WorkflowDtos;
using EngagetoEntities.Entities;
using EngagetoRepository.Services;
using Humanizer;
using Mapster;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using System.Security.Claims;

namespace Engageto.Controllers.WorkflowController
{
    [ApiController]
    [Route("api/[controller]")]
    public class WorkflowController : BaseController
    {
        private readonly IWorkflowService _workflowService;
        private  readonly IUserIdentityService _userIdentityService;


        public WorkflowController(
            IWorkflowService workflowService,
            IUserIdentityService userIdentityService
            )
         {
            _workflowService = workflowService;
            _userIdentityService = userIdentityService;
         }

        [HttpPost]
        [Authorize]
        public async Task<IActionResult> CreateWorkflow([FromBody] CreateWorkflowDto workflowDto)
        {
            try
            {
                var currentUserId = _userIdentityService.UserId;

                var workflowId = await _workflowService.CreateWorkflowAsync(workflowDto, currentUserId);
                return Ok(CreateSuccessResponse(workflowId, "Workflow created successfully"));
            }
            catch (Exception ex)
            {
                return StatusCode(500, CreateErrorResponse<string>(ex.Message, ex.StackTrace));
            }
        }

        [HttpPut("{workFlowId}")]
        [Authorize]
        public async Task<IActionResult> UpdateWorkflow([FromBody] UpdateWorkflowDto workflowDto, Guid workFlowId)
        {
            try
            {
                var result = await _workflowService.UpdateWorkflowAsync(workFlowId, workflowDto);
                return Ok(CreateSuccessResponse(result, "Workflow updated successfully"));
            }
            catch (Exception ex)
            {
                return StatusCode(500, CreateErrorResponse<string>(ex.Message, ex.StackTrace));
            }
        }

        [HttpGet("{companyId}")]
        [Authorize]
        public async Task<IActionResult> GetWorkflows(Guid companyId)
        {
            try
            {
                var workflows = await _workflowService.GetWorkflowsAsync(companyId);
                return Ok(CreateSuccessResponse(workflows, "Workflows retrieved successfully"));
            }
            catch (Exception ex)
            {
                return StatusCode(500, CreateErrorResponse<string>(ex.Message, ex.StackTrace));
            }
        }

        [HttpDelete("{companyId}/{workflowId}")]
        [Authorize]
        public async Task<IActionResult> DeleteWorkflow(Guid companyId, Guid workflowId)
        {
            try
            {
                var result = await _workflowService.DeleteWorkflowAsync(workflowId, companyId);
                return Ok(CreateSuccessResponse(result, "Workflow deleted successfully"));
            }
            catch (Exception ex)
            {
                return StatusCode(500, CreateErrorResponse<string>(ex.Message, ex.StackTrace));
            }
        }
        [HttpPost("workflow")]
        public async Task<bool> SendWorkfow([FromBody] Contacts contact, string? message, bool? isNewCustomer, bool? isStatusChange, bool? isProjectChange)
        {
            try
            {

                var result = await _workflowService.ProcessWorkflowAsync(contact, message, isNewCustomer ?? false, isStatusChange ?? false, isProjectChange ?? false);
                return true;
            }
            catch (Exception ex)
            {
                throw new Exception($"{ex}, Error getting workflows");

            }
        }


        [HttpGet("GetWorkflowById/{id}")]
        [Authorize]
        public async Task<IActionResult> GetWorkflowById(Guid id)
        {
            try
            {
                var workflow = await _workflowService.GetWorkflowByIdAsync(id);
                return Ok(CreateSuccessResponse(workflow, "Workflow retrieved successfully"));
            }
            catch (Exception ex)
            {
                return StatusCode(500, CreateErrorResponse<string>(ex.Message, ex.StackTrace));
            }
        }
        [HttpPost("AddKeyword")]
        [Authorize]
        public async Task<IActionResult> AddKeyword([FromBody] AddKeywordDto request)
        {
            try
            {
                var result = await _workflowService.AddKeywordsToWorkflowAsync(request);
                return Ok(result);
            }
            catch (Exception ex)
            {
                return StatusCode(500, new { success = false, message = "An error occurred", error = ex.Message });
            }
        }

        [HttpDelete("DeleteKeywords/{workflowId}")]
        [Authorize]
        public async Task<IActionResult> DeleteKeyword(Guid workflowId, List<string> keyword)
        {
            try
            {
                var userId = _userIdentityService.UserId;
                var result = await _workflowService.DeleteKeywordsAsync(workflowId, keyword, userId);
                if (!result)
                    return NotFound(new { success = false, message = $"keyword doesn't exist at the given workflow id." });

                return Ok(new { success = true, message = $"Keyword deleted successfully" });
            }
            catch (Exception ex)
            {
                return StatusCode(500, new { success = false, message = "An error occurred", error = ex.Message });
            }
        }


        [HttpGet("GetAllKeywords/{workflowId}")]
        [Authorize]
        public async Task<IActionResult> GetAllKeywords(Guid workflowId)
        {
            try
            {
                var keywords = await _workflowService.GetAllKeywordsAsync(workflowId);
                return Ok(CreateSuccessResponse(keywords, "Keywords retrieved successfully"));
            }
            catch (Exception ex)
            {
                return StatusCode(500, CreateErrorResponse<string>(ex.Message, ex.StackTrace));
            }
        }

        [HttpGet("GetKeywords/{businessId}")]
        [Authorize]
        public async Task<IActionResult> GetKeywordsByBusinessId(Guid businessId)
        {
            try
            {
                var keywords = await _workflowService.GetKeywordsByBusinessId(businessId);
                return Ok(CreateSuccessResponse(keywords, "Keywords retrieved successfully"));
            }
            catch (Exception ex)
            {
                return StatusCode(500, CreateErrorResponse<string>(ex.Message, ex.StackTrace));
            }
        }

        [HttpGet("GetFlowStartNodes/{businessId}")]
        [Authorize]
        public async Task<IActionResult> GetFlowStartNodes(Guid businessId)
        {
            try
            {
                var flowStartNodes = await _workflowService.GetFlowStartNodesByBusinessIdAsync(businessId);

                if (flowStartNodes == null || !flowStartNodes.Any())
                {
                    return NotFound(CreateErrorResponse<string>("No FlowStart nodes found for the specified business ID", null));
                }

                return Ok(CreateSuccessResponse(flowStartNodes, "FlowStart nodes retrieved successfully"));
            }
            catch (Exception ex)
            {
                return StatusCode(500, CreateErrorResponse<string>(ex.Message, ex.StackTrace));
            }
        }

        [HttpPost("ToggleActive/{workflowId}")]
        [Authorize]
        public async Task<IActionResult> ToggleWorkflowActiveStatus(Guid workflowId)
        {
            try
            {
                var isActive = await _workflowService.ToggleWorkflowActiveStatusAsync(workflowId);
                string status = isActive ? "activated" : "deactivated";
                return Ok(CreateSuccessResponse(isActive, $"Workflow {status} successfully"));
            }
            catch (Exception ex)
            {
                return StatusCode(500, CreateErrorResponse<string>(ex.Message, ex.StackTrace));
            }
        }
    }
}