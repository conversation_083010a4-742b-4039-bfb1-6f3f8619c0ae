﻿using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using System.Security.Claims;
using EngagetoContracts.UserContracts;
using EngagetoEntities.UserEntities.Dtos;

namespace Engageto.Controllers.UserControllers
{
    [Route("api/[controller]")]
    [ApiController]
    public class CountryDetailsController : ControllerBase
    {
        private readonly ICountryDetailsService _countryDetailsRepository;

        public CountryDetailsController(ICountryDetailsService countryDetailsRepository)
        {
            _countryDetailsRepository = countryDetailsRepository;
        }

        [HttpPost("add-country")]
       // [AuthorizeRoles(RoleConstants.Admin, RoleConstants.Owner, RoleConstants.Teammate)]
        public async Task<IActionResult> AddCountry([FromForm] string countryName, string countryCode, IFormFile countryImage)
        {
            try
            {
                var currentUserIdClaim = User.Claims.FirstOrDefault(c => c.Type == ClaimTypes.Name);

                if (currentUserIdClaim == null || !Guid.TryParse(currentUserIdClaim.Value, out Guid currentUserId))
                {
                    return BadRequest(new { Message = "Invalid current user." });
                }

                var countryId = await _countryDetailsRepository.AddCountryDetails(currentUserId, countryName, countryCode, countryImage);

                if (countryId != Guid.Empty)
                {
                    return Ok(new { CountryId = countryId, Message = "Country added successfully." });
                }
                else
                {
                    return BadRequest(new { Message = "Failed to add Country." });
                }
            }
            catch (ArgumentException ex)
            {
                return BadRequest(new { Message = ex.Message });
            }
            catch (Exception ex)
            {
                return StatusCode(500, new { Message = "Internal Server Error" });
            }
        }

        [HttpPut("update-country/{countryId}")]
     //   [AuthorizeRoles(RoleConstants.Admin, RoleConstants.Owner, RoleConstants.Teammate)]
        public async Task<IActionResult> UpdateCountry(Guid countryId, [FromForm] UpdateCountryDto updateCountryDto)
        {
            try
            {
                var currentUserIdClaim = User.Claims.FirstOrDefault(c => c.Type == ClaimTypes.Name);

                if (currentUserIdClaim == null || !Guid.TryParse(currentUserIdClaim.Value, out Guid currentUserId))
                {
                    return BadRequest(new { Message = "Invalid current user." });
                }

                var success = await _countryDetailsRepository.UpdateCountryDetails(currentUserId, countryId, updateCountryDto);

                if (success)
                {
                    return Ok(new { Message = "Country updated successfully." });
                }
                else
                {
                    return BadRequest(new { Message = "Failed to update Country." });
                }
            }
            catch (ArgumentException ex)
            {
                return BadRequest(new { Message = ex.Message });
            }
            catch (Exception ex)
            {
                return StatusCode(500, new { Message = "Internal Server Error" });
            }
        }

        [HttpDelete("delete-country/{countryId}")]
       // [AuthorizeRoles(RoleConstants.Admin, RoleConstants.Owner, RoleConstants.Teammate)]
        public async Task<IActionResult> DeleteCountry(Guid countryId)
        {
            try
            {
                var currentUserIdClaim = User.Claims.FirstOrDefault(c => c.Type == ClaimTypes.NameIdentifier);

                if (currentUserIdClaim == null || !Guid.TryParse(currentUserIdClaim.Value, out Guid currentUserId))
                {
                    return BadRequest(new { Message = "Invalid current user." });
                }

                var success = await _countryDetailsRepository.DeleteCountryDetails(currentUserId, countryId);

                if (success)
                {
                    return Ok(new { Message = "Country deleted successfully." });
                }
                else
                {
                    return BadRequest(new { Message = "Failed to delete Country." });
                }
            }
            catch (ArgumentException ex)
            {
                return BadRequest(new { Message = ex.Message });
            }
            catch (Exception ex)
            {
                return StatusCode(500, new { Message = "Internal Server Error" });
            }
        }

        [HttpGet("get-all-countries")]
      //  [AuthorizeRoles(RoleConstants.Admin, RoleConstants.Owner, RoleConstants.Teammate)]
        public async Task<IActionResult> GetAllCountries([FromQuery] string searchQuery = null)
        {
            try
            {
                var countries = await _countryDetailsRepository.GetAllCountries(searchQuery);
                return Ok(countries);
            }
            catch (Exception ex)
            {
                return StatusCode(500, new { Message = "Internal Server Error" });
            }
        }
        [HttpGet("get-all-country-codes")]
       // [AuthorizeRoles(RoleConstants.Admin, RoleConstants.Owner, RoleConstants.Teammate)]
        public async Task<IActionResult> GetAllCountryCodes()
        {
            try
            {
                var countryCodes = await _countryDetailsRepository.GetAllCountryCodesAsync();
                return Ok(countryCodes);
            }
            catch (Exception ex)
            {
                return StatusCode(500, new { Message = "Internal Server Error" });
            }
        }
    }
}
