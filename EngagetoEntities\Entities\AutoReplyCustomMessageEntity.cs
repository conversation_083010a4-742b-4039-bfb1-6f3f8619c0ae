﻿using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;


namespace EngagetoEntities.Entities
{
    [Table("AutoReplyCustomMessageEntities")]
    public class AutoReplyCustomMessageEntity : BaseEntity
    {
        [Key]
        public Guid Id { get; set; }
        public Guid AutoReplyId { get; set; }
        [ForeignKey("AutoReplyId")]
        public AutoReplyAutomationEntity AutoReplyAutomationEntity { get; set; }
        public string BodyMessage { get; set; }
        public string? Buttons { get; set; }

        public AutoReplyCustomMessageEntity() { }
        public AutoReplyCustomMessageEntity(Guid autoReplyId, Guid userId, string bodyMessage,string? buttons)
        {
            Id = Guid.NewGuid();
            AutoReplyId = autoReplyId;
            BodyMessage = bodyMessage;
            Buttons = buttons;
            CreatedAt = DateTime.UtcNow;
            CreatedBy = userId;
            UpdatedAt = DateTime.UtcNow;
            UpdatedBy = userId;
        }
    }
}
