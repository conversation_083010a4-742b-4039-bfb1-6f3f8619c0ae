﻿using EngagetoEntities.Extensions;
using System.ComponentModel.DataAnnotations;

namespace EngagetoEntities.Dtos
{
    public class TextData
    {
        [Required(ErrorMessage = "Contact Can't be empty.")]
        [ContactNumberList(ErrorMessage = "Invalid contact number. Each contact must be a valid phone number.")]
        public List<string> Contact { get; set; } = default!;
        [Required(ErrorMessage = "Can't be empty.")]
        public string TextMessage { get; set; } = default!;
    }

    public class ContactNumbers
    {
        [Required(ErrorMessage = "Contact is requied.")]
        public List<string> Contact { get; set; } = default!;
    }
    public class MediaData
    {
        [Required(ErrorMessage = "Contact can't be empty.")]
        [ContactNumberList(ErrorMessage = "Invalid contact number. Each contact must be a valid phone number.")]
        public List<string> Contact { get; set; } = default!;
        public string MediaUrl { get; set; } = default!;
        public string? Caption { get; set; }
    }

    public class TextMediaMassages
    {
        public List<string> ContactNumbers { get; set; } = new();
        public Guid? ReplyId { get; set; }
        public string? Textmessage { get; set; }
        public string? MediaUrl { get; set; }
    }

}
