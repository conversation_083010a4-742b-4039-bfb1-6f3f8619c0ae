﻿namespace EngagetoEntities.Dtos
{
    public class UserDto : BaseUserDto
    {
    }
    public class BaseUserDto
    {
        public Guid Id { get; set; }
        public string BusinessId { get; set; }
        public string Name { get; set; }
        public string EmailAddress { get; set; }
        public string? CountryCode { get; set; }
        public string PhoneNumber { get; set; }
        public string CountryName { get; set; }
        public string Address { get; set; }
        public string About { get; set; }
        public string Designation { get; set; }
        public bool Status { get; set; }
        public string Image { get; set; }
        public DateTime CreationDate { get; set; }
    }
    public class ViewUserDto : BaseUserDto { }
}
