using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using System.Security.Claims;
using EngagetoContracts.Services;
using EngagetoEntities.Dtos.WorkflowDtos;
using EngagetoRepository.Services;

namespace Engageto.Controllers.WorkflowCustomResponseController
{
    [ApiController]
    [Route("api/[controller]")]
    public class WorkflowCustomResponseController : BaseController
    {
        private readonly IWorkflowCustomResponseService _workflowCustomResponseService;
        private readonly ILogger<WorkflowCustomResponseController> _logger;
        private readonly  IUserIdentityService _userIdentityService;

        public WorkflowCustomResponseController(
            IWorkflowCustomResponseService workflowCustomResponseService,
            ILogger<WorkflowCustomResponseController> logger,
            IUserIdentityService userIdentityService)
        {
            _workflowCustomResponseService = workflowCustomResponseService;
            _logger = logger;
            _userIdentityService = userIdentityService;
        }

        [HttpPost]
        [Authorize]
        public async Task<IActionResult> CreateWorkflowCustomerResponse([FromBody] WorkflowCustomerResponseDto request)
        {
            try
            {
                var currentUserIdClaim = User.Claims.FirstOrDefault(c => c.Type == ClaimTypes.Name);
                if (currentUserIdClaim == null || !Guid.TryParse(currentUserIdClaim.Value, out Guid currentUserId))
                {
                    return Unauthorized(new { success = false, message = "Invalid user" });
                }

                var response = await _workflowCustomResponseService.CreateWorkflowCustomResponseAsync(request, currentUserId);
                return Ok(CreateSuccessResponse(response, "Workflow custom response created successfully"));
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error creating workflow custom response");
                return StatusCode(500, CreateErrorResponse<string>(ex.Message, ex.StackTrace));
            }
        }

        [HttpPut("{id}")]
        [Authorize]
        public async Task<IActionResult> UpdateWorkflowCustomerResponse(Guid id, [FromBody] WorkflowCustomerResponseDto request)
        {
            try
            {
                var currentUserId = _userIdentityService.UserId;
                var response = await _workflowCustomResponseService.UpdateWorkflowCustomResponseAsync(id, request, currentUserId);
                return Ok(CreateSuccessResponse(response, "Workflow custom response updated successfully"));
            }
            catch (KeyNotFoundException ex)
            {
                return NotFound(new { success = false, message = ex.Message });
            }
            catch (Exception ex)
            {;
                return StatusCode(500, CreateErrorResponse<string>(ex.Message, ex.StackTrace));
            }
        }

        [HttpGet]
        [Authorize]
        public async Task<IActionResult> GetAllWorkflowCustomerResponses()
        {
            try
            {
                var responses = await _workflowCustomResponseService.GetAllWorkflowCustomResponsesAsync();
                return Ok(CreateSuccessResponse(responses, "Workflow custom responses retrieved successfully"));
            }
            catch (Exception ex)
            {
                return StatusCode(500, CreateErrorResponse<string>(ex.Message, ex.StackTrace));
            }
        }

        [HttpGet("{id}")]
        [Authorize]
        public async Task<IActionResult> GetWorkflowCustomerResponseById(Guid id)
        {
            try
            {
                var response = await _workflowCustomResponseService.GetWorkflowCustomResponseByIdAsync(id);
                return Ok(CreateSuccessResponse(response, "Workflow custom response retrieved successfully"));
            }
            catch (KeyNotFoundException ex)
            {
                return NotFound(new { success = false, message = ex.Message });
            }
            catch (Exception ex)
            {
                return StatusCode(500, CreateErrorResponse<string>(ex.Message, ex.StackTrace));
            }
        }
    }
} 