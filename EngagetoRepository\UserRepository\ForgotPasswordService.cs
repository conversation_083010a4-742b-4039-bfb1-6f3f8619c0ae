﻿using EngagetoContracts.GeneralContracts;
using EngagetoContracts.UserContracts;
using EngagetoEntities.Entities;
using EngagetoEntities.UserEntities.Models;
using EngagetoRepository.GeneralServices;
using Microsoft.AspNetCore.Http;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Configuration;

namespace EngagetoRepository.UserRepository
{
    public class ForgotPasswordService : IForgotPasswordService
    {
        private readonly ApplicationDBContext _context;
        private readonly IEmailService _emailService;
        private readonly Random random = new Random();
        private readonly IHttpContextAccessor _contextAccessor;
        private readonly IConfiguration _configuration;
        IEnvironmentService _environmentService;
        private readonly string _websiteLink;

        public ForgotPasswordService(IConfiguration config, ApplicationDBContext context, IEmailService emailService, IHttpContextAccessor contextAccessor, IEnvironmentService environmentService)
        {
            _context = context;
            _emailService = emailService;
            _contextAccessor = contextAccessor;
            _configuration = config;
            _environmentService = environmentService;

            if (_environmentService.IsDevelopment)
                _websiteLink = _environmentService.GetDevWebsiteLink();
            else
                _websiteLink = _environmentService.GetProdWebsiteLink();
        }


        public async Task<bool> SendOTPTokenAsync(string email)
        {
            var accountUser = await _context.Ahex_CRM_Users.FirstOrDefaultAsync(u => u.EmailAddress == email);

            if (accountUser != null)
            {

                return await StoreOTPTokenAsync(email);
            }

            return false;
        }

        private async Task<bool> StoreOTPTokenAsync(string email)
        {
            var httpContext = _contextAccessor.HttpContext;
            if (httpContext == null)
            {
                Console.WriteLine("HttpContext is not available.");
                return false;
            }

            var otpToken = GenerateOTPToken();
            var expiration = DateTime.UtcNow.AddMinutes(5);

            var existingEntry = await _context.ForgotPasswords.FirstOrDefaultAsync(fp =>
                             fp.Email == email);

            if (existingEntry != null)
            {
                existingEntry.OTPToken = otpToken;
                existingEntry.OTPTokenExpiration = expiration;
            }
            else
            {
                var forgotPasswordEntry = new ForgotPassword
                {
                    Email = email,
                    OTPToken = otpToken,
                    OTPTokenExpiration = expiration
                };

                _context.ForgotPasswords.Add(forgotPasswordEntry);
            }

            try
            {
                await _context.SaveChangesAsync();
            }
            catch (Exception ex)
            {
                Console.WriteLine($"{ex.Message}");
                return false;
            }

            try
            {
                bool emailSent = await _emailService.SendEmailAsync(
                    email,
                    "Forgot Password",
                    $"<p>Hi,</p>" +
                    $"<p>Your password OTP is: {otpToken}</p>",
                    _configuration["SmtpSettings:LogoUrl"],
                    _websiteLink
                );

                return emailSent;
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Exception while sending email: {ex}");
                return false;
            }
        }




        public async Task<bool> VerifyOTPToken(string otp, string email)
        {

            var manageAccountUser = await _context.Ahex_CRM_Users.FirstOrDefaultAsync(u => u.EmailAddress == email);
            if (manageAccountUser != null)
            {
                return await VerifyOTPTokenForUser(otp, email);
            }

            return false;
        }

        private async Task<bool> VerifyOTPTokenForUser(string otp, string email)
        {
            try
            {
                var latestOTPEntity = await _context.ForgotPasswords
                     .Where(fp => fp.OTPTokenExpiration > DateTime.UtcNow &&
                                  fp.Email == email &&
                                  fp.OTPToken == otp)
                     .OrderByDescending(fp => fp.OTPTokenExpiration)
                     .FirstOrDefaultAsync();

                return latestOTPEntity != null && latestOTPEntity.OTPToken == otp;
            }
            catch (Exception ex)
            {

                return false;
            }
        }

        public async Task<bool> UpdatePassword(string email, string newPassword, string confirmPassword, HttpContext httpContext)
        {

            if (newPassword != confirmPassword)
            {
                return false;
            }

            try
            {

                string newPasswordHash = HashPassword(newPassword);


                if (string.IsNullOrEmpty(email))
                {
                    return false;
                }


                var manageAccountUser = await _context.Ahex_CRM_Users.FirstOrDefaultAsync(u => u.EmailAddress == email);
                if (manageAccountUser != null)
                {
                    manageAccountUser.Password = newPasswordHash;
                    await _context.SaveChangesAsync();
                    return true;
                }


                return false;
            }
            catch (Exception ex)
            {

                Console.WriteLine($"Exception occurred while updating password: {ex}");
                return false;
            }
        }


        private string HashPassword(string password)
        {

            return BCrypt.Net.BCrypt.HashPassword(password);
        }
        private string GenerateOTPToken()
        {
            const int otpLength = 6;
            int minValue = (int)Math.Pow(10, otpLength - 1);
            int maxValue = (int)Math.Pow(10, otpLength) - 1;

            int otpValue = new Random().Next(minValue, maxValue);

            return otpValue.ToString("D6");
        }
    }
}
