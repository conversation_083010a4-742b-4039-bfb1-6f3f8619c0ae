﻿using Microsoft.AspNetCore.Http;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace EngagetoRepository.WebhookRepository.Hubs.Service
{
   
        public class CustomFormFile : IFormFile
        {
            private readonly Stream _stream;
            private readonly string _contentType;
            private readonly string _fileName;

            public CustomFormFile(Stream stream, string contentType, string fileName)
            {
                _stream = stream ?? throw new ArgumentNullException(nameof(stream));
                _contentType = contentType;
                _fileName = fileName ?? throw new ArgumentNullException(nameof(fileName));
            }

            public string ContentType => _contentType;

            public string ContentDisposition => $"form-data; name=\"{Name}\"; filename=\"{_fileName}\"";

            public IHeaderDictionary Headers => new HeaderDictionary();

            public long Length => _stream.Length;

            public string Name => "file";

            public string FileName => _fileName;

            public void CopyTo(Stream target)
            {
                _stream.CopyTo(target);
            }

            public async Task CopyToAsync(Stream target, CancellationToken cancellationToken = default)
            {
                await _stream.CopyToAsync(target, cancellationToken);
            }

            public Stream OpenReadStream()
            {
                return _stream;
            }
        }
}
