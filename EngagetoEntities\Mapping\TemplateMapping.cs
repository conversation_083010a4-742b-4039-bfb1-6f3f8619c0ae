﻿using EngagetoEntities.Dtos.ConversationDtos;
using EngagetoEntities.Dtos.TemplateDtos;
using EngagetoEntities.Dtos.WebhookDtos;
using EngagetoEntities.Entities;
using EngagetoEntities.Enums;
using Mapster;
using Newtonsoft.Json;

namespace EngagetoEntities.Mapping
{
    public static class TemplateMapping
    {
        public static void RegisterMappings()
        {
            TypeAdapterConfig<CreateTemplateDto, Template>
                .NewConfig()
                .Map(dest => dest.UserId, src => src.UserId)
                .Map(dest => dest.LanguageCode, src => src.Language.ToString())
                .Map(dest => dest.MediaAwsUrl, src => src.MediaFile)
                .Map(dest => dest.UpdatedDate, src => DateTime.UtcNow);

            TypeAdapterConfig<EditTemplateDto, CreateTemplateDto>
                .NewConfig()
                .Map(dest => dest.UserId, src => src.UserId);

            TypeAdapterConfig<EditTemplateDto, Template>
                .NewConfig()
                .Map(dest => dest.MediaAwsUrl, src => src.MediaFile);

            TypeAdapterConfig<Template, Template>
                .NewConfig();

            TypeAdapterConfig<Conversations, ConversationDto>
                .NewConfig()
                .Map(dest => dest.UrlButtonNames,
                     src => string.IsNullOrWhiteSpace(src.UrlButtonNames)
                        ? Array.Empty<string>()
                        : src.UrlButtonNames.Split(',', StringSplitOptions.RemoveEmptyEntries))
                .Map(dest => dest.RedirectUrls,
                     src => string.IsNullOrWhiteSpace(src.RedirectUrls)
                        ? Array.Empty<string>()
                        : src.RedirectUrls.Split(',', StringSplitOptions.RemoveEmptyEntries))
                .Map(dest => dest.QuickReplies,
                     src => string.IsNullOrWhiteSpace(src.QuickReplies)
                        ? Array.Empty<string>()
                        : src.QuickReplies.Split(',', StringSplitOptions.RemoveEmptyEntries));

            TypeAdapterConfig<Conversations, ConversationDto>
                .NewConfig()
                .Map(dest => dest.UrlButtonNames,
                     src => string.IsNullOrWhiteSpace(src.UrlButtonNames)
                        ? Array.Empty<string>()
                        : src.UrlButtonNames.Split(',', StringSplitOptions.RemoveEmptyEntries))
                .Map(dest => dest.RedirectUrls,
                     src => string.IsNullOrWhiteSpace(src.RedirectUrls)
                        ? Array.Empty<string>()
                        : src.RedirectUrls.Split(',', StringSplitOptions.RemoveEmptyEntries))
                .Map(dest => dest.QuickReplies,
                     src => string.IsNullOrWhiteSpace(src.QuickReplies)
                        ? Array.Empty<string>()
                        : src.QuickReplies.Split(',', StringSplitOptions.RemoveEmptyEntries))
                .Map(dest => dest.TemplateMediaFile, src => src.TemplateMediaUrl)
                .Map(dest => dest.Action, src => !string.IsNullOrEmpty(src.Action) ? JsonConvert.DeserializeObject(src.Action) : null);

            TypeAdapterConfig<Conversations, WebhookResponseDto>
                .NewConfig()
                .Map(dest => dest.Buttons, src => GetButtons(src));

            TypeAdapterConfig<WATemplateDto, Template>
            .NewConfig()
            .Map(dest => dest.MetaId, src => src.Id)
            .Map(dest => dest.TemplateName, src => src.Name)
            .Map(dest => dest.LanguageCode, src => src.Language)
            .Map(dest => dest.MediaType, src => GetTemplateHeaderInfo(src.Components).mediatype)
            .Map(dest => dest.Header, src => GetTemplateHeaderInfo(src.Components).text)
            .Map(dest => dest.MediaAwsUrl, src => GetTemplateHeaderInfo(src.Components).url)
            .Map(dest => dest.Body, src => src.Components.FirstOrDefault(x => x.Type.Contains("BODY")).Text)
            .Map(dest => dest.Buttons, src => GetWATemplateButtons(src.Components))
            .Map(dest => dest.Footer, src => src.Components.Any(x => x.Type != null && x.Type.Contains("FOOTER"))
                    ? src.Components.First(x => x.Type.Contains("FOOTER")).Text
                    : string.Empty)

            .Map(dest => dest.Status, src => src.Status)
            .Map(dest => dest.Category, src => src.Category);
        }

        public static (MediaType mediatype, string? text, string? url) GetTemplateHeaderInfo(List<ComponentDto> components)
        {
            if (components.Any(x => x.Type.Contains("HEADER")))
            {
                var component = components.First(x => x.Type.Contains("HEADER"));
                var text = component.Text;
                var mediaType = component.Format ?? MediaType.NONE;
                string url = string.Empty;
                if ((mediaType != MediaType.TEXT) && (mediaType != MediaType.NONE))
                    url = component.Example?.HeaderHandle?[0] ?? string.Empty;

                return (mediaType, text, url);
            }
            return (MediaType.NONE, null, null);
        }

        public static List<Button> GetWATemplateButtons(List<ComponentDto> components)
        {
            List<Button> buttons = new();
            if (components.Any(x => x.Type.Contains("BUTTONS")))
            {
                var component = components.FirstOrDefault(x => x.Type == "BUTTONS");
                foreach (var button in component?.Buttons ?? new())
                {
                    switch (button.Type)
                    {
                        case Enums.WAButton.URL:
                            buttons.Add(new Button() { ButtonName = button.Text, ButtonValue = button.Url, ButtonType = button.Type.ToString() });
                            break;
                        case Enums.WAButton.PHONE_NUMBER:
                            buttons.Add(new Button() { ButtonName = button.Text, ButtonValue = button.PhoneNumber, ButtonType = button.Type.ToString() });
                            break;
                        case Enums.WAButton.QUICK_REPLY:
                            buttons.Add(new Button() { ButtonValue = button.Text, ButtonType = button.Type.ToString() });
                            break;
                        case Enums.WAButton.COPY_CODE:
                            buttons.Add(new Button() { ButtonName = button.Text, ButtonValue = button.Example?[0], ButtonType = button.Type.ToString() });
                            break;
                    }
                }
                return buttons;
            }
            return buttons;
        }
        public static List<Dtos.WebhookDtos.ButtonDto> GetButtons(Conversations conv)
        {
            var buttons = new List<Dtos.WebhookDtos.ButtonDto>();

            AddPhoneButtonIfPresent(conv, buttons);
            AddQuickReplyButtonsIfPresent(conv, buttons);
            AddUrlButtonsIfPresent(conv, buttons);

            return buttons;
        }

        private static void AddPhoneButtonIfPresent(Conversations conv, List<Dtos.WebhookDtos.ButtonDto> buttons)
        {
            if (string.IsNullOrWhiteSpace(conv.PhoneNumber)) return;

            var phoneNumberAndCountryCode = PhoneNumberHelper.GetCountryCodeAndPhoneNumber(conv.PhoneNumber);
            buttons.Add(new Dtos.WebhookDtos.ButtonDto
            {
                CountryCode = phoneNumberAndCountryCode.CountryCode,
                ButtonType = "PHONE_NUMBER",
                ButtonValue = conv.PhoneNumber,
                ButtonName = conv.CallButtonName
            });
        }

        private static void AddQuickReplyButtonsIfPresent(Conversations conv, List<Dtos.WebhookDtos.ButtonDto> buttons)
        {
            if (string.IsNullOrWhiteSpace(conv.QuickReplies)) return;

            var quickReplies = conv.QuickReplies.Split(',')
                .Select(reply => new Dtos.WebhookDtos.ButtonDto
                {
                    ButtonType = "QUICK_REPLY",
                    ButtonValue = reply
                });

            buttons.AddRange(quickReplies);
        }

        private static void AddUrlButtonsIfPresent(Conversations conv, List<Dtos.WebhookDtos.ButtonDto> buttons)
        {
            if (string.IsNullOrWhiteSpace(conv.RedirectUrls) || string.IsNullOrWhiteSpace(conv.UrlButtonNames)) return;

            var urls = conv.RedirectUrls.Split(',');
            var urlButtonNames = conv.UrlButtonNames.Split(',');

            if (urls.Length != urlButtonNames.Length) return;

            var urlButtons = urls.Select((url, i) => new Dtos.WebhookDtos.ButtonDto
            {
                ButtonType = "URL",
                ButtonValue = url,
                ButtonName = urlButtonNames[i]
            });

            buttons.AddRange(urlButtons);
        }
    }
}
