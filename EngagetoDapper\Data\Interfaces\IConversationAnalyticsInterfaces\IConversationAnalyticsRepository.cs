﻿using EngagetoEntities.Entities;
using System.Data;


namespace EngagetoDapper.Data.Interfaces.IConversationAnalyticsInterfaces
{
    public interface IConversationAnalyticsRepository
    {
        Task<bool> SaveConversationAnalyticsAsync<T>(string tableName, List<string> columns, IEnumerable<T> entities, IDbTransaction transaction);
        Task<List<ConversationAnalyticsEntity>> GetAnalyticsDetailsByStartAndEndDateAsync(string companyId, DateTime startDate, DateTime endDate, string? ConversationCategory = null);
        Task<IEnumerable<T>> GetDefaultConversationAnalyticPriceAsync<T>(int? year = null);
        Task<bool> SaveConversationAnalyticsJobRequestAsync<T>(string tableName, List<string> columns, T entities);
        Task<bool> UpdateConversationAnalyticsJobRequestAsync<T>(string tableName, List<string> columns, T entity, Dictionary<string, object> conditions);
        Task<T?> GetConversationAnalyticsJobRequestAsync<T>(string companyId);
        Task<bool> IsSentWelcomeMessageAsync(string companyId, string phoneNumber);
    }
}
