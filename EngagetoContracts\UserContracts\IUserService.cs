﻿using Microsoft.AspNetCore.Http;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using EngagetoEntities.UserEntities.Models;

namespace EngagetoContracts.UserContracts
{
    public interface IUserService
    {
        Task<bool> UpdateUserPasswordAsync(/*Guid userId,*/string Email, string newPassword, string confirmPassword, string temporaryPassword);
        Task<AssignRoleResult> AssignRoleToUserAsync(Guid currentUserId, Guid targetUserId, Guid roleId);

        Task<bool> UpdateAssignedRoleAsync(string email, string companyId, Guid newRoleId);
        Task<bool> DeleteAssignedRolesAsync(Guid currentUserId, Guid targetUserId, Guid roleId);
    }
}
