﻿using EngagetoEntities.Enums;

namespace EngagetoEntities.Dtos.CampaignDto
{
    public class CampaignMessageCountsDto
    {
        public string CampaignId { get; set; }
        public string CampaignTitle { get; set; }
        public string? Createdby { get; set; }
        public int? Scheduled { get; set; }
        public int? Attempted { get; set; }
        public int? Sent { get; set; }
        public int? Delivered { get; set; }
        public int? Read { get; set; }
        public int? Failed { get; set; }
        public int? Replied { get; set; }
        public int? Undelivered { get; set; }
        public CampaignState State { get; set; }
        public DateTime? DateSetLive { get; set; }
        public List<ConversationDetails> Conversations { get; set; }
    }
    public class ConversationDetails
    {
        public string Status { get; set; }
        public DateTime CreatedAt { get; set; }
        public string? TextMessage { get; set; }
        public string? MediaFileName { get; set; }
        public string? MediaMimeType { get; set; }
        public string? MediaUrl { get; set; }
        public string? MediaCaption { get; set; }
        public string? TemplateMediaUrl { get; set; }
        public string? TemplateHeader { get; set; }
        public string? TemplateBody { get; set; }
        public string? TemplateFooter { get; set; }
        public string? CallButtonName { get; set; }
        public string? PhoneNumber { get; set; }
        public string? UrlButtonNames { get; set; }
        public string? RedirectUrls { get; set; }
        public string? QuickReplies { get; set; }
    }

    public class ViewCampaignAnalyticsDto
    {
        public Guid CampaignId { get; set; }
        public string CampaignTitle { get; set; }
        public CampaignState State { get; set; }
        public DateTime? CreatedDate { get; set; }
        public string? Createdby { get; set; }
        public DateTime? DateSetLive { get; set; }
    }

}
