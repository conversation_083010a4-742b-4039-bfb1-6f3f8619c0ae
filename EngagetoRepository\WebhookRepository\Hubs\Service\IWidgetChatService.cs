﻿using EngagetoContracts.Services;
using EngagetoEntities.Dtos.ConversationDtos;
using EngagetoEntities.Dtos.WidgetChatHubDtos;

namespace EngagetoRepository.WebhookRepository.Hubs.Service
{
    public interface IWidgetChatService:ITransientService
    {
        Task<WidgetChatDto> SendWidgetMessageToWhatsappAsync(WidgetChatDto message);
        Task ReceiveMessageToWidgetHubAsync(ConversationDto message);
    }
}
