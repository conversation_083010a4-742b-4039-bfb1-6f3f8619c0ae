﻿using EngagetoDapper.Data.Dapper.Repositories.InboxRepositories;
using EngagetoDapper.Data.Interfaces.GenericInterfaces;
using EngagetoDapper.Data.Interfaces.IUserInterfaces.IUserServices;
using EngagetoEntities.Dtos.ContactDtos;
using EngagetoEntities.Entities;
using EngagetoEntities.Enums;

namespace EngagetoDapper.Data.Dapper.Services.UserServices
{
    public class InboxService : IInboxService
    {
        private readonly IGenericRepository _genericRepository;
        private readonly IInboxRepository _inboxRepository;

        public InboxService(IGenericRepository genericRepository, IInboxRepository inboxRepository)
        {
            _genericRepository = genericRepository;
            _inboxRepository = inboxRepository;
        }
        public async Task<List<ChatStatusEntity>> GetChatStatusByContactIdAsync(Guid contactId)
        {
            try
            {
                return await _genericRepository
                    .GetByObjectAsync<ChatStatusEntity>(new Dictionary<string, object> { { "ContactId", contactId } });

            }
            catch (Exception ex)
            {
                throw new Exception(ex.Message);
            }
        }

        public async Task<bool> SaveAndUpdateChatStatusAsync(Guid contactId, ChatStatus chatStatus, Guid? userId)
        {
            try
            {
                var result = await _genericRepository
                      .SaveAsync<ChatStatusEntity>(new ChatStatusEntity(contactId, chatStatus, userId));

                return result > 0;
            }
            catch (Exception ex)
            {
                throw new Exception(ex.Message);
            }
        }

        public async Task<List<ChatStatusEntityDto>> GetChatStatusAsync(Guid businessId)
        {
            try
            {
                return (await _inboxRepository.GetChatStatusAsync(businessId)).ToList();

            }
            catch (Exception ex)
            {
                throw new Exception(ex.Message);
            }
        }
        public async Task<int> UpdateChatStatusAsync(Guid businessId)
        {
            try
            {
                return await _inboxRepository.UpdateOpenChatStatusAsync(businessId);
            }
            catch (Exception ex)
            {
                throw new Exception(ex.Message);
            }
        }
        public async Task<bool> SaveContactAssignmentAsync(string companyId, Guid contactId, Guid userId, Guid assignedToUserId)
        {
            try
            {
                Guid.TryParse(companyId, out var businessId);
                ContactAssignmentHistoryEntity contactAssignment = new ContactAssignmentHistoryEntity(contactId, userId, businessId, assignedToUserId);
                var result = await _genericRepository.SaveAsync(contactAssignment);
                return result > 0;
            }
            catch (Exception ex)
            {
                throw new Exception(ex.Message, ex);
            }
        }
        public async Task<IEnumerable<ContactAssignmentHistoryDto>> GetContactAssignmentsAsync(string? companyId, Guid? contactId)
        {
            try
            {
                Guid.TryParse(companyId, out var businessId);
                var result = await _inboxRepository.GetContactAssignmentsAsync<ContactAssignmentHistoryDto>(businessId, contactId)
                .ConfigureAwait(false);
                return result;
            }
            catch (Exception ex)
            {
                throw new Exception(ex.Message);
            }
        }
        public async Task<IEnumerable<ContactAssignAndReassignHistoryDto>> GetContactAssignmentHistoriesAsync(string? companyId,
            Guid? contactId, DateTime fromDate, DateTime toDate, CancellationToken cancellationToken)
        {
            try
            {
                Guid.TryParse(companyId, out var businessId);
                var result = await _inboxRepository
                    .GetContactAssignmentHistoriesAsync<ContactAssignAndReassignHistoryDto>(businessId, contactId, fromDate, toDate, cancellationToken)
                .ConfigureAwait(false);
                return result;
            }
            catch (Exception ex)
            {
                throw new Exception(ex.Message);
            }
        }
        public async Task<bool> CheckConversationSatus(string companyId, string phoneNumber, CancellationToken cancellationToken)
        {
            try
            {
                var result = await _inboxRepository.CheckConversationStatus(companyId, phoneNumber, cancellationToken);
                return result;
            }
            catch (Exception ex)
            {
                throw new Exception(ex.Message);
            }
        }

        public async Task UpdateErrorMessageAsync(Guid conversationId, CancellationToken cancellationToken)
        {
            try
            {
                var conversation = (await _genericRepository.GetByObjectAsync<EngagetoEntities.Entities.Conversations>(new Dictionary<string, object> { { "Id", conversationId }, { "Status", 0 } }))?.FirstOrDefault();
                if (conversation != null)
                {
                    conversation.ErrorMessage = "This message was not delivered to maintain healthy ecosystem engagement.";
                    await _genericRepository.UpdateRecordAsync(conversation, new Dictionary<string, object> { { "Id", conversationId }, { "Status", 0 } });
                }
            }
            catch (Exception ex)
            {
                Console.WriteLine(ex.Message);
            }
        }
        public async Task<List<EngagetoEntities.Entities.Conversations>?> GetConversationsAsync(Guid conversationId, CancellationToken cancellationToken)
        {
            try
            {
                var conversations = await _genericRepository.GetByObjectAsync<EngagetoEntities.Entities.Conversations>(new Dictionary<string, object> { { "Id", conversationId } });
                return conversations;
            }
            catch (Exception ex)
            {
                Console.WriteLine(ex.Message);
                return null;
            }
        }

        public async Task<WATemplateCategory> GetTemplateCategoryAsync(string referenceId)
        {
            try
            {
               return await _inboxRepository.GetTemplateCategoryAsync(referenceId);    
            }
            catch (Exception ex)
            {
                Console.WriteLine(ex.Message);
                return WATemplateCategory.None;
            }
        }

        public async Task<bool> SaveConversationCostAsync(ConversationAnalyticsEntity conversation)
        {
            try
            {
                return await _inboxRepository.SaveConversationCostAsync(conversation);
            }
            catch (Exception ex)
            {
                Console.WriteLine(ex.Message);
                return false;
            }
        }
    }
}
