﻿using EngagetoEntities.Enums;

namespace EngagetoEntities.Dtos.WebhookDtos
{
    public class WebhookResponseDto : FieldDto
    {
        public Guid Id { get; set; }
        public ConvStatus Status { set; get; }
        public string? TextMessage { get; set; }
        public string? MediaCaption { get; set; }
        public string? MediaFileName { get; set; }
        public string? MediaMimeType { get; set; }
        public string? MediaUrl { get; set; }
        public string? TemplateMediaType { set; get; }
        public List<ButtonDto>? Buttons { get; set; }
        public string? ContactNo { set; get; }
        public string? ContactName { set; get; }
        public string? ErrorMessage { get; set; }
        public DateTime CreatedAt { set; get; }
    }

    public class ButtonDto
    {
        public string? CountryCode { get; set; }
        public string? ButtonType { get; set; }
        public string? ButtonName { get; set; }
        public string? ButtonValue { get; set; }
    }
}
