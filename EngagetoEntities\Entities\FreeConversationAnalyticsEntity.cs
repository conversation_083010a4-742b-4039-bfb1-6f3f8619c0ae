﻿using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations.Schema;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace EngagetoEntities.Entities
{
    [Table("FreeConversationAnalyticsEntities")]
    public class FreeConversationAnalyticsEntity
    {
        public int Id { get; set; }
        public int PlanId { get; set; }
        public int FreeConversation { get; set; }
        public string PlanType { get; set; }
        public string ConversationCategory { get; set; }
    }
}
