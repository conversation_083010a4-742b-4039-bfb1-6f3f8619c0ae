﻿using Engageto.Attributes;
using EngagetoContracts.UserContracts;
using EngagetoEntities.Entities;
using EngagetoEntities.Response;
using EngagetoEntities.Settings;
using EngagetoEntities.UserEntities.Dtos;
using EngagetoEntities.UserEntities.Models;
using EngagetoEntities.Utilities;
using iTextSharp.text.pdf;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Options;
using System.Security.Claims;
using static EngagetoEntities.UserEntities.Models.ExpiryDateAttribute;

namespace Engageto.Controllers.UserControllers
{
    [Route("api/[controller]")]
    [ApiController]
    public class PaymentsDetailsController : ControllerBase
    {
        private readonly ILogger<PaymentsDetailsController> _logger;
        private readonly IRazorpayPaymentService _paymentRepository;
        private readonly IRazorPayService _razorpayService;
        private readonly ApplicationDBContext _dbContext;
        private readonly IPlanEntitiesService _planEntitiesService;
        private readonly IWalletService _walletService;
        private readonly TemplateFiles _templateFiles;
        private readonly EngagetoDapper.Data.Interfaces.IUserInterfaces.IUserServices.IUserService _userService;


        public PaymentsDetailsController(ILogger<PaymentsDetailsController> logger,
            IRazorpayPaymentService paymentRepository,
            IRazorPayService razorpayService,
            ApplicationDBContext dbContext,
            IPlanEntitiesService planEntitiesService,
            IWalletService walletService,
            IOptions<TemplateFiles> templateFiles,
            EngagetoDapper.Data.Interfaces.IUserInterfaces.IUserServices.IUserService userService)
        {
            _logger = logger;
            _paymentRepository = paymentRepository;
            _razorpayService = razorpayService;
            _dbContext = dbContext;
            _planEntitiesService = planEntitiesService;
            _walletService = walletService;
            _userService = userService;
            _templateFiles = templateFiles.Value;
        }

        /* [HttpPost("create-order")]
         [Authorize]
         public async Task<IActionResult> CreateOrder([FromBody] OrderCreateRequest request)
         {
             try
             {
                 var orderId = await _razorpayService.CreateOrder(request.Amount, request.Currency, request.Receipt);
                 return Ok(new { OrderId = orderId });
             }
             catch (Exception ex)
             {
                 return StatusCode(500, new { Message = $"Failed to create order: {ex.Message}" });
             }
         }

         [HttpPost("verify-payment")]
         [Authorize] 
         public async Task<IActionResult> VerifyPayment([FromBody] PaymentVerifyRequest request)
         {
             try
             {
                 var isValid = await _razorpayService.VerifyPaymentSignature(request.OrderId, request.PaymentId, request.Signature);
                 if (isValid)
                 {
                     return Ok(new { Message = "Payment verified successfully." });
                 }
                 else
                 {
                     return BadRequest(new { Message = "Invalid payment signature." });
                 }
             }
             catch (Exception ex)
             {
                 return StatusCode(500, new { Message = $"Failed to verify payment: {ex.Message}" });
             }
         }*/

        [HttpPost("add-payment-card")]
        [Authorize]
        public async Task<IActionResult> AddPaymentCard([FromForm] PaymentCardDetailsDto paymentCardDto)
        {
            try
            {
                var currentUserIdClaim = User.Claims.FirstOrDefault(c => c.Type == ClaimTypes.Name);

                if (currentUserIdClaim == null || !Guid.TryParse(currentUserIdClaim.Value, out Guid currentUserId))
                {
                    return BadRequest(new { Message = "Invalid current user." });
                }

                var cardId = await _paymentRepository.AddPaymentCardAsync(currentUserId, paymentCardDto);

                return Ok(new { CardId = cardId });
            }
            catch (Exception ex)
            {
                return StatusCode(500, new { Message = $"Internal Server Error: {ex.Message}" });
            }
        }
        [HttpGet("payment-cards")]
        [Authorize]
        public async Task<IActionResult> GetAllPaymentCards(string searchQuery = null)
        {
            try
            {
                var currentUserIdClaim = User.Claims.FirstOrDefault(c => c.Type == ClaimTypes.Name);

                if (currentUserIdClaim == null || !Guid.TryParse(currentUserIdClaim.Value, out Guid currentUserId))
                {
                    return BadRequest(new { Message = "Invalid current user." });
                }

                var paymentCards = await _paymentRepository.GetAllPaymentCardsAsync(currentUserId, searchQuery);
                if (paymentCards == null)
                {
                    return StatusCode(500, new { Message = "Failed to retrieve payment cards." });
                }

                return Ok(paymentCards);
            }
            catch (Exception ex)
            {
                return StatusCode(500, new { Message = $"Internal Server Error: {ex.Message}" });
            }
        }
        [HttpDelete("{cardId}")]
        public async Task<IActionResult> DeletePaymentCard(Guid cardId)
        {
            try
            {
                var deleted = await _paymentRepository.DeletePaymentCardAsync(cardId);
                if (deleted)
                    return Ok(new { Message = "Payment card deleted successfully." });
                else
                    return NotFound(new { Message = "Payment card not found." });
            }
            catch (Exception ex)
            {
                return StatusCode(500, new { Message = $"An error occurred: {ex.Message}" });
            }
        }
        [HttpPost("create-order")]
        [ValidateModelState]
        [Authorize]
        public async Task<IActionResult> CreateOrder([FromBody] OrderRequestModel request)
        {
            try
            {
                DiscountEntity? discount = null;
                if (request.DiscountId != null)
                {
                    discount = await _userService.GetDiscountById(request.DiscountId.Value);
                    if (discount == null)
                        return BadRequest(new { Message = "Invalid Discount Id provided." });

                    if (discount.ValidFrom >= DateTime.Now || discount.ValidTo <= DateTime.Now)
                        return BadRequest(new { Message = "Discount Coupon Expired." });
                }

                var currentUserIdClaim = User.Claims.FirstOrDefault(c => c.Type == ClaimTypes.Name);

                if (currentUserIdClaim == null || !Guid.TryParse(currentUserIdClaim.Value, out Guid currentUserId))
                {
                    return BadRequest(new { Message = "Invalid current user." });
                }

                if (request.DiscountId != null)
                {
                    var paymentDetails = await _razorpayService.GetPaymentWalletDetailsByCompanyIdAsync(currentUserId, request.DiscountId.Value);
                    if (paymentDetails != null && paymentDetails.Count > 0)
                        return BadRequest(new { Message = "Coupon already used by client." });
                }
                var paymentDetail = await _razorpayService.CreateOrderAsync(currentUserId, request);
                return Ok(new { orderId = paymentDetail });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error creating order");
                return StatusCode(500, new { Message = "Failed to create order." });
            }
        }


        [HttpPost("verify-signature")]
        public async Task<IActionResult> VerifyPayment([FromBody] PaymentVerificationModel request)
        {
            try
            {
                var currentUserIdClaim = User.Claims.FirstOrDefault(c => c.Type == ClaimTypes.Name);

                if (currentUserIdClaim == null || !Guid.TryParse(currentUserIdClaim.Value, out Guid currentUserId))
                {
                    return BadRequest(new { Message = "Invalid current user." });
                }
                var result = await _razorpayService.VerifyPaymentAsync(currentUserId, request);
                return Ok(result);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error verifying payment");
                return StatusCode(500, "Failed to verify payment");
            }
        }
        [HttpGet("plan-details/{companyId}")]
        [Authorize]
        public async Task<IActionResult> GetPaymentsByUserId(string companyId)
        {
            try
            {
                var payments = await _razorpayService.GetLastPaidPlanByUserIdAsync(companyId);
                if (payments == null)
                {
                    return BadRequest(new { Message = "No active paid plans found for the specified user." });
                }

                return Ok(payments);
            }
            catch (Exception ex)
            {
                return StatusCode(500, $"An error occurred while retrieving payments: {ex.Message}");
            }
        }

        //[HttpPost("wallet/add-amount")]
        //[Authorize]
        //public async Task<IActionResult> AddAmountToWallet([FromBody] EngagetoEntities.UserEntities.Models.OrderDto request)
        //{
        //    try
        //    {
        //        var currentUserIdClaim = User.Claims.FirstOrDefault(c => c.Type == ClaimTypes.Name);

        //        if (currentUserIdClaim == null || !Guid.TryParse(currentUserIdClaim.Value, out Guid currentUserId))
        //        {
        //            return BadRequest(new { Message = "Invalid current user." });
        //        }

        //        var orderId = await _razorpayService.CreateWalletOrderAsync(currentUserId, request.OrderAmount, request.Currency);
        //        return Ok(new WalletOrderResponse { OrderId = orderId, Message = "Order Created Successfully." });
        //    }
        //    catch (Exception ex)
        //    {
        //        return BadRequest(ex.Message);
        //        //_logger.LogError(ex, "Error creating wallet order");
        //        //return StatusCode(500, "Failed to create wallet order");
        //    }
        //}

        [HttpPost("FreeSubscriptionPlan")]
        [AllowAnonymous]
        public async Task<IActionResult> FreeSubscriptionPlan(FreeSubscriptionPlanDto freeSubscriptionPlan)
        {
            try
            {
                var currentUserIdClaim = User.Claims.FirstOrDefault(c => c.Type == ClaimTypes.Name);

                if (currentUserIdClaim == null || !Guid.TryParse(currentUserIdClaim.Value, out Guid currentUserId))
                {
                    return BadRequest(new { Message = "Invalid current user." });
                }
                var plan = await _planEntitiesService.GetPlanById(freeSubscriptionPlan.PlanId);
                if (plan == null)
                    throw new Exception("The plan is not exist");

                if (!plan.Status)
                    throw new Exception("The plan is not active");

                var walletAndSubscription = await _walletService.GetWalletAndSubscriptionPlanAsync(freeSubscriptionPlan.CompanyId);
                if (!string.IsNullOrEmpty(walletAndSubscription?.subscriptionPlan.PlanName))
                    throw new Exception("A free subscription is not available to the client.");

                Subscriptions subscriptions = new Subscriptions()
                {
                    CompanyId = freeSubscriptionPlan.CompanyId,
                    DurationType = freeSubscriptionPlan.PlanType.ToString(),
                    PlanId = freeSubscriptionPlan.PlanId,
                    PlanStartDate = DateTime.Now,
                    PlanEndDate = DateTime.Now.AddDays(7),
                    Status = "Paid",
                    UserId = currentUserId.ToString(),
                    CreatedAt = DateTime.Now,
                    CreatedBy = currentUserId.ToString()
                };
                var subscriptionResult = await _razorpayService.SaveSubscriptionAsync(subscriptions);
                if (subscriptionResult == null)
                    throw new Exception("The subscription could not be added because something went wrong.");

                var paymentWalletDetails = new PaymentWalletDetail()
                {
                    CompanyId = freeSubscriptionPlan.CompanyId,
                    UserId = currentUserId,
                    PlanId = plan.Id,
                    DurationType = freeSubscriptionPlan.PlanType.ToString(),
                    OrderId = null,
                    OrderAmount = 0,
                    Currency = "INR",
                    Status = "Paid",
                    CreatedBy = currentUserId,
                    CreatedAt = DateTime.Now,
                    PlanStartDate = DateTime.Now,
                    PlanEndDate = DateTime.Now.AddDays(7),
                    SubscriptionId = subscriptionResult.Id
                };
                await _razorpayService.SavePaymentWalletAsync(paymentWalletDetails);
                return Ok(new ApiResponse<string>
                {
                    Success = true,
                    Data = null,
                    Message = "Free subscription Added successfully."
                });
            }
            catch (Exception ex)
            {
                return StatusCode(500, new ApiResponse<string>()
                {
                    Success = false,
                    Message = ex.Message,
                    Data = null,
                    Errors = ex.StackTrace
                });
            }
        }

        [HttpPost("wallet/verify-payment")]
        [Authorize]
        public async Task<IActionResult> VerifyWalletPayment([FromBody] PaymentVerificationDto request)
        {
            try
            {
                var result = await _razorpayService.VerifyWalletPaymentAsync(request.RazorpayOrderId, request.RazorpayPaymentId, request.RazorpaySignature);
                if (result)
                {
                    var currentUserIdClaim = User.Claims.FirstOrDefault(c => c.Type == ClaimTypes.Name);
                    if (currentUserIdClaim == null || !Guid.TryParse(currentUserIdClaim.Value, out Guid currentUserId))
                    {
                        return BadRequest(new { Message = "Invalid current user." });
                    }
                    var payment = await _dbContext.PaymentWalletDetails
                .FirstOrDefaultAsync(p => p.OrderId == request.RazorpayOrderId && p.UserId == currentUserId);

                    if (payment == null)
                    {
                        return BadRequest(new { Message = "Payment not found." });
                    }
                    // Add amount to wallet
                    var (isSuccess, warningMessage) = await _razorpayService.AddAmountToWalletAsync(request.RazorpayOrderId, currentUserId);

                    if (isSuccess)
                    {
                        if (!string.IsNullOrEmpty(warningMessage))
                        {
                            return Ok(new { Message = "Wallet balance updated successfully.", Warning = warningMessage });
                        }
                        return Ok(new { Message = "Wallet balance updated successfully." });
                    }
                    else
                    {
                        return BadRequest(new { Message = "Failed to update wallet balance." });
                    }
                    //await _razorpayService.AddAmountToWalletAsync(request.RazorpayOrderId, currentUserId);

                    //return Ok(new { Message = "Wallet balance updated successfully." });                  
                }
                else
                {
                    return BadRequest(new { Message = "Invalid payment signature." });
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error verifying wallet payment");
                return StatusCode(500, new { Message = $"{ex.Message}" });
            }
        }
        [HttpGet]
        [Route("SubscriptionDeductions")]
        [Authorize]
        public async Task<IActionResult> GetSubscriptionDeductionDetails([FromQuery] DateTime? startDate, [FromQuery] DateTime? endDate)
        {
            try
            {
                var currentUserIdClaim = User.Claims.FirstOrDefault(c => c.Type == ClaimTypes.Name);

                if (currentUserIdClaim == null || !Guid.TryParse(currentUserIdClaim.Value, out Guid currentUserId))
                {
                    return BadRequest(new ApiResponse<string>
                    {
                        Success = false,
                        Message = "Invalid current user."
                    });
                }
                var companyDetails = await _dbContext.Ahex_CRM_Users.FirstOrDefaultAsync(x => x.Id == currentUserId);
                var query = from payment in _dbContext.PaymentWalletDetails
                            join subscription in _dbContext.Subscriptions
                            on payment.SubscriptionId equals subscription.Id
                            where payment.CompanyId == companyDetails.CompanyId
                            select new
                            {
                                Payment = payment,
                                Subscription = subscription
                            };

                if (startDate.HasValue)
                {
                    query = query.Where(p => p.Payment.UpdatedAt >= startDate.Value);
                }

                if (endDate.HasValue)
                {
                    query = query.Where(p => p.Payment.UpdatedAt <= endDate.Value);
                }

                var paymentWalletDetails = await query
                    .Select(p => new PaymentWalletDetailDto
                    {
                        Id = p.Payment.Id,
                        UpdatedAt = p.Payment.UpdatedAt,
                        IGSTAmount = p.Payment.IGSTAmount,
                        TotalAmount = p.Payment.TotalAmount,
                        Status = p.Payment.Status,
                        OrderAmount = p.Payment.OrderAmount,
                        Description = "Subscription Charges-renewal"
                    })
                    .ToListAsync();

                return Ok(new ApiResponse<List<PaymentWalletDetailDto>>
                {
                    Success = true,
                    Message = "Payment wallet details retrieved successfully",
                    Data = paymentWalletDetails
                });
            }
            catch (Exception ex)
            {
                return StatusCode(500, new ApiResponse<string>
                {
                    Success = false,
                    Message = ex.Message,
                    Errors = ex.StackTrace
                });
            }
        }



        [HttpGet]
        [Route("DownloadSubscriptionDeductionsInvoice/{id}")]
        [Authorize]
        public async Task<IActionResult> CreateSubscriptionDeductionsPdf(int id)
        {
            try
            {
                var currentUserIdClaim = User.Claims.FirstOrDefault(c => c.Type == ClaimTypes.Name);
                if (currentUserIdClaim == null || !Guid.TryParse(currentUserIdClaim.Value, out Guid currentUserId))
                {
                    throw new Exception("Invalid current user.");
                }

                var userDetails = await GetAccountDetails(currentUserId);
                Guid companyId = Guid.Empty;
                Guid.TryParse(userDetails.CompanyId, out companyId);
                var businessDetails = await GetCompanyByIdAsync(currentUserId, companyId);

                // Get a single subscription deduction
                var subscriptionDeduction = await _razorpayService.GetSubscriptionDeductionsByIdAsync(id) ?? new();
                var plan = await _planEntitiesService.GetPlanById(subscriptionDeduction.PlanId);
                if (subscriptionDeduction != null)
                {
                    var filePath = _templateFiles.Subscriptions;
                    MemoryStream stream = new MemoryStream();
                    string mimeType = CommonHelper.GetMimeFileType(filePath);

                    using (MemoryStream memoryStream = new MemoryStream())
                    {
                        try
                        {
                            PdfReader pdfReader = new PdfReader(filePath);
                            using (PdfStamper pdfStamper = new PdfStamper(pdfReader, memoryStream))
                            {
                                AcroFields pdfFormFields = pdfStamper.AcroFields;
                                // Billing details
                                pdfFormFields.SetField("company", businessDetails.CompanyLegalName ?? businessDetails.BusinessName);
                                pdfFormFields.SetField("name", userDetails.Name);
                                pdfFormFields.SetField("address", businessDetails.CompanyAddress);
                                pdfFormFields.SetField("email", businessDetails.BusinessEmail);
                                // Subscription deduction details
                                pdfFormFields.SetField("plan name", plan.PlanName);
                                pdfFormFields.SetField("payment date", $"{subscriptionDeduction.CreatedAt:dd MMM yyyy}");
                                pdfFormFields.SetField("plan start date", $"{subscriptionDeduction.PlanStartDate:dd MMM yyyy}");
                                pdfFormFields.SetField("plan end date", $"{subscriptionDeduction.PlanEndDate:dd MMM yyyy}");
                                pdfFormFields.SetField("AmountRow1", subscriptionDeduction.OrderAmount.ToString());
                                pdfFormFields.SetField("StatusRow1", subscriptionDeduction.Status?.ToString() ?? "Pending");
                                pdfFormFields.SetField("IGSTRow1", subscriptionDeduction.IGSTAmount?.ToString() ?? "0");
                                pdfFormFields.SetField("IGST Row1", "18%");
                                pdfFormFields.SetField("total amount", subscriptionDeduction.TotalAmount?.ToString() ?? "0");
                                pdfStamper.FormFlattening = true;
                            }
                            pdfReader.Close();
                            byte[] buffer = CommonHelper.CompressPdf(memoryStream.ToArray());

                            var invoiceDate = subscriptionDeduction.PlanStartDate;
                            var invoiceFileName = $"Invoice_{invoiceDate:MM/yy}.pdf";

                            var response = new FileResponse
                            {
                                FileName = invoiceFileName,
                                Amount = subscriptionDeduction.OrderAmount ?? 0,
                                FileContent = buffer
                            };

                            return Ok(new ApiResponse<FileResponse>
                            {
                                Success = true,
                                Message = "Stream data from invoice",
                                Data = response
                            });
                        }
                        catch (Exception ex)
                        {
                            throw;
                        }
                    }
                }
                else
                {
                    throw new Exception("Subscription deduction details are not found");
                }
            }
            catch (Exception ex)
            {
                return StatusCode(500, new ApiResponse<string?>
                {
                    Success = false,
                    Message = ex.Message,
                    Errors = ex.StackTrace
                });
            }
        }

        private async Task<Ahex_CRM_Users> GetAccountDetails(Guid currentUserId)
        {
            return await _dbContext.Ahex_CRM_Users.FirstOrDefaultAsync(x => x.Id == currentUserId) ?? throw new Exception("Not Found User");
        }
        private async Task<Ahex_CRM_BusinessDetails> GetCompanyByIdAsync(Guid currentUserId, Guid companyId)
        {
            try
            {
                return await _dbContext.Ahex_CRM_BusinessDetails.FindAsync(companyId);
            }
            catch (Exception)
            {
                return null;
            }
        }
    }
}

