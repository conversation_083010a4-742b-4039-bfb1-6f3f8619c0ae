﻿using EngagetoEntities.Enums;
using System.ComponentModel.DataAnnotations.Schema;


namespace EngagetoEntities.Entities
{
    [Table("AutomationCustomerResponseEntities")]
    public class AutomationCustomerResponseEntity : BaseEntity
    {
        public Guid Id { get; set; }
        public Guid? ReferenceId { get; set; }  // Generic foreign key
        public Guid? CompanyId { get; set; }
        public Guid? UserId { get; set; }
        public ReferenceTableType? ReferenceTableType { get; set; } //Type of the referenced table
        public Guid? VeriableNameEntityId { get; set; }
        public string? ResponseMessage { get; set; }

        public AutomationCustomerResponseEntity(Guid userId, bool IsUpdate = false)
        {
            CreatedBy = userId;
            UpdatedBy = userId;
            IsDeleted = false;
            if (!IsUpdate)
            {
                CreatedAt = DateTime.Now;
            }


        }
        public AutomationCustomerResponseEntity() { }

    }
}
