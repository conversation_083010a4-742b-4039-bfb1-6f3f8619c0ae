﻿using EngagetoBackGroundJobs.Interfaces;
using EngagetoContracts.TemplateContracts;
using EngagetoContracts.WebhookContracts.ReceivedNotification;
using EngagetoDapper.Data.Dtos;
using EngagetoDapper.Data.Interfaces.GenericInterfaces;
using EngagetoEntities.DdContext;
using EngagetoEntities.Dtos.MetaDto;
using EngagetoEntities.Entities;
using EngagetoEntities.Enums;
using EngagetoEntities.UserEntities.Models;
using EngagetoEntities.Utilities;
using Newtonsoft.Json;
using Newtonsoft.Json.Linq;
using System.Text;
using System.Text.RegularExpressions;

namespace EngagetoRepository.WebhookRepository.ReceivedMessageRepo
{
    internal class WAWebhookHelper : IWAWebhookHelper
    {
        private readonly IGenericRepository _genericRepository;
        private readonly IJobService _jobService;
        private readonly ITemplate _templateService;
        private readonly ApplicationDbContext _appDbContext;
        public WAWebhookHelper(IGenericRepository genericRepository,
             ITemplate templateService,
             IJobService jobService,
             ApplicationDbContext appDbContext)
        {
            _genericRepository = genericRepository;
            _templateService = templateService;
            _jobService = jobService;
            _appDbContext = appDbContext;
        }
        public async Task InboxSettingMessageAsync(string businessId, string phoneNumber,string countryCode)
        {
            try
           {
                var contactDetails = await _genericRepository.GetRecordByRequestFilter<Contacts>(new()
                    {
                        new()
                        {
                            Key = "Contact",
                            Value = phoneNumber,
                            Operator = "="
                        },
                        new()
                        {
                            Key = "CountryCode",
                            Value = countryCode,
                            Operator = "like"
                        },
                        new()
                        {
                            Key = "BusinessId",
                            Value = businessId,
                            Operator = "="
                        }
                    }, "Contacts");

                if (contactDetails?.Count == 0) return;

                var contact = contactDetails?.FirstOrDefault();

                if (contact?.ChatStatus == ChatStatus.New)
                {
                    var template = (await _genericRepository.GetByObjectAsync<EngagetoEntities.Entities.Template>(new() { { "BusinessId", businessId }, { "Enabled", true }, { "Feature", EngagetoEntities.Enums.Feature.WelcomeMessage } }, "Templates"))?.LastOrDefault();

                    if (template == null) return;

                    var inboxSettingVariables = await _genericRepository.GetByObjectAsync<InboxSettingsVariable>(new() { { "TemplateId", template.TemplateId } }, "InboxSettingsVariables");
                    await ProcessTemplateAsync(template, inboxSettingVariables.Select(x => x.FallBackValue ?? string.Empty).ToList() ?? new(), businessId, phoneNumber);

                }
                else if ((await IsOutOffOffice(businessId)) == true)
                {
                    var template = (await _genericRepository.GetByObjectAsync<EngagetoEntities.Entities.Template>(new() { { "BusinessId", businessId }, { "Enabled", true }, { "Feature", EngagetoEntities.Enums.Feature.OutofOfficeMessage } }, "Templates"))?.LastOrDefault();

                    if (template == null) return;
                    var inboxSettingVariables = await _genericRepository.GetByObjectAsync<InboxSettingsVariable>(new() { { "TemplateId", template.TemplateId } }, "InboxSettingsVariables");
                    await ProcessTemplateAsync(template, inboxSettingVariables.Select(x => x.FallBackValue ?? string.Empty).ToList() ?? new(), businessId, phoneNumber);
                }
                else
                {
                    var template = (await _genericRepository.GetByObjectAsync<EngagetoEntities.Entities.Template>(new() { { "BusinessId", businessId }, { "Enabled", true }, { "Feature", EngagetoEntities.Enums.Feature.WelcomeMessage } }, "Templates"))?.LastOrDefault();

                    if (template != null)
                    {
                        var inboxSettingVariables = await _genericRepository.GetByObjectAsync<InboxSettingsVariable>(new() { { "TemplateId", template.TemplateId } }, "InboxSettingsVariables");
                        await ProcessTemplateAsync(template, inboxSettingVariables.Select(x => x.FallBackValue ?? string.Empty).ToList() ?? new(), businessId, phoneNumber);
                    }
                    else
                    {
                        template = (await _genericRepository.GetByObjectAsync<EngagetoEntities.Entities.Template>(new() { { "BusinessId", businessId }, { "Enabled", true }, { "Feature", EngagetoEntities.Enums.Feature.DelayedResponseMessage } }, "Templates"))?.LastOrDefault();
                        if (template == null) return;
                        TimeSpan delay = template?.Delay ?? TimeSpan.Zero;

                        var inboxSettingVariables = await _genericRepository.GetByObjectAsync<InboxSettingsVariable>(new() { { "TemplateId", template.TemplateId } }, "InboxSettingsVariables");
                        int milliseconds = 0;
                        var dev = inboxSettingVariables.ToList();

                        var dateTimeOffset = DateTime.UtcNow.Add(delay);
                        var jobId = _jobService.Schedule(() => ProcessTemplateAsync(template, inboxSettingVariables.Select(x => x.FallBackValue ?? string.Empty).ToList() ?? new(), businessId, phoneNumber), dateTimeOffset);
                        contact.DelayResponseJobID = jobId;
                        _appDbContext.Update(contact);
                        _appDbContext.SaveChanges();

                    }
                }
            }catch(Exception ex)
            {
                Console.WriteLine(ex.Message);
            }
        }

        public async Task UpdateContactChatStatusAsync(Contacts contact)
        {
            try
            {
                await _genericRepository
                                    .SaveAsync<ChatStatusEntity>(new ChatStatusEntity(contact.ContactId, ChatStatus.open, contact.UserId));
                contact.ChatStatus = EngagetoEntities.Enums.ChatStatus.open;
                _appDbContext.Contacts.Update(contact);
                _appDbContext.SaveChanges();
            }
            catch (Exception ex) { }
        }

        #region Send template
        private async Task ProcessTemplateAsync(EngagetoEntities.Entities.Template template, List<string> values, string companyId, string phoneNumber)
        {
            string headerValue = string.Empty;
            if (string.IsNullOrEmpty(template.Header) && template.MediaType != EngagetoEntities.Enums.MediaType.NONE)
            {
                if (string.IsNullOrEmpty(template.Header))
                {
                    headerValue = template.MediaAwsUrl;
                }
                else
                {
                    if (StringHelper.GetVariableCount(template.Header ?? string.Empty) > 0)
                    {
                        headerValue = values.FirstOrDefault() ?? string.Empty;
                        values = values.Skip(1).ToList();
                        template.Header = ReplaceMatches(template.Header ?? string.Empty, Regex.Matches(template.Header ?? string.Empty, @"\{\{(\d+)\}\}"), new() { headerValue });
                        template.Body = ReplaceMatches(template.Body, Regex.Matches(template.Body ?? string.Empty, @"\{\{(\d+)\}\}"), values);
                    }
                }
            }
            var jsonObject = await _templateService.GetWhatsAppSendTemplatePayloadAsync(companyId, template.TemplateId, template.TemplateName, phoneNumber, headerValue, values);
            var result = await SendTempalteAsync(companyId, jsonObject);
            int status = 5;
            if (result.isSucess)
            {
                var response = result.response as SendMessageResponseDto;
                status = GetStatus(response.Messages);
                var saveResult = await SaveConversationAsync(template, response.Messages.FirstOrDefault()?.WhatsAppMessageId ?? string.Empty, companyId, phoneNumber, status);
            }
            else if (!result.isSucess)
            {
                var saveResult = await SaveConversationAsync(template, string.Empty, companyId, phoneNumber, status);
            }
        }
        #endregion
        #region out office 
        public async Task<bool> IsOutOffOffice(string businessId)
        {
            string DOW = DateTime.UtcNow.DayOfWeek.ToString().ToLower();

            var workingHours = (await _genericRepository.GetByObjectAsync<WorkingHours>(new Dictionary<string, object> { { "BusinessId", businessId.ToLowerInvariant() } }, "WorkingHours"))?.FirstOrDefault();
            TimeSpan? toTimepan = null;
            TimeSpan? fromTimeSpan = null;
            if (workingHours != null)
            {
                switch (DOW.ToLower())
                {
                    case "sunday":
                        if (workingHours.SundayTo.HasValue)
                            toTimepan = (TimeSpan)workingHours?.SundayTo;
                        if (workingHours.SundayFrom.HasValue)
                            fromTimeSpan = (TimeSpan)workingHours?.SundayFrom;
                        break;
                    case "monday":
                        if (workingHours.MondayTo.HasValue)
                            toTimepan = (TimeSpan)workingHours?.MondayTo;
                        if (workingHours.MondayFrom.HasValue)
                            fromTimeSpan = (TimeSpan)workingHours?.MondayFrom;
                        break;
                    case "tuesday":
                        if (workingHours.TuesdayTo.HasValue)
                            toTimepan = (TimeSpan)workingHours?.TuesdayTo;
                        if (workingHours.TuesdayFrom.HasValue)
                            fromTimeSpan = (TimeSpan)workingHours?.TuesdayFrom;
                        break;
                    case "wednesday":
                        if (workingHours.WednesdayTo.HasValue)
                            toTimepan = (TimeSpan)workingHours?.WednesdayTo;
                        if (workingHours.WednesdayFrom.HasValue)
                            fromTimeSpan = (TimeSpan)workingHours?.WednesdayFrom;
                        break;
                    case "thursday":
                        if (workingHours.ThursdayTo.HasValue)
                            toTimepan = (TimeSpan)workingHours?.ThursdayTo;
                        if (workingHours.ThursdayFrom.HasValue)
                            fromTimeSpan = (TimeSpan)workingHours?.ThursdayFrom;
                        break;
                    case "friday":
                        if (workingHours.FridayTo.HasValue)
                            toTimepan = (TimeSpan)workingHours?.FridayTo;
                        if (workingHours.FridayFrom.HasValue)
                            fromTimeSpan = (TimeSpan)workingHours?.FridayFrom;
                        break;
                    case "saturday":
                        if (workingHours.SaturdayTo.HasValue)
                            toTimepan = (TimeSpan)workingHours?.SaturdayTo;
                        if (workingHours.SaturdayFrom.HasValue)
                            fromTimeSpan = (TimeSpan)workingHours?.SaturdayFrom;
                        break;
                    default:
                        throw new ArgumentException("Invalid day of the week");
                }
            }
            if (workingHours?.TimeZone != null)
            {
                DateTime currentTimeUtc = DateTime.UtcNow;
                TimeSpan timeSpan = TimeZoneInfo.ConvertTimeFromUtc(currentTimeUtc, TimeZoneInfo.FindSystemTimeZoneById(workingHours.TimeZone)).TimeOfDay;

                if ((!(fromTimeSpan <= timeSpan && toTimepan >= timeSpan)) || (toTimepan == null && fromTimeSpan == null))
                {
                    return true;
                }
            }

            return false;
        }
        #endregion

        #region send template to clients
        private async Task<(object? response, bool isSucess)> SendTempalteAsync(string businessId, JObject jsonObject)
        {
            var metaDetails = (await _genericRepository.GetByObjectAsync<BusinessDetailsMeta>(new() { { "BusinessId", businessId } }, "BusinessDetails_Metas"))?.FirstOrDefault();

            if (metaDetails == null)
                return (null, false);

            var url = MetaApi.GetSendTemplateUrl(metaDetails.PhoneNumberID);
            using var client = new HttpClient();
            var content = new StringContent(JsonConvert.SerializeObject(jsonObject), Encoding.UTF8, "application/json");
            client.DefaultRequestHeaders.Authorization = new System.Net.Http.Headers.AuthenticationHeaderValue("Bearer", metaDetails.Token);

            var response = await client.PostAsync(url, content);
            var responseContent = await response.Content.ReadAsStringAsync();

            if (response.IsSuccessStatusCode)
            {
                var result = JsonConvert.DeserializeObject<SendMessageResponseDto>(responseContent);
                return (result, true);
            }
            else
            {
                var errorResponse = JsonConvert.DeserializeObject<CreateTempateErrorResponseDto>(responseContent);
                return (errorResponse, false);
            }
        }
        #endregion
        #region save conversation
        private async Task<EngagetoEntities.Entities.Conversations> SaveConversationAsync(EngagetoEntities.Entities.Template template, string? waId, string companyId, string contactNo, int status)
        {
            var phoneNumberButtons = template.Buttons?.FirstOrDefault(b => b.ButtonType == "PHONE_NUMBER");
            var urlButtons = template.Buttons?.Where(b => b.ButtonType == "URL").ToList();
            var quickReplyButtons = template.Buttons?.Where(b => b.ButtonType == "QUICK_REPLY").ToList();
            var contact = _appDbContext.Contacts.FirstOrDefault(i => i.BusinessId.ToString() == companyId && (i.CountryCode+i.Contact).Replace("+","") == contactNo.Replace("+",""));
            var conv = new EngagetoEntities.Entities.Conversations
            {
                Id = Guid.NewGuid(),
                TemplateMediaType = template.MediaType,
                TemplateMediaUrl = template.MediaAwsUrl,
                CreatedAt = DateTime.UtcNow,
                WhatsAppMessageId = waId ?? string.Empty,
                From = companyId?.ToLowerInvariant() ?? string.Empty,
                To = contactNo,
                BusinessId = Guid.TryParse(companyId, out var tempGuid) ? tempGuid : Guid.Empty,
                ContactId = contact?.ContactId ?? Guid.Empty,
                TemplateBody = template.Body,
                TemplateHeader = template.Header,
                TemplateFooter = template.Footer,
                CallButtonName = phoneNumberButtons?.ButtonName ?? "",
                PhoneNumber = phoneNumberButtons?.ButtonValue ?? "",
                UrlButtonNames = string.Join(",", urlButtons.Select(m => m.ButtonName)),
                RedirectUrls = string.Join(",", urlButtons.Select(x => x.ButtonValue)),
                QuickReplies = string.Join(",", quickReplyButtons.Select(m => m.ButtonValue)),
                Status = (ConvStatus)status,
                MessageType = MessageType.Template,
                ReferenceId = template.TemplateId.ToString()
            };
            var result = await _templateService.SaveConversationAsync(conv);
            return result;
        }
        private static string ReplaceMatches(string input, MatchCollection matches, List<string> values)
        {
            foreach (Match match in matches.Cast<Match>())
            {
                if (int.TryParse(match.Groups[1].Value, out int index) && index >= 1 && index <= values.Count)
                {
                    string pattern = $"{{{{{index}}}}}";
                    input = input.Replace(pattern, values[index - 1]);
                }
            }
            return StringHelper.FormateString(input);
        }
        private int GetStatus(List<Messages>? messages)
        {
            int status = (int)messages.FirstOrDefault().Status;
            switch (messages?.FirstOrDefault()?.Status ?? SendStatus.Rejected)
            { 
                case SendStatus.Accepted:
                    status = (int)ConvStatus.sent;
                    break;
                case SendStatus.Rejected:
                    status = (int)ConvStatus.failed;
                    break;
                default:
                    status = (int)ConvStatus.failed;
                    break;
            }
            return status;
        }

        #endregion
    }
}
