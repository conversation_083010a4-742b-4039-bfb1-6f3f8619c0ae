﻿using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations.Schema;
using System.ComponentModel.DataAnnotations;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace EngagetoEntities.Entities
{
    [Table("NotificationEntities")]
    public class NotificationEntities
    {
        [Key]
        [DatabaseGenerated(DatabaseGeneratedOption.None)]
        public int Id { get; set; }
        public string? Name { get; set; }
        public string? ParentNotificationId { get; set; }
    }
    public class NotificationDto
    {
        public string MainMenu { get; set; }
        public bool Status { get; set; }
        public List<SubMenuDto> SubMenus { get; set; }
    }

    public class SubMenuDto
    {
        public string Name { get; set; }
        public bool Status { get; set; }
    }
}
