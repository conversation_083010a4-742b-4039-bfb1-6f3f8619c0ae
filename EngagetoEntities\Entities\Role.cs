﻿using EngagetoEntities.UserEntities.Models;
using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace EngagetoEntities.Entities
{
    [Table("Role")]
    public class Role
    {
        [Key]
        [DatabaseGenerated(DatabaseGeneratedOption.Identity)]
        public int Sno { get; set; }

        public Guid Id { get; set; }

        [Required]
        [StringLength(100)]
        public string Name { get; set; }

        public int Level { get; set; }
        public string? CompanyId { get; set; }
        public ICollection<UserRole> UserRoles { get; set; }
    }
    public class RoleDto
    {

        /*[Required(ErrorMessage = "Id is required")]
        public Guid Id { get; set; }*/

        [Required(ErrorMessage = "Name is required")]
        public string Name { get; set; }
        public string CompanyId { get; set; }
    }
}
