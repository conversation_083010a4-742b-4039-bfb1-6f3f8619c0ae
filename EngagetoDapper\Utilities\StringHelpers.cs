﻿using System.ComponentModel.DataAnnotations.Schema;
using System.Reflection;
using System.Xml.Linq;

namespace EngagetoDapper.Utilities
{
    public class StringHelpers
    {
        public static string ConvertListToXml<T>(List<T> entities, string rootElementName = "Entities", string itemElementName = "Entity")
        {
            var xmlElements = new XElement(rootElementName,
                new List<XElement>(
                    entities.ConvertAll(entity =>
                    {
                        var properties = typeof(T).GetProperties();
                        var itemElements = new List<XElement>();
                        foreach (var prop in properties)
                        {
                            object value = prop.GetValue(entity);
                            if (prop.PropertyType.IsEnum)
                            {
                                value = (int)value;  // Convert enum to integer
                            }
                            itemElements.Add(new XElement(prop.Name, value));
                        }
                        return new XElement(itemElementName, itemElements);
                    })
                )
            );
            return xmlElements.ToString();
        }

        public static DateTime GetIndianDateTime()
        {
            TimeZoneInfo indiaZone = TimeZoneInfo.FindSystemTimeZoneById("India Standard Time");
            DateTime utcNow = DateTime.UtcNow;
            return TimeZoneInfo.ConvertTimeFromUtc(utcNow, indiaZone);
        }

        public static DateTime GetIndianDateTime(DateTime dateTime)
        {
            TimeZoneInfo indiaZone = TimeZoneInfo.FindSystemTimeZoneById("India Standard Time");
            return TimeZoneInfo.ConvertTimeFromUtc(dateTime, indiaZone);
        }

        public static List<string> GetPropertyNames<T>()
        {
            return typeof(T).GetProperties(BindingFlags.Public | BindingFlags.Instance)
                            .Select(prop => prop.Name)
                            .ToList();
        }
        public static string GetTableName<T>()
        {
            var tableAttribute = (TableAttribute)Attribute.GetCustomAttribute(typeof(T), typeof(TableAttribute));
            if (tableAttribute != null)
            {
                return tableAttribute.Name;
            }
            return typeof(T).Name;
        }

    }
}
