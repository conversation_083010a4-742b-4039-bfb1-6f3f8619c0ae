﻿using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.Xml.Serialization;

namespace EngagetoEntities.Entities
{

    public class AutoReplyVeriableEntity
    {
        [Key]
        public Guid Id { get; set; }
        public Guid AutoReplyCustomMessageId { get; set; }
        [ForeignKey("AutoReplyCustomMessageId")]
        public AutoReplyCustomMessageEntity AutoCustomMessage { get; set; }
        public int Index { get; set; } = 1;
        public string Veriable { get; set; }
        public string Value { get; set; }
        public string FallbackValue { get; set; }

    }
}
