﻿namespace EngagetoEntities.Enums
{
    public enum IntegrationEvent
    {
        None = 0,
        OneTimeLeadGen,         // Shortened from OneTimeCustomerMessageLeadGen
        OneTimeReceived,        // Shortened from OneTimeReceivedCustomerMessage  
        Received,               // Shortened from ReceivedCustomerMessage
        Sent,                   // Shortened from SentMessage
        Delivered,              // Shortened from DeliveredMessage
        Read,                   // Shortened from ReadMessage
        Template,               // Kept as Template (optional: TemplateMsg)
        AutoReply,
        LeadGen
    }
}
