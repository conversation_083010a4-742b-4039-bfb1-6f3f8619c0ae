﻿using EngagetoEntities.Entities;

namespace EngagetoContracts.UserContracts
{
    public interface IConversationAnalyticsService
    {
        public Task<List<BusinessDetailsMeta>> GetAllBusinessMetaAccountsAsync();
        public Task<bool> SaveConversationAnalyticsDetailsAsync(List<ConversationAnalyticsEntity> conversationAnalyticsDto);
        public Task<List<ConversationAnalyticsEntity>> GetAnalyticsDetailsByCompanyIdAsync(string companyId);
        public Task<List<ConversationAnalyticsEntity>> GetAnalyticsDetailsByStartAndEndDateAsync(string companyId, DateTime startDate, DateTime endDate);
        public Task<List<ConversationAnalyticsEntity>> GetConversationAnalyticsByIdAsync(Guid id);
    }
}
