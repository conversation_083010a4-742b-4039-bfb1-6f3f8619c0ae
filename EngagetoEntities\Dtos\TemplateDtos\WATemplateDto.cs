﻿using EngagetoEntities.Enums;
using Microsoft.AspNetCore.Http;
using Newtonsoft.Json;
using System.Text.Json.Serialization;

namespace EngagetoEntities.Dtos.TemplateDtos
{
    public class WATemplateData
    {
        [JsonPropertyName("data")]
        public List<WATemplateDto> Data { get; set; } = new();
        public WAPagingDto Paging { get; set; }
    }
    public class WATemplateDto
    {
        public string Id { get; set; }
        [JsonPropertyName("name")]
        public string Name { get; set; }
        [JsonPropertyName("parameter_format")]
        public string ParameterFormat { get; set; }
        public List<ComponentDto> Components { get; set; } = new();
        public string Language { get; set; }
        public WATemplateStatus Status { get; set; }
        public WATemplateCategory Category { get; set; }
    }
    public class WAPagingDto
    {
        public CursorDto? Cursors { get; set; }
        public string? Next { get; set; }
        public string? Previous { get; set; }
    }
    public class CursorDto
    {
        public string Before { get; set; }
        public string After { get; set; }
    }
    public class ComponentDto
    {
        public string Type { get; set; }
        public MediaType? Format { get; set; }
        public string? Text { get; set; }
        [JsonPropertyName("example")]
        public WAExampleDto? Example { get; set; }
        [JsonPropertyName("buttons")]
        public List<WAButtonDto>? Buttons { get; set; }

    }
    public class WAExampleDto
    {
        [JsonPropertyName("header_handle")]
        public List<string>? HeaderHandle { get; set; }
    }
    public class WAButtonDto
    {
        public WAButton Type { get; set; }
        public string? Text { get; set; }
        [JsonPropertyName("phone_number")]
        public string? PhoneNumber { get; set; }
        public string? Url { get; set; }
        [JsonPropertyName("example")]
        public List<string>? Example { get; set; }
    }

    public class GetTemplateData
    {
        [JsonProperty("name")]
        public string Name { get; set; }
        [JsonProperty("status")]
        public string Status { get; set; } = default!;
        [JsonProperty("category")]
        public string Category { get; set; } = default!;

        [JsonProperty("error")]
        public string Error { get; set; } = default!;
    }
    public class GetTemplateResponse
    {
        [JsonProperty("data")]
        public List<GetTemplateData> Data { get; set; } = default!;
        [JsonProperty("error")]
        public string Error { get; set; } = default!;
    }

}
