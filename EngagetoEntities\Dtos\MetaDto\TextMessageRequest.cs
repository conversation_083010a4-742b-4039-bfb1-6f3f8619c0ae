﻿using Newtonsoft.Json;

namespace EngagetoEntities.Dtos.MetaDto
{
    public class TextMessageRequest
    {
        [JsonProperty("messaging_product")]
        public string MessagingProduct { get; private set; } = "whatsapp";

        [JsonProperty("recipient_type")]
        public string RecipientType { get; private set; } = "individual";

        [JsonProperty("to")]
        public string To { get; set; }

        [JsonProperty("type")]
        public string Type { get; private set; } = "text";

        [JsonProperty("text")]
        public WhatsAppText Text { get; set; }
        [JsonProperty("context")]
        public MessageContext Context { get; set; }
    }

    public class WhatsAppText
    {
        [JsonProperty("preview_url")]
        public bool PreviewUrl { get; set; }

        [JsonProperty("body")]
        public string Body { get; set; }
    }
    public class MessageContext
    {
        [JsonProperty("message_id")]
        public string MessageId { get; set; }
    }
}
