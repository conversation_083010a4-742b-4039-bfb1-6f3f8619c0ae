﻿using EngagetoEntities.Dtos.MetaDto;

namespace EngagetoContracts.WebhookContracts.SentMessage
{
    public interface ISentMessage
    {
        /// <summary>
        /// this inteface is use for send message, mark message as read
        /// </summary>
        Task<object> SendMessageAsync(TextMessageRequest textMessage, Guid BusinessId);
        Task<object> SendMessageAsync(ImageMessageRequest Message, Guid BusinessId);
        Task<object> SendMessageAsync(VideoMessageRequest Message, Guid BusinessId);
        Task<object> SendMessageAsync(AudioMessageRequest Message, Guid BusinessId);
        Task<object> SendMessageAsync(DocumentMessageRequest Message, Guid BusinessId);
    }
}
