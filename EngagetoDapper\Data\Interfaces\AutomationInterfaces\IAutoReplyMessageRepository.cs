﻿using EngagetoEntities.Entities;

namespace EngagetoDapper.Data.Interfaces.AutomationInterfaces
{
    public interface IAutoReplyMessageRepository
    {
        public Task<T?> SaveAutoReplyMessageAsync<T>(AutoReplyCustomMessageEntity customMessageDto, Guid userId);
        public Task<IEnumerable<T>> GetAutoReplyMessageAsync<T>(Guid companyId, Guid? autoReplyAutomationId);
        public Task<T?> SaveAutoReplyVeriablesAsync<T>(string xml);
        
    }
}
