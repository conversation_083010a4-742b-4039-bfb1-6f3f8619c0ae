﻿using EngagetoContracts.Services;
using EngagetoEntities.Dtos.WhatsAppChargeDtos;

namespace EngagetoContracts.MetaContracts
{
    public interface IMetaCostInfoService: ITransientService
    {
        Task<bool> AddMetaMasterChargeAsync(MasterMetaPriceDtos masterMetaPriceDtos);
        Task<List<MasterMetaPriceDtos>> GetAllMetaMasterChargeAsync();
        Task<MasterMetaPriceDtos> GetMetaMasterChargeByIdAsync(Guid id);
        Task<bool> UpdateMetaMasterChargeAsync(Guid Id, MasterMetaPriceDtos masterMetaPriceDtos);
        Task<bool> DeleteMetaMasterChargeAsync(Guid Id);
        Task<bool> AddMetaCustomChargeAsync(CustomMetapPriceDtos customMetapPriceDtos, string businessId);
        Task<List<CustomMetapPriceDtos>> GetAllMetaCustomChargeAsync();
        Task<CustomMetapPriceDtos> GetMetaCustomChargeByIdAsync(Guid id);
        Task<bool> UpdateMetaCustomChargeAsync(Guid Id, CustomMetapPriceDtos customMetaPriceDtos);
        Task<bool> DeleteMetaCustomChargeAsync(Guid Id);
        Task<decimal> GetCostAsync(string businessId, string phoneNumber, string categoryType);
        Task<decimal> GetMetaCostAsync(string businessId, string countryId, string category);
    }
}
