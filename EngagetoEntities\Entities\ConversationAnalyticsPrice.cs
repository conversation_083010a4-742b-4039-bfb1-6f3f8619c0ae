﻿using System.ComponentModel.DataAnnotations.Schema;

namespace EngagetoEntities.Entities
{
    [Table("ConversationAnalyticsPriceEntities")]
    public class ConversationAnalyticsPriceEntity
    {

        public Guid Id { get; set; }
        public string? CompanyId { get; set; }
        public string ConversationCategory { get; set; }
        public decimal Cost { get; set; }
        public int? Year { get; set; }
        public bool IsActive { get; set; }
        public int? PlanId { get; set; }
        public string? PlanType { get; set; }
    }
}
