﻿using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace EngagetoEntities.Entities
{
    [Table("WorkflowResponseHistoryEntities")]
    public class WorkflowResponseHistoryEntity : BaseEntity
    {
        [Key]
        public Guid Id { get; set; } // Primary Key
        public Guid CompanyId { get; set; }
        public Guid? ContactId { get; set; }
        public string WhatsappMessageId { get; set; }
        public string WorkflowName { get; set; }
        public Guid WorkflowId { get; set; }
        public int? Step { get; set; }
        public string? Response { get; set; }
        public long? WorkflowStartId { get; set; }
    }
}
