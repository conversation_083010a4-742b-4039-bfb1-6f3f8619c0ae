﻿using System;
using System.Collections.Generic;

namespace EngagetoDatabase.ContactsDatabase.Models
{
    public partial class Role
    {
        public Role()
        {
            UserRoles = new HashSet<UserRole>();
        }

        public Guid Id { get; set; }
        public int Sno { get; set; }
        public string Name { get; set; } = null!;
        public int Level { get; set; }

        public virtual ICollection<UserRole> UserRoles { get; set; }
    }
}
