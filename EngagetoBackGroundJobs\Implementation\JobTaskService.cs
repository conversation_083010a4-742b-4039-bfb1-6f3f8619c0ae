﻿using EngagetoBackGroundJobs.Interfaces;
using EngagetoDapper.Utilities;
using Hangfire.Console;
using Hangfire.Server;
using Humanizer;
using Microsoft.Extensions.Logging;


namespace EngagetoBackGroundJobs.Implementation
{
    public class JobTaskService : IJobTaskService
    {
        private readonly ILogger<JobTaskService> _logger;
        private readonly EngagetoContracts.UserContracts.IConversationAnalyticsService _conversationAnalyticsService;
        private readonly IConversationAnalythicsService _conversationAnalythicsService;
        private readonly EngagetoDapper.Data.Interfaces.IEmailInterfaces.IEmailService _mailService;
        private readonly ICleanUnusedDataService _cleanUnusedDataService;
        private readonly INotificationService _notificationService;

        public JobTaskService(ILogger<JobTaskService> logger,
            EngagetoContracts.UserContracts.IConversationAnalyticsService analyticsService,
            IConversationAnalythicsService conversationAnalythicsService,
            EngagetoDapper.Data.Interfaces.IEmailInterfaces.IEmailService emailService,
            ICleanUnusedDataService cleanUnusedDataService,
            INotificationService notificationService)
        {
            _logger = logger;
            _conversationAnalyticsService = analyticsService;
            _conversationAnalythicsService = conversationAnalythicsService;
            _mailService = emailService;
            _cleanUnusedDataService = cleanUnusedDataService;
            _notificationService = notificationService;
        }

        public async Task GetConversationAnalyticsCost(PerformContext? context)
        {
            try
            {
                var indianTimeZone = TimeZoneInfo.FindSystemTimeZoneById("India Standard Time");
                var nowDateTime = DateTime.UtcNow;

                // Ensure hour does not go below 0

                var startDate = new DateTime(nowDateTime.Year, nowDateTime.Month, nowDateTime.Day, nowDateTime.Hour, 0, 0, DateTimeKind.Utc).AddDays(-1);
                var endDate = startDate.AddDays(1);

                var startDateUnixtime = new DateTimeOffset(startDate, TimeSpan.Zero).ToUnixTimeSeconds();
                var endDateUnixtime = new DateTimeOffset(endDate, TimeSpan.Zero).ToUnixTimeSeconds();

                var whatsAppAccountDetails = await _conversationAnalyticsService.GetAllBusinessMetaAccountsAsync();
                foreach (var account in whatsAppAccountDetails)
                {
                    var result = await _conversationAnalythicsService
                        .ProcessMetaConversationAnalyticsRequestAsync(context, account, startDate, endDate, startDateUnixtime, endDateUnixtime);
                }
            }
            catch (Exception ex)
            {
                await SendErrorEmailAsync(context.JobId, DateTime.Now, DateTime.Now.AddHours(1), ex);
                _logger.LogError($"Exception getting to pulling the GetConversationAnalyticsCost: {ex.Message}, {ex.StackTrace} JOBID : {context.JobId}");
                HangfireConsoleWrite(context, $"Exception getting to pulling the GetConversationAnalyticsCost: {ex.Message}, {ex.StackTrace} JOBID : {context.JobId}", ConsoleTextColor.Red);
            }
        }
        public async Task CleanUnusedDataAsync(PerformContext? context)
        {
            try
            {
                // Table = ConversationAnalyticsJobRequest, WebhookEvents
                var startTime = DateTime.UtcNow;
                HangfireConsoleWrite(context, $"Start cleaning unused data: start time: {startTime} JOB ID: {context.JobId}", ConsoleTextColor.Green);
                await _cleanUnusedDataService.CleanUnusedDataAsync();
                HangfireConsoleWrite(context, $"Cleaning up unused data is complete: start time: {DateTime.UtcNow} JOB ID: {context.JobId}", ConsoleTextColor.Green);
            }
            catch (Exception ex)
            {
                _logger.LogError($"Exception getting to cleaning the UnusedData: {ex.Message}, {ex.StackTrace} JOBID : {context.JobId}");
                HangfireConsoleWrite(context, $"Exception getting to cleaning the UnusedData: {ex.Message}, {ex.StackTrace} JOBID : {context.JobId}", ConsoleTextColor.Red);
            }
        }
        public async Task SendNotificationAsync(PerformContext? context)
        {
            await _notificationService.SendSubscriptionEmailNotificationAsync(context);
        }
        public async Task SendAnalyticsReportNotificationAsync(PerformContext? context)
        {
            await _notificationService.SendAnalyticsReportAsync(context);
        }

        #region Helper
        public static void HangfireConsoleWrite(PerformContext console, string text, ConsoleTextColor consoleTextColor = null)
        {
            if (consoleTextColor != null)
            {
                console.SetTextColor(consoleTextColor);
            }

            console.WriteLine(text);

            if (consoleTextColor != null)
            {
                console.ResetTextColor();
            }
        }
        public async Task SendErrorEmailAsync(string? jobId, DateTime? startDate, DateTime? endDate, Exception? ex)
        {
            await _mailService.SendEmailAsync(new EngagetoEntities.ServiceModels.EmailMessage()
            {
                To = new List<string>() { "<EMAIL>", "<EMAIL>", "<EMAIL>" },
                Subject = $"Error: Meta Conversation Cost Deduction JOB Issue.",
                Body = $@"
                        <html>
                        <body>
                            <p>Dear Team,</p>
                            <p>An issue has been detected while processing the cost deduction.</strong>.</p>
                            <p>Details:</p>
                            <ul>
                                <li>Job ID: <strong>{jobId ?? "Not specified"}</strong></li>
                                <li>Error Message: {ex?.Message}</li>
                                <li>Stack Trace: {ex?.StackTrace}</li>
                                <li>Start Time: {StringHelpers.GetIndianDateTime(startDate ?? DateTime.MinValue).ToString("yyyy-MM-dd HH:mm:ss") ?? "Not available"}</li>
                                <li>End Time: {StringHelpers.GetIndianDateTime(endDate ?? DateTime.MinValue).ToString("yyyy-MM-dd HH:mm:ss") ?? "Not available"}</li>
                            </ul>
                            <p>Please take appropriate action to resolve this issue promptly.</p>
                            <p>Best regards,<br/>Your Hangfire JOB</p>
                        </body>
                        </html>"
            });
        }

        
        #endregion

    }
}
