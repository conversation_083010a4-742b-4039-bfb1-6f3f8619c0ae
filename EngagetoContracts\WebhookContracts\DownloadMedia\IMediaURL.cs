﻿using EngagetoContracts.Services;
using Microsoft.AspNetCore.Http;

namespace EngagetoContracts.WebhookContracts.DownloadMedia
{
    public interface IMediaURL
    {
        Task<string> GetByMediaId(string meidaId, string PhoneNumberId);
        Task<string> GetByFile(IFormFile media);
        Task<string> UploadFileAsync(byte[] fileBytes, string fileName);
        Task<(bool IsValid, string ErrorMessage)> ValidateMediaFile(string mediaFileUrl);
    }
}
