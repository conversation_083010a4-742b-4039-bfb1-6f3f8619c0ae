﻿using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using EngagetoEntities.Entities;

namespace EngagetoEntities.UserEntities.Models
{
    public class WalletEntity
    {
        [Key]
        [DatabaseGenerated(DatabaseGeneratedOption.Identity)]
        public int Id { get; set; }

        public Guid? UserId { get; set; }

        public string? ComapanyId { get; set; }

        public decimal? WalletBalance { get; set; }

        public Guid? CreatedBy { get; set; }

        public Guid? UpdatedBy { get; set; }

        //[Required]
        public DateTime? CreatedAt { get; set; }

        //[Required]
        public DateTime? UpdatedAt { get; set; }

        public ICollection<PaymentWalletDetail> PaymentWalletDetails { get; set; }
    }
    public class PaymentVerificationDto
    {
        public string? RazorpayOrderId { get; set; }
        public string? RazorpayPaymentId { get; set; }
        public string? RazorpaySignature { get; set; }
        //public decimal? OrderAmount { get; set; }
    }
    //public class OrderDto
    //{
    //    public decimal OrderAmount { get; set; }
    //    public string? Currency { get; set; }
    //    public string PlanType { get; set; }
}
public class WalletOrderResponse
{
    public string OrderId { get; set; }
    public string Message { get; set; }
}
public class WalletSettings
{
    public const decimal MinimumOrderAmount = 50.00m;
}

