﻿using Microsoft.AspNetCore.Http;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using EngagetoEntities.UserEntities.Dtos;
using EngagetoEntities.Dtos.BusinessDetailsDtos;
using EngagetoEntities.Entities;

namespace EngagetoContracts.UserContracts
{
    public interface ICompanyDetailsService
    {
        // Task<Guid> AddCompanyAsync(Guid currentUserId, CompanyRequest companyRequest, IFormFile companylogo);
        Task<bool> UpdateCompanyFieldsAsync(Guid currentUserId, Guid companyId, Ahex_CRM_BusinessDetailsDto updatedCompany);
        Task<bool> DeleteCompanyAsync(Guid currentUserId, Guid companyId);
        Task<Ahex_CRM_BusinessDetails> GetCompanyByIdAsync(Guid companyId);
        Task<IEnumerable<Ahex_CRM_BusinessDetails>> GetAllCompaniesAsync(Guid currentUserId, string searchQuery, bool includeInactive, string sortBy, bool isSortAscending);
        Task<string> UploadLogoAsync(Guid clientId, IFormFile file);
        Task<Ahex_CRM_BusinessDetails> UpdateBillingDetailsAsyc(BillingDetailsDto detailsDto);
        Task<string?> GetCompanyCountryCode(Guid companyId);
        Task<List<TenantDtos>> GetTenantsAsync();
    }
}
