﻿using EngagetoEntities.Dtos.WebhookDtos;

namespace EngagetoContracts.WebhookContracts.ReceivedNotification
{
    public interface IWAWebhookMessageServiceAsync
    {
        Task ProcessWAWebhookMessageAsync(WAWebhookDto message,CancellationToken token);
        Task HandleReceviedMessageAsync(WAWebhookDto webhookDto, CancellationToken token);
        Task ProcessSentMessageAsync(WAWebhookDto message, CancellationToken token);
        Task HandleReceivedTemplateStatus(WAWebhookDto message, string field, CancellationToken token);
    }
}
