﻿using EngagetoBackGroundJobs.Interfaces;
using EngagetoContracts.CampaignContracts;
using EngagetoEntities.DdContext;
using EngagetoEntities.Dtos.CampaignDto;
using EngagetoEntities.Dtos.MetaDto;
using EngagetoEntities.Entities;
using EngagetoEntities.Enums;
using EngagetoEntities.Utilities;
using EngagetoEntities.Validations;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Mvc.ModelBinding;
using Microsoft.EntityFrameworkCore;
using Newtonsoft.Json;
using System.ComponentModel.DataAnnotations;
using System.Globalization;
using System.Reflection;
using System.Runtime.InteropServices;
using System.Security.Claims;

namespace Engageto.Controllers.CampaignControllers
{
    [Route("api/[controller]")]
    [ApiController]
    public class CampaignController : ControllerBase
    {
        private readonly ICampaign _CampaignService;
        private readonly FileValidator _fileValidator;
        public IHttpClientFactory _httpClientFactory { get; set; }
        private ApplicationDbContext _CompaignDbContext;
        private List<Campaign> _campaigns = new List<Campaign>();
        private bool isValidFile;
        private readonly EngagetoDapper.Data.Interfaces.IUserInterfaces.IUserServices.IUserService _userService;
        private readonly JsonSerializer _serializer = new JsonSerializer();
        private IConfiguration _Configuration { get; set; }
        private IJobService _jobService { get; set; }
        public static IHttpContextAccessor _httpContextAccessor;
        private readonly EngagetoDapper.Data.Interfaces.CampaignInterfaces.ICampaignServices.IConversationsService _conversationsService;
        public CampaignController(ICampaign CampaignService, EngagetoDapper.Data.Interfaces.IUserInterfaces.IUserServices.IUserService userService,
                                  IHttpContextAccessor HttpContextAccessor, IJobService IJobService,
                                  IHttpClientFactory IHttpClientFactory, IConfiguration Configuration,
                                  ApplicationDbContext dbContext,
                                  FileValidator fileValidator,
                                  EngagetoDapper.Data.Interfaces.CampaignInterfaces.ICampaignServices.IConversationsService conversationsService)
        {
            _fileValidator = fileValidator;
            _CampaignService = CampaignService;
            _userService = userService;
            _CompaignDbContext = dbContext;
            _Configuration = Configuration;
            _httpClientFactory = IHttpClientFactory;
            _jobService = IJobService;
            _httpClientFactory = IHttpClientFactory;
            _conversationsService = conversationsService;

        }

        [HttpGet("check-campaign-title/{BusinessId}/{CampaignTitle}")]
        [Authorize]
        public async Task<IActionResult> CheckTemplateNames(string BusinessId, string CampaignTitle)
        {
            try
            {
                var currentUserIdClaim = User.Claims.FirstOrDefault(c => c.Type == ClaimTypes.Name);

                if (currentUserIdClaim == null || !Guid.TryParse(currentUserIdClaim.Value, out Guid currentUserId))
                {
                    return BadRequest(new { Message = "Invalid current user." });
                }
                var result = await _userService.IsValidateMetaAccount(BusinessId.ToString(), currentUserId);

                if (string.IsNullOrEmpty(CampaignTitle))
                {
                    return BadRequest(new { Message = "Campaign Title is required" });
                }
                if (CampaignTitle.Length > 30)
                {
                    return BadRequest(new { Message = "Maximum 30 characters allowed in Campaign Title." });
                }
                // Check if TemplateName already exists and is not in draft status
                var existingTemplate = await _CompaignDbContext.Campaigns.FirstOrDefaultAsync(t => t.CampaignTitle == CampaignTitle && t.BusinessId == BusinessId);

                if (existingTemplate != null)
                {
                    return BadRequest(new { Message = $"Campaign Title '{CampaignTitle}' exists and it must be unique. " });
                }

                return Ok(new { Message = $"Campaign Title '{CampaignTitle}' is valid" });
            }
            catch (Exception ex)
            {
                return StatusCode(500, new { Message = ex.Message });
            }
        }




        [HttpPost("CreateCampaign")]
        [Authorize]
        public async Task<IActionResult> CampaignAsync([FromBody] Campaigns model)
        {
            try
            {
                var currentUserIdClaim = User.Claims.FirstOrDefault(c => c.Type == ClaimTypes.Name);
                if (currentUserIdClaim == null || !Guid.TryParse(currentUserIdClaim.Value, out Guid currentUserId))
                {
                    return BadRequest(new { Message = "Invalid current user." });
                }
                // Handle media meta validation in all case.
                var metaAccount = await _userService.IsValidateMetaAccount(model.BusinessId.ToString(), currentUserId);
                await _conversationsService.VerifySubscriptionAndExpectedWalletBalanceAysc(model.BusinessId, model.Audiance.Count);

                if (model.Audiance == null || model.Audiance.Count == 0)
                {
                    return BadRequest(new { Message = "Audiance is required" });
                }
                if (string.IsNullOrEmpty(model.CampaignTitle))
                {
                    return BadRequest(new { Message = "CampaignTitle is required" });
                }
                if (model.CampaignTitle.Length > 30)
                {
                    return BadRequest(new { Message = "Maximum 30 characters allowed in Campaign Title." });
                }
                var dbContext = _CompaignDbContext.Campaigns;
                var existingCampaignTitle = dbContext.Select(t => t.CampaignTitle);
                if (existingCampaignTitle.Contains(model.CampaignTitle))
                {
                    return BadRequest(new { Message = $"Campaign Title '{model.CampaignTitle}' exists and it must be unique." });
                }
                if (!string.IsNullOrEmpty(model.MediaFile))
                {
                    isValidFile = await _fileValidator.ValidateFileAsync(_httpClientFactory.CreateClient(), model.MediaFile);
                }

                if (model.ScheduledDateTime.HasValue)
                {
                    TimeSpan delay = (TimeSpan)(model.ScheduledDateTime - DateTime.UtcNow);
                    if (delay.TotalMilliseconds < 0)
                    {
                        throw new ArgumentException("Scheduled time should be in the future.");
                    }
                    DateTimeOffset dateTimeOffset = StringHelper.ConvertToDateTimeOffset(model.ScheduledDateTime);
                    var jobId = _jobService.Schedule(() => _CampaignService.CreateCampaignAsync(model), dateTimeOffset);
                    var SendcampaignDto = await _CampaignService.CreateCampaignDtoAsync(model, jobId);
                    return Ok(new
                    {
                        Message = "Campaign Scheduled Successfully",
                        ScheduleJobId = SendcampaignDto.ScheduleJobId,
                        CampaignId = SendcampaignDto.CampaignId,
                        delay
                    });
                }
                else
                {
                    var result = await _CampaignService.CreateCampaignAsync(model);
                    if (result.IsSuccessStatusCode)
                    {
                        return Ok(new { Message = "Campaign sent successfully" });
                    }
                    else
                    {
                        var errorContent = await result.Content.ReadAsStringAsync();
                        SendTemplateErrorResponseDto errorResponse;
                        using (var reader = new StringReader(errorContent))
                        using (var jsonReader = new JsonTextReader(reader))
                        {
                            errorResponse = _serializer.Deserialize<SendTemplateErrorResponseDto>(jsonReader);
                        }
                        var SendTemplateErrorResponse = new SendTemplateErrorResponseDto
                        {
                            SendTemplateErrorMessage = new ErrorDetails
                            {
                                Message = errorResponse.SendTemplateErrorMessage.Message,
                                ErrorData = errorResponse.SendTemplateErrorMessage.ErrorData,
                            }
                        };
                        return BadRequest(new
                        {
                            Message = SendTemplateErrorResponse.SendTemplateErrorMessage.Message,
                            // ErrorData = SendTemplateErrorResponse.SendTemplateErrorMessage.ErrorData
                        });
                    }
                }
            }
            catch (ArgumentException ex)
            {
                return BadRequest(new { Message = ex.Message });
            }
            catch (Exception ex)
            {
                return BadRequest(new { Message = ex.Message });
            }
        }

        [HttpPut("EditCampaign")]
        [Authorize]
        public async Task<IActionResult> EditScheduledCampaign([FromBody] Campaigns newModel)
        {
            try
            {
                var currentUserIdClaim = User.Claims.FirstOrDefault(c => c.Type == ClaimTypes.Name);
                if (string.IsNullOrEmpty(newModel.BusinessId))
                {
                    return BadRequest(new { Message = "BusinessId is required" });
                }
                if (currentUserIdClaim == null || !Guid.TryParse(currentUserIdClaim.Value, out Guid currentUserId))
                {
                    return BadRequest(new { Message = "Invalid current user." });
                }
                // Handle media meta validation in all case.
                var metaAccount = await _userService.IsValidateMetaAccount(newModel.BusinessId.ToString(), currentUserId);
                var existingCampaign = await _CompaignDbContext.Campaigns.FirstOrDefaultAsync(c => c.CampaignId == newModel.CampaignId);

                if (existingCampaign == null)
                {
                    return NotFound(new { Message = "No campaign found for the given campaignId." });
                }
                if (newModel.Audiance == null || newModel.Audiance.Count == 0)
                {
                    return BadRequest(new { Message = "Audiance is required" });
                }

                if (string.IsNullOrEmpty(newModel.CampaignTitle))
                {
                    return BadRequest(new { Message = "CampaignTitle is required" });
                }
                if (!string.IsNullOrEmpty(newModel.MediaFile))
                {
                    isValidFile = await _fileValidator.ValidateFileAsync(_httpClientFactory.CreateClient(), newModel.MediaFile);
                }

                if (!string.IsNullOrEmpty(existingCampaign.ScheduleJobId))
                {
                    bool jobDeleted = _jobService.Delete(existingCampaign.ScheduleJobId);
                    if (!jobDeleted)
                    {
                        throw new InvalidOperationException("Failed to delete the Hangfire job.");
                    }
                }
                if (newModel.ScheduledDateTime.HasValue)
                {
                    TimeSpan delay = (TimeSpan)(newModel.ScheduledDateTime - DateTime.UtcNow);
                    if (delay.TotalMilliseconds < 0)
                    {
                        throw new ArgumentException("Scheduled time should be in the future.");
                    }
                    DateTimeOffset dateTimeOffset = StringHelper.ConvertToDateTimeOffset(newModel.ScheduledDateTime);
                    var jobId = _jobService.Schedule(() => _CampaignService.CreateCampaignAsync(newModel), dateTimeOffset);
                    existingCampaign.ScheduleJobId = jobId;
                    await _CompaignDbContext.SaveChangesAsync();
                    var sendCampaignDto = await _CampaignService.CreateCampaignDtoAsync(newModel, jobId);
                    return Ok(new
                    {
                        Message = "Campaign Scheduled Successfully",
                        ScheduleJobId = jobId,
                        CampaignId = existingCampaign.CampaignId,
                        delay
                    });
                }
                return Ok(new { Success = true, Message = "Campaign updated successfully." });
            }
            catch (ArgumentException ex)
            {
                return BadRequest(new { Message = ex.Message });
            }
            catch (Exception ex)
            {
                // Handle any other exceptions
                return StatusCode(500, new { Message = "An error occurred while editing the campaign.", Details = ex.Message });
            }
        }

        [HttpGet("GetById")]
        [Authorize]
        public async Task<IActionResult> GetCampaignBySchedule([FromQuery, BindRequired] string BusinessId, [FromQuery, BindRequired] Guid UserId, [FromQuery, BindRequired] Guid CampaignId)
        {
            try
            {
                // Replace this with your actual method to fetch campaigns by schedule
                var campaign = await _CompaignDbContext.Campaigns.FirstOrDefaultAsync(c => c.CampaignId == CampaignId);

                if (campaign == null)
                {
                    return NotFound(new { Message = "No campaign found for the given schedule." });
                }
                var Business = await _CompaignDbContext.BusinessDetailsMetas.FirstOrDefaultAsync(t => t.BusinessId == BusinessId);
                var user = await _CompaignDbContext.Users.FirstOrDefaultAsync(u => u.Id == campaign.UserId);
                if (Business == null || user == null)
                {
                    return BadRequest(new { Message = "Business or User with the specified ID does not exist." });
                }
                if (string.IsNullOrEmpty(Business?.BusinessId) || user?.Id == Guid.Empty)
                {
                    return BadRequest(new { Message = "BusinessId or UserId is invalid." });
                }
                if (!string.Equals(user?.CompanyId, BusinessId, StringComparison.OrdinalIgnoreCase))
                {
                    return BadRequest(new { Message = "User does not have access to the specified business." });
                }
                Campaigns campaignDto = new Campaigns
                {
                    CampaignId = campaign.CampaignId,
                    Audiance = campaign.Audiance.ToLower().Split(',').ToList(),
                    BusinessId = campaign.BusinessId,
                    UserId = campaign.UserId,
                    CampaignTitle = campaign.CampaignTitle,
                    SendTextType = campaign.SendTextType,
                    MediaUrl = campaign.MediaUrl,
                    TemplateId = campaign.TemplateId,
                    HeaderVariableValue = new Variable { value = campaign.HeaderValue },
                    BodyVariableValues = string.IsNullOrEmpty(campaign.BodyValues) ? new List<Variable>() : campaign.BodyValues.Split(',').Select(value => new Variable { value = value }).ToList(),
                    RedirectUrlVariableValues = campaign.RedirectUrlsValue.Split(',').ToArray(),
                    DateSetLive = campaign.DateSetLive,
                    Createdby = user.Name,
                    State = campaign.State,
                };
                return Ok(campaignDto);
            }
            catch (Exception ex)
            {
                return StatusCode(500, new { Message = $"Internal server error: {ex.Message}" });
            }
        }

        [HttpDelete("DeleteScheduledCampaign")]
        [Authorize]
        public async Task<IActionResult> DeleteScheduledCampaigns(string BusinessId, Guid UserId, Guid CampaignId)
        {
            try
            {
                var campaign = await _CompaignDbContext.Campaigns.FirstOrDefaultAsync(c => c.CampaignId == CampaignId);
                if (campaign == null)
                {
                    return NotFound(new { Message = "No campaign found for the given campaignId." });
                }
                var Business = await _CompaignDbContext.BusinessDetailsMetas.FirstOrDefaultAsync(t => t.BusinessId == BusinessId);
                var user = await _CompaignDbContext.Users.FirstOrDefaultAsync(u => u.Id == campaign.UserId);
                if (Business == null || user == null)
                {
                    return BadRequest(new { Message = "Business or User with the specified ID does not exist." });
                }
                if (string.IsNullOrEmpty(Business?.BusinessId) || user?.Id == Guid.Empty)
                {
                    return BadRequest(new { Message = "BusinessId or UserId is invalid." });
                }
                if (!string.Equals(user?.CompanyId, BusinessId, StringComparison.OrdinalIgnoreCase))
                {
                    return BadRequest(new { Message = "User does not have access to the specified business." });
                }
                if (!string.IsNullOrEmpty(campaign.ScheduleJobId))
                {
                    bool jobDeleted = _jobService.Delete(campaign.ScheduleJobId);
                    if (!jobDeleted)
                    {
                        throw new InvalidOperationException("Failed to delete the Hangfire job.");
                    }
                }
                _CompaignDbContext.Campaigns.Remove(campaign);
                await _CompaignDbContext.SaveChangesAsync();

                return Ok(new { Success = true, Message = "Scheduled campaign deleted successfully." });
            }
            catch (InvalidOperationException ex)
            {
                return BadRequest(new { Success = false, Message = ex.Message });
            }
            catch (Exception ex)
            {
                // Log exception details here if necessary
                return StatusCode(500, new { Success = false, Message = "An error occurred while deleting the scheduled campaign." });
            }
        }


        [HttpGet("CampaignMessage_Counts")]
        [Authorize]
        public async Task<ActionResult<IEnumerable<CampaignMessageCountsDto>>> GetCampaignMessageCounts([FromQuery, BindRequired] string BusinessId, [FromQuery, BindRequired] Guid UserId)
        {
            var Business = await _CompaignDbContext.BusinessDetailsMetas.FirstOrDefaultAsync(t => t.BusinessId == BusinessId);
            var user = await _CompaignDbContext.Users.FirstOrDefaultAsync(u => u.Id == UserId);
            if (Business == null || user == null)
            {
                return BadRequest(new { Message = "Business or User with the specified ID does not exist." });
            }

            if (string.IsNullOrEmpty(Business?.BusinessId) || user?.Id == Guid.Empty)
            {
                return BadRequest(new { Message = "BusinessId or UserId is invalid." });
            }

            if (!string.Equals(user?.CompanyId, BusinessId, StringComparison.OrdinalIgnoreCase))
            {
                return BadRequest(new { Message = "User does not have access to the specified business." });
            }

            // Join Campaigns and Conversations
            var campaigns = await _CompaignDbContext.Campaigns.Where(c => c.BusinessId == BusinessId.ToString()).ToListAsync();

            // Join Campaigns and Conversations using LINQ to Objects
            var joinedData = from campaign in campaigns
                             join conversation in _CompaignDbContext.Conversations
                             on campaign.WhatsAppMessagesId equals conversation.WhatsAppMessageId
                             select new
                             {
                                 CampaignId = campaign.CampaignId,
                                 Createdby = campaign.Createdby,
                                 CampaignTitle = campaign.CampaignTitle,
                                 ConversationStatus = conversation.Status
                             };

            // Group by CampaignTitle and calculate message counts
            var groupedData = from data in joinedData
                              group data by data.CampaignTitle into g
                              select new
                              {
                                  CampaignId = g.First().CampaignId,
                                  Createdby = g.First().Createdby,
                                  CampaignTitle = g.Key,
                                  SentCount = g.Count(c => c.ConversationStatus == EngagetoEntities.Enums.ConvStatus.sent) + g.Count(c => c.ConversationStatus == EngagetoEntities.Enums.ConvStatus.delivered),
                                  DeliveredCount = g.Count(c => c.ConversationStatus == EngagetoEntities.Enums.ConvStatus.delivered) + g.Count(c => c.ConversationStatus == EngagetoEntities.Enums.ConvStatus.read),
                                  ReadCount = g.Count(c => c.ConversationStatus == EngagetoEntities.Enums.ConvStatus.read)
                              };

            // Execute the query and retrieve results
            var campaignMessageCounts = groupedData.ToList();

            // Convert results to CampaignMessageCountsDto
            var campaignMessageCountsDto = campaignMessageCounts.Select(item => new CampaignMessageCountsDto
            {
                CampaignId = item.CampaignId.ToString(),
                CampaignTitle = item.CampaignTitle,
                Createdby = item.Createdby,
                Sent = item.SentCount,
                Delivered = item.DeliveredCount,
                Read = item.ReadCount,
                State = campaigns.Where(t => t.CampaignTitle == item.CampaignTitle).Select(s => s.State).FirstOrDefault(),
                DateSetLive = campaigns.Where(t => t.CampaignTitle == item.CampaignTitle).Select(s => s.DateSetLive).FirstOrDefault()

            }).ToList();
            return campaignMessageCountsDto;
        }


        /// <summary>
        /// CampaignMessageCount_Details 
        /// </summary>
        /// <param name="BusinessId"></param>
        /// <param name="campaignTitle"></param>
        /// <returns>CampaignMessageCount_Details along with Campign Details </returns>

        [HttpGet("CampaignMessageCount_Details")]
        [Authorize]
        public async Task<ActionResult<List<CampaignMessageCountsDto>>> GetCampaignMessageCounts([FromQuery, BindRequired] string BusinessId, [FromQuery, BindRequired] Guid UserId, [FromQuery, BindRequired] List<string> campaignTitles)
        {
            var Business = await _CompaignDbContext.BusinessDetailsMetas.FirstOrDefaultAsync(t => t.BusinessId == BusinessId);
            var user = await _CompaignDbContext.Users.FirstOrDefaultAsync(u => u.Id == UserId);

            if (Business == null || user == null)
            {
                return BadRequest("Business or User with the specified ID does not exist.");
            }

            if (string.IsNullOrEmpty(Business?.BusinessId) || user?.Id == Guid.Empty)
            {
                return BadRequest("BusinessId or UserId is invalid.");
            }

            if (!string.Equals(user?.CompanyId, BusinessId, StringComparison.OrdinalIgnoreCase))
            {
                return BadRequest("User does not have access to the specified business.");
            }
            var campaignMessageCountsDtoList = new List<CampaignMessageCountsDto>();
            foreach (var campaignTitle in campaignTitles)
            {
                // Join Campaigns and Conversations
                var campaign = await _CompaignDbContext.Campaigns.FirstOrDefaultAsync(c => c.BusinessId == BusinessId && c.CampaignTitle == campaignTitle);

                if (campaign != null)
                {
                    var whatsappMessageIds = campaign.WhatsAppMessagesId.Split(',');
                    // Join Campaigns and Conversations using LINQ to Objects


                    var conversations = await _CompaignDbContext.Conversations
                        .Where(conversation => whatsappMessageIds.Contains(conversation.WhatsAppMessageId))
                        .ToListAsync();

                    int sentCount = conversations.Count(status => status.Status == EngagetoEntities.Enums.ConvStatus.sent) +
                                     conversations.Count(status => status.Status == EngagetoEntities.Enums.ConvStatus.delivered) +
                                      conversations.Count(status => status.Status == EngagetoEntities.Enums.ConvStatus.read);

                    int deliveredCount = conversations.Count(status => status.Status == EngagetoEntities.Enums.ConvStatus.delivered) +
                                           conversations.Count(status => status.Status == EngagetoEntities.Enums.ConvStatus.read);

                    int readCount = conversations.Count(status => status.Status == EngagetoEntities.Enums.ConvStatus.read);

                    var conversationDetails = conversations.Select(c => new ConversationDetails
                    {
                        CreatedAt = c.CreatedAt,
                        TextMessage = c.TextMessage,
                        MediaFileName = c.MediaFileName,
                        MediaMimeType = c.MediaMimeType,
                        MediaUrl = c.MediaUrl,
                        MediaCaption = c.MediaCaption,
                        TemplateMediaUrl = c.TemplateMediaUrl,
                        TemplateHeader = c.TemplateHeader,
                        TemplateBody = c.TemplateBody,
                        TemplateFooter = c.TemplateFooter,
                        CallButtonName = c.CallButtonName,
                        PhoneNumber = c.PhoneNumber,
                        UrlButtonNames = c.UrlButtonNames,
                        RedirectUrls = c.RedirectUrls,
                        QuickReplies = c.QuickReplies
                    }).ToList();

                    // Create CampaignMessageCountsDto
                    var campaignMessageCountsDto = new CampaignMessageCountsDto
                    {
                        CampaignTitle = campaign.CampaignTitle,
                        Sent = sentCount,
                        Delivered = deliveredCount,
                        Read = readCount,
                        State = campaign.State,
                        DateSetLive = campaign.DateSetLive,
                        Conversations = conversationDetails
                    };
                    campaignMessageCountsDtoList.Add(campaignMessageCountsDto);
                }
            }

            return campaignMessageCountsDtoList;
        }



        [HttpGet("CampaignDetails_ById")]
        [Authorize]
        public async Task<ActionResult<List<CampaignMessageCountsDto>>> GetCampaignMessage([FromQuery, BindRequired] string BusinessId, [FromQuery, BindRequired] Guid UserId, [FromQuery, BindRequired] List<string> campaignIds)
        {
            var Business = await _CompaignDbContext.BusinessDetailsMetas.FirstOrDefaultAsync(t => t.BusinessId == BusinessId);
            var user = await _CompaignDbContext.Users.FirstOrDefaultAsync(u => u.Id == UserId);

            if (Business == null || user == null)
            {
                return BadRequest("Business or User with the specified ID does not exist.");
            }

            if (string.IsNullOrEmpty(Business?.BusinessId) || user?.Id == Guid.Empty)
            {
                return BadRequest("BusinessId or UserId is invalid.");
            }

            if (!string.Equals(user?.CompanyId, BusinessId, StringComparison.OrdinalIgnoreCase))
            {
                return BadRequest("User does not have access to the specified business.");
            }
            var campaignMessageCountsDtoList = new List<CampaignMessageCountsDto>();

            foreach (var campaignId in campaignIds)
            {
                // Join Campaigns and Conversations
                var campaign = await _CompaignDbContext.Campaigns.FirstOrDefaultAsync(c => c.BusinessId == BusinessId && c.CampaignId.ToString() == campaignId);

                if (campaign != null)
                {
                    var whatsappMessageId = campaign.WhatsAppMessagesId.Split(',');

                    // Join Campaigns and Conversations using LINQ to Objects
                    var joinedData = from conversation in _CompaignDbContext.Conversations
                                     where whatsappMessageId.Contains(conversation.WhatsAppMessageId)
                                     select new { Conversation = conversation };

                    // Join Campaigns and Conversations using LINQ to Objects
                    var joinedDatas = from conversation in _CompaignDbContext.Conversations
                                      where conversation.WhatsAppMessageId == campaign.WhatsAppMessagesId
                                      select new { Conversation = conversation };

                    var whatsappMessageIds = await _CompaignDbContext.Campaigns
                                           .Where(c => c.BusinessId == BusinessId && c.CampaignId.ToString() == campaignId)
                                           .Select(c => c.WhatsAppMessagesId)
                                           .ToListAsync();

                    List<Conversations> conversations = await _CompaignDbContext.Conversations
                                       .Where(conversation => whatsappMessageIds.Contains(conversation.WhatsAppMessageId))
                                       .ToListAsync();

                    // Retrieve conversation details
                    var conversationDetails = await joinedData.Select(c => new ConversationDetails
                    {
                        CreatedAt = c.Conversation.CreatedAt,
                        TextMessage = c.Conversation.TextMessage,
                        MediaFileName = c.Conversation.MediaFileName,
                        MediaMimeType = c.Conversation.MediaMimeType,
                        MediaUrl = c.Conversation.MediaUrl,
                        MediaCaption = c.Conversation.MediaCaption,
                        TemplateMediaUrl = c.Conversation.TemplateMediaUrl,
                        TemplateHeader = c.Conversation.TemplateHeader,
                        TemplateBody = c.Conversation.TemplateBody,
                        TemplateFooter = c.Conversation.TemplateFooter,
                        CallButtonName = c.Conversation.CallButtonName,
                        PhoneNumber = c.Conversation.PhoneNumber,
                        UrlButtonNames = c.Conversation.UrlButtonNames,
                        RedirectUrls = c.Conversation.RedirectUrls,
                        QuickReplies = c.Conversation.QuickReplies
                    }).ToListAsync();

                    // Create CampaignMessageCountsDto
                    var campaignMessageCountsDto = new CampaignMessageCountsDto
                    {
                        CampaignId = campaign.CampaignId.ToString(),
                        CampaignTitle = campaign.CampaignTitle,
                        Createdby = campaign.Createdby,
                        DateSetLive = campaign.DateSetLive,
                        Conversations = conversationDetails
                    };
                    campaignMessageCountsDtoList.Add(campaignMessageCountsDto);
                }
            }

            return campaignMessageCountsDtoList;
        }

        /// <summary>
        /// CampaignDetails_filters filter the CampaignDetails of the base of there name , seaching, shorting pagination
        /// </summary>
        /// <param name="BusinessId"></param>
        /// <param name="UserId"></param>
        /// <param name="filtering"></param>
        /// <param name="page"></param>
        /// <param name="perPage"></param>
        /// <returns></returns>


        [HttpPost("CampaignDetail_filters")]
        [Authorize]
        public async Task<ActionResult<IEnumerable<object>>> GetCampaignMessageCounts([FromQuery, BindRequired] string BusinessId,
                                                                                        [FromQuery, BindRequired] Guid UserId,
                                                                                           [FromBody, Optional] CampaignFilter? filtering,
                                                                                      [Range(1, 100), Required, FromQuery] int page = 1,
                                                                                   [Range(1, 100), Required, FromQuery] int perPage = 10)
        {

            var Business = await _CompaignDbContext.BusinessDetailsMetas.FirstOrDefaultAsync(t => t.BusinessId == BusinessId);
            var user = await _CompaignDbContext.Users.FirstOrDefaultAsync(u => u.Id == UserId);
            var campaigns = await _CompaignDbContext.Campaigns.Where(c => c.BusinessId == BusinessId.ToString()).ToListAsync();

            if (Business == null || user == null)
            {
                return BadRequest(new { Message = "Business or User with the specified ID does not exist." });
            }

            if (string.IsNullOrEmpty(Business?.BusinessId) || user?.Id == Guid.Empty)
            {
                return BadRequest(new { Message = "BusinessId or UserId is invalid." });
            }

            if (!string.Equals(user?.CompanyId, BusinessId, StringComparison.OrdinalIgnoreCase))
            {
                return BadRequest(new { Message = "User does not have access to the specified business." });
            }
            // Fetch campaigns data from the database
            var campaignsFromDb = _CompaignDbContext.Campaigns.Where(c => !string.IsNullOrEmpty(c.WhatsAppMessagesId))
                                                              .Select(c => new { c.CampaignTitle, c.WhatsAppMessagesId })
                                                              .ToList();

            // Split WhatsAppMessagesId and transform data in memory
            var campaignsWhatsAppMessagesId = campaignsFromDb.SelectMany(c => c.WhatsAppMessagesId.Split(new[] { ',' }, StringSplitOptions.RemoveEmptyEntries)
                                                             .Select(w => new { CampaignTitle = c.CampaignTitle, WhatsAppMessagesId = w }))
                                                             .ToList();

            var all_campaignsWhatsAppMessagesIds = campaignsWhatsAppMessagesId.Select(c => c.WhatsAppMessagesId)
                                                                              .ToList();

            // Retrieve conversations based on WhatsAppMessageIds
            var conversations = _CompaignDbContext.Conversations.Where(conversation => all_campaignsWhatsAppMessagesIds
                                                                         .Contains(conversation.WhatsAppMessageId))
                                                                         .ToList();

            // Retrieve conversations based on WhatsAppMessageIds and select ReplyId
            var conversationsReplyId = _CompaignDbContext.Conversations.Where(conversation => all_campaignsWhatsAppMessagesIds
                                                                                 .Contains(conversation.WhatsAppMessageId))
                                                                                 .Select(conversation => new { conversation.WhatsAppMessageId, conversation.ReplyId })
                                                                                 .ToList();
            // Retrieve all conversations replyId                                                                   
            var allconverstionreply = _CompaignDbContext.Conversations.Select(conv => new { conv.ReplyId }).ToList();



            // Join Campaigns and Conversations using LINQ to Objects
            var joinedData = from campaign in campaigns
                             from messageId in campaign.WhatsAppMessagesId?.Split(new[] { ',' }, StringSplitOptions.RemoveEmptyEntries) ?? Array.Empty<string>()
                             join conversation in conversations
                             on messageId equals conversation.WhatsAppMessageId into conversationGroup
                             from conversation in conversationGroup.DefaultIfEmpty()
                             select new
                             {
                                 CampaignId = campaign.CampaignId,
                                 Createdby = campaign.Createdby,
                                 CampaignTitle = campaign.CampaignTitle,
                                 ConversationStatus = conversation?.Status ?? EngagetoEntities.Enums.ConvStatus.sent,
                                 State = campaign.State,
                                 DateSetLive = campaign.DateSetLive,
                                 ConversationReplyId = conversation?.ReplyId,
                             };
            // Ensure joinedData is not null
            var filteredData = joinedData.Where(data => data != null).ToList();

            // Join data in memory && Count of replies corresponding to each WhatsAppMessageId in the campaign table
            var campaignReplies = campaignsWhatsAppMessagesId.Join(allconverstionreply, c => c.WhatsAppMessagesId,
                                                                                             conv => conv.ReplyId,
                                                                                             (c, conv) => new { c.CampaignTitle, c.WhatsAppMessagesId })
                                                                                             .GroupBy(c => c.CampaignTitle)
                                                                                             .Select(g => new
                                                                                             {
                                                                                                 CampaignName = g.Key,
                                                                                                 ReplyCount = g.Count()
                                                                                             })
                                                                                              .ToList();
            // Calculate RepliedCount per Campaign
            var groupedData = from data in filteredData
                              group data by data.CampaignTitle into g
                              let replyCount = campaignReplies.Where(cr => cr.CampaignName == g.Key)
                                .Select(cr => cr.ReplyCount).FirstOrDefault()
                              select new
                              {
                                  CampaignId = g.First().CampaignId,
                                  Createdby = g.First().Createdby,
                                  CampaignTitle = g.Key,

                                  SentCount = g.Count(c => c.ConversationStatus == EngagetoEntities.Enums.ConvStatus.sent) +
                                              g.Count(c => c.ConversationStatus == EngagetoEntities.Enums.ConvStatus.delivered) +
                                              g.Count(c => c.ConversationStatus == EngagetoEntities.Enums.ConvStatus.read),

                                  DeliveredCount = g.Count(c => c.ConversationStatus == EngagetoEntities.Enums.ConvStatus.delivered) +
                                                   g.Count(c => c.ConversationStatus == EngagetoEntities.Enums.ConvStatus.read),

                                  ReadCount = g.Count(c => c.ConversationStatus == EngagetoEntities.Enums.ConvStatus.read),

                                  DateSetLive = campaigns.Where(t => t.CampaignTitle == g.Key).Select(s => s.DateSetLive).FirstOrDefault(),
                                  RepliedCount = replyCount
                              };

            int totalCount = groupedData.Count();


            //Apply  Searching
            if (filtering?.Searching != null && !string.IsNullOrEmpty(filtering.Searching.Value))
            {
                string searchTerm = filtering.Searching.Value.ToLower();
                groupedData = groupedData.Where(c => c.CampaignTitle.ToLower().Contains(searchTerm));
            }

            // Apply sorting
            if (filtering?.Sorting != null && !string.IsNullOrEmpty(filtering.Sorting.Column))
            {
                string columnName = filtering.Sorting.Column.ToLower();
                switch (columnName)
                {
                    case "campaigntitle":
                        groupedData = filtering.Sorting.Order.ToLower() == "desc" ? groupedData.OrderByDescending(c => c.CampaignTitle) :
                                                                                    groupedData.OrderBy(c => c.CampaignTitle);
                        break;
                    case "sentcount":
                        groupedData = filtering.Sorting.Order.ToLower() == "desc" ? groupedData.OrderByDescending(c => c.SentCount) :
                                                                                    groupedData.OrderBy(c => c.SentCount);
                        break;
                    case "deliveredcount":
                        groupedData = filtering.Sorting.Order.ToLower() == "desc" ? groupedData.OrderByDescending(c => c.DeliveredCount) :
                                                                                    groupedData.OrderBy(c => c.DeliveredCount);
                        break;
                    case "readcount":
                        groupedData = filtering.Sorting.Order.ToLower() == "desc" ? groupedData.OrderByDescending(c => c.ReadCount) :
                                                                                    groupedData.OrderBy(c => c.ReadCount);
                        break;
                    case "datesetlive":
                        groupedData = filtering.Sorting.Order.ToLower() == "desc" ? groupedData.OrderByDescending(c => c.DateSetLive) :
                                                                                    groupedData.OrderBy(c => c.DateSetLive);
                        break;
                    default:
                        // Handle invalid column name
                        break;
                }
            }
            // Apply Filtering
            if (filtering?.Filtering != null && filtering.Filtering.Conditions?.Any() == true)
            {
                campaigns = ApplyFilter(campaigns, filtering.Filtering);
            }
            var campaignMessageCountsDto = groupedData.Select(item => new CampaignMessageCountsDto
            {
                CampaignId = item.CampaignId.ToString(),
                CampaignTitle = item.CampaignTitle,
                Createdby = item.Createdby,
                Attempted = item.SentCount,
                Sent = item.SentCount,
                Delivered = item.DeliveredCount,
                Read = item.ReadCount,
                Replied = item.RepliedCount,
                State = campaigns.Where(t => t.CampaignTitle == item.CampaignTitle).Select(s => s.State).FirstOrDefault(),
                DateSetLive = campaigns.Where(t => t.CampaignTitle == item.CampaignTitle).Select(s => s.DateSetLive).FirstOrDefault()

            }).Where(x => x.State == CampaignState.Completed)
              .OrderByDescending(item => item.DateSetLive)
              .ToList();

            int totalRecords = campaignMessageCountsDto.Count();

            // Apply pagination
            var paginatedCampaignMessageCountsDto = campaignMessageCountsDto
                                                    .Skip((page - 1) * perPage)
                                                    .Take(perPage)
                                                    .ToList();

            return Ok(new
            {
                Total = totalRecords,
                Page = page,
                Per_Page = perPage,
                Data = paginatedCampaignMessageCountsDto,

            });
        }
        private List<Campaign> ApplyFilter(List<Campaign> campaigns, CampaignFilterGroup filtering)
        {
            List<Campaign> filteredList = new List<Campaign>(campaigns);

            if (filtering.FilterType == "and")
            {
                foreach (var condition in filtering.Conditions)
                {
                    var property = typeof(Campaign).GetProperty(condition.Column, BindingFlags.IgnoreCase | BindingFlags.Public | BindingFlags.Instance);
                    if (property != null)
                    {
                        filteredList = ApplyCondition(filteredList, condition, property);
                    }
                }
            }
            else if (filtering.FilterType == "or")
            {
                filteredList = new List<Campaign>();
                foreach (var condition in filtering.Conditions)
                {
                    var property = typeof(Campaign).GetProperty(condition.Column, BindingFlags.IgnoreCase | BindingFlags.Public | BindingFlags.Instance);
                    if (property != null)
                    {
                        var tempFilteredList = ApplyCondition(campaigns, condition, property);
                        foreach (var item in tempFilteredList)
                        {
                            if (!filteredList.Contains(item))
                            {
                                filteredList.Add(item);
                            }
                        }
                    }
                }
            }
            return filteredList;
        }

        private List<Campaign> ApplyCondition(List<Campaign> list, CampaignFilterCondition condition, PropertyInfo property)
        {
            if (property != null)
            {
                if (property.PropertyType == typeof(string))
                {
                    switch (condition.Operator.ToLower())
                    {
                        case "equal":
                            return list.Where(item => property.GetValue(item).Equals(condition.Value)).ToList();

                        case "notequal":
                            return list.Where(item => !property.GetValue(item).Equals(condition.Value)).ToList();

                        case "contains":
                            return list.Where(item => ((string)property.GetValue(item)).Contains(condition.Value)).ToList();

                        case "startswith":
                            return list.Where(item => ((string)property.GetValue(item)).StartsWith(condition.Value)).ToList();

                        case "endswith":
                            return list.Where(item => ((string)property.GetValue(item)).EndsWith(condition.Value)).ToList();

                        default:
                            // Handle unsupported operator
                            return list;
                    }
                }
                else if (property.PropertyType == typeof(DateTime?))
                {
                    if (DateTime.TryParseExact(condition.Value, "yyyy-MM-dd", CultureInfo.InvariantCulture, DateTimeStyles.None, out DateTime dateTimeValue))
                    {
                        switch (condition.Operator.ToLower())
                        {
                            case "equal":
                                list = list.Where(item => ((DateTime?)property.GetValue(item)).Equals(dateTimeValue)).ToList();
                                break;

                            case "notequal":
                                list = list.Where(item => !((DateTime?)property.GetValue(item)).Equals(dateTimeValue)).ToList();
                                break;

                            case "after":
                                list = list.Where(item => ((DateTime?)property.GetValue(item))?.CompareTo(dateTimeValue) > 0).ToList();
                                break;

                            case "before":
                                list = list.Where(item => ((DateTime?)property.GetValue(item))?.CompareTo(dateTimeValue) < 0).ToList();
                                break;

                            case "last7days":
                                DateTime last7Days = DateTime.Today.AddDays(-7);
                                list = list.Where(item => ((DateTime?)property.GetValue(item)) >= last7Days).ToList();
                                break;

                            case "last14days":
                                DateTime last14Days = DateTime.Today.AddDays(-14);
                                list = list.Where(item => ((DateTime?)property.GetValue(item)) >= last14Days).ToList();
                                break;

                            case "last30days":
                                DateTime last30Days = DateTime.Today.AddDays(-30);
                                list = list.Where(item => ((DateTime?)property.GetValue(item)) >= last30Days).ToList();
                                break;

                            // Add more date-based operators as needed
                            default:
                                // Handle unsupported operator
                                break;
                        }
                    }
                }

                else if (property.PropertyType == typeof(CampaignState))
                {
                    if (int.TryParse(condition.Value, out int intValue))
                    {
                        switch (condition.Operator.ToLower())
                        {
                            case "equal":
                                list = list.Where(item => ((int)property.GetValue(item)).Equals(intValue)).ToList();
                                break;

                            case "notequal":
                                list = list.Where(item => !((int)property.GetValue(item)).Equals(intValue)).ToList();
                                break;

                            case "greaterthan":
                                list = list.Where(item => ((int)property.GetValue(item)) > intValue).ToList();
                                break;

                            case "lessthan":
                                list = list.Where(item => ((int)property.GetValue(item)) < intValue).ToList();
                                break;
                            // Add more int-based operators as needed
                            default:
                                // Handle unsupported operator
                                break;
                        }
                    }
                }

            }
            return list;
        }
        /// <summary>
        /// Use for filter schedule data
        /// </summary>
        /// <param name="BusinessId"></param>
        /// <param name="UserId"></param>
        /// <param name="filtering"></param>
        /// <param name="page"></param>
        /// <param name="perPage"></param>
        /// <returns></returns>
        [HttpPost("AllScheduleCampaignDetails")]
        [Authorize]
        public async Task<ActionResult<IEnumerable<object>>> GetAllCampaign([FromQuery, BindRequired] string BusinessId,
                                                                                [FromQuery, BindRequired] Guid UserId,
                                                                                [FromBody, Optional] CampaignFilter? filtering,
                                                                                [Range(1, 100), Required, FromQuery] int page = 1,
                                                                                [Range(1, 100), Required, FromQuery] int perPage = 10)
        {
            var Business = await _CompaignDbContext.BusinessDetailsMetas.FirstOrDefaultAsync(t => t.BusinessId == BusinessId);
            var user = await _CompaignDbContext.Users.FirstOrDefaultAsync(u => u.Id == UserId);
            var campaigns = await _CompaignDbContext.Campaigns.Where(c => c.BusinessId == BusinessId.ToString()).ToListAsync();

            List<Campaigns> data = campaigns

            .Select(campaign => new Campaigns
            {
                CampaignId = campaign.CampaignId,
                Audiance = (campaign.Audiance ?? "").ToLower().Split(',').ToList(),
                BusinessId = campaign.BusinessId,
                UserId = campaign.UserId,
                CampaignTitle = campaign.CampaignTitle,
                SendTextType = campaign.SendTextType,
                MediaUrl = campaign.MediaUrl,
                TemplateId = campaign.TemplateId,
                HeaderVariableValue = new Variable { value = campaign.HeaderValue },
                BodyVariableValues = string.IsNullOrEmpty(campaign.BodyValues) ? new List<Variable>() : campaign.BodyValues.Split(',').Select(value => new Variable { value = value }).ToList(),
                RedirectUrlVariableValues = (campaign.RedirectUrlsValue ?? "").Split(',').ToArray(),
                DateSetLive = campaign.DateSetLive,
                Createdby = campaign.Createdby ?? "",
                State = campaign.State,
                AutomationJson = campaign.AutomationJson
            })
              .Where(campaign => campaign.State == CampaignState.Scheduled)
              .OrderByDescending(campaign => campaign.DateSetLive)
              .ToList();

            // Searching
            if (filtering?.Searching != null && !string.IsNullOrEmpty(filtering.Searching.Value))
            {
                string searchTerm = filtering.Searching.Value.ToLower();
                data = data.Where(c => c.CampaignTitle.ToLower().Contains(searchTerm)).ToList();
            }
            // Filtering
            if (filtering?.Filtering != null && filtering.Filtering.Conditions?.Any() == true)
            {
                data = ApplyFilter1(data, filtering.Filtering);
            }

            int totalRecords = data.Count;

            // Apply pagination
            List<Campaigns> paginatedData = data
                .OrderByDescending(campaign => campaign.DateSetLive)
                .Skip((page - 1) * perPage)
                .Take(perPage)
                .ToList();

            return Ok(new
            {
                Total = totalRecords,
                Page = page,
                Per_Page = perPage,
                Data = paginatedData,
            });
        }
        private List<Campaigns> ApplyFilter1(List<Campaigns> data, CampaignFilterGroup filtering)
        {
            List<Campaigns> filteredList = data;

            if (filtering.FilterType == "and")
            {
                foreach (var condition in filtering.Conditions)
                {
                    var property = typeof(Campaigns).GetProperty(condition.Column, BindingFlags.IgnoreCase | BindingFlags.Public | BindingFlags.Instance);
                    if (property != null)
                    {
                        filteredList = ApplyCondition1(filteredList, condition, property);
                    }
                }
            }
            else if (filtering.FilterType == "or")
            {
                //  List<Campaigns> filteredList = data;
                filteredList = data;
                foreach (var condition in filtering.Conditions)
                {
                    var property = typeof(Campaigns).GetProperty(condition.Column, BindingFlags.IgnoreCase | BindingFlags.Public | BindingFlags.Instance);
                    if (property != null)
                    {
                        var tempFilteredList = ApplyCondition1(filteredList, condition, property);
                        foreach (var item in tempFilteredList)
                        {
                            if (!filteredList.Contains(item))
                            {
                                filteredList.Add(item);
                            }
                        }
                    }
                }
            }
            return filteredList;
        }

        private List<Campaigns> ApplyCondition1(List<Campaigns> list, CampaignFilterCondition condition, PropertyInfo property)
        {
            if (property != null)
            {
                if (property.PropertyType == typeof(string))
                {
                    switch (condition.Operator.ToLower())
                    {
                        case "equal":
                            return list.Where(item => property.GetValue(item).Equals(condition.Value)).ToList();

                        case "notequal":
                            return list.Where(item => !property.GetValue(item).Equals(condition.Value)).ToList();

                        case "contains":
                            return list.Where(item => ((string)property.GetValue(item)).Contains(condition.Value)).ToList();

                        case "startswith":
                            return list.Where(item => ((string)property.GetValue(item)).StartsWith(condition.Value)).ToList();

                        case "endswith":
                            return list.Where(item => ((string)property.GetValue(item)).EndsWith(condition.Value)).ToList();

                        default:
                            // Handle unsupported operator
                            return list;
                    }
                }
                else if (property.PropertyType == typeof(DateTime?))
                {
                    if (DateTime.TryParseExact(condition.Value, "yyyy-MM-dd", CultureInfo.InvariantCulture, DateTimeStyles.None, out DateTime dateTimeValue))
                    {
                        switch (condition.Operator.ToLower())
                        {
                            case "equal":
                                list = list.Where(item => ((DateTime?)property.GetValue(item)).Equals(dateTimeValue)).ToList();
                                break;

                            case "notequal":
                                list = list.Where(item => !((DateTime?)property.GetValue(item)).Equals(dateTimeValue)).ToList();
                                break;

                            case "after":
                                list = list.Where(item => ((DateTime?)property.GetValue(item))?.CompareTo(dateTimeValue) > 0).ToList();
                                break;

                            case "before":
                                list = list.Where(item => ((DateTime?)property.GetValue(item))?.CompareTo(dateTimeValue) < 0).ToList();
                                break;

                            case "last7days":
                                DateTime last7Days = DateTime.Today.AddDays(-7);
                                list = list.Where(item => ((DateTime?)property.GetValue(item)) >= last7Days).ToList();
                                break;

                            case "last14days":
                                DateTime last14Days = DateTime.Today.AddDays(-14);
                                list = list.Where(item => ((DateTime?)property.GetValue(item)) >= last14Days).ToList();
                                break;

                            case "last30days":
                                DateTime last30Days = DateTime.Today.AddDays(-30);
                                list = list.Where(item => ((DateTime?)property.GetValue(item)) >= last30Days).ToList();
                                break;

                            // Add more date-based operators as needed
                            default:
                                // Handle unsupported operator
                                break;
                        }
                    }
                }
                else if (property.PropertyType == typeof(CampaignState))
                {
                    if (int.TryParse(condition.Value, out int intValue))
                    {
                        switch (condition.Operator.ToLower())
                        {
                            case "equal":
                                list = list.Where(item => ((int)property.GetValue(item)).Equals(intValue)).ToList();
                                break;

                            case "notequal":
                                list = list.Where(item => !((int)property.GetValue(item)).Equals(intValue)).ToList();
                                break;

                            case "greaterthan":
                                list = list.Where(item => ((int)property.GetValue(item)) > intValue).ToList();
                                break;

                            case "lessthan":
                                list = list.Where(item => ((int)property.GetValue(item)) < intValue).ToList();
                                break;
                            // Add more int-based operators as needed
                            default:
                                // Handle unsupported operator
                                break;
                        }
                    }
                }

            }
            return list;
        }
    }
}




































