﻿using EngagetoContracts.GeneralContracts;

namespace Engageto.Middleware
{
    public class EnvironmentDetectionMiddleware
    {
        private readonly RequestDelegate _next;

        public EnvironmentDetectionMiddleware(RequestDelegate next)
        {
            _next = next;
        }

        public async Task InvokeAsync(HttpContext context, IEnvironmentService environmentService)
        {
            var request = context.Request;
            var requestUrl = $"{request.Scheme}://{request.Host}{request.Path}";
            environmentService.RequestHost = request.Host;
            environmentService.RequestScheme = request.Scheme;
            if (requestUrl.Contains("connect.engageto.in"))
            {
                environmentService.IsDevelopment = false;
            }
            else
            {
                environmentService.IsDevelopment = true;
            }

            await _next(context);
        }
    }

}
