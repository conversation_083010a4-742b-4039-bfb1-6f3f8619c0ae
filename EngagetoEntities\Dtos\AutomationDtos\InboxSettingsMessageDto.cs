﻿using EngagetoEntities.Enums;

namespace EngagetoEntities.Dtos.AutomationDtos
{
    public class InboxSettingsMessageDto
    {
        public Feature? Feature { get; set; }
        public string? MediaUrl { get; set; }
        public string? Message { get; set; }
        public bool? Enabled { get; set; }
        public TimeSpan? Delay { get; set; }
        public List<Variables>? Variables { get; set; }
    }
    public class Variables
    {
        public string? Variable { get; set; }
        public string? Value { get; set; }
        public string? Fallbackvalue { get; set; }
    }
}
