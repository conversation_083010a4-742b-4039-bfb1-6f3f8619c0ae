﻿using EngagetoContracts.AutomationContracts;
using System;
using System.Collections.Generic;
using System.Data;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace EngagetoRepository.AutomationRepository
{
    public sealed class UnitOfWork : IUnitOfWork
    {
        private readonly Guid _id;
        private readonly IDbConnection _dbConnection;
        private IDbTransaction _dbTransaction;

        public UnitOfWork(IDbConnection dbConnection)
        {
            _id = Guid.NewGuid();
            _dbConnection = dbConnection;
        }


        IDbConnection IUnitOfWork.Connection
        {
            get
            {
                // 🔧 CRITICAL FIX: Open connection when accessed if closed
                if (_dbConnection.State == System.Data.ConnectionState.Closed)
                    _dbConnection.Open();
                return _dbConnection;
            }
        }
        IDbTransaction IUnitOfWork.Transaction => _dbTransaction;
        Guid IUnitOfWork.Id => _id;


        public void Begin()
        {
            // 🔧 ENSURE: Connection is open before starting transaction
            if (_dbConnection.State == System.Data.ConnectionState.Closed)
                _dbConnection.Open();
            _dbTransaction = _dbConnection.BeginTransaction();
        }

        public void Commit()
        {
            _dbTransaction.Commit();
            Dispose();
        }

        public void Rollback()
        {
            _dbTransaction.Rollback();
            Dispose();
        }

        public void Dispose()
        {
            try
            {
                // Dispose transaction first
                if (_dbTransaction != null)
                {
                    _dbTransaction.Dispose();
                    _dbTransaction = null;
                }
            }
            finally
            {
                // 🔧 CRITICAL FIX: Dispose the connection to prevent leaks
                if (_dbConnection != null && _dbConnection.State != System.Data.ConnectionState.Closed)
                {
                    _dbConnection.Close();
                    _dbConnection.Dispose();
                }
            }
        }
    }
}
