﻿using EngagetoEntities.Dtos.ConversationDtos;


namespace EngagetoDapper.Data.Interfaces.CampaignInterfaces.ICampaignServices
{
    public interface IConversationsService
    {
        Task<List<ConverstationDetailsDto>> GetConversationDetailsAsync(string CompanyId);
        Task VerifySubscriptionAsync(string companyId, bool isVerifyLowWalletBalance = false, int count = 1);
        Task VerifySubscriptionAndExpectedWalletBalanceAysc(string companyId, int count = 1);
    }
}
