﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using EngagetoEntities.Entities;

namespace EngagetoContracts.UserContracts
{
    public interface IHelpCenterService
    {
        Task<List<HelpCenterDto>> GetAllHelpItemsAsync();
        Task<List<HelpCenterDto>> SearchHelpItemsAsync(string searchTerm);
        Task<Guid> CreateHelpCenterAsync(Guid currentuserId, HelpCenterDto helpCenterDto);
        Task<bool> UpdateHelpCenterAsync(Guid Id, UpdateHelpCenterDto helpCenterDto);
        Task<bool> DeleteHelpCenterAsync(Guid id);

    }
}
