﻿using EngagetoContracts.MetaContracts;
using EngagetoContracts.UserContracts;
using EngagetoEntities.Dtos.WhatsAppChargeDtos;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using System.ComponentModel.DataAnnotations;

namespace Engageto.Controllers.WhatsAppChargeController
{
    [Route("api/[controller]")]
    [ApiController]
    public class WhatsAppChargeController : ControllerBase
    {
        private readonly IMetaCostInfoService _metaApiService;
        private readonly ICompanyDetailsService _companyDetailsService;

        public WhatsAppChargeController(IMetaCostInfoService metaApiService, ICompanyDetailsService companyDetailsService)
        {
            _metaApiService = metaApiService;
            _companyDetailsService = companyDetailsService;
        }

        [HttpPost("MasterMetaCharge")]
        [Authorize]
        public async Task<IActionResult> CreateMasterChargeByMeta([FromBody] MasterMetaPriceDtos masterMetaPriceDtos)
        {
            try
            {
                bool isCreated = await _metaApiService.AddMetaMasterChargeAsync(masterMetaPriceDtos);
                return Ok(new { Message = "Successfully  master  charge By Meta is created." });
            }
            catch (InvalidOperationException ex)
            {
                return BadRequest(ex.Message);
            }
            catch (Exception ex)
            {
                return BadRequest(ex.Message);
            }
        }

        [HttpGet("MasterMetaCharge")]
        [Authorize]
        public async Task<IActionResult> GetAllMasterChargeByMeta()
        {
            var getAllMasterChagre = await _metaApiService.GetAllMetaMasterChargeAsync();
            if (getAllMasterChagre == null)
            {
                return NotFound();
            }
            return Ok(getAllMasterChagre);
        }

        [HttpGet("MasterMetaCharge/{id}")]
        [Authorize]
        public async Task<IActionResult> GetMasterChargeOfMetaById(Guid id)
        {
            var getMasterChargeById = await _metaApiService.GetMetaMasterChargeByIdAsync(id);

            if (getMasterChargeById == null)
            {
                return NotFound();
            }
            return Ok(getMasterChargeById);
        }

        [HttpPut("MasterMetaCharge/{id}")]
        [Authorize]
        public async Task<IActionResult> UpdateMasterChargeOfMeta(Guid id, MasterMetaPriceDtos masterMetaPriceDtos)
        {
            try
            {
                bool isUpdated = await _metaApiService.UpdateMetaMasterChargeAsync(id, masterMetaPriceDtos);
                return Ok(new { Message = " master charge of meta  have been successfully updated." });
            }
            catch (InvalidOperationException ex)
            {
                return BadRequest(new { Message = ex.Message });
            }
            catch (Exception ex)
            {
                return BadRequest(new { Message = $"Internal Server Error: {ex.Message}" });
            }
        }

        [HttpDelete("MasterMetaCharge/{id}")]
        [Authorize]
        public async Task<IActionResult> DeleteMasterChargeOfMeta(Guid id)
        {
            try
            {
                bool isDeleted = await _metaApiService.DeleteMetaMasterChargeAsync(id);
                return Ok(new { Message = "Successfully master Chrage Of Meta have been removed." });

            }
            catch (Exception ex)
            {
                return BadRequest($"Error removing  master charge  of meta : {ex.Message}");
            }

        }

        [HttpPost("CustomMetaCharge")]
        [Authorize]
        public async Task<IActionResult> CreateCustomChargeByMeta([FromBody] CustomMetapPriceDtos customMetapPriceDtos, [FromQuery, Required] Guid businessId)
        {
            try
            {
                var businessDetails = await _companyDetailsService.GetCompanyByIdAsync(businessId);
                if (businessDetails == null)
                {
                    return NotFound("Invalid Business Id.");

                }

                bool isCreated = await _metaApiService.AddMetaCustomChargeAsync(customMetapPriceDtos, businessId.ToString());
                return Ok(new { Message = "Successfully  custom  charge By Meta is created." });
            }
            catch (InvalidOperationException ex)
            {
                return BadRequest(ex.Message);
            }
            catch (Exception ex)
            {
                return BadRequest(ex.Message);
            }
        }


        [HttpGet("CustomMetaCharge")]
        [Authorize]
        public async Task<IActionResult> GetAllCustomChargeByMeta()
        {
            var getAllCustomChagre = await _metaApiService.GetAllMetaCustomChargeAsync();
            if (getAllCustomChagre == null)
            {
                return NotFound();
            }
            return Ok(getAllCustomChagre);
        }

        [HttpGet("CustomMetaCharge/{id}")]
        [Authorize]
        public async Task<IActionResult> GetCustomChargeOfMetaById(Guid id)
        {
            var getCustomChargeById = await _metaApiService.GetMetaCustomChargeByIdAsync(id);

            if (getCustomChargeById == null)
            {
                return NotFound();
            }
            return Ok(getCustomChargeById);
        }
        [HttpPut("CustomMetaCharge/{id}")]
        [Authorize]
        public async Task<IActionResult> UpdateCustomChargeOfMeta(Guid id, CustomMetapPriceDtos customMetaPriceDtos)
        {
            try
            {
                bool isUpdated = await _metaApiService.UpdateMetaCustomChargeAsync(id, customMetaPriceDtos);
                return Ok(new { Message = " custom charge of meta  have been successfully updated." });
            }
            catch (InvalidOperationException ex)
            {
                return BadRequest(new { Message = ex.Message });
            }
            catch (Exception ex)
            {
                return BadRequest(new { Message = $"Internal Server Error: {ex.Message}" });
            }
        }

        [HttpDelete("CustomMetaCharge/{id}")]
        [Authorize]
        public async Task<IActionResult> DeleteCustomChargeOfMeta(Guid id)
        {
            try
            {
                bool isDeleted = await _metaApiService.DeleteMetaCustomChargeAsync(id);
                return Ok(new { Message = "Successfully custom Chrage Of Meta have been removed." });

            }
            catch (Exception ex)
            {
                return BadRequest($"Error removing  master charge  of meta : {ex.Message}");
            }
        }

        [HttpGet("GetCost")]
        [Authorize]
        public async Task<IActionResult> GetCost([FromQuery, Required] string businessId, string phoneNumber, string categoryType)
        {
            try
            {
                var cost = await _metaApiService.GetCostAsync(businessId, phoneNumber, categoryType);
                return Ok("Final cost of meta:  " + cost);
            }
            catch (Exception ex)
            {
                return BadRequest($"Error  : {ex.Message}");
            }
        }
    }
}
