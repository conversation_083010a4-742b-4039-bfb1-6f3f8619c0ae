﻿namespace EngagetoMeta.Repositories
{
    public interface ILogHistoryRepository
    {
        public Task<bool> SaveSuccessLogHistoryAsyn(string? apiName, object? requestBody, object? response, string? notes, string? whatsaapBusinessAccId, string? field);
        public Task<bool> SaveErrorLogHistoryAsyn(string? apiName, object? requestBody, string? notes, string? errorMessage, object? stackTrace, string? whatsaapBusinessAccId, string? field);
    }
}
