﻿using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations.Schema;
using System.ComponentModel.DataAnnotations;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace EngagetoEntities.UserEntities.Models
{
    [Table("Languages")]
    public class Language
    {
        [Key]
        public Guid LanguageId { get; set; }
        public string LanguageCode { get; set; }
        public string LanguageName { get; set; }


    }
    public class LanguageDto
    {
        public Guid LanguageId { get; set; }
        [Required(ErrorMessage = "Language code is required")]
        public string LanguageCode { get; set; }

        [Required(ErrorMessage = "Language name is required")]
        public string LanguageName { get; set; }

    }
    public class LanguageUpdateDto
    {
        public string LanguageCode { get; set; }
        public string LanguageName { get; set; }


    }
    public class UserLanguageRequest
    {
        [Required(ErrorMessage = "Language name is required")]
        public string LanguageName { get; set; }

    }
    public class LanguagePreferenceRequest
    {
        [Required(ErrorMessage = "Language code is required")]
        public string LanguageCode { get; set; }

        [Required(ErrorMessage = "Language name is required")]
        public string LanguageName { get; set; }
    }
}
