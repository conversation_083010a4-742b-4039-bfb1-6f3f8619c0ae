﻿using EngagetoDapper.Data.Connections;
using EngagetoDapper.Data.Dtos;
using EngagetoDapper.Data.Interfaces.GenericInterfaces;
using EngagetoDapper.Data.Interfaces.IConversationAnalyticsInterfaces;
using EngagetoDapper.Data.Interfaces.IUserInterfaces.IUserRepositories;
using EngagetoDapper.Utilities;
using EngagetoEntities.Entities;
using EngagetoEntities.UserEntities.Dtos;
using Mapster;
using Microsoft.Extensions.Logging;

namespace EngagetoDapper.Data.Dapper.Services.ConversationAnalyticsServices
{
    public class ConversationAnalyticsService : IConversationAnalyticsService
    {
        private readonly IGenericRepository _genericRepository;
        private readonly IUnitOfWork _unitOfWork;
        private readonly IConversationAnalyticsRepository _conversationAnalyticsRepository;
        private readonly ILogger<ConversationAnalyticsService> _logger;
        private readonly IUserRepository _userRepository;
        public ConversationAnalyticsService(IGenericRepository _genericRepository,
            IUnitOfWork unitOfWork,
            IConversationAnalyticsRepository analyticsRepository,
            ILogger<ConversationAnalyticsService> logger,
            IUserRepository userRepository)
        {
            this._genericRepository = _genericRepository;
            _unitOfWork = unitOfWork;
            _conversationAnalyticsRepository = analyticsRepository;
            _logger = logger;
            _userRepository = userRepository;
        }
        public async Task<List<ConversationAnalyticsPriceEntity>> GetConversationAnalyticsPriceByYear(string companyId, int year)
        {
            try
            {
                var subscriptions = await _genericRepository.GetByNameAsync<Subscriptions>(
                    new Dictionary<string, string> { { companyId, "CompanyId" } });

                if (subscriptions?.Any(x => x.RenewEndDate.HasValue && x.RenewEndDate.Value.Date >= DateTime.Now.Date) == true)
                {
                    var filters = new Dictionary<string, object>
                        {
                            { "CompanyId", companyId },
                            { "Year", year },
                            { "IsActive", true },
                            { "PlanId",subscriptions.First().PlanId }
                        };

                    var analyticsCost = await _genericRepository.GetByObjectAsync<ConversationAnalyticsPriceEntity>(filters);

                    if (analyticsCost != null && analyticsCost.Any())
                    {
                        return analyticsCost;
                    }
                }

                return new List<ConversationAnalyticsPriceEntity>();
            }
            catch (Exception ex)
            {
                throw new Exception($"Error in fetching the conversation analytics cost: {ex.Message}", ex);
            }
        }
        public async Task<ConversationAnalyticsPriceEntity> GetConversationAnalyticsPriceByCategoryName(string companyId, string categoryName, int year)
        {
            try
            {
                var filters = new Dictionary<string, object>();
                filters.Add("CompanyId", companyId);
                filters.Add("ConversationCategory", categoryName);
                filters.Add("Year", year);
                filters.Add("IsActive", true);

                var analyticsCost = await _genericRepository.GetByObjectAsync<ConversationAnalyticsPriceEntity>(filters);
                if (analyticsCost != null && analyticsCost.Any())
                {
                    return analyticsCost.First();
                }
                else
                    return new();
            }
            catch (Exception ex)
            {
                throw new Exception($"Error in fetching the conversation analytics cost by category name,{ex}");
            }
        }
        public async Task<List<BusinessDetailsMeta>> GetAllBusinessMetaAccountsAsync()
        {
            return await _genericRepository.GetAllAsync<BusinessDetailsMeta>();
        }
        public async Task<bool> SaveConversationAnalyticsDetailsAsync(List<ConversationAnalyticsEntity> conversationAnalyticsDto)
        {
            try
            {
                int limit = 50;
                int count = 0;

                if (!conversationAnalyticsDto.Any())
                    return true;

                var columns = StringHelpers.GetPropertyNames<ConversationAnalyticsEntity>();
                var tableName = StringHelpers.GetTableName<ConversationAnalyticsEntity>();

                _unitOfWork.Begin();

                while (conversationAnalyticsDto.Skip(limit * count).Take(limit).Any())
                {
                    var batch = conversationAnalyticsDto.Skip(limit * count).Take(limit);
                    var result = await _conversationAnalyticsRepository
                        .SaveConversationAnalyticsAsync(tableName, columns, batch, _unitOfWork.Transaction);

                    if (!result)
                    {
                        throw new Exception("An error occurred while saving the data.");
                    }
                    count++;
                }
                _unitOfWork.Commit();
                return true;
            }
            catch (Exception ex)
            {
                _unitOfWork.Rollback();
                throw new Exception("An error occurred while saving the Conversation analytics.", ex);
            }
        }
        public async Task<List<ConversationAnalyticsEntity>> GetAnalyticsDetailsByCompanyIdAsync(string companyId)
        {
            try
            {
                var filters = new Dictionary<string, string>() { { companyId, "CompanyId" } };
                return await _genericRepository.GetByNameAsync<ConversationAnalyticsEntity>(filters);

            }
            catch (Exception ex)
            {
                throw new Exception("An error occurred while getting the Conversation analytics.", ex);
            }
        }
        public async Task<List<ConversationAnalyticsEntity>> GetAnalyticsDetailsByStartAndEndDateAsync(string companyId, DateTime startDate, DateTime endDate, string? conversationCategory)
        {
            try
            {
                return await _conversationAnalyticsRepository.GetAnalyticsDetailsByStartAndEndDateAsync(companyId, startDate, endDate, conversationCategory);
            }
            catch (Exception ex)
            {
                throw new Exception("An error occurred while getting the Conversation analytics by start and end date.", ex);
            }
        }
        public async Task<List<ConversationAnalyticsEntity>> GetConversationAnalyticsByIdAsync(Guid id)
        {
            try
            {
                var filters = new Dictionary<Guid, string>() { { id, "Id" } };
                return await _genericRepository.GetByGuidIdAsync<ConversationAnalyticsEntity>(filters);
            }
            catch (Exception ex)
            {
                throw new Exception("An error occurred while getting the Conversation analytics.", ex);
            }
        }
        public async Task<List<ConversationAnalyticsPriceEntity>> GetDefaultConversationAnalyticsPrice()
        {
            try
            {
                var results = await _conversationAnalyticsRepository.GetDefaultConversationAnalyticPriceAsync<ConversationAnalyticsPriceEntity>(DateTime.Now.Year);
                if (results != null && results.Any())
                {
                    return results.ToList();
                }
                else
                {
                    return (await _conversationAnalyticsRepository.GetDefaultConversationAnalyticPriceAsync<ConversationAnalyticsPriceEntity>()).ToList();
                }
            }
            catch (Exception ex)
            {
                throw new Exception("An error occurred while getting the Conversation analytics.", ex);
            }
        }
        public async Task<bool> SaveConversationCostAsync(ConversationCostDto conversationCostDto)
        {
            ConversationAnalyticsPriceEntity freeConversationAnalyticsEntity = new ConversationAnalyticsPriceEntity()
            {
                Id = Guid.NewGuid(),
                ConversationCategory = conversationCostDto.ConversationCategory.ToString(),
                Cost = conversationCostDto.Cost,
                PlanId = conversationCostDto.PlanId,
                Year = conversationCostDto.Year,
                CompanyId = conversationCostDto.CompanyId,
                IsActive = true,
                PlanType = conversationCostDto.PlanType.ToString(),
            };
            var result = await _genericRepository.SaveAsync(freeConversationAnalyticsEntity);
            return result > 0;

        }
        public async Task<bool> SaveConversationAnalyticsRequestAsync(ConversationAnalyticsJobRequest conversationAnalyticsJob)
        {
            try
            {

                var conversationAnalyticsJobRequestDto = conversationAnalyticsJob.Adapt<ConversationAnalyticsJobRequestDto>();
                var columns = StringHelpers.GetPropertyNames<ConversationAnalyticsJobRequestDto>();

                return await _conversationAnalyticsRepository.SaveConversationAnalyticsJobRequestAsync("ConversationAnalyticsJobRequest", columns, conversationAnalyticsJobRequestDto);
            }
            catch (Exception ex)
            {
                _logger.LogError($"An error occurred while saving the SaveConversationAnalyticsRequest: {ex}");
                return false;
            }
        }
        public async Task<bool> UpdateConversationAnalyticsRequestAsync(ConversationAnalyticsJobRequest conversationAnalyticsJob)
        {
            try
            {
                var filter = new Dictionary<string, object>()
                {
                    {"CompanyId", conversationAnalyticsJob.CompanyId},
                    {"StartDate", conversationAnalyticsJob.StartDate}
                };
                ConversationAnalyticsJobRequestDto conversationAnalyticsJobRequest = conversationAnalyticsJob.Adapt<ConversationAnalyticsJobRequestDto>();
                var columns = StringHelpers.GetPropertyNames<ConversationAnalyticsJobRequestDto>();
                var result = await _conversationAnalyticsRepository.UpdateConversationAnalyticsJobRequestAsync("ConversationAnalyticsJobRequest", columns, conversationAnalyticsJobRequest, filter);
                return result;
            }
            catch (Exception ex)
            {
                _logger.LogError($"An error occurred while updating the SaveConversationAnalyticsRequest: {ex}");
                return false;
            }
        }
        public async Task<DateTime?> GetCurrentDayConversationAnalyticsJobRequestsAsync(string companyId)
        {
            try
            {
                var result = await _conversationAnalyticsRepository
                    .GetConversationAnalyticsJobRequestAsync<ConversationAnalyticsJobRequest>(companyId);

                if (result != null)
                {
                    return result?.StartDate;
                }
                return null;
            }
            catch (Exception ex)
            {
                _logger.LogError($"An error occurred while getting current day conversation analytics job requests: {ex}");
                return null;
            }
        }
        public async Task<ConversationAnalyticsJobRequest?> GetConversationAnalyticsJobRequest(string companyId, DateTime date)
        {
            try
            {
                List<RequestFilterDto> filterDto = new List<RequestFilterDto>()
                {
                    new RequestFilterDto { Key = "CompanyId", Operator = "=", Value = companyId },
                    new RequestFilterDto { Key = "StartDate", Operator = "=", Value = date}
                };
                return (await _genericRepository.GetRecordByRequestFilter<ConversationAnalyticsJobRequest>(filterDto))?.FirstOrDefault();
            }
            catch (Exception ex)
            {
                _logger.LogError($"An error occurred while Get the GetConversationAnalyticsJobRequest: {ex}");
                return null;
            }
        }
        public async Task<bool> SaveConversationCostDetuctionHistoryAsync(List<EngagetoEntities.Entities.Conversations> conversations, string companyId, string? userId = null)
        {
            try
            {
                var subscription = await _userRepository.GetSubscriptionDetailsAsync<SubscriptionDetailsDto>(companyId);
                if (subscription is null || subscription?.PlanName == "Intro")
                    return false;

                conversations = conversations.Where(x => x.TemplateMediaType != null && x.From.ToLower() == companyId.ToLower()).ToList();

                if (!conversations.Any())
                    return false;

                userId = userId ?? string.Empty;
                Guid userGuidId = Guid.NewGuid();
                if (!string.IsNullOrEmpty(userId))
                    Guid.TryParse(userId, out userGuidId);

                var wallet = (await _genericRepository.GetByObjectAsync<UserWalletEntity>(new Dictionary<string, object>()
                {
                    {"CompanyId",companyId}

                })).FirstOrDefault();
                EngagetoEntities.Entities.Template? templateInfo = new();

                foreach (var conversation in conversations)
                {
                    switch (conversation.MessageType)
                    {
                        case EngagetoEntities.Enums.MessageType.Template:
                            var templateId = conversations.FirstOrDefault(x => x.TemplateMediaType != null
                                   && x.MessageType == EngagetoEntities.Enums.MessageType.Template)?.ReferenceId;
                            if (templateId == null)
                                continue;

                            templateInfo = (await _genericRepository
                               .GetByObjectAsync<EngagetoEntities.Entities.Template>(new Dictionary<string, object> { { "TemplateId", templateId } }, "Templates"))?.FirstOrDefault();
                            break;
                        case EngagetoEntities.Enums.MessageType.Campaigns:

                            var campaignId = conversations.FirstOrDefault(x => x.TemplateMediaType != null
                                   && x.MessageType == EngagetoEntities.Enums.MessageType.Campaigns)?.ReferenceId;
                            if (campaignId == null)
                                continue;
                            Guid.TryParse(campaignId, out Guid campaignGuid);
                            var campaign = (await _genericRepository
                               .GetByObjectAsync<Campaign>(new Dictionary<string, object> { { "CampaignId", campaignGuid } }, "Campaigns"))?.FirstOrDefault();

                            if (campaign == null)
                                continue;

                            if (campaign.TemplateId != null && campaign.TemplateId != Guid.Empty)
                            {
                                templateInfo = (await _genericRepository
                                    .GetByObjectAsync<EngagetoEntities.Entities.Template>(new Dictionary<string, object> { { "TemplateId", campaign.TemplateId } }, "Templates"))?.FirstOrDefault();
                            }
                            break;
                    }
                    var price = await GetCosts(companyId);
                    var category = templateInfo?.Category ?? EngagetoEntities.Enums.WATemplateCategory.MARKETING;
                    var cost = (1 * (price.FirstOrDefault(x => x.ConversationCategory.ToLower() == category.ToString().ToLower()
                        && subscription?.PlanId == x.PlanId)?.Cost ?? 0));

                    if (wallet == null)
                        continue;

                    wallet.ExpectedWalletBallance -= cost;
                    var destinationPhoneNumbers = string.Join(",", conversations.Select(x => x.To));
                    ConversationCostDetuctionHistory detuctionHistory =
                        new(companyId,
                            userGuidId,
                            1,
                            string.Empty,
                            category.ToString(),
                            cost,
                            (wallet?.ExpectedWalletBallance ?? 0),
                            destinationPhoneNumbers);


                    var result = await _genericRepository.SaveAsync(detuctionHistory);
                    await _genericRepository.UpdateRecordAsync(wallet, new Dictionary<string, object>() { { "WalletId", wallet.WalletId } });
                }
                return true;
            }
            catch (Exception ex)
            {
                return false;
            }
        }
        #region Helper method
        private List<DateTime> GetHourTimeBeforeDateTime(DateTime date)
        {
            return Enumerable.Range(0, date.Hour + 1)
                .Select(hour => new DateTime(date.Year, date.Month, date.Day, hour, 0, 0))
                .ToList();
        }
        private async Task<List<ConversationAnalyticsPriceEntity>> GetCosts(string businessId)
        {
            var costs = await GetConversationAnalyticsPriceByYear(businessId, DateTime.Now.Year);
            if (!costs.Any())
            {
                costs = await GetDefaultConversationAnalyticsPrice();
            }
            return costs;
        }
        #endregion

    }
}
