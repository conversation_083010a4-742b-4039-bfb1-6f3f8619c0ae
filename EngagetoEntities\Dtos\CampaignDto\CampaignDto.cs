﻿using EngagetoEntities.Enums;
using System.ComponentModel.DataAnnotations;

namespace EngagetoEntities.Dtos.CampaignDto
{
    public class Campaigns
    {
        public string BusinessId { get; set; }
        public Guid? UserId { get; set; }
        public Guid? CampaignId { get; set; }
        public List<string> Audiance { get; set; }
        public string CampaignTitle { get; set; }
        public string? SendTextType { get; set; }
        public Guid? TemplateId { get; set; }
        public Variable? HeaderVariableValue { get; set; }
        public List<Variable>? BodyVariableValues { get; set; }
        public string[]? RedirectUrlVariableValues { get; set; }
        public string? MediaUrl { get; set; }
        public string? MediaFile { get; set; }
        [DataType(DataType.DateTime)]
        public DateTime? ScheduledDateTime { get; set; }
        public SchedulType ScheduleRepeater { get; set; }
        public DateTime? DateSetLive { get; set; }
        public CampaignState State { get; set; }
        public string? Createdby { get; set; }
        public string? AutomationJson {  get; set; }
    }
    public class Variable
    {
        public string? value { get; set; }
        public string? fallBackValue { get; set; }
    }
}




