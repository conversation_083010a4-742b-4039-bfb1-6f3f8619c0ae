﻿using EngagetoEntities.Utilities;
using System.ComponentModel.DataAnnotations.Schema;

namespace EngagetoEntities.Entities
{
    [Table("WebhookEndpointEntities")]
    public class WebhookEndpointEntity
    {
        public Guid Id { get; set; }
        public string CompanyId { get; set; } = default!;
        public Guid? UserId { get; set; }
        public string WebhookUrl { get; set; } = default!;
        public string? ApiKeyName { get; set; } = "API-Key";
        public string? ApiKey { get; set; }
        public string? Description { get; set; }
        public DateTime CreatedAt { get; set; } = DateTime.UtcNow;
        public bool IsActive { get; set; } = true;

        public WebhookEndpointEntity() { }

        public WebhookEndpointEntity(string companyId,
            Guid? userId,
            string webhookUrl,
            string? description = null,
            string? apiKeyName = null,
            string? apiKey = null,
            DateTime? createdAt = null,
            bool isActive = true)
        {
            Id = Guid.NewGuid();
            CompanyId = companyId;
            UserId = userId;
            WebhookUrl = webhookUrl;
            ApiKeyName = apiKeyName ?? "API-Key";
            ApiKey = apiKey;
            Description = description;
            CreatedAt = createdAt ?? StringHelper.GetIndianDateTime();
            IsActive = isActive;
        }
    }
}
