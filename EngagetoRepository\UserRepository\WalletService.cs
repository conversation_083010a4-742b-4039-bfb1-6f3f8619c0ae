﻿using AutoMapper;
using EngagetoContracts.UserContracts;
using EngagetoDapper.Data.Interfaces.CommonInterfaces;
using EngagetoEntities.Dtos.WalletDtos;
using EngagetoEntities.Entities;
using EngagetoEntities.Enums;
using EngagetoEntities.UserEntities.Dtos;
using EngagetoEntities.UserEntities.Models;
using EngagetoEntities.Utilities;
using Mapster;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Logging;

namespace EngagetoRepository.UserRepository
{
    public class WalletService : IWalletService
    {
        private readonly ILogger<WalletService> _logger;
        private readonly ApplicationDBContext _dbContext;
        private readonly IMapper _mapper;
        private readonly ICommonService _commonService;
        public WalletService(ApplicationDBContext dbContext, ILogger<WalletService> logger, IMapper mapper, ICommonService commonService)
        {
            _dbContext = dbContext;
            _logger = logger;
            _mapper = mapper;
            _commonService = commonService;
        }
        public async Task<UserWalletEntity> CreateWalletAsync(UserWalletEntity wallet)
        {
            var userInformation = await _dbContext.Ahex_CRM_Users.FirstAsync(x => x.Id == wallet.UserId || (wallet.CompanyId != null && wallet.CompanyId == x.CompanyId));
            if (userInformation != null)
            {
                wallet.CompanyId = userInformation?.CompanyId;
                wallet.UserId = userInformation.Id;
                var existingWallet = await GetWalletAsync(wallet.CompanyId);
                if (existingWallet == null)
                {
                    await _dbContext.UserWalletEntities.AddAsync(wallet);
                    await _dbContext.SaveChangesAsync();
                    return wallet;
                }
                else
                {
                    _logger.LogInformation($"A wallet already exists for this user: {wallet.UserId}");
                    return existingWallet;
                }
            }
            throw new Exception("Not Found the companyId for this user");
        }

        public async Task<bool> DeleteWalletAsync(string companyId)
        {
            var wallet = await GetWalletAsync(companyId);
            if (wallet == null)
            {
                return false;
            }
            else
            {
                wallet.IsDeleted = true;
                await UpdateWalletAsync(wallet);
                return false;
            }
        }

        public async Task<WalletAndSubscription?> GetWalletAndSubscriptionPlanAsync(string companyId, Guid? userId = null)
        {
            try
            {
                bool isPlatform = RoleConstants.CompanyId == companyId;
                if (isPlatform)
                {
                    await UpdatePlatformSubscriptionAsync(companyId, userId ?? Guid.Empty);
                }
                var planDiscounts = await _commonService.GetPlanDiscountsAsync();
                var rawData = await (
                    from s in _dbContext.Subscriptions
                    where s.CompanyId == companyId
                    join pw in _dbContext.PaymentWalletDetails
                    on s.Id equals pw.SubscriptionId
                    where pw.Status == "Paid"
                    join uw in _dbContext.UserWalletEntities
                    on s.CompanyId equals uw.CompanyId into uwGroupJoin
                    from uw in uwGroupJoin.DefaultIfEmpty()
                    join p in _dbContext.PlanEntities
                    on s.PlanId equals p.Id
                    select new
                    {
                        Subscription = s,
                        UserWallet = uw,
                        Plan = p.Clone() as PlanEntities,
                        PaymentDetail = pw
                    }).ToListAsync();

                var result = rawData
                    .GroupBy(x => new { x.Subscription, x.UserWallet, x.Plan })
                    .Select(g => new
                    {
                        Subscription = g.Key.Subscription,
                        UserWallet = g.Key.UserWallet,
                        Plan = g.Key.Plan,
                        PaymentDetails = g.Select(x => x.PaymentDetail).ToList()
                    })
                    .FirstOrDefault();


                if (result == null)
                {
                    return null;
                }

                DateTime expireDate = result.Subscription?.RenewEndDate ?? result.Subscription?.PlanEndDate ?? DateTime.MinValue;
                bool isActive = expireDate.Date >= StringHelper.GetIndianDateTime().Date;

                var samePlanWithClientsCount = await _dbContext.Subscriptions
                    .Where(x => x.PlanId == result.Plan.Id)
                    .CountAsync();

                int totalClients = _dbContext.Ahex_CRM_BusinessDetails.Count();
                decimal planDiscountValue = 0;


                switch (result.Subscription.DurationType ?? "Monthly")
                {
                    case nameof(PlanType.Monthly):
                        planDiscountValue = GetPlanDiscountValue(result.Plan, planDiscounts, PlanType.Monthly);
                        result.Plan.Price = Math.Round((result.Plan.Price - planDiscountValue) ?? 0);
                        break;
                    case nameof(PlanType.Quarterly):
                        planDiscountValue = GetPlanDiscountValue(result.Plan, planDiscounts, PlanType.Quarterly);
                        result.Plan.Price = Math.Round(((result.Plan.Price * 3) - planDiscountValue) ?? 0);
                        break;
                    case nameof(PlanType.Annually):
                        planDiscountValue = GetPlanDiscountValue(result.Plan, planDiscounts, PlanType.Annually);  // Changed from Quarterly to Annually
                        result.Plan.Price = Math.Round(((result.Plan.Price * 12) - planDiscountValue) ?? 0);
                        break;
                }

                return new WalletAndSubscription
                {
                    WalletBalance = result.UserWallet?.Balance ?? 0,
                    subscriptionPlan = new SubscriptionPlanDto
                    {
                        TotalClients = totalClients,
                        SamePlanWithClients = samePlanWithClientsCount,
                        PlanId = result.Subscription.PlanId,
                        PlanName = result.Plan?.PlanName ?? string.Empty,
                        IsActive = isActive,
                        PlanEndDate = expireDate,
                        PlanType = result.Subscription?.DurationType ?? string.Empty,
                        CurrentSubscriptionPlanAmount = result.Plan.Price ?? 0,
                        PaymentDetails = result.PaymentDetails?.Adapt<List<PaymentDetailsDto>>()
                    }
                };
            }
            catch (Exception ex)
            {
                throw new Exception($"Error fetching wallet and subscription plan for company {companyId}: {ex.Message}", ex);
            }
        }


        public async Task<UserWalletEntity?> GetWalletAsync(string companyId)
        {
            return await _dbContext.UserWalletEntities.FirstOrDefaultAsync(x => x.CompanyId == companyId && !x.IsDeleted) ?? null;
        }

        public async Task<UserWalletEntity> UpdateWalletAsync(UserWalletEntity walletEntity)
        {
            var wallet = await GetWalletAsync(walletEntity.CompanyId);
            if (wallet == null)
            {
                _logger.LogInformation($"Wallet not found for this company: {walletEntity.CompanyId}");
                throw new Exception("Wallet Not Found");
            }
            else
            {
                _mapper.Map(walletEntity, wallet);
                await _dbContext.SaveChangesAsync();
                return walletEntity;
            }
        }

        private static decimal GetPlanDiscountValue(PlanEntities plan, List<PlanDiscountEntity>? planDiscounts, PlanType planType)
        {
            var discount = planDiscounts?
                .FirstOrDefault(x => x.PlanId == plan.Id);

            if (planDiscounts == null || !planDiscounts.Any())
                return 0;

            if (discount == null)
                return 0;

            decimal discountValue = 0;
            decimal planPrice = plan.Price ?? 0;
            if (discount.DiscountType == DiscountType.Amount.ToString())
            {
                discountValue = planType switch
                {
                    PlanType.Weekly => discount.WeeklyDiscount ?? 0,
                    PlanType.Monthly => discount.MonthlyDiscount ?? 0,
                    PlanType.Quarterly => discount.QuarterlyDiscount ?? 0,
                    PlanType.Annually => discount.AnnuallyDiscount ?? 0,
                    _ => 0,
                };
            }
            else
            {
                discountValue = planType switch
                {
                    PlanType.Weekly => (discount.WeeklyDiscount ?? 0) != 0 ?
                        (planPrice / 4) * (discount.WeeklyDiscount ?? 0) / 100 : 0,
                    PlanType.Monthly => (discount.MonthlyDiscount ?? 0) != 0 ?
                        planPrice * (discount.MonthlyDiscount ?? 0) / 100 : 0,
                    PlanType.Quarterly => (discount.QuarterlyDiscount ?? 0) != 0 ?
                        (planPrice * 3) * (discount.QuarterlyDiscount ?? 0) / 100 : 0,
                    PlanType.Annually => (discount.AnnuallyDiscount ?? 0) != 0 ?
                        planPrice * 12 * (discount.AnnuallyDiscount ?? 0) / 100 : 0,
                    _ => 0,
                };
            }
            return discountValue;
        }

        private async Task UpdatePlatformSubscriptionAsync(string companyId, Guid currentUserId)
        {
            var plan = await _dbContext.PlanEntities
                .OrderByDescending(x => x.Price)
                .FirstOrDefaultAsync();

            var subscription = await _dbContext.Subscriptions
                .FirstOrDefaultAsync(x => x.CompanyId == companyId);

            if (subscription == null)
            {
                subscription = new Subscriptions
                {
                    CompanyId = companyId,
                    DurationType = PlanType.Annually.ToString(),
                    PlanId = plan.Id,
                    PlanStartDate = DateTime.Now,
                    PlanEndDate = DateTime.Now.AddYears(1),
                    Status = "Paid",
                    UserId = currentUserId.ToString(),
                    CreatedAt = DateTime.Now,
                    CreatedBy = currentUserId.ToString()
                };

                _dbContext.Subscriptions.Add(subscription);
                await _dbContext.SaveChangesAsync(); 
                await SavePaymentWalletAsync(companyId, currentUserId, plan.Id, subscription.Id);
            }
            else if ((subscription.RenewEndDate?.Date ?? subscription.PlanEndDate.Date) <= DateTime.Now.Date)
            {
                subscription.PlanId = plan.Id;
                subscription.RenewStartDate = DateTime.Now;
                subscription.RenewEndDate = DateTime.Now.AddYears(1);
                subscription.UpdatedAt = DateTime.Now;
                subscription.UpdatedBy = currentUserId.ToString();

                _dbContext.Subscriptions.Update(subscription);
                await _dbContext.SaveChangesAsync(); 
                await SavePaymentWalletAsync(companyId, currentUserId, plan.Id, subscription.Id);
            }
        }

        private async Task SavePaymentWalletAsync(string companyId, Guid userId, int planId, int subscriptionId)
        {
            var paymentWalletDetails = new PaymentWalletDetail
            {
                CompanyId = companyId,
                UserId = userId,
                PlanId = planId,
                DurationType = PlanType.Annually.ToString(),
                OrderId = null,
                OrderAmount = 0,
                Currency = "INR",
                Status = "Paid",
                CreatedBy = userId,
                CreatedAt = DateTime.Now,
                PlanStartDate = DateTime.Now,
                PlanEndDate = DateTime.Now.AddYears(1),
                SubscriptionId = subscriptionId
            };

            await _dbContext.PaymentWalletDetails.AddAsync(paymentWalletDetails);
            await _dbContext.SaveChangesAsync();
        }

        public async Task<TenantWalletDto?> GetTenantWalletAsync(string tenantId,DateTime fromDate,DateTime toDate)
        {
            var businessDetails = await _dbContext.Ahex_CRM_BusinessDetails.FirstOrDefaultAsync(x => x.TenantId == tenantId);
            if (businessDetails == null) return null;
            var walletDetails = await _dbContext.UserWalletEntities
                .Where(x => x.CompanyId == businessDetails.Id.ToString()) 
                .Select(i => new TenantWalletDto
                {
                    Balance = i.Balance,
                    TenantId = tenantId,
                    ExpectedBalance = i.ExpectedWalletBallance ?? 0
                })
                .FirstOrDefaultAsync();

            if(walletDetails == null) return null;

            var transactions = await _dbContext.ConversationAnalyticsEntities.Where(x => x.CompanyId == businessDetails.Id.ToString() && x.StartDate.Date >= fromDate.Date && x.StartDate.Date <= toDate.Date)
                .GroupBy(i => i.ConversationCategory)
                .Select(g => new
                {
                    ConversationCategory = g.Key,
                    TotalCost = g.Sum(x => x.Cost),        
                    ConversationCount = g.Sum(x => x.Conversation)
                })
                .ToListAsync();

            var marketing = transactions.FirstOrDefault(x => x.ConversationCategory == ConversationCategoriesEnum.MARKETING.ToString());
            var utilty = transactions.FirstOrDefault(x => x.ConversationCategory == ConversationCategoriesEnum.UTILITY.ToString());
            var authentication = transactions.FirstOrDefault(x => x.ConversationCategory == ConversationCategoriesEnum.AUTHENTICATION.ToString());
            walletDetails.Marketing = new() { Category = ConversationCategoriesEnum.MARKETING.ToString(), Cost = marketing?.TotalCost ?? 0,Count = marketing?.ConversationCount ?? 0};
            walletDetails.Utility = new() { Category = ConversationCategoriesEnum.UTILITY.ToString(), Cost = utilty?.TotalCost ?? 0, Count = utilty?.ConversationCount ?? 0 };
            walletDetails.Authentication = new() { Category = ConversationCategoriesEnum.AUTHENTICATION.ToString(), Cost = authentication?.TotalCost ?? 0, Count = authentication?.ConversationCount ?? 0 };
            return walletDetails;
        }
        
    }
}
