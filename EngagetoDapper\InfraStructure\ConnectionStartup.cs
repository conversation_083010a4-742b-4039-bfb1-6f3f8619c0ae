﻿using EngagetoDapper.Data.Connections;
using Microsoft.Data.SqlClient;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.DependencyInjection;
using System.Data;


namespace EngagetoDapper.InfraStructure
{
    public static class ConnectionStartup
    {
        public static IServiceCollection AddConnection(this IServiceCollection services, IConfiguration config)
        {
            // Register DapperConnectionFactory
            var connectionString = config["ConnectionStrings:ConnStr"];
            services.AddSingleton<ISqlConnectionFactory,SqlConnectionFactory>();
            services.AddScoped<IDbConnection>(sp =>
            {
                var connection = new SqlConnection(connectionString);
                // 🔧 FIX: Don't open connection in registration
                return connection;
            })
            .AddScoped<IUnitOfWork>(sp =>
            {
                var dbConnection = sp.GetRequiredService<IDbConnection>();
                return new UnitOfWork(dbConnection);
            });
            // 🗑️ REMOVED: DapperConnectionFactory - unnecessary complexity
            // Services now use IUnitOfWork directly for better transaction handling
            // 🔧 FIX: Removed duplicate UnitOfWork registration
            return services;
        }
    }
}
