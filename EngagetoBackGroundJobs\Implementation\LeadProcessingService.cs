﻿using EngagetoDapper.Data.Interfaces.ILogInterfaces;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Hosting;
using Newtonsoft.Json;

namespace EngagetoBackGroundJobs.Implementation
{
    using EngagetoContracts.WebhookContracts.ReceivedNotification;
    using EngagetoEntities.Dtos.IntegrationDtos;
    using EngagetoEntities.Enums;
    using System;
    using System.Threading;
    using System.Threading.Channels;
    using System.Threading.Tasks;

    public class LeadProcessingService : BackgroundService
    {
        private readonly IServiceScopeFactory _serviceScopeFactory;
        private readonly Channel<IntegrationAccountRecord> _leadChannel;
        private const int MaxConcurrency = 5; // Max concurrent tasks

        public LeadProcessingService(IServiceScopeFactory serviceScopeFactory)
        {
            _serviceScopeFactory = serviceScopeFactory;

            // Create an unbounded channel with single writer and reader capabilities
            _leadChannel = Channel.CreateBounded<IntegrationAccountRecord>(new BoundedChannelOptions(MaxConcurrency)
            {
                FullMode = BoundedChannelFullMode.Wait, // Wait when channel is full
                SingleWriter = false,
                SingleReader = false
            });
        }

        /// <summary>
        /// Adds lead data to the processing channel
        /// </summary>
        public async Task SetLeadDataAsync(IntegrationAccountRecord record, CancellationToken cancellationToken)
        {
            await _leadChannel.Writer.WriteAsync(record, cancellationToken);
        }

        protected override async Task ExecuteAsync(CancellationToken stoppingToken)
        {
            var processingTasks = new Task[MaxConcurrency];

            // Start the worker tasks
            for (int i = 0; i < MaxConcurrency; i++)
            {
                processingTasks[i] = Task.Run(() => ProcessLeadsAsync(stoppingToken), stoppingToken);
            }

            // Wait for all workers to complete
            await Task.WhenAll(processingTasks);
        }

        private async Task ProcessLeadsAsync(CancellationToken stoppingToken)
        {
            while (await _leadChannel.Reader.WaitToReadAsync(stoppingToken))
            {
                while (_leadChannel.Reader.TryRead(out var record))
                {
                    try
                    {
                        using var scope = _serviceScopeFactory.CreateScope();
                        var leadService = scope.ServiceProvider.GetRequiredService<ILeadgenIntegrationService>();
                        var logService = scope.ServiceProvider.GetRequiredService<ILogHistoryService>();

                        bool result = await leadService.ProcessIntegrationForLeadAsync(record);

                        await logService.SaveInformationLogHistoryAsyn(
                            "LeadProcessingService",
                            JsonConvert.SerializeObject(record),
                            result.ToString(),
                            $"API Call Result: {result}"
                        );
                    }
                    catch (Exception ex)
                    {
                        // Log the error properly
                        using var scope = _serviceScopeFactory.CreateScope();
                        var logService = scope.ServiceProvider.GetRequiredService<ILogHistoryService>();
                        await logService.SaveErrorLogHistoryAsyn(
                            "LeadProcessingService",
                            "",
                            "",
                            ex.Message,
                            ex.StackTrace
                        );
                    }
                }
            }
        }
    }
}

