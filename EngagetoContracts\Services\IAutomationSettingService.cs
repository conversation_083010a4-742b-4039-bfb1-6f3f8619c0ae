﻿using EngagetoEntities.Dtos.AutomationDtos;
using EngagetoEntities.Entities;

namespace EngagetoContracts.Services
{
    public interface IAutomationSettingService: ITransientServiceWithScoped
    {
        Task<int> SaveAutomationSettingAsync(BaseAutomationSettingDto settingDto);
        Task<ViewAutomationSettingDto> GetAutomationSettingAsync(string businessId);
        Task<ViewAutomationSettingDto> UpdateAutomationSettingAsync(EditAutomationSettingDto settingDto);
        Task SendAutomationMessageAsync(string businessId,Contacts contact, Conversations conv, Guid? userId);
    }
}
