﻿using EngagetoContracts.AutomationContracts;
using Dapper;
using System.Data;
using EngagetoEntities.Entities;

namespace EngagetoRepository.AutomationRepository
{
    public class AutoReplyAutomationRepostry : IAutoReplyAutomationRepostry
    {
        private readonly IUnitOfWork _unitOfWork;
        public AutoReplyAutomationRepostry(IUnitOfWork unitOfWork)
        {
            _unitOfWork = unitOfWork;
        }
        public async Task<T?> SaveAutoReplyAutomationAsync<T>(AutoReplyAutomationEntity autoReplyAutomation)
        {
            return await _unitOfWork.Connection.ExecuteScalarAsync<T>("AutoReplyAutomationEntity_Save",
                             new
                             {
                                 autoReplyAutomation.Id,
                                 autoReplyAutomation.CompanyId,
                                 autoReplyAutomation.UserId,
                                 autoReplyAutomation.Input,
                                 autoReplyAutomation.InputVariation,
                                 autoReplyAutomation.AutoReplyType,
                                 autoReplyAutomation.IsDeleted,
                                 autoReplyAutomation.WorkflowName
                             },
                             _unitOfWork.Transaction,
                             commandType: CommandType.StoredProcedure);
        }
    }
}
