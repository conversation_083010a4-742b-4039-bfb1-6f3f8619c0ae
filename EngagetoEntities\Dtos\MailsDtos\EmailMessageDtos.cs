﻿using Microsoft.AspNetCore.Http;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace EngagetoEntities.Dtos.MailsDtos
{
    public class EmailMessageDtos
    {
        public string Subject { get; set; }
        public string Body { get; set; }
        public List<IFormFile>?Attachments { get; set; } = new List<IFormFile>();
        public List<string> To { get; set; } = new List<string>();
        public List<string>? Cc { get; set; } = new List<string>();
        public List<string>? Bcc { get; set; } = new List<string>();
       
    }
}
