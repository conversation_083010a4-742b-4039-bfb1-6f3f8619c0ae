﻿namespace EngagetoEntities.Dtos.WebhookDtos
{
    public static class WAMessageDto
    {
        public static string GetWAMessage(string phoneNumber, string text)
        {
            return string.Format(@"{{
                ""messaging_product"": ""whatsapp"",    
                ""recipient_type"": ""individual"",
                ""to"": ""{0}"",
                ""type"": ""text"",
                ""text"": {{
                    ""preview_url"": false,
                    ""body"": ""{1}""
                }}
            }}", phoneNumber, text);
        }
        public static string GetReplyWAMessage(string messageId, string phoneNumber, string text)
        {
            return string.Format(@"{{
                    ""messaging_product"": ""whatsapp"",
                    ""recipient_type"": ""individual"",
                    ""to"": ""{0}"",
                    ""context"": {{
                        ""message_id"": ""{1}""
                    }},
                    ""type"": ""text"",
                    ""text"": {{
                        ""preview_url"": false,
                        ""body"": ""{2}""
                    }}
                }}", phoneNumber, messageId, text);
        }
        public static string GetWAMediaPayload(string type, string phoneNumber, string url, string text)
        {
            return string.Format(@"{{
              ""messaging_product"": ""whatsapp"",
              ""recipient_type"": ""individual"",
              ""to"": ""{0}"",
              ""type"": ""{1}"",
              ""{1}"": {{
                ""link"": ""{2}"",
                ""caption"": ""{3}""
              }}
            }}", phoneNumber, type, url, text);
        }
        public static string GetReplyWAMediaPayload(string type, string waId, string phoneNumber, string url, string text)
        {
            return string.Format(@"{{
                ""messaging_product"": ""whatsapp"",
                ""recipient_type"": ""individual"",
                ""to"": ""{0}"",
                ""context"": {{
                    ""message_id"": ""{1}""
                }},
                ""type"": ""{2}"",
                ""{2}"": {{
                    ""link"": ""{3}"",
                    ""caption"": ""{4}""
                }}
            }}", phoneNumber, waId, type, url, text);
        }
    }
}