﻿using EngagetoDapper.Data.Interfaces.GenericInterfaces;
using EngagetoDapper.Data.Interfaces.IUserInterfaces.IUserServices;
using EngagetoDapper.Utilities;
using EngagetoEntities.Entities;
using EngagetoEntities.UserEntities.Dtos;
using Mapster;
using System.Text;

namespace EngagetoDapper.Data.Dapper.Services.UserServices
{
    public class ApiKeyService : IApiKeyService
    {
        private readonly IGenericRepository _genericRepository;
        public ApiKeyService(IGenericRepository genericRepository)
        {
            _genericRepository = genericRepository;
        }
        public async Task<bool> SaveApiKeyAsync(string compayId)
        {
            try
            {
                Guid Id;
                if (!Guid.TryParse(compayId, out Id))
                    throw new Exception("CompanyId is not valid");

                var businessDetails = (await _genericRepository
                    .GetByObjectAsync<Ahex_CRM_BusinessDetails>(new Dictionary<string, object>()
                        {
                            { "Id",Id }
                        }))?.FirstOrDefault()
                        ?? throw new Exception("Not found company");

                var ApiKeyDetails = await GetApiDetailsAsync(compayId);
                if (ApiKeyDetails != null)
                    throw new Exception("The API key for this company has already been generated.");

                ApiKeyEntity apiKeyEntity = new ApiKeyEntity(compayId, businessDetails?.BusinessName ?? string.Empty);
                var result = await _genericRepository.SaveAsync(apiKeyEntity);
                return result > 0;
            }
            catch (Exception ex)
            {
                throw;
            }
        }

        public async Task<ApiKeyEntity?> GetApiDetailsAsync(string companyId,string? secretKey = null)
        {
            try
            {
                var filter = new Dictionary<string, object>()
                    {
                        {"CompanyId",companyId },
                        {"IsActive", true }
                    };
                if(secretKey != null)
                    filter.Add("SecretKey", secretKey);

                return (await _genericRepository.GetByObjectAsync<ApiKeyEntity>(filter))?.FirstOrDefault();
            }
            catch (Exception ex)
            {
                throw;
            }
        }
        public async Task<ApiKeyEntity?> UpdateApiKeyAsync(string companyId)
        {
            try
            {
                var columns = StringHelpers.GetPropertyNames<ApiKeyDto>();
                var tableName = StringHelpers.GetTableName<ApiKeyEntity>();

                var apiKeyDetails = await GetApiDetailsAsync(companyId)
                    ?? throw new Exception("Api key Details not found.");

                var apiDto = apiKeyDetails.Adapt<ApiKeyDto>();
                apiDto.SecretKey = GenerateApiKey(Guid.NewGuid().ToString());
                apiDto.Key = GenerateApiKey($"{companyId},{apiDto.SecretKey}");
                await _genericRepository.UpdateRecordAsync<ApiKeyDto>(tableName, columns, apiDto, new Dictionary<string, object>()
                {
                    {"CompanyId", companyId},
                    {"IsActive", true }
                });
                return await GetApiDetailsAsync(companyId);
            }
            catch (Exception ex)
            {
                throw new Exception(ex.Message);
            }
        }

        #region Helper
        private string GenerateApiKey(string key)
        {
            byte[] textBytes = Encoding.UTF8.GetBytes(key);

            string base64String = Convert.ToBase64String(textBytes);
            return base64String;
        }
        #endregion
    }
}
