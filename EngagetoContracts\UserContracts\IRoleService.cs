﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using EngagetoEntities.Entities;

namespace EngagetoContracts.UserContracts
{
    public interface IRoleService
    {
        Task<List<Role>> GetAllRolesAsync(string companyId, string searchQuery, string sortBy, bool isSortAscending);
        Task<Role> GetRoleByIdAsync(Guid roleId);
        Task<bool> AddRoleAsync(RoleDto roleDto, Guid currentUserId);
        Task<bool> UpdateRoleAsync(Guid roleId, RoleDto updatedRoleDto, Guid currentUserId);
        Task<bool> DeleteRoleAsync(Guid roleId);
    }
}
