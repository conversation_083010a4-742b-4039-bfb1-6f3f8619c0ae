﻿namespace EngagetoEntities.Utilities
{
    public static class MetaApi
    {
        private const string version21 = "v21.0";
        private const string BaseApiUrl = "https://graph.facebook.com";
        private const string CreateTemplateApiName = "message_templates";
        private const string CreateAuthenticationTemplateApiName = "upsert_message_templates";
        private const string SendMessageApiName = "messages";

        public static string GetBaseUrl() => $"{BaseApiUrl}/{version21}/";
        public static string GetCreateTemplateUrl(string businessNumber) => $"{BaseApiUrl}/{version21}/{businessNumber}/{CreateTemplateApiName}";
        public static string GetCreateAuthenticationTemplateUrl(string businessNumber) => $"{BaseApiUrl}/{version21}/{businessNumber}/{CreateAuthenticationTemplateApiName}";
        //  public static string GetCreateTemplateUrl(string businessNumber) => $"{BaseApiUrl}/{version21}/{businessNumber}/{CreateTemplateApiName}";
        public static string GetSendTemplateUrl(string businessNumber) => $"{BaseApiUrl}/{version21}/{businessNumber}/{SendMessageApiName}";
        public static string GetUpdateTemplateUrl(string templateId) => $"{BaseApiUrl}/{version21}/{templateId}";
        public static string GetHealthStatusUrl(string accountId) => $"{BaseApiUrl}/{version21}/{accountId}?fields=health_status";
        public static string GetMessageTemplatePreview(string accountId)
        {
            return $"{BaseApiUrl}/{version21}/{accountId}/message_template_previews";
        }
        public static string GetAccountDetailsUrl(string accountId) => $"{BaseApiUrl}/{version21}/{accountId}?fields=id,name,phone_numbers";
        public static string GetCreateAuthTemplateUrl(string accountId) => $"{BaseApiUrl}/{version21}/{accountId}/upsert_message_templates";
        public static string GetMetaTemplateUrl(string whatsappBusinessId) => $"{BaseApiUrl}/{version21}/{whatsappBusinessId}/message_templates";
        public static string GetMetaBlockUnblockUrl(string phoneNumberId) => $"{BaseApiUrl}/{version21}/{phoneNumberId}/block_users";
    }
}
