// EngagetoEntities/Entities/CustomerWorkflowTracker.cs
using EngagetoEntities.Enums;
using System;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using System.Text.Json;
using System.Text.Json.Nodes;

namespace EngagetoEntities.Entities
{
    [Table("CustomerWorkflowTrackers")]
    public class CustomerWorkflowTracker : BaseEntity
    {
        [Key]
        public Guid Id { get; set; }
        public Guid? BusinessId { get; set; }
        public Guid ContactId { get; set; }
        public Guid? WorkflowId { get; set; }
        public Guid? NodeId { get; set; }
        public NodeType NodeType { get; set; }
        public string? CustomerReponse { get; set; }
        public string? AttributeName { get; set; }    
        public DateTime?  CompletedAt { get; set; }
    }
}