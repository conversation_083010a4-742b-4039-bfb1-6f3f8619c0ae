﻿using AutoMapper;
using EngagetoEntities.Entities;
using EngagetoEntities.UserEntities.Dtos;

namespace EngagetoEntities.Mapping
{
    public class UserWalletNotificationProfile : Profile
    {
        public UserWalletNotificationProfile()
        {
            CreateMap<WalletNotification, WalletNotificationDto>()
             .ForMember(dest => dest.Id, opt => opt.MapFrom(src => src.Id))
             .ForMember(dest => dest.Category, opt => opt.MapFrom(src => src.Category.ToString()))
             .ForMember(dest => dest.WalletId, opt => opt.MapFrom(src => src.WalletId))
             .ForMember(dest => dest.TransactionId, opt => opt.MapFrom(src => src.TransactionId))
             .ForMember(dest => dest.IsRead, opt => opt.MapFrom(src => src.IsRead))
             .ForMember(dest => dest.Amount, opt => opt.MapFrom(src => src.Amount))
             .ForMember(dest => dest.Title, opt => opt.MapFrom(src => src.Title))
             .ForMember(dest => dest.Description, opt => opt.MapFrom(src => src.Description))
             .ForMember(dest => dest.Type, opt => opt.MapFrom(src => src.Type.ToString()));

        }
    }
}
