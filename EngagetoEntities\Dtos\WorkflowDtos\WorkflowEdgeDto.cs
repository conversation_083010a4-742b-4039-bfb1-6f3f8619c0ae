﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace EngagetoEntities.Dtos.WorkflowDtos
{
    public class WorkflowEdgeDto
    {
        public Guid Id { get; set; }
        public Guid SourceNodeId { get; set; }
        public string? SourceHandle { get; set; }
        public string? TargetHandle { get; set; }
        public string? Type { get; set; }
        public List<WorkflowEdgeTargetDto> Targets { get; set; } = new List<WorkflowEdgeTargetDto>();
    }

    public class WorkflowEdgeTargetDto
    { 
        public Guid TargetNodeId { get; set; }
        public string? Condition { get; set; }
    }

    public class ViewWorkflowEdgeDto : WorkflowEdgeDto 
    {
        public Guid Id { get; set; }
        public WorkflowNodeDto? Source { get; set; }
        public List<WorkflowEdgeTargetDto> WorkflowEdgeTargetNodes { get; set; } = default!;
    }
}
