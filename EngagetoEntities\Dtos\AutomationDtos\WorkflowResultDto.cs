﻿using EngagetoEntities.Enums;

namespace EngagetoEntities.Dtos.AutomationDtos
{
    public class WorkflowResultDto
    {
        public Guid Id { get; set; }
        public string WorkflowName { get; set; }
        public Guid CompanyId { get; set; }
        public Guid? UserId { get; set; }
        public string Title { get; set; }
        public int? Step { get; set; }
        public WorkflowResponseType FlowResponseType { get; set; }
        public string? WorkflowButtons { get; set; }
        public Guid? WorkflowListId { get; set; }
        public string? ListName { get; set; }
        public string? ButtonName { get; set; }
        public string? Inputs { get; set; }
        public Guid? VeriableId { get; set; }
        public int? Index { get; set; }
        public VariableType? Type { get; set; }
        public string? Veriable { get; set; }
        public string? Value { get; set; }
        public string? FallbackValue { get; set; }
        public ReferenceTableType ReferenceTableType { get; set; }
        public Guid? WorkflowId { get; set; }
        public Guid? CustomerResponseId { get; set; }
        public Guid? VeriableNameEntityId { get; set; }
        public string? ResponseMessage { get; set; }
        public string? VeriableName { get; set; }
        public string? WebhookTriggerUrl { get; set; }
        public string? WebhookTriggerBody { get; set; }
        public string? WebhookTriggerHeader { get; set; }
        public string? WebhookTriggerHttpMethod { get; set; }
        public string? DefaultErrorResponse { get; set; }
        public int? StepType { get; set; }
    }
}
