﻿using EngagetoEntities.Enums;

namespace EngagetoEntities.Dtos.BusinessDetailsDtos
{
    public class TenantDtos
    {
        public Guid BusinessId { get; set; }
        public string? TenantId { get; set; }
        public string? BusinessName { get; set; }
        public string? BusinessCategory { get; set; }
        public bool Status { get; set; }
        public string? CompanyLogoLink { get; set; }
        public DateTime? CreatedAt { get; set; }
        public decimal? Balance { get; set; }
        public string? PhoneNumber { get; set; }
        public QualityScore? QualityRating { get; set; }
        public long? MessageLimit { get; set; }
        public string? Tier { get; set; }
        public int? ConversationCount { get; set; }
        public decimal? Cost { get; set; }
    }
}
