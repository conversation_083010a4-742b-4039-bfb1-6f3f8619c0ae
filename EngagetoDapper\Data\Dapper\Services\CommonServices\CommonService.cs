﻿using EngagetoDapper.Data.Connections;
using EngagetoDapper.Data.Interfaces.CommonInterfaces;
using EngagetoDapper.Data.Interfaces.GenericInterfaces;
using EngagetoEntities.DdContext;
using EngagetoEntities.Dtos.AutomationDtos;
using EngagetoEntities.Entities;
using Mapster;
using Microsoft.EntityFrameworkCore;
using Newtonsoft.Json;
using OfficeOpenXml;

namespace Engageto.Dapper.Data.Dapper.Services.CommonServices
{
    public class CommonService : ICommonService
    {
        private readonly IGenericRepository _genericRespostry;
        private readonly IUnitOfWork _unitOfWork;
        private readonly ApplicationDbContext _contactsDbContext;

        public CommonService(ApplicationDbContext contactsDbContext,
            IGenericRepository genericRespostry,
            IUnitOfWork unitOfWork)
        {
            _genericRespostry = genericRespostry;
            _unitOfWork = unitOfWork;
            _contactsDbContext = contactsDbContext;
        }
        public async Task<List<AutomationCustomerResponseEntity>> GetAutomationCustomerResponseAsync(Guid companyId, Guid? userId)
        {
            try
            {
                Dictionary<Guid, string> filters = new Dictionary<Guid, string>();
                filters.Add(companyId, "CompanyId");
                if (userId != null)
                    filters.Add(userId ?? Guid.Empty, "UserId");
                var results = await _genericRespostry.GetByGuidIdAsync<AutomationCustomerResponseEntity>(filters);
                return results.Where(x => !x.IsDeleted).ToList();
            }
            catch (Exception)
            {
                throw;
            }
        }
        public async Task<MemoryStream> GenerateWorkflowResponseListByName(string WorkflowName, Guid CompanyId)
        {
            var workflowHistory = await _contactsDbContext.WorkflowResponseHistoryEntities
                .Where(m => m.WorkflowName == WorkflowName && m.CompanyId == CompanyId)
                .OrderBy(m => m.CreatedAt)
                .ToListAsync();  // Load into memory

            ExcelPackage.LicenseContext = LicenseContext.NonCommercial;

            // Prepare Excel package
            using (var package = new ExcelPackage())
            {
                var worksheet = package.Workbook.Worksheets.Add("Workflow Data");

                // Add static header columns for Serial Number, Phone Number, Customer Name, Workflow Start Time
                worksheet.Cells[1, 1].Value = "Serial Number";
                worksheet.Cells[1, 2].Value = "Phone Number";
                worksheet.Cells[1, 3].Value = "Customer Name";
                worksheet.Cells[1, 4].Value = "Workflow Start Time (UTC)";

                if (workflowHistory == null || !workflowHistory.Any())
                {
                    var emptyStream = new MemoryStream();
                    package.SaveAs(emptyStream);
                    emptyStream.Position = 0;
                    return emptyStream;
                }

                // Group workflow history by ContactId
                var groupedData = workflowHistory.GroupBy(m => m.ContactId);

                // Get the maximum step number to generate columns for questions and answers
                int maxStep = workflowHistory.Max(m => m.Step ?? 0);
                int col = 5;
                for (int i = 1; i <= maxStep; i++)
                {
                    worksheet.Cells[1, col].Value = $"Step {i} Question";
                    worksheet.Cells[1, col + 1].Value = $"Step {i} Answer";
                    col += 2; // Two columns for each step
                }

                int row = 2; // Start from the second row to add data
                int serialNumber = 1;

                // Load contacts and workflows into memory to prevent multiple open readers
                var contacts = await _contactsDbContext.Contacts
                    .Where(m => m.BusinessId == CompanyId)
                    .ToListAsync();  // Load contacts into memory

                var workflows = await _contactsDbContext.WorkflowEntities
                .Where(m => m.CompanyId == CompanyId)
                .ToDictionaryAsync(m => m.Id, m => m.Title ?? "Workflow Webhook Trigger");

                foreach (var group in groupedData)
                {
                    var contactHistory = group.OrderBy(h => h.CreatedAt).ToList();
                    var customer = contacts.FirstOrDefault(m => m.ContactId == group.Key);
                    var customerName = customer?.Name;
                    var phoneNumber = customer != null ? customer.CountryCode + customer.Contact : null;

                    int? previousStep = null;

                    foreach (var stepRecord in contactHistory)
                    {
                        // Add a new row if the step is descending or equal to the previous one
                        if (previousStep.HasValue && stepRecord.Step <= previousStep)
                        {
                            row++;
                            serialNumber++;
                        }

                        if (stepRecord.Step == 1)
                        {
                            worksheet.Cells[row, 4].Value = stepRecord.CreatedAt?.ToString("yyyy:MM:dd HH:mm:ss:fff");
                        }

                        // Write Serial Number, Phone Number, Customer Name, and the Workflow Start Time for each row
                        worksheet.Cells[row, 1].Value = serialNumber;
                        worksheet.Cells[row, 2].Value = phoneNumber;
                        worksheet.Cells[row, 3].Value = customerName;

                        // Find the column corresponding to the current step
                        int currentCol = 5 + (stepRecord.Step.Value - 1) * 2;

                        // Look up the workflow title and response
                        var question = workflows.ContainsKey(stepRecord.WorkflowId) ? workflows[stepRecord.WorkflowId] : "Webhook Trigger";
                        var response = stepRecord.Response;

                        worksheet.Cells[row, currentCol].Value = question;  // Step i Question
                        worksheet.Cells[row, currentCol + 1].Value = response; // Step i Answer

                        // Update the previous step
                        previousStep = stepRecord.Step;
                    }

                    row++;
                }

                var stream = new MemoryStream();
                package.SaveAs(stream);
                stream.Position = 0;

                return stream;
            }
        }

        public async Task<List<WorkflowVariableResponseDto>> WorkflowVariableResponse(Guid contactId)
        {
            // Fetch the data and project to an intermediate result
            var workflowResponses = await _contactsDbContext.WorkflowResponseHistoryEntities
               .Where(wfs => wfs.ContactId == contactId)
               .OrderByDescending(wfs => wfs.CreatedAt) // Order by CreatedAt descending
               .ToListAsync();

            // Step 2: Fetch automation customer responses for the fetched workflow responses
            var workflowIds = workflowResponses.Select(wfs => wfs.WorkflowId).Distinct().ToList();
            var automationResponses = await _contactsDbContext.AutomationCustomerResponseEntities
                .Where(acs => workflowIds.Contains(acs.ReferenceId ?? Guid.Empty))
                .OrderByDescending(m => m.CreatedAt) // Order by CreatedAt descending
                .ToListAsync();

            // Step 3: Fetch all variable name entities
            var variableIds = automationResponses.Select(acs => acs.VeriableNameEntityId).Distinct().ToList();
            var variableNames = await _contactsDbContext.VeriableNameEntities
                .Where(vr => variableIds.Contains(vr.Id))
                .OrderByDescending(m => m.CreatedAt) // Order by CreatedAt descending
                .ToListAsync();

            // Step 4: Manually combine the fetched data
            var combinedData = (from acs in automationResponses
                                join wfs in workflowResponses on acs.ReferenceId equals wfs.WorkflowId
                                join vr in variableNames on acs.VeriableNameEntityId equals vr.Id
                                select new
                                {
                                    ResponseId = wfs.Id,
                                    VariableId = vr.Id,
                                    VariableName = vr.VeriableName,
                                    wfs.Response,
                                    wfs.CreatedAt
                                })
                               .ToList();

            // Step 5: Group by VariableId, and for each group, select the most recent one by CreatedAt
            var result = combinedData
                .GroupBy(x => x.VariableId)
                .Select(g => g.OrderByDescending(x => x.CreatedAt).First()) // Get top 1 by CreatedAt in descending order for each VariableId
                .Select(x => new WorkflowVariableResponseDto
                {
                    ResponseId = x.ResponseId,
                    Variable = x.VariableName,
                    Value = x.Response,
                    VariableId = x.VariableId
                })
                .ToList();

            return result;

        }

        public async Task<List<WorkflowListDto>> GetInputListAsync(Guid companyId, Guid? userId)
        {
            try
            {
                var filters = new Dictionary<Guid, string>();
                filters.Add(companyId, "CompanyId");

                if (userId != null)
                {
                    filters.Add(userId.Value, "UserId");
                }
                var results = await _genericRespostry.GetByGuidIdAsync<InputListEntity>(filters);

                if (results.Any())
                {
                    return results.Where(x => !x.IsDeleted).Adapt<List<WorkflowListDto>>();

                }
                return new List<WorkflowListDto>();
            }
            catch (Exception)
            {
                throw;
            }

        }

        public async Task<List<VeriableNameEntity>> GetVeriableNameAsync(Guid companyId, Guid? userId)
        {
            try
            {
                Dictionary<Guid, string> filters = new Dictionary<Guid, string>();
                filters.Add(companyId, "CompanyId");
                if (userId != null)
                    filters.Add(userId ?? Guid.Empty, "UserId");
                var results = await _genericRespostry.GetByGuidIdAsync<VeriableNameEntity>(filters);
                return results.Where(x => !x.IsDeleted).ToList();
            }
            catch (Exception)
            {
                throw;
            }
        }

        public async Task<AutomationCustomerResponseEntity> SaveAutomationCustomerResponseAsync(AutoReplyWorkflowCustomerResponseDto customerResponseDto)
        {
            try
            {
                var entity = customerResponseDto.Adapt<AutomationCustomerResponseEntity>();
                _unitOfWork.Begin();

                entity.CreatedAt = DateTime.Now;
                entity.UpdatedAt = DateTime.Now;
                entity.Id = Guid.NewGuid();
                entity.CreatedBy = entity.UserId;
                entity.UpdatedBy = entity.UserId;
                var inserted = await _genericRespostry.SaveAsync<AutomationCustomerResponseEntity>(entity);

                _unitOfWork.Commit();
                if (inserted > 0)
                {
                    var result = await _genericRespostry
                        .GetByGuidIdAsync<AutomationCustomerResponseEntity>(new Dictionary<Guid, string>()
                        {
                            { entity.Id,"Id" }
                        });
                    return result.FirstOrDefault();
                }
                else
                    throw new Exception("Auto Reply Customer response has not been inserted. Please connect with team");

            }
            catch (Exception)
            {
                _unitOfWork.Rollback();
                throw;
            }
        }

        public async Task<WorkflowListDto> SaveInputListAsync(WorkflowListDto workflowListDto)
        {
            try
            {
                var entity = workflowListDto.Adapt<InputListEntity>();
                _unitOfWork.Begin();

                entity.CreatedAt = DateTime.Now;
                entity.UpdatedAt = DateTime.Now;
                entity.Id = Guid.NewGuid();
                entity.CreatedBy = entity.UserId;
                entity.UpdatedBy = entity.UserId;
                var inserted = await _genericRespostry.SaveAsync<InputListEntity>(entity);

                _unitOfWork.Commit();
                if (inserted > 0)
                {
                    var result = (await _genericRespostry
                        .GetByGuidIdAsync<InputListEntity>(new Dictionary<Guid, string>()
                        {
                            { entity.Id,"Id" }
                        })).FirstOrDefault();
                    return result.Adapt<WorkflowListDto>();
                }
                else
                    throw new Exception("Input list has not been inserted. Please connect with team");


            }
            catch (Exception)
            {
                _unitOfWork.Rollback();
                throw;
            }
        }

        public async Task<VeriableNameEntity> SaveVeriableNameAsync(VeriableNameEntity entity)
        {
            try
            {
                var veriableNames = await _genericRespostry.GetByObjectAsync<VeriableNameEntity>(new Dictionary<string, object>
                {
                    { "CompanyId", entity.CompanyId },
                    { "VeriableName", entity.VeriableName }
                });
                if (veriableNames != null && veriableNames.Count > 0)
                    throw new Exception("This name is already used by the client.");

                _unitOfWork.Begin();

                entity.CreatedAt = DateTime.Now;
                entity.UpdatedAt = DateTime.Now;
                entity.Id = Guid.NewGuid();
                entity.CreatedBy = entity.UserId;
                entity.UpdatedBy = entity.UserId;
                var inserted = await _genericRespostry.SaveAsync<VeriableNameEntity>(entity);

                _unitOfWork.Commit();
                if (inserted > 0)
                {
                    var result = (await _genericRespostry
                        .GetByGuidIdAsync<VeriableNameEntity>(new Dictionary<Guid, string>()
                        {
                            { entity.Id,"Id" }
                        })).FirstOrDefault();
                    return result;
                }
                else
                {
                    _unitOfWork.Rollback();
                    throw new Exception("Veriable name has not been inserted. Please connect with team");
                }

            }
            catch (Exception)
            {
                throw;
            }
        }

        public async Task<AutomationCustomerResponseEntity> UpdateAutomationCustomerResponseAsync(AutomationCustomerResponseEntity entity)
        {
            try
            {
                _unitOfWork.Begin();
                entity.IsDeleted = false;
                entity.UpdatedBy = entity.UserId;
                var result = await _genericRespostry.UpdateRecordAsync<AutomationCustomerResponseEntity>(entity, new Dictionary<string, object>() { { "Id", entity.Id } });

                _unitOfWork.Commit();
                return result;

            }
            catch (Exception ex)
            {
                _unitOfWork.Rollback();
                throw new Exception("An error occurred while updating the Automation Customer Response Entity.", ex);
            }
        }

        public async Task<WorkflowListDto> UpdateInputListAsync(WorkflowListDto entity)
        {
            try
            {
                _unitOfWork.Begin();
                if (entity.Id == null) throw new ArgumentNullException(nameof(entity.Id), "Id can't be null");
                var adaptEntity = entity.Adapt<InputListEntity>();

                var inputListEntities = await _genericRespostry.GetByGuidIdAsync<InputListEntity>(
                    new Dictionary<Guid, string> { { entity.Id.Value, "Id" } }
                );
                if (!inputListEntities.Any())
                {
                    throw new Exception("Not Found Data");
                }
                var inputListEntity = inputListEntities.First();
                inputListEntity.Inputs = JsonConvert.SerializeObject(entity.Inputs);
                inputListEntity.ButtonName = entity.ButtonName;
                inputListEntity.ListName = entity.ListName;
                inputListEntity.UpdatedAt = DateTime.UtcNow;
                inputListEntity.UpdatedBy = entity.UserId;

                var updatedEntity = await _genericRespostry.UpdateRecordAsync(inputListEntity, new Dictionary<string, object>() { { "Id", inputListEntity.Id } });

                _unitOfWork.Commit();
                return updatedEntity.Adapt<WorkflowListDto>();
            }
            catch (Exception ex)
            {

                _unitOfWork.Rollback();
                throw new Exception("An error occurred while updating the input list.", ex);
            }
        }
        public async Task<List<PlanDiscountEntity>> GetPlanDiscountsAsync()
        {
            return await _genericRespostry.GetAllAsync<PlanDiscountEntity>();
        }
    }
}
