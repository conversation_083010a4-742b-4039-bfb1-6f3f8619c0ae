﻿using EngagetoEntities.UserEntities.Dtos;
using EngagetoEntities.UserEntities.Models;

namespace EngagetoDapper.Data.Interfaces.IUserInterfaces.IUserServices
{
    public interface IResourcePermissionService
    {
        Task<bool> AddResourcePermissionAsync(RequestResourcePermissionDto requestResourcePermissionDto, Guid userId);
        Task<List<RequestResourcePermissionDto>> GetResourcePermissionsAsync();
    }
}
