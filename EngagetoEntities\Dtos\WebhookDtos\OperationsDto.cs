﻿namespace EngagetoEntities.Dtos.WebhookDtos
{
    //public class OperationsDto
    //{

    //    public Search? Searching { get; set; }
    //    public Sort? Sorting { get; set; }
    //    public FilterGroup? Filtering { get; set; }
    //}
    //public class Search
    //{
    //    public string? Column { get; set; }
    //    public string? Value { get; set; }
    //}

    //public class Sort
    //{
    //    public string? Column { get; set; }
    //    public string? Order { get; set; }
    //}

    //public class FilterGroup
    //{
    //    public string FilterType { get; set; }
    //    public List<FilterCondition> Conditions { get; set; }
    //}

    //public class FilterCondition
    //{
    //    public string Column { get; set; }
    //    public string Operator { get; set; }
    //    public string Value
    //    {

    //        get; set;
    //    }
    //}
}
