﻿using EngagetoEntities.Dtos.WidgetChatHubDtos;
using EngagetoRepository.WebhookRepository.Hubs.Service;
using Microsoft.AspNetCore.SignalR;
using System.Text.Json;


namespace EngagetoRepository.WebhookRepository.Hubs
{
    public class WidgetChatHub: Hub<IWidgetChatHub>
    {
        private readonly IWidgetChatService _widgetChatService;
        JsonSerializerOptions options = new JsonSerializerOptions
        {
            PropertyNamingPolicy = JsonNamingPolicy.CamelCase,
            WriteIndented = true 
        };

        public WidgetChatHub(IWidgetChatService widgetChatService)
        {
            _widgetChatService = widgetChatService;
        }

        public override async Task OnDisconnectedAsync(Exception exception)
        {
            await base.OnDisconnectedAsync(exception);
        }

        public override Task OnConnectedAsync()
        {
            return base.OnConnectedAsync();
        }
        public async Task JoinGroupAsync(string id)
        {
            // Add the client to a group based on the userId
            await Groups.AddToGroupAsync(Context.ConnectionId, id.ToLowerInvariant());
            BodyMessage message = new BodyMessage()
            {
                Text = new() { Text = "Group join successfully." },
                Type = EngagetoEntities.Enums.MediaType.TEXT,
            };
            await Clients.Group(id).ReceiveMessageAsync(new WidgetChatDto { From = string.Empty,Body = JsonSerializer.Serialize(message, options) });
        }
        public async Task SendMessageToGroupAsync(WidgetChatDto widgetChatDto)
        {
            //widgetChatDto.BodyMessage = Newtonsoft.Json.JsonConvert.DeserializeObject<BodyMessage>(widgetChatDto.Body);
            var result = await _widgetChatService.SendWidgetMessageToWhatsappAsync(widgetChatDto);
            await Clients.Group(widgetChatDto.Id.ToString().ToLowerInvariant()).ReceiveMessageAsync(result);
        }
    }
}
