﻿using EngagetoEntities.Enums;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using System.Text;

namespace EngagetoEntities.Entities
{
    [Table("ApiKeyEntities")]
    public class ApiKeyEntity
    {
        [Key]
        public Guid Id { get; set; }
        public string CompanyId { get; set; }
        public string Key { get; set; }
        public string? SecretKey { get; set; }
        public string? Owner { get; set; }
        public DateTime CreatedAt { get; set; }
        public bool IsActive { get; set; } = true;
        public SourceType Source { get; set; }
        public LeadSource? SubSource { get; set; }

        [NotMapped]
        private TimeZoneInfo indianTimeZone = TimeZoneInfo.FindSystemTimeZoneById("India Standard Time");

        public ApiKeyEntity() { }

        public ApiKeyEntity(string companyId, string owner,bool isActive = true)
        {
            Id = Guid.NewGuid();
            SecretKey = GenerateBase64(Guid.NewGuid().ToString());
            Key = GenerateBase64($"{companyId},{SecretKey}");
            CompanyId = companyId;
            Owner = owner;
            CreatedAt = TimeZoneInfo.ConvertTime(DateTime.UtcNow,indianTimeZone);
            IsActive = isActive;
        }
        private string GenerateBase64(string key)
        {
            byte[] textBytes = Encoding.UTF8.GetBytes(key);

            string base64String = Convert.ToBase64String(textBytes);
            return base64String;
        }

    }
}
