﻿using EngagetoEntities.Abstract;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace EngagetoEntities.Entities
{
    public class WorkflowCustomerResponse :  BaseEntity
    {
        public Guid Id { get; set; }
        public Guid? BusinessId { get; set; }
        public Guid? WorkflowId { get; set; }
        public Guid? NodeId { get; set; }
        public Guid? UserId { get; set; }
        public Guid? AttributeId { get; set; }
    }
}
