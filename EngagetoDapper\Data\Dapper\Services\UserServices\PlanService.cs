﻿using EngagetoDapper.Data.Interfaces.GenericInterfaces;
using EngagetoDapper.Data.Interfaces.IUserInterfaces.IUserServices;
using EngagetoEntities.Entities;
using EngagetoEntities.UserEntities.Dtos;
using EngagetoEntities.Utilities;

namespace EngagetoDapper.Data.Dapper.Services.UserServices
{
    public class PlanService: IPlanService
    {
        private readonly IGenericRepository _genericRepository;
        public PlanService(IGenericRepository genericRepository)
        {
            _genericRepository = genericRepository;
        }

        public async Task<bool> AddPlanDiscountAsync(PlanDiscountDto discountDto)
        {
            try
            {
                var tableName = StringHelper.GetTableName<PlanDiscountEntity>();
                var columns = StringHelper.GetPropertyNames<PlanDiscountDto>();
                var result = await _genericRepository.InsertRecordsAsync(tableName, columns, new List<PlanDiscountDto> { discountDto });
                return result;
            }
            catch (Exception ex)
            {
                throw;
            }
        }
        public async Task<List<PlanDiscountEntity>> GetPlanDiscountsAsync()
        {
            try
            {
                var result = await _genericRepository.GetRecordByRequestFilter<PlanDiscountEntity>(new List<Dtos.RequestFilterDto>()
                {
                    new Dtos.RequestFilterDto("Year",DateTime.Now.Year,"<="),
                    new Dtos.RequestFilterDto("IsDelete",false,"=")
                });
                return result;
            }
            catch (Exception ex)
            {
                throw;
            }
        }
    }
}
