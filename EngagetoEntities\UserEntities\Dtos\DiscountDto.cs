﻿using EngagetoEntities.Enums;
using EngagetoEntities.UserEntities.Models;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace EngagetoEntities.UserEntities.Dtos
{
    public class DiscountDto
    {
        public string DiscountCode { get; set; }
        public string? Description { get; set; }
        public DiscountType DiscountType { get; set; }
        public decimal DiscountValue { get; set; }
        public DateTime ValidFrom { get; set; }
        public DateTime ValidTo { get; set; }
        public bool? IsActive { get; set; } = true;
    }
}
