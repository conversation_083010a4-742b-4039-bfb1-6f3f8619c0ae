﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using EngagetoEntities.UserEntities.Models;
using static EngagetoEntities.UserEntities.Models.ExpiryDateAttribute;

namespace EngagetoContracts.UserContracts
{
    public interface IRazorpayPaymentService
    {
        // Task<string> CreatePaymentAsync(Guid currentUserId, PaymentCreateRequest paymentCreateRequest);
        //Task<bool> VerifyPaymentAsync(PaymentVerificationRequest paymentVerificationRequest);
        Task<Guid> AddPaymentCardAsync(Guid userId, PaymentCardDetailsDto paymentCardDto);
        Task<IEnumerable<PaymentCardDetails>> GetAllPaymentCardsAsync(Guid currentUserId, string searchQuery = null);
        Task<bool> DeletePaymentCardAsync(Guid cardId);
    }
}
