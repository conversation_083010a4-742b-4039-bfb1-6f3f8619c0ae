﻿using EngagetoContracts.UserContracts;
using EngagetoDapper.Data.Interfaces.IUserInterfaces.IUserServices;
using EngagetoEntities.Entities;
using EngagetoEntities.Enums;
using EngagetoEntities.UserEntities.Models;
using EngagetoEntities.Utilities;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Options;
using Razorpay.Api;
using System.Security.Cryptography;
using System.Text;


namespace EngagetoRepository.Repository
{
    public class RazorPayService : IRazorPayService
    {
        private readonly ILogger<RazorPayService> _logger;
        private readonly IConfiguration _configuration;
        private readonly RazorpayClient _razorpayClient;
        private readonly ApplicationDBContext _dbContext;
        private readonly IOptions<WalletSettings> _walletSettings;
        private readonly IResourcePermissionService _resourcePermissionService;
        private readonly IWalletService _walletService;

        public RazorPayService(ILogger<RazorPayService> logger,
            IConfiguration configuration,
            ApplicationDBContext dbContext,
            IOptions<WalletSettings> walletSettings,
            IResourcePermissionService resourcePermissionService,
            IWalletService walletService)
        {
            _configuration = configuration;
            _logger = logger;
            var apiKey = _configuration["Razorpay:ApiKey"];
            var apiSecret = _configuration["Razorpay:ApiSecret"];
            _razorpayClient = new RazorpayClient(apiKey, apiSecret);
            _dbContext = dbContext;
            _walletSettings = walletSettings;
            _resourcePermissionService = resourcePermissionService;
            _walletService = walletService;
        }
        public async Task<Subscriptions> GetLastPaidPlanByUserIdAsync(string companyId)
        {
            var lastPayment = await _dbContext.Subscriptions
                .Where(p => p.CompanyId == companyId && p.Status == "paid")
                .OrderByDescending(p => p.UpdatedAt)
                .FirstOrDefaultAsync();

            return lastPayment;
        }
        public async Task<string> CreateOrder(decimal amount, string currency, string receipt)
        {
            var order = new Dictionary<string, object>
        {
            { "amount", amount * 100 },
            { "currency", currency },
            { "receipt", receipt },
            { "payment_capture", 1 }
        };

            var razorpayOrder = _razorpayClient.Order.Create(order);

            return razorpayOrder["id"]?.ToString();
        }

        public async Task<bool> VerifyPaymentSignature(string orderId, string paymentId, string signature)
        {
            var attributes = $"{orderId}|{paymentId}";

            var hmacSha256 = new HMACSHA256(Encoding.UTF8.GetBytes(_configuration["Razorpay:ApiSecret"]));
            var hash = hmacSha256.ComputeHash(Encoding.UTF8.GetBytes(attributes));

            var calculatedSignature = BitConverter.ToString(hash).Replace("-", "").ToLower();

            return calculatedSignature.Equals(signature, StringComparison.OrdinalIgnoreCase);
        }
        public async Task<string> CreateOrderAsync(Guid currentUser, OrderRequestModel request)
        {
            try
            {
                var resourcePermission = await _resourcePermissionService.GetResourcePermissionsAsync();
                var razorpayKeyId = _configuration["Razorpay:ApiKey"];
                var razorpayKeySecret = _configuration["Razorpay:ApiSecret"];
                RazorpayClient razorpayClient = new RazorpayClient(razorpayKeyId, razorpayKeySecret);

                Dictionary<string, object> options = new Dictionary<string, object>
                    {
                        { "amount", request.TotalAmount * 100 },
                        { "currency", "INR" },
                        { "receipt", "rec1" },
                        { "payment_capture", 1 }
                    };

                var response = razorpayClient.Order.Create(options);
                var userCompany = await _dbContext.Ahex_CRM_Users
                                                  .Where(u => u.Id == currentUser)
                                                  .Select(u => u.CompanyId)
                                                  .FirstOrDefaultAsync();

                var plan = await _dbContext.PlanEntities
                                           .FirstOrDefaultAsync(p => p.PlanName == request.SubscriptionPlanId);

                var subscription = await _dbContext.Subscriptions
                                                  .FirstOrDefaultAsync(s => s.CompanyId == userCompany);

                //var resource = resourcePermission.FirstOrDefault(x => x.PlanId == plan?.Id);
                var startDate = StringHelper.ConvertIntoIndianDateTime(DateTime.UtcNow);
                var endDate = CalculateEndDate(request.DurationType, startDate);
                if (!request.IsUpgradePlan ?? false && subscription is not null)
                {
                    DateTime? renewEndDate = subscription.RenewEndDate;
                    DateTime? planEndDate = subscription.PlanEndDate;

                    startDate = renewEndDate.HasValue
                        ? (renewEndDate.Value > StringHelper.ConvertIntoIndianDateTime(DateTime.UtcNow) ? renewEndDate.Value.AddDays(1) : StringHelper.ConvertIntoIndianDateTime(DateTime.UtcNow))
                        : (planEndDate.HasValue ? (planEndDate.Value > StringHelper.ConvertIntoIndianDateTime(DateTime.UtcNow) ? planEndDate.Value.AddDays(1) : StringHelper.ConvertIntoIndianDateTime(DateTime.UtcNow)) : StringHelper.ConvertIntoIndianDateTime(DateTime.UtcNow));

                    endDate = CalculateEndDate(request.DurationType, startDate);
                }
                var oldPaymentDetails = new PaymentWalletDetail
                {
                    SubscriptionId = subscription?.Id,
                    TotalAmount = request.TotalAmount,
                    IGSTAmount = request.IGSTAmount,
                    DiscountId = request.DiscountId,
                    CompanyId = userCompany,
                    UserId = currentUser,
                    PlanId = plan.Id,
                    DurationType = request.DurationType ?? "Monthly",
                    OrderId = response["id"].ToString(),
                    OrderAmount = request.Amount,
                    Currency = response["currency"].ToString(),
                    Status = "Pending",
                    CreatedBy = currentUser,
                    CreatedAt = StringHelper.ConvertIntoIndianDateTime(DateTime.UtcNow),
                    PlanStartDate = startDate,
                    PlanEndDate = endDate
                };
                _dbContext.PaymentWalletDetails.Add(oldPaymentDetails);
                await _dbContext.SaveChangesAsync();
                return response["id"].ToString();
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error creating order");
                throw;
            }
        }

        public async Task<string> VerifyPaymentAsync(Guid currentUser, PaymentVerificationModel request)
        {
            try
            {
                var razorpayKeySecret = _configuration["Razorpay:ApiSecret"];
                string body = $"{request.RazorpayOrderId}|{request.RazorpayPaymentId}";

                string expectedSignature = ComputeSignature(razorpayKeySecret, body);


                if (expectedSignature != request.RazorpaySignature)
                    throw new Exception("Payment verification failed: Invalid signature");

                var paymentDetail = await _dbContext.PaymentWalletDetails
                    .FirstOrDefaultAsync(p => p.OrderId == request.RazorpayOrderId && p.UserId == currentUser)
                    ?? throw new Exception("Payment record not found");

                var subscription = await _dbContext.Subscriptions
                    .FirstOrDefaultAsync(s => s.CompanyId == paymentDetail.CompanyId);

                var startDate = StringHelper.ConvertIntoIndianDateTime(DateTime.UtcNow);
                var endDate = CalculateEndDate(paymentDetail.DurationType, startDate);

                if (subscription == null)
                {
                    subscription = CreateNewSubscription(paymentDetail, currentUser, startDate, endDate);
                    await _dbContext.Subscriptions.AddAsync(subscription);
                }
                else
                {
                    if (!request.IsUpgradePlan ?? false)
                    {
                        DateTime? renewEndDate = subscription.RenewEndDate;
                        DateTime? planEndDate = subscription.PlanEndDate;

                        startDate = renewEndDate.HasValue
                            ? (renewEndDate.Value > StringHelper.ConvertIntoIndianDateTime(DateTime.UtcNow) ? renewEndDate.Value.AddDays(1) : StringHelper.ConvertIntoIndianDateTime(DateTime.UtcNow))
                            : (planEndDate.HasValue ? (planEndDate.Value > StringHelper.ConvertIntoIndianDateTime(DateTime.UtcNow) 
                            ? planEndDate.Value.AddDays(1) : StringHelper.ConvertIntoIndianDateTime(DateTime.UtcNow)) 
                            : StringHelper.ConvertIntoIndianDateTime(DateTime.UtcNow));
                        
                        endDate = CalculateEndDate(paymentDetail.DurationType, startDate);
                    }
                    subscription.DurationType = paymentDetail.DurationType;
                    subscription.PlanId = paymentDetail.PlanId;
                    subscription.RenewStartDate = startDate;
                    subscription.RenewEndDate = endDate;
                    subscription.Status = "Paid";
                    _dbContext.Entry(subscription).State = EntityState.Modified;
                }

                UpdatePaymentDetail(paymentDetail, request, currentUser, subscription.Id);

                await _dbContext.SaveChangesAsync();

                return "Payment verification successful.";
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error verifying payment");
                throw;
            }
        }

        private static string ComputeSignature(string secret, string body)
        {
            using (var hmac = new HMACSHA256(Encoding.UTF8.GetBytes(secret)))
            {
                byte[] hashBytes = hmac.ComputeHash(Encoding.UTF8.GetBytes(body));
                return BitConverter.ToString(hashBytes).Replace("-", "").ToLower();
            }
        }
        private static DateTime CalculateEndDate(string? durationType, DateTime startDate)
        {
            return (durationType ?? "Monthly") switch
            {
                nameof(PlanType.Weekly) => startDate.AddDays(7),
                nameof(PlanType.Monthly) => startDate.AddMonths(1),
                nameof(PlanType.Quarterly) => startDate.AddMonths(3),
                nameof(PlanType.Annually) => startDate.AddYears(1),
                _ => startDate.AddMonths(1)
            };
        }

        // Helper method to create a new subscription
        private static Subscriptions CreateNewSubscription(PaymentWalletDetail paymentDetail, Guid currentUser, DateTime startDate, DateTime endDate)
        {
            return new Subscriptions
            {
                UserId = currentUser.ToString(),
                DurationType = paymentDetail.DurationType ?? "Monthly",
                CompanyId = paymentDetail.CompanyId,
                PlanId = paymentDetail.PlanId,
                Status = "Paid",
                PlanStartDate = startDate,
                PlanEndDate = endDate,
                CreatedAt = StringHelper.ConvertIntoIndianDateTime(DateTime.UtcNow),
                CreatedBy = currentUser.ToString(),
            };
        }

        // Helper method to update the end date for an existing subscription
        private static DateTime UpdateSubscriptionEndDate(Subscriptions subscription, PaymentWalletDetail paymentDetail, DateTime startDate, DateTime endDate)
        {
            if (paymentDetail.DurationType != subscription.DurationType)
            {
                if (Enum.TryParse<PlanType>(paymentDetail.DurationType, true, out var newPlanType)
                    && Enum.TryParse<PlanType>(subscription.DurationType, true, out var currentPlanType))
                {
                    if (newPlanType > currentPlanType)
                    {
                        endDate = CalculateEndDate(paymentDetail.DurationType, startDate);
                    }
                    else
                    {
                        TimeSpan difference = endDate.Subtract(StringHelper.ConvertIntoIndianDateTime(DateTime.UtcNow));
                        endDate = StringHelper.ConvertIntoIndianDateTime(DateTime.UtcNow).AddDays(difference.TotalDays);
                    }
                }
            }
            return CalculateEndDate(paymentDetail.DurationType, StringHelper.ConvertIntoIndianDateTime(DateTime.UtcNow));
        }

        // Helper method to update the payment detail
        private void UpdatePaymentDetail(PaymentWalletDetail paymentDetail, PaymentVerificationModel request, Guid currentUser, int subscriptionId)
        {
            paymentDetail.PaymentId = request.RazorpayPaymentId;
            paymentDetail.Signature = request.RazorpaySignature;
            paymentDetail.Status = "Paid";
            paymentDetail.UpdatedAt = StringHelper.ConvertIntoIndianDateTime(DateTime.UtcNow);
            paymentDetail.PlanStartDate = paymentDetail.UpdatedAt;
            paymentDetail.UpdatedBy = currentUser;
            paymentDetail.SubscriptionId = subscriptionId;
            _dbContext.Entry(paymentDetail).State = EntityState.Modified;
        }


        //Wallet-Transcations//
        public async Task<string> CreateWalletOrderAsync(Guid userId, decimal amount, string currency)
        {
            try
            {
                decimal minimumAmount = WalletSettings.MinimumOrderAmount;
                if (amount < minimumAmount)
                {
                    throw new Exception($"Minimum order amount is {minimumAmount} {currency}");
                }
                var user = await _dbContext.Ahex_CRM_Users.FindAsync(userId);
                var userCompany = await _dbContext.Ahex_CRM_Users
                                                    .Where(u => u.Id == userId)
                                                    .Select(u => u.CompanyId)
                                                    .FirstOrDefaultAsync();
                if (user == null) throw new Exception("User not found");

                var walletEntity = await _dbContext.WalletEntities.FirstOrDefaultAsync(w => w.UserId == userId);
                if (walletEntity == null)
                {
                    walletEntity = new WalletEntity
                    {
                        UserId = userId,
                        ComapanyId = userCompany,
                        WalletBalance = 0m,
                        CreatedBy = userId,
                        CreatedAt = StringHelper.ConvertIntoIndianDateTime(DateTime.UtcNow),
                    };
                    _dbContext.WalletEntities.Add(walletEntity);
                    await _dbContext.SaveChangesAsync();
                }

                var options = new Dictionary<string, object>
                    {
                        { "amount", amount * 100 },
                        { "currency", currency },
                        { "receipt", "wallet_recharge_" + StringHelper.ConvertIntoIndianDateTime(DateTime.UtcNow).Ticks }
                    };

                Order order = _razorpayClient.Order.Create(options);
                var paymentDetail = new PaymentWalletDetail
                {
                    WalletEntitiesId = walletEntity.Id,
                    UserId = userId,
                    OrderId = order["id"].ToString(),
                    OrderAmount = amount,
                    Currency = currency,
                    Status = "Pending",
                    CreatedBy = userId,
                    CreatedAt = StringHelper.ConvertIntoIndianDateTime(DateTime.UtcNow),
                    //UpdatedAt = DateTime.UtcNow,
                };

                _dbContext.PaymentWalletDetails.Add(paymentDetail);
                await _dbContext.SaveChangesAsync();

                return order["id"].ToString();
            }
            catch (Exception ex)
            {
                throw new Exception("Error creating wallet order: " + ex.Message, ex);
            }
        }

        public async Task<bool> VerifyWalletPaymentAsync(string orderId, string paymentId, string signature)
        {
            try
            {
                var paymentDetail = await _dbContext.PaymentWalletDetails.FirstOrDefaultAsync(t => t.OrderId == orderId);
                if (paymentDetail == null) throw new Exception("Transaction not found");

                var attributes = new Dictionary<string, string>
            {
                { "razorpay_order_id", orderId },
                { "razorpay_payment_id", paymentId },
                { "razorpay_signature", signature }
            };

                Utils.verifyPaymentSignature(attributes);

                paymentDetail.PaymentId = paymentId;
                paymentDetail.Signature = signature;
                paymentDetail.Currency = paymentDetail.Currency;
                paymentDetail.UserId = paymentDetail.UserId;
                paymentDetail.PaymentMode = paymentDetail.PaymentMode;
                paymentDetail.Status = "Paid";
                paymentDetail.UpdatedBy = paymentDetail.UserId;
                paymentDetail.UpdatedAt = StringHelper.ConvertIntoIndianDateTime(DateTime.UtcNow);

                await _dbContext.SaveChangesAsync();

                return true;
            }
            catch (Exception ex)
            {
                throw new Exception("Error verifying wallet payment: " + ex.Message, ex);
            }
        }

        public async Task<(bool isSuccess, string message)> AddAmountToWalletAsync(string orderId, Guid userId)
        {

            using (var transaction = await _dbContext.Database.BeginTransactionAsync())
            {
                try
                {
                    var paymentDetail = await _dbContext.PaymentWalletDetails
                .Include(p => p.WalletEntities)
                .FirstOrDefaultAsync(t => t.UserId == userId && t.OrderId == orderId);

                    if (paymentDetail == null)
                    {
                        throw new Exception("No payments found for the user");
                    }

                    var walletEntity = paymentDetail.WalletEntities;
                    //if (amount == 0)
                    //{
                    // If amount is not passed, add the existing OrderAmount to WalletBalance
                    //paymentDetail.OrderAmount += walletEntity.WalletBalance.Value;
                    if (walletEntity.WalletBalance == null)
                    {
                        walletEntity.WalletBalance = 0;
                    }
                    walletEntity.WalletBalance += paymentDetail.OrderAmount;

                    string message = null;
                    if (walletEntity.WalletBalance < 100)
                    {
                        message = "Insufficient funds. Minimum balance required is RS 100 ";
                    }
                    //}
                    //else
                    //{
                    //    // If amount is passed, add it to OrderAmount and then add the new sum to WalletBalance
                    //    paymentDetail.OrderAmount = amount;
                    //    decimal currentBalance = walletEntity.WalletBalance.GetValueOrDefault();
                    //    decimal newBalance = currentBalance + paymentDetail.OrderAmount.Value;

                    //    // Check if the new balance is below the minimum balance threshold
                    //    if (newBalance < WalletSettings.MinimumOrderAmount)
                    //    {
                    //        throw new Exception($"Insufficient funds. Minimum balance required is Rs. {WalletSettings.MinimumOrderAmount}");
                    //    }

                    //    walletEntity.WalletBalance = newBalance;
                    //}
                    //// Check if the new balance is below the minimum balance threshold
                    //decimal newBalance = walletEntity.WalletBalance.GetValueOrDefault();
                    //decimal minimumBalance = 100m; // Minimum balance requirement set to 100 INR
                    //if (newBalance < minimumBalance)
                    //{
                    //    throw new Exception($"Insufficient funds. Minimum balance required is Rs. {minimumBalance}");
                    //}
                    walletEntity.UpdatedBy = userId;
                    walletEntity.UpdatedAt = DateTime.UtcNow;
                    _dbContext.PaymentWalletDetails.Update(paymentDetail);
                    _dbContext.WalletEntities.Update(walletEntity);
                    await _dbContext.SaveChangesAsync();

                    await transaction.CommitAsync();
                    return (true, message);
                }
                catch (Exception ex)
                {
                    await transaction.RollbackAsync();
                    throw new Exception("Error adding amount to wallet: " + ex.Message, ex);
                }
            }
        }
        public async Task<PaymentWalletDetail> GetSubscriptionDeductionsByIdAsync(int id)
        {
            try
            {
                var paymentDetail = await _dbContext.PaymentWalletDetails
                    .FirstOrDefaultAsync(p => p.Id == id);

                if (paymentDetail == null)
                {
                    return null;
                }

                return paymentDetail;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error fetching subscription deduction details");
                throw;
            }
        }

        private async Task<string> GetPlanName(int planId)
        {
            var plan = await _dbContext.PlanEntities.FirstOrDefaultAsync(p => p.Id == planId);
            return plan?.PlanName ?? "Unknown Plan";
        }
        public async Task<List<PaymentWalletDetail>> GetPaymentWalletDetailsByCompanyIdAsync(Guid userId, Guid discountId)
        {
            return await (from user in _dbContext.Ahex_CRM_Users
                          join customer in _dbContext.Ahex_CRM_BusinessDetails
                          on user.CompanyId equals customer.Id.ToString()
                          join payment in _dbContext.PaymentWalletDetails
                          on customer.Id.ToString() equals payment.CompanyId
                          where payment.DiscountId == discountId
                          && payment.Status == "Paid"
                          select payment).ToListAsync();

        }


        public async Task<PaymentWalletDetail> SavePaymentWalletAsync(PaymentWalletDetail paymentWalletDetail)
        {
            await _dbContext.PaymentWalletDetails.AddAsync(paymentWalletDetail);
            await _dbContext.SaveChangesAsync();
            return paymentWalletDetail;
        }

        public async Task<Subscriptions> SaveSubscriptionAsync(Subscriptions subscription, bool isUpdate = false)
        {
            if (isUpdate)
            {
                _dbContext.Subscriptions.Update(subscription);
            }
            else
            {
                await _dbContext.Subscriptions.AddAsync(subscription);
            }
            await _dbContext.SaveChangesAsync();
            return subscription;
        }

        public async Task<bool> FreePlanSubscriptionAsync(string companyId, Guid userid)
        {
            try
            {
                var plan = await _dbContext.PlanEntities.FirstOrDefaultAsync(x => x.PlanName.Contains("Intro"));
                if (plan == null)
                    return false;

                var walletAndSubscription = await _walletService.GetWalletAndSubscriptionPlanAsync(companyId);
                if (!string.IsNullOrEmpty(walletAndSubscription?.subscriptionPlan.PlanName))
                    throw new Exception("A free subscription is not available to the client.");

                Subscriptions subscriptions = new Subscriptions()
                {
                    CompanyId = companyId,
                    DurationType = PlanType.Weekly.ToString(),
                    PlanId = plan.Id,
                    PlanStartDate = DateTime.Now,
                    PlanEndDate = DateTime.Now.AddDays(7),
                    Status = "Paid",
                    UserId = userid.ToString(),
                    CreatedAt = DateTime.Now,
                    CreatedBy = userid.ToString()
                };
                var subscriptionResult = await SaveSubscriptionAsync(subscriptions);
                if (subscriptionResult == null)
                    return false;

                var paymentWalletDetails = new PaymentWalletDetail()
                {
                    CompanyId = companyId,
                    UserId = userid,
                    PlanId = plan.Id,
                    DurationType = PlanType.Weekly.ToString(),
                    OrderId = null,
                    OrderAmount = 0,
                    Currency = "INR",
                    Status = "Paid",
                    CreatedBy = userid,
                    CreatedAt = DateTime.Now,
                    PlanStartDate = DateTime.Now,
                    PlanEndDate = DateTime.Now.AddDays(7),
                    SubscriptionId = subscriptionResult.Id
                };
                await SavePaymentWalletAsync(paymentWalletDetails);
                return true;
            }
            catch (Exception ex)
            {
                return false;
            }
        }
    }

}


