﻿using Newtonsoft.Json;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace EngagetoEntities.Dtos.MetaDto
{
    public class EditTemplateErrorResponseDto
    {
        [JsonProperty("error")]
        public EditErrorDetail EditError { get; set; }
    }


    public class EditErrorDetail
    {
        [JsonProperty("message")]
        public string Message { get; set; }



        [JsonProperty("error_user_title")]
        public string ErrorUserTitle { get; set; }

        [JsonProperty("error_user_msg")]
        public string ErrorUserMsg { get; set; }


    }

    public class UpdateTemplateResponse
    {
        [JsonProperty("success")]
        public bool Success { get; set; }

        [JsonProperty("error")]
        public string Error { get; set; } = default!;
    }

}
