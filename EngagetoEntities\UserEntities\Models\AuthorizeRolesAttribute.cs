﻿using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Mvc.Filters;
using Microsoft.EntityFrameworkCore;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Security.Claims;
using System.Threading.Tasks;
using EngagetoEntities.UserEntities.Models;

namespace EngagetoEntities.UserEntities.Models
{
    /*  public class AuthorizeRolesAttribute : AuthorizeAttribute, IAsyncAuthorizationFilter
      {
          private readonly string[] _roles;

          public AuthorizeRolesAttribute(params string[] roles)
          {
              _roles = roles;
          }
          public async Task OnAuthorizationAsync(AuthorizationFilterContext context)
          {
              // Check if user is authenticated
              if (!context.HttpContext.User.Identity.IsAuthenticated)
              {
                  context.Result = new UnauthorizedResult();
                  return;
              }

              // Fetch user's role name from your database based on the current user's identifier
              var userId = context.HttpContext.User.FindFirst(ClaimTypes.Name)?.Value;
              if (string.IsNullOrEmpty(userId))
              {
                  context.Result = new ForbidResult();
                  return;
              }

              var dbContext = context.HttpContext.RequestServices.GetService(typeof(ApplicationDBContext)) as ApplicationDBContext;
              var roleName = await dbContext.GetUserRoleNameAsync(userId);


              if (string.IsNullOrEmpty(roleName))
              {
                  context.Result = new UnauthorizedResult();
                  return;
              }

              // Check if the user has the "Admin" or "Owner" role
              if (roleName != RoleConstants.Admin && roleName != RoleConstants.Owner)
              {
                  context.Result = new UnauthorizedResult();
                  return;
              }
          }
          public async Task<string> GetUserRoleNameAsync(string userId)
          {
              var roleName = await Ahex_CRM_Users
                  .Where(u => u.Id == userId)
                  .Join(Roles,
                      user => user.RoleId,
                      role => role.RoleId,
                      (user, role) => role.RoleName)
                  .FirstOrDefaultAsync();

              return roleName;
          }

          *//* public async Task OnAuthorizationAsync(AuthorizationFilterContext context)
           {
               // Check if user is authenticated
               if (!context.HttpContext.User.Identity.IsAuthenticated)
               {
                   context.Result = new UnauthorizedResult();
                   return;
               }

               // Fetch user's roles, pages, and statuses from your database based on the current user's identifier
               var userId = context.HttpContext.User.FindFirst(ClaimTypes.Name)?.Value;
               if (string.IsNullOrEmpty(userId))
               {
                   context.Result = new ForbidResult();
                   return;
               }

               var userRolesAndStatuses = await GetUserRolesAsync(context, userId);

               // Check if user has any of the required roles with the appropriate status
               if (!_roles.Any(role => userRolesAndStatuses.Any(ur => ur.RoleName == role && ur.Status)))
               {
                   context.Result = new ForbidResult();
                   return;
               }
           }

           private async Task<IEnumerable<(string RoleName, int PageId, bool Status)>> GetUserRolesAsync(AuthorizationFilterContext context, string userId)
           {
               try
               {
                   var dbContext = context.HttpContext.RequestServices.GetService(typeof(ApplicationDBContext)) as ApplicationDBContext;

                   var userRolesAndStatuses = await dbContext.MenuRoleRelationshipEntities
                       .Where(mrr => mrr.Role.UserRoles.Any(ur => ur.Id.ToString() == userId))
                       .Select(mrr => new { RoleName = mrr.Role.Name, PageId = mrr.PageId, Status = mrr.Status })
                       .ToListAsync();

                   return userRolesAndStatuses.Select(mrr => (mrr.RoleName, mrr.PageId, mrr.Status));
               }
               catch (Exception ex)
               {
                   Console.WriteLine($"Error fetching user roles: {ex.Message}");
                   return Enumerable.Empty<(string RoleName, int PageId, bool Status)>();
               }
           }*//*
      }
  */
    public class AuthorizeRolesAttribute : AuthorizeAttribute, IAsyncAuthorizationFilter
    {
        private readonly string[] _roles;

        public AuthorizeRolesAttribute(params string[] roles)
        {
            _roles = roles;
        }

        public async Task OnAuthorizationAsync(AuthorizationFilterContext context)
        {
           
            if (!context.HttpContext.User.Identity.IsAuthenticated)
            {
                context.Result = new UnauthorizedResult();
                return;
            }

           
            var userId = context.HttpContext.User.FindFirst(ClaimTypes.Name)?.Value;
            if (string.IsNullOrEmpty(userId))
            {
                context.Result = new ForbidResult();
                return;
            }

            var dbContext = context.HttpContext.RequestServices.GetService(typeof(ApplicationDBContext)) as ApplicationDBContext;
            var roleName = await GetUserRoleNameAsync(dbContext, userId);

            if (string.IsNullOrEmpty(roleName))
            {
                context.Result = new UnauthorizedResult();
                return;
            }

           
            if (roleName != RoleConstants.Admin && roleName != RoleConstants.Owner  && roleName != RoleConstants.Teammate)
            {
                context.Result = new UnauthorizedResult();
                return;
            }
        }

        private async Task<string> GetUserRoleNameAsync(ApplicationDBContext dbContext, string userId)
        {
            var roleName = await dbContext.Ahex_CRM_Users
                .Where(u => u.Id.ToString() == userId)
                .Join(dbContext.Roles, 
                    user => user.RoleId, 
                    role => role.Id.ToString(),
                    (user, role) => role.Name)
                .FirstOrDefaultAsync();

            return roleName;
        }
    }
}
