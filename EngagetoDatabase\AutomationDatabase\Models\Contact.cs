﻿namespace EngagetoDatabase.AutomationDataBase.Models;

public partial class Contact
{
    public Guid ContactId { get; set; }

    public string Name { get; set; } = null!;

    public Guid BusinessId { get; set; }

    public Guid? UserId { get; set; }

    public string CountryCode { get; set; } = null!;

    public string Contact1 { get; set; } = null!;

    public string? Email { get; set; }

    public string? CountryName { get; set; }

    public string? Tags { get; set; }

    public DateTime? CreatedDate { get; set; }

    public bool IsActive { get; set; }

    public string? Note { get; set; }

    public int ChatStatus { get; set; }

    public bool? IsSpam { get; set; }

    public string? WorkflowName { get; set; }
    public int? WorkflowStep { get; set; }
}
