﻿using EngagetoEntities.UserEntities.Dtos;

namespace EngagetoDapper.Data.Interfaces.IUserInterfaces.IUserRepositories
{
    public interface IUserRepository
    {
        Task<IEnumerable<MenuDetailsDto>> GetMenuDetailsAsync(string companyId, string roleId);
        Task<IEnumerable<MenuPermissionDto>> GetPermissionMenuAsync(string companyId, string roleId);
        Task<IEnumerable<T>> GetCompanyWalletTransactionsHistoryAsync<T>(string companyId);
        Task<IEnumerable<T>> GetMetaAccountAsync<T>(string companyId, Guid? userId = null);
        Task<IEnumerable<T>> GetInboxContactsAsync<T>(string companyId, Guid userId, string businessPhoneNumber, CancellationToken cancellationToken);
        Task<IEnumerable<T>> GetExpectedWalletBalanceAsync<T>(string companyId);
        Task<IEnumerable<T>> GetAgentsAsync<T>(string companyId, bool? status = true);
        Task<T?> GetSubscriptionDetailsAsync<T>(string companyId);
        Task<IEnumerable<T>> GetExpiringOrExpiredSubscriptionsAsync<T>();
        Task<IEnumerable<T>> GetUsersByBusinessIdAync<T>(string? businessId, string? waPhoneNumberId);
    }
}
