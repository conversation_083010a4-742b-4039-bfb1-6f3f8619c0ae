using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace EngagetoEntities.Entities
{
    [Table("WorkflowEdgeTargetNodes")]
    public class WorkflowEdgeTargetNode : BaseEntity
    {
        [Key]
        public Guid Id { get; set; }
        public Guid EdgeId { get; set; }
        public string? BusinessId { get; set; } 
        public Guid TargetNodeId { get; set; }
        public string? Condition { get; set; }
        // Navigation properties
        [ForeignKey("EdgeId")]
        public virtual WorkflowEdge Edge { get; set; }
        [ForeignKey("TargetNodeId")]
        public virtual WorkflowNode? TargetNode { get; set; }
    }
}