﻿using EngagetoBackGroundJobs.Interfaces;
using EngagetoContracts.TemplateContracts;
using EngagetoContracts.WebhookContracts.DownloadMedia;
using EngagetoDapper.Data.Interfaces.AnalyticsInterfaces;
using EngagetoDapper.Data.Interfaces.GenericInterfaces;
using EngagetoDapper.Data.Interfaces.IEmailInterfaces;
using EngagetoDapper.Data.Interfaces.ILogInterfaces;
using EngagetoDapper.Data.Interfaces.IUserInterfaces.IUserServices;
using EngagetoEntities.Dtos.MetaDto;
using EngagetoEntities.Entities;
using EngagetoEntities.Enums;
using EngagetoEntities.ServiceModels;
using EngagetoEntities.UserEntities.Dtos;
using EngagetoEntities.UserEntities.Models;
using EngagetoEntities.Utilities;
using Hangfire.Console;
using Hangfire.Server;
using iTextSharp.text.pdf;
using Microsoft.Extensions.Configuration;
using Newtonsoft.Json.Linq;
using System.Text.RegularExpressions;

namespace EngagetoBackGroundJobs.Implementation
{
    public class NotificationService : INotificationService
    {
        private readonly IEmailService _emailService;
        private readonly IUserService _userService;
        private readonly IConfiguration _configuration;
        private readonly IGenericRepository _genericRepository;
        private readonly IAnalyticsService _analyticsService;
        private readonly ITemplate _template;
        private readonly IHttpService _httpService;
        private readonly ILogHistoryService _logHistoryService;
        private readonly IMediaURL _mediaURL;
        public NotificationService(IEmailService emailService,
            IUserService userService,
            IConfiguration configuration,
            IGenericRepository genericRepository,
            IAnalyticsService analyticsService,
            ITemplate template,
            IHttpService httpService,
            ILogHistoryService logHistoryService,
            IMediaURL mediaURL)
        {
            _emailService = emailService;
            _userService = userService;
            _configuration = configuration;
            _genericRepository = genericRepository;
            _analyticsService = analyticsService;
            _template = template;
            _httpService = httpService;
            _logHistoryService = logHistoryService;
            _mediaURL = mediaURL;
        }
        public async Task<bool> SendSubscriptionEmailNotificationAsync(PerformContext? context)
        {
            try
            {
                var plateformEmail = _configuration["SmtpSettings:SmtpUsername"];
                var expiringOrExpiredSubscriptions = await _userService.GetExpiringOrExpiredSubscriptionsAsync();
                if (!expiringOrExpiredSubscriptions.Any())
                    return true;

                foreach (var subscription in expiringOrExpiredSubscriptions)
                {
                    var isActive = subscription.PlanEndDate.HasValue ? subscription.PlanEndDate.Value.Date >= DateTime.UtcNow.Date : false;
                    if (subscription.PlanEndDate.HasValue && string.IsNullOrEmpty(plateformEmail))
                    {

                        var emailBody = GetEmailBody(isActive, subscription.BusinessName, subscription.PlanName, subscription.PlanEndDate.Value);
                        var toEmail = new List<string>() { plateformEmail };
                        if (!string.IsNullOrEmpty(subscription.Email))
                            toEmail.Add(subscription.Email);

                        EmailMessage emailMessage = new EmailMessage()
                        {
                            To = toEmail,
                            Subject = "Your Subscription is About to Expire.",
                            Body = emailBody
                        };
                        await _emailService.SendEmailAsync(emailMessage);
                    }
                }
                return true;
            }
            catch (Exception ex)
            {
                HangfireConsoleWrite(context, "Error got to sending to subscription expired notification.", ConsoleTextColor.Red);
                return false;
            }
        }

        public async Task<bool> SendAnalyticsReportAsync(PerformContext? context)
        {
            try
            {
                var plateformCompanyId = _configuration["Facebook:BusinessIdLocal"];
                var plateformNumber = _configuration["Facebook:PhoneNumberId"];
                var url = MetaApi.GetSendTemplateUrl(plateformNumber);
                var businessDetails = await _genericRepository.GetAllAsync<Ahex_CRM_BusinessDetails>();
                var businessMetaDetails = await _genericRepository.GetByObjectAsync<BusinessDetailsMeta>(new Dictionary<string, object>
                {
                    { "BusinessId",plateformCompanyId}
                });
                var token = businessMetaDetails.FirstOrDefault()?.Token;
                var templates = await _genericRepository.GetRecordByRequestFilter<EngagetoEntities.Entities.Template>(new List<EngagetoDapper.Data.Dtos.RequestFilterDto>
                {
                    new EngagetoDapper.Data.Dtos.RequestFilterDto("TemplateName",new List<string>
                    {"weekly_overview_report", "weekly_agenperformance_report", "weekly_inbox_report" },"in")
                }, "Templates");

                if (businessDetails.Any())
                {
                    foreach (var businessDetail in businessDetails)
                    {
                        try
                        {
                            var overviewTemplate = templates.FirstOrDefault(x => x.TemplateName == "weekly_overview_report");
                            var agentPerformanceTemplate = templates.FirstOrDefault(x => x.TemplateName == "weekly_agenperformance_report");
                            var inboxTemplate = templates.FirstOrDefault(x => x.TemplateName == "weekly_inbox_report");

                            if (string.IsNullOrEmpty(businessDetail.PhoneNumber) || !businessMetaDetails.Any())
                                continue;

                            CancellationTokenSource cts = new CancellationTokenSource();
                            CancellationToken cancellationToken = cts.Token;

                            var startDate = DateTime.UtcNow.Date.AddDays(-7);
                            var endDate = DateTime.UtcNow.AddDays(1);

                            var overViewReport = await _analyticsService.GetAnalyticsOverviewAsync(businessDetail.Id.ToString(),
                                new EngagetoEntities.UserEntities.Dtos.AnalyticsOverviewFilterDto
                                {
                                    FromDate = startDate,
                                    ToDate = endDate,
                                    IsExportData = false
                                }, cancellationToken);

                            var agenPerformance = await _analyticsService.GetAgentPerformanceAsync(businessDetail.Id.ToString(),
                                new EngagetoEntities.UserEntities.Dtos.AnalyticsOverviewFilterDto
                                {
                                    FromDate = startDate,
                                    ToDate = endDate,
                                    IsExportData = false
                                }, cancellationToken);

                            var inboxAnalytics = await _analyticsService.GetInboxAnalyticsAsync(businessDetail.Id.ToString(), new EngagetoEntities.UserEntities.Dtos.InboxAnalyticsFilterDto
                            {
                                FromDate = startDate,
                                ToDate = endDate,
                                IsExportData = false
                            }, cancellationToken);

                            var result = await SendOverviewReportAsync(overviewTemplate, businessDetail, plateformCompanyId, startDate, endDate, overViewReport, url, token);
                            await SendInboxReportAsync(inboxTemplate, businessDetail, plateformCompanyId, startDate, endDate, inboxAnalytics, url, token);
                            if (agenPerformance.FirstOrDefault() != null && agentPerformanceTemplate != null)
                                await SendAgentPerformanceReportAsync(agentPerformanceTemplate, businessDetail, plateformCompanyId, startDate, endDate, agenPerformance.FirstOrDefault() ?? new(), url, token);
                            HangfireConsoleWrite(context, $"The process of sending analytics reports to the client was completed successfully. companyId:{businessDetail.Id}", ConsoleTextColor.Green);
                            await _logHistoryService.SaveSuccessLogHistoryAsyn("SendAnalyticsReportAsync", null, result, $"Overview/{businessDetail.Id}");
                        }
                        catch (Exception ex)
                        {
                            HangfireConsoleWrite(context, $"Error in the process of sending analytics reports to the company:{businessDetail.Id}", ConsoleTextColor.Red);
                            await _logHistoryService.SaveErrorLogHistoryAsyn("SendAnalyticsReportAsync", null, $"Processing to Analytics report for client: {businessDetail.Id}", ex.Message, ex.StackTrace);
                        }
                    }
                }
                return true;
            }
            catch (Exception ex)
            {

                HangfireConsoleWrite(context, $"Error in the process of sending analytics reports to the company.", ConsoleTextColor.Red);
                await _logHistoryService.SaveErrorLogHistoryAsyn("SendAnalyticsReportAsync", null, "Processing to Analytics report", ex.Message, ex.StackTrace);
                return false;
            }
        }
        #region Helper Methods
        private static void HangfireConsoleWrite(PerformContext console, string text, ConsoleTextColor consoleTextColor = null)
        {
            if (consoleTextColor != null)
            {
                console.SetTextColor(consoleTextColor);
            }

            console.WriteLine(text);

            if (consoleTextColor != null)
            {
                console.ResetTextColor();
            }
        }
        public async Task<EngagetoEntities.Entities.Conversations> SendOverviewReportAsync(EngagetoEntities.Entities.Template template,
            Ahex_CRM_BusinessDetails businessDetail,
            string platformCompanyId,
            DateTime startDate,
            DateTime endDate,
            AnalyticsOverviewDto overviewReport,
            string url,
            string token)
        {
            try
            {
                var bodyVariableValues = new List<string>
                {
                    businessDetail.BusinessName ?? string.Empty,
                    startDate.ToString("dd MMM,yyyy"),
                    endDate.ToString("dd MMM,yyyy"),
                    overviewReport.TotalConversation.ToString(),
                    overviewReport.ConversationDuration.ToString(),
                    overviewReport.AverageResponseTime.ToString()
                };

                return await SendReportAsync(template, businessDetail, platformCompanyId, "weekly_overview_report",
                    bodyVariableValues, url, token);
            }
            catch (Exception ex)
            {
                throw new Exception(ex.Message, ex);
            }
        }

        public async Task<EngagetoEntities.Entities.Conversations> SendInboxReportAsync(EngagetoEntities.Entities.Template template,
            Ahex_CRM_BusinessDetails businessDetail,
            string platformCompanyId,
            DateTime startDate,
            DateTime endDate,
            InboxAnalyticsDasboardDto inboxAnalyticsDashboard,
            string url,
            string token)
        {
            try
            {
                var bodyVariableValues = new List<string>
                {
                    startDate.ToString("dd MMM,yyyy"),
                    endDate.ToString("dd MMM,yyyy"),
                    inboxAnalyticsDashboard.InboxAnalytics.OpenCount.ToString(),
                    inboxAnalyticsDashboard.InboxAnalytics.PendingCount.ToString(),
                    inboxAnalyticsDashboard.InboxAnalytics.ResolvedCount.ToString(),
                    inboxAnalyticsDashboard.InboxAnalytics.ExpiredCount.ToString(),
                    inboxAnalyticsDashboard.InboxAnalytics.UnAssinedCount.ToString()
                };

                return await SendReportAsync(template, businessDetail, platformCompanyId, "weekly_inbox_report",
                    bodyVariableValues, url, token);
            }
            catch (Exception ex)
            {
                throw new Exception(ex.Message, ex);
            }
        }

        public async Task<EngagetoEntities.Entities.Conversations> SendAgentPerformanceReportAsync(
            EngagetoEntities.Entities.Template template,
            Ahex_CRM_BusinessDetails businessDetail,
            string platformCompanyId,
            DateTime startDate,
            DateTime endDate,
            AgentPerformanceDto agentPerformance,
            string url,
            string token)
        {
            try
            {
                var bodyMatches = Regex.Matches(template.Body, @"\{\{(\d+)\}\}");
                var bodyVariableValues = new List<string>
                {
                    startDate.ToString("dd MMM,yyyy"),
                    endDate.ToString("dd MMM,yyyy")
                };

                var jsonObject = await _template.GetWhatsAppSendTemplatePayloadAsync(
                    platformCompanyId,
                    null,
                    "weekly_agenperformance_report",
                    string.Concat(businessDetail.CountryCode.Replace("+", ""), businessDetail.PhoneNumber),
                    null,
                    bodyVariableValues
                );

                //var pdfTemplate = "https://uploadengagteto.s3.eu-north-1.amazonaws.com/3af5bd4e-a3fc-46e2-92d4-b37a107e2f6a_8/26/2024%202:18:11%20PM.pdf";
                var filePath = TemplateFileHelper.GetTemplateFilePath("AgenPerfomance.pdf");
                string mimeType = CommonHelper.GetMimeFileType(filePath);

                using (var memoryStream = new MemoryStream())
                {
                    try
                    {
                        using (var pdfReader = new PdfReader(filePath))
                        {
                            using (var pdfStamper = new PdfStamper(pdfReader, memoryStream))
                            {
                                AcroFields pdfFormFields = pdfStamper.AcroFields;
                                pdfFormFields.SetField("Customer Phone Number", agentPerformance.Agent.Name);
                                pdfFormFields.SetField("Assigned Agent", agentPerformance.AssignedCount.ToString() ?? string.Empty);
                                pdfFormFields.SetField("First Customer Message Date Time", agentPerformance.ReassignedCount.ToString() ?? string.Empty);
                                pdfFormFields.SetField("Assigned Date Time", agentPerformance.RespondedCount.ToString() ?? string.Empty);
                                pdfFormFields.SetField("Agent 1st Response Time", agentPerformance.ResolvedCount.ToString() ?? string.Empty);
                                pdfStamper.FormFlattening = true;
                            }
                        }
                        byte[] buffer = CommonHelper.CompressPdf(memoryStream.ToArray());
                        string uploadedFileUrl = await _mediaURL.UploadFileAsync(buffer, Guid.NewGuid() + "_AgentPerformance" + ".pdf");
                        var components = jsonObject["template"]?["components"] as JArray;
                        if (components != null)
                        {
                            foreach (var component in components)
                            {
                                var parameters = component["parameters"] as JArray;
                                if (parameters != null)
                                {
                                    foreach (var parameter in parameters)
                                    {
                                        var type = parameter["type"]?.ToString();
                                        if (type == "document")
                                        {
                                            var documentObject = parameter["document"] as JObject ?? new();
                                            documentObject["link"] = uploadedFileUrl;
                                            break;

                                        }
                                    }
                                }
                            }
                        }

                        template.MediaAwsUrl = uploadedFileUrl;
                        var result = await _httpService.PostAsync<SendMessageResponseDto>(
                            url,
                            new Dictionary<string, string> { { "Authorization", $"Bearer {token}" } },
                            jsonObject
                        );
                        var status = GetStatus(result?.Messages);
           
                        var contact = (!string.IsNullOrEmpty(businessDetail.PhoneNumber) && !string.IsNullOrEmpty(businessDetail.CountryCode))
                                                ? (await _genericRepository.GetByObjectAsync<Contacts>( new Dictionary<string, object>
                                                                                {
                                                                                   { "Contact", businessDetail.PhoneNumber },
                                                                                   { "CountryCode", businessDetail.CountryCode },
                                                                                   { "BusinessId", Guid.Parse(platformCompanyId) }
                                                                                })
                                                  ).FirstOrDefault()
                                                : null;

                        EngagetoEntities.Entities.Conversations conv = new()
                        {
                            Id = Guid.NewGuid(),
                            TemplateMediaType = template.MediaType,
                            TemplateMediaUrl = template.MediaAwsUrl,
                            CreatedAt = DateTime.UtcNow,
                            WhatsAppMessageId = result.Messages?.FirstOrDefault()?.WhatsAppMessageId ?? string.Empty,
                            From = platformCompanyId,
                            To = string.Concat(businessDetail.CountryCode.Replace("+", ""), businessDetail.PhoneNumber),                          
                            BusinessId = Guid.TryParse(platformCompanyId, out var tempGuid) ? tempGuid : Guid.Empty,
                            ContactId = contact != null ? contact.ContactId : Guid.Empty,
                            TemplateBody = ReplaceMatches(template.Body, bodyMatches, bodyVariableValues ?? new List<string>())
                                .Replace("\\\"", "\"").Replace("\\n", "\n"),
                            TemplateHeader = null,
                            TemplateFooter = template.Footer,
                            CallButtonName = null,
                            PhoneNumber = null,
                            UrlButtonNames = null,
                            RedirectUrls = null,
                            QuickReplies = null,
                            Status = status,
                            MessageType = EngagetoEntities.Enums.MessageType.Template,
                            ReferenceId = template.TemplateId.ToString()
                        };

                        return await _template.SaveConversationAsync(conv);
                    }
                    catch (Exception ex)
                    {
                        throw new Exception("An error occurred while processing the PDF or sending the WhatsApp message.", ex);
                    }
                }
            }
            catch (Exception ex)
            {
                throw new Exception("An error occurred while generating the agent performance report.", ex);
            }
        }



        private async Task<EngagetoEntities.Entities.Conversations> SendReportAsync(EngagetoEntities.Entities.Template template,
            Ahex_CRM_BusinessDetails businessDetail,
            string platformCompanyId,
            string reportType,
            List<string> bodyVariableValues,
            string url,
            string token)
        {
            var bodyMatches = Regex.Matches(template.Body, @"\{\{(\d+)\}\}");
            var jsonObject = await _template.GetWhatsAppSendTemplatePayloadAsync(
                platformCompanyId,
                null,
                reportType,
                string.Concat(businessDetail.CountryCode.Replace("+", ""), businessDetail.PhoneNumber),
                null,
                bodyVariableValues
            );

            var result = await _httpService.PostAsync<SendMessageResponseDto>(
                url,
                new Dictionary<string, string> { { "Authorization", $"Bearer {token}" } },
                jsonObject
            );

            var status = GetStatus(result?.Messages);
            var contact = (!string.IsNullOrEmpty(businessDetail.PhoneNumber) && !string.IsNullOrEmpty(businessDetail.CountryCode))
                                                ? (await _genericRepository.GetByObjectAsync<Contacts>(new Dictionary<string, object>
                                                                                {
                                                                                   { "Contact", businessDetail.PhoneNumber },
                                                                                   { "CountryCode", businessDetail.CountryCode },
                                                                                   { "BusinessId", platformCompanyId }
                                                                                })
                                                  ).FirstOrDefault()
                                                : null;
            EngagetoEntities.Entities.Conversations sendTemplateEntity = new()
            {
                Id = Guid.NewGuid(),
                TemplateMediaType = template.MediaType,
                TemplateMediaUrl = template.MediaAwsUrl,
                CreatedAt = DateTime.UtcNow,
                WhatsAppMessageId = result.Messages?.FirstOrDefault()?.WhatsAppMessageId ?? string.Empty,
                From = platformCompanyId,
                To = string.Concat(businessDetail.CountryCode.Replace("+", ""), businessDetail.PhoneNumber),
                BusinessId = Guid.TryParse(platformCompanyId, out var tempGuid) ? tempGuid : Guid.Empty,
                ContactId = contact != null ? contact.ContactId : Guid.Empty,
                TemplateBody = ReplaceMatches(template.Body, bodyMatches, bodyVariableValues ?? new List<string>())
                    .Replace("\\\"", "\"").Replace("\\n", "\n"),
                TemplateHeader = null,
                TemplateFooter = template.Footer,
                CallButtonName = null,
                PhoneNumber = null,
                UrlButtonNames = null,
                RedirectUrls = null,
                QuickReplies = null,
                Status = status,
                MessageType = MessageType.Template,
                ReferenceId = template.TemplateId.ToString()
            };

            return await _template.SaveConversationAsync(sendTemplateEntity);
        }

        private string ReplaceMatches(string templateBody, MatchCollection bodyMatches, List<string> bodyVariableValues)
        {
            foreach (Match match in bodyMatches)
            {
                if (int.TryParse(match.Groups[1].Value, out int index) && index >= 0 && index <= bodyVariableValues.Count)
                {
                    templateBody = templateBody.Replace(match.Value, bodyVariableValues[index - 1]);
                }
            }
            return templateBody;
        }
        private ConvStatus GetStatus(List<EngagetoEntities.Dtos.MetaDto.Messages>? messages)
        {
            ConvStatus status = ConvStatus.sent;
            switch (messages?.FirstOrDefault()?.Status ?? SendStatus.Rejected)
            {
                case SendStatus.Accepted:
                    status = ConvStatus.sent;
                    break;
                case SendStatus.Rejected:
                    status = ConvStatus.failed;
                    break;
                default:
                    status = ConvStatus.failed;
                    break;
            }
            return status;
        }

        public string GetEmailBody(bool isExpiringSoon, string businessName, string planName, DateTime planEndDate)
        {
            if (isExpiringSoon)
            {
                return $@"
            <html>
            <body>
                <p>Dear {businessName},</p>
                <p>We wanted to remind you that your subscription plan for the <strong>Engageto</strong> application is set to expire on <strong>{planEndDate:dd MMMM, yyyy}</strong>.</p>
                <p><strong>Plan Details:</strong></p>
                <ul>
                    <li>Plan Name: {planName}</li>
                    <li>Expiration Date: {planEndDate:dd MMMM, yyyy}</li>
                </ul>
                <p>Please take action to renew your subscription to ensure uninterrupted service in the Engageto application. You can renew your plan by visiting your account dashboard or contacting our support team.</p>
                <p>If you need assistance or have any questions, feel free to reach out to us.</p>
                <p>Thank you for being a valued customer of Engageto.</p>
                <p>Best regards,<br />The Engageto Team <br/> <EMAIL> <br/> https://www.app.engageto.com/</p>
            </body>
            </html>";
            }
            else
            {
                return $@"
            <html>
            <body>
                <p>Dear {businessName},</p>
                <p>We noticed that your subscription plan for the <strong>Engageto</strong> application expired on <strong>{planEndDate:dd MMMM, yyyy}</strong>.</p>
                <p><strong>Plan Details:</strong></p>
                <ul>
                    <li>Plan Name: {planName}</li>
                    <li>Expiration Date: {planEndDate:dd MMMM, yyyy}</li>
                </ul>
                <p>To continue enjoying our services in the Engageto application, please renew your subscription as soon as possible. You can renew your plan by logging into your account dashboard or contacting our support team.</p>
                <p>If you need assistance or have any questions, we're here to help.</p>
                <p>Thank you for your continued trust in the Engageto application.</p>
                <p>Best regards,<br />The Engageto Team <br/> <EMAIL> <br/> https://www.app.engageto.com/</p>
            </body>
            </html>";
            }
        }


        #endregion
    }
}
