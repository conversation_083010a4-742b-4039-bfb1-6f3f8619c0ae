﻿using Newtonsoft.Json;

namespace EngagetoEntities.Dtos.MetaDto
{
    public class SendTemplateErrorResponseDto
    {

        [JsonProperty("error")]
        public ErrorDetails SendTemplateErrorMessage { get; set; }
    }

    public class ErrorDetails
    {
        [JsonProperty("message")]
        public string Message { get; set; }


        [JsonProperty("error_data")]
        public ErrorData ErrorData { get; set; }


    }

    public class ErrorData
    {
        [JsonProperty("messaging_product")]
        public string MessagingProduct { get; set; }

        [JsonProperty("details")]
        public string Details { get; set; }
    }
}
