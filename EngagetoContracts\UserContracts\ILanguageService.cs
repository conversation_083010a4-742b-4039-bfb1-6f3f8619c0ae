﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using EngagetoEntities.UserEntities.Models;

namespace EngagetoContracts.UserContracts
{
    public interface ILanguageService
    {
        Task<Guid> CreateLanguageAsync(string languageCode, string languageName);
        Task<bool> UpdateLanguageAsync(Guid languageId, LanguageUpdateDto languageUpdateDto);
        Task<bool> DeleteLanguageAsync(Guid languageId);
        Task<IEnumerable<LanguageDto>> GetAllLanguagesAsync(string searchKeyword = null);
        Task<Guid> SetUserPreferredLanguageAsync(Guid userId, string languageName);
    }
}
