﻿using EngagetoEntities.Enums;
using System.ComponentModel.DataAnnotations;

namespace EngagetoEntities
{
    public class CarouselTemplateDto
    {
        public string BusinessId { get; set; } = default!;
        public Guid UserId { get; set; }
        public Guid? TemplateId { get; set; } // this is for recreating template from draft
        public string TemplateName { get; set; } = default!;

        [Required(ErrorMessage = "Language can't be empty")]
        public Language LanguageCode { get; set; }

        [Required(ErrorMessage = "Category can't be empty")]
        public WATemplateCategory Category { get; set; }
        public string? SubCategory { get; set; }
        public MediaType MediaType { get; set; }
        public string? Header { get; set; }
        public string Body { get; set; }
        public string? MediaFile { get; set; }
        public List<CarouselCardsDto>? CarouselCardsJson { get; set; }
    }

    public class CarouselCardsDto
    {
        public MediaType? MediaUrlType { get; set; }
        public string? HeaderMediaUrl { get; set; }
        public string Body { get; set; } = default!;
        public string? CallButtonName { get; set; }
        public string? CountryCode { get; set; }
        public string? PhoneNumber { get; set; }
        public List<string>? UrlButtonName { get; set; }
        public List<string>? RedirectUrl { get; set; }
        public List<string>? QuickReply { get; set; }
    }

    public class SendCarouselTemplateDto
    {
        public string BusinessId { get; set; } = default!;
        public Guid UserId { get; set; }
        public Guid TemplateId { get; set; }
        public List<string> Contact { get; set; } = default!;
        public string[]? BodyVariableValues { get; set; }
        public List<CarouselCardVariableDto>? CarouselVariables { get; set; }
    }

    public class CarouselCardVariableDto
    {
        public string[]? BodyCarouselVariableValues { get; set; }
        public string[]? RedirectUrlVariableValues { get; set; }
        public string? MediaUrl { get; set; }
    }
}
