﻿using Newtonsoft.Json.Linq;

namespace EngagetoContracts.MetaContracts
{
    public interface IMetaAuthenticationService
    {
        void AddBodyComponent(ref JArray components, bool? add_security_recommendation);

        // Method to add a footer component to the components array
        void AddFooterComponent(ref JArray components, string? code_expiration_minutes);

        // Method to add buttons component to the components array
        void AddButtonsComponent(ref JArray components, string? otpType, string? packageName, string? signatureHash);

        // Method to build the complete components array
        JObject BuildComponents(bool? add_security_recommendation, string? code_expiration_minutes, string? otpType, string package_name, string signature_hash);

    }
}
