﻿using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace EngagetoEntities.UserEntities.Models
{
    [Table("Payments")]
    public class Payments
    {
        [Key]
        [DatabaseGenerated(DatabaseGeneratedOption.Identity)]
        public int Id { get; set; }
        public string? OrderId { get; set; }
        public string? Receipt { get; set; }
        public string? PaymentId { get; set; }
        public decimal? Amount { get; set; }
        public string? Currency { get; set; }
        public string? Status { get; set; }
        public string? Signature { get; set; }
        public string? UserId { get; set; }
        public string? CompanyId { get; set; }
        public string? PlanName { get; set; }
        public DateTime? PlanStartDate { get; set; }
        public DateTime? PlanEndDate { get; set; }
        public DateTime? PaymentDate { get; set; }
    }
   /* public class OrderRequestModel
    {
        public decimal Amount { get; set; }
        public string? subscriptionPlanId { get; set; }
    }
    public class PaymentVerificationModel
    {
        public string? RazorpayOrderId { get; set; }
        public string? RazorpayPaymentId { get; set; }
        public string? RazorpaySignature { get; set; }
    }*/

}
