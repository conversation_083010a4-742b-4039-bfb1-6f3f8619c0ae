﻿using EngagetoEntities.Entities;
using Hangfire.Server;

namespace EngagetoBackGroundJobs.Interfaces
{
    public interface IConversationAnalythicsService
    {
        Task<bool> ProcessMetaConversationAnalyticsRequestAsync(PerformContext? context,
            BusinessDetailsMeta account,
            DateTime startDate,
            DateTime endDate,
            long startDateUnixtime,
            long endDateUnixtime);
    }
}
