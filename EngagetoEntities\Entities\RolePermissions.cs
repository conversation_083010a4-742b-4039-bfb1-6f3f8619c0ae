﻿using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace EngagetoEntities.Entities
{
    [Table("RolePermissions")]
    public class RolePermissions
    {
        [Key]
        [DatabaseGenerated(DatabaseGeneratedOption.Identity)]
        public int Id { get; set; }
        public string? RoleId { get; set; }
        public string? AccessToRoleId { get; set; }

        public string? CompanyId { get; set; }
        /* [NotMapped]
         public Role? Role { get; set; }
         [NotMapped]
         public Role? AccessToRole { get; set; }*/
    }
}
