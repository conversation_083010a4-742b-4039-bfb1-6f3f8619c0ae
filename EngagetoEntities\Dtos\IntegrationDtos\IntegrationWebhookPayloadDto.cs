﻿namespace EngagetoEntities.Dtos.IntegrationDtos
{
    public class IntegrationWebhookPayloadDto
    {
        public string BusinessId { get; set; }
        public Dictionary<string, List<string>>? Payload { get; set; } 
        public List<string> Contact { get; set; }
        public List<string> Conversation { get; set; }
        public List<String> IntegrationPayloadForExternal { get; set; }
    }
}
