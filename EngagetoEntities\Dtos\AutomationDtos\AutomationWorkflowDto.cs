﻿using EngagetoEntities.Enums;
using System.ComponentModel.DataAnnotations;


namespace EngagetoEntities.Dtos.AutomationDtos
{
    public class AutomationWorkflowDto
    {
        public Guid? Id { get; set; }
        public int? StepType { get; set; }
        public string? WorkflowName { get; set; }
        [Required(ErrorMessage = "CompanyId is a required Field")]
        public Guid CompanyId { get; set; }
        public Guid? UserId { get; set; }
        public string? Title { get; set; }
        public WorkflowResponseType? FlowResponseType { get; set; }
        public List<string>? AutoReplyWorkflowButtons { get; set; }
        public Guid? WorkflowListId { get; set; }
        public AutoReplyWorkflowCustomerRequestDto? ResponseCustomerMessage { get; set; }
        public List<VeriableDto>? Veriables { get; set; }
        public string? WebhookTriggerUrl { get; set; }
        public string? WebhookTriggerBody { get; set; }
        public List<WebhookHeader>? WebhookTriggerHeader { get; set; }
        public string? WebhookTriggerHttpMethod { get; set; }

        public string? DefaultErrorResponse { get; set; }


    }
    public class WebhookHeader
    {
        public string? Key { get; set; }
        public string? Value { get; set; }
    }
    public class WorkflowButtonDto
    {
        public string Text { get; set; }
    }

    public class WorkflowResponseButtonDto
    {
        public List<WorkflowButtonDto> WorkflowButtons { get; set; }
        public AutoReplyWorkflowCustomerResponseDto ResponseCustomerMessage { get; set; }
    }
    public class WorkflowResponseList
    {
        public WorkflowListDto AutoReplyWorkflowList { get; set; }
        public AutoReplyWorkflowCustomerResponseDto ResponseCustomerMessage { get; set; }
    }

    public class AutomationWorkflowResponseDto
    {
        public Guid Id { get; set; }
        public int? Step { get; set; }
        public int? StepType { get; set; }
        public string? WorkflowName { get; set; }
        [Required(ErrorMessage = "CompanyId is a required Field")]
        public Guid CompanyId { get; set; }
        public Guid? UserId { get; set; }
        [Required(ErrorMessage = "Title is a required Field")]
        public string Title { get; set; }
        public WorkflowResponseType FlowResponseType { get; set; }
        public List<string>? AutoReplyWorkflowButtons { get; set; }
        public WorkflowListDto? AutoReplyWorkflowList { get; set; }
        public AutoReplyWorkflowCustomerResponseDto ResponseCustomerMessage { get; set; }
        public List<VeriableDto>? Veriables { get; set; }
        public string? WebhookTriggerUrl { get; set; }
        public string? WebhookTriggerBody { get; set; }
        public List<WebhookHeader>? WebhookTriggerHeader { get; set; }
        public string? DefaultErrorResponse { get; set; }
        public string? WebhookTriggerHttpMethod { get; set; }
    }
}
