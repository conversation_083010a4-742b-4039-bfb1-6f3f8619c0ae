﻿using EngagetoMeta.Dtos;
using EngagetoMeta.Repositories;
using EngagetoMeta.Services;
using EngagetoMeta.Settings;
using Microsoft.AspNetCore.Mvc;
using Newtonsoft.Json;

namespace EngagetoMeta.Controllers
{
    [Route("api/[controller]")]
    [ApiController]
    public class WebhookController : ControllerBase
    {
        private readonly WebhookConfig _webhookConfig;
        private readonly MetaConfig _metaConfig;
        private readonly HttpClient _httpClient;
        private readonly ILogHistoryRepository _logger;
        private readonly IServiceBusProducerService _serviceBusProducer;
        private readonly ILogger<WebhookController> _controllerLogger;

        public WebhookController(
            WebhookConfig webhookConfig,
            MetaConfig metaConfig,
            IHttpClientFactory httpClientFactory,
            ILogHistoryRepository logger,
            IServiceBusProducerService serviceBusProducer,
            ILogger<WebhookController> controllerLogger)
        {
            _webhookConfig = webhookConfig;
            _metaConfig = metaConfig;
            _httpClient = httpClientFactory.CreateClient();
            _logger = logger;
            _serviceBusProducer = serviceBusProducer;
            _controllerLogger = controllerLogger;
        }


        [HttpGet("receive-WAmessage")]
        public ActionResult<string> VerifyWAWebhookAsync([FromQuery(Name = "hub.mode")] string hubMode,
                                                                   [FromQuery(Name = "hub.challenge")] int hubChallenge,
                                                                   [FromQuery(Name = "hub.verify_token")] string hubVerifyToken)
        {
            var verifyToken = _webhookConfig.VerifyToken;
            if (!hubVerifyToken.Equals(verifyToken))
            {
                return Forbid("VerifyToken doesn't match");
            }
            return Ok(hubChallenge);
        }

        [HttpPost("receive-WAmessage")]
        public async Task<IActionResult> ReceivedWAWebhookMessageAsync([FromBody] dynamic receivedMessage, CancellationToken token)
        {
            var field = "";
            string whatsaapBusinessAccId = "";
            var correlationId = Guid.NewGuid().ToString();

            try
            {
                // Log the received message for debugging
                _controllerLogger.LogInformation("📥 Received webhook message | CorrelationId: {CorrelationId}", correlationId);
                string messageString = receivedMessage?.ToString() ?? "null";
                _controllerLogger.LogDebug("📋 Raw webhook data: {WebhookData}", messageString);

                // Deserialize the incoming message to the DTO
                var webhookDto = JsonConvert.DeserializeObject<WebhookDto>(receivedMessage.ToString());

                // Extract relevant data
                whatsaapBusinessAccId = webhookDto.Entry?[0].Id ?? string.Empty;
                var entry = webhookDto.Entry?[0];
                var change = entry?.Changes?[0];
                field = change?.Field ?? "unknown";
                var contactDetails = change?.Value?.Contacts?[0];
                var statuses = change?.Value?.Statuses;

                _controllerLogger.LogInformation("🔍 Processing field: {Field} | BusinessId: {BusinessId} | CorrelationId: {CorrelationId}",
                    field, whatsaapBusinessAccId, correlationId);

                // Use the webhook DTO directly for the producer service

                // Send message to Azure Service Bus using new ServiceBusProducerService
                _controllerLogger.LogInformation("📤 Sending message to Service Bus | Field: {Field} | BusinessId: {BusinessId} | CorrelationId: {CorrelationId}",
                    field, whatsaapBusinessAccId, correlationId);

                await _serviceBusProducer.SendWebhookMessageAsync(webhookDto, receivedMessage, token);

                // Log success to database
                await _logger.SaveSuccessLogHistoryAsyn(
                    $"ProcessWAWebhookMessageAsync:{field}",
                    receivedMessage,
                    null,
                    $"Message sent to Service Bus queue",
                    whatsaapBusinessAccId,
                    field);

                return Ok(new
                {
                    Status = "Success",
                    Message = "Webhook message received and queued for processing",
                    CorrelationId = correlationId,
                    Field = field,
                    BusinessId = whatsaapBusinessAccId
                });
            }
            catch (Exception ex)
            {
                await _logger.SaveErrorLogHistoryAsyn(
                    $@"ProcessWAWebhookMessageAsync:{field}",
                    receivedMessage,
                    "Error processing webhook message",
                    ex.Message,
                    ex.StackTrace,
                    whatsaapBusinessAccId,
                    field);

                _controllerLogger.LogError(ex, "❌ Error processing webhook | CorrelationId: {CorrelationId} | Error: {Error}",
                    correlationId, ex.Message);

                return StatusCode(StatusCodes.Status500InternalServerError, new
                {
                    Status = "Error",
                    Message = "An error occurred while processing the webhook request",
                    CorrelationId = correlationId,
                    Error = ex.Message
                });
            }
        }

        // 🔹 Convert EngagetoMeta WebhookDto for Service Bus
        private WebhookDto ConvertToServiceBusDto(WebhookDto webhookDto)
        {
            try
            {
                // Return the webhook DTO as is - no conversion needed
                if (webhookDto == null)
                {
                    throw new InvalidOperationException("WebhookDto is null");
                }

                return webhookDto;
            }
            catch (Exception ex)
            {
                _controllerLogger.LogError(ex, "❌ Error validating WebhookDto: {Error}", ex.Message);
                throw;
            }
        }

        // 🔹 Fire-and-Forget API Call Helper (keeping for potential future use)
        private void FireAndForgetPostAsync(string url, HttpContent content)
        {
            Task.Run(async () =>
            {
                try
                {
                    var l = await _httpClient.PostAsync(url, content);
                }
                catch (Exception ex)
                {
                    _controllerLogger.LogError(ex, "❌ API Call Failure: {Url} | Error: {Error}", url, ex.Message);
                }
            }).ContinueWith(t =>
            {
                if (t.Exception != null)
                {
                    _controllerLogger.LogError(t.Exception, "❌ Unhandled Exception in fire-and-forget call");
                }
            }, TaskContinuationOptions.OnlyOnFaulted);
        }
    }
}
