﻿using EngagetoDapper.Data.Interfaces.AnalyticsInterfaces;
using EngagetoEntities.Dtos.AutomationDtos;
using EngagetoEntities.Response;
using EngagetoEntities.UserEntities.Dtos;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;

namespace Engageto.Controllers.AnalyticsControllers
{
    [Route("api/[controller]")]
    [ApiController]
    public class AnalyticsController : ControllerBase
    {
        private readonly IAnalyticsService _analyticsService;
        public AnalyticsController(IAnalyticsService analyticsService)
        {
            _analyticsService = analyticsService;
        }

        [HttpGet("GetOverviewAnalytics/{companyId}")]
        [Authorize]
        public async Task<IActionResult> GetOverviewAnalytics(string companyId, [FromQuery] AnalyticsOverviewFilterDto filterDot, CancellationToken cancellationToken)
        {
            try
            {
                if (filterDot.IsExportData ?? false)
                {
                    var reportStream = await _analyticsService.DownloadReportAsync(companyId, filterDot.FromDate.Value, filterDot.ToDate.Value, cancellationToken);
                    return Ok(new ApiResponse<byte[]>
                    {
                        Success = true,
                        Data = reportStream.ToArray(),
                        Message = "overview Analytics data."
                    });
                }
                else
                {
                    var result = await _analyticsService.GetAnalyticsOverviewAsync(companyId, filterDot, cancellationToken);
                    return Ok(new ApiResponse<AnalyticsOverviewDto>
                    {
                        Success = true,
                        Data = result,
                        Message = "Overview Analytics data."
                    });
                }
            }
            catch (Exception ex)
            {
                return StatusCode(500, new ApiResponse<string>()
                {
                    Success = false,
                    Message = ex.Message,
                    Errors = ex.StackTrace
                });
            }
        }
        [HttpGet("GetAgentPerformance/{companyId}")]
        [Authorize]
        public async Task<IActionResult> GetAgentPerformance(string companyId, [FromQuery] AnalyticsOverviewFilterDto filterDot, CancellationToken cancellationToken)
        {
            try
            {
                if (filterDot.IsExportData ?? false)
                {
                    var reportStream = await _analyticsService.DownalodAgentPerformanceReportAsync(companyId, filterDot, cancellationToken);
                    return Ok(new ApiResponse<byte[]>
                    {
                        Success = true,
                        Data = reportStream.ToArray(),
                        Message = "Agent performance Analytics data."
                    });
                }
                var result = await _analyticsService.GetAgentPerformanceAsync(companyId, filterDot, cancellationToken);
                return Ok(new ApiResponse<List<AgentPerformanceDto>>
                {
                    Success = true,
                    Data = result,
                    Message = "Agent Performance data."
                });
            }
            catch (Exception ex)
            {
                return StatusCode(500, new ApiResponse<string>()
                {
                    Success = false,
                    Message = ex.Message,
                    Errors = ex.StackTrace
                });
            }
        }

        [HttpGet("GetInboxAnalytics/{companyId}")]
        [Authorize]
        public async Task<IActionResult> GetInboxAnalytics(string companyId, [FromQuery] InboxAnalyticsFilterDto filterDot, CancellationToken cancellationToken)
        {
            try
            {
                if (filterDot.IsExportData ?? false)
                {
                    var reportStream = await _analyticsService.DownloadInboxAnalyticsReportAsync(companyId, filterDot, cancellationToken);
                    return Ok(new ApiResponse<byte[]>
                    {
                        Success = true,
                        Data = reportStream.ToArray(),
                        Message = "Inbox Analytics data."
                    });
                }
                var result = await _analyticsService.GetInboxAnalyticsAsync(companyId, filterDot, cancellationToken);
                return Ok(new ApiResponse<InboxAnalyticsDasboardDto>
                {
                    Success = true,
                    Data = result,
                    Message = "Inbox Analytics data."
                });
            }
            catch (Exception ex)
            {
                return StatusCode(500, new ApiResponse<string>()
                {
                    Success = false,
                    Message = ex.Message,
                    Errors = ex.StackTrace
                });
            }
        }
        [HttpGet("conversation-analytics/{tenantId}")]
        [AllowAnonymous]
        public async Task<IActionResult> GetTenantConversationCountAsync(string tenantId, DateTime? fromDate, DateTime? toDate, CancellationToken cancellationToken)
        {
            try
            {
                var result = await _analyticsService.GetTenantCCostAnalyticAsync(tenantId, fromDate ?? DateTime.UtcNow.AddDays(-1).Date, toDate ?? DateTime.UtcNow.AddDays(-1).Date, cancellationToken);
                return Ok(new ApiResponse<TenantCCostAnalyticsDto>
                {
                    Success = true,
                    Data = result,
                    Message = "conversation anlaytics cost and count details"
                });
            }
            catch (Exception ex)
            {
                return StatusCode(500, new ApiResponse<string>()
                {
                    Success = false,
                    Message = ex.Message,
                    Errors = ex.StackTrace
                });
            }
        }

        [HttpGet("template-analytics/{tenantId}")]
        [AllowAnonymous]
        public async Task<IActionResult> GetTemplateAnalytics(string tenantId, DateTime? fromDate, DateTime? toDate, CancellationToken cancellationToken)
        {
            try
            {
                var result = await _analyticsService.GetTemplateAnalyticsAsync(tenantId, fromDate ?? DateTime.UtcNow.AddDays(-1).Date, toDate ?? DateTime.UtcNow.AddDays(-1).Date, cancellationToken);
                return Ok(new ApiResponse<TemplateAnalyticsDto>
                {
                    Success = true,
                    Data = result,
                    Message = "conversation anlaytics cost and count details"
                });
            }
            catch (Exception ex)
            {
                return StatusCode(500, new ApiResponse<string>()
                {
                    Success = false,
                    Message = ex.Message,
                    Errors = ex.StackTrace
                });
            }
        }
    }
}
