using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace EngagetoEntities.Entities
{
    [Table("WorkflowKeywords")]
    public class WorkflowKeyword : BaseEntity
    {
        [Key]
        public Guid Id { get; set; }
        [Required]
        public Guid WorkflowId { get; set; }
        [Required]
        [StringLength(50)]
        public Guid WorkflowNodeId { get; set; }
        public string Keyword { get; set; }
        public string BusinessId { get; set; }
        [ForeignKey("WorkflowId")]
        public virtual Workflow Workflow { get; set; }
    }
} 