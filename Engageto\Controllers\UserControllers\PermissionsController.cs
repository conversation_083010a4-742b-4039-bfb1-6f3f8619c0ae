﻿using EngagetoContracts.UserContracts;
using EngagetoEntities.Dtos.ApiResponseDtos;
using EngagetoEntities.Entities;
using EngagetoEntities.UserEntities.Models;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using Microsoft.EntityFrameworkCore;
using System.ComponentModel.DataAnnotations;
using System.Security.Claims;

namespace Engageto.Controllers.UserControllers
{
    [Route("api/[controller]")]
    [ApiController]
    public class PermissionsController : ControllerBase
    {
        private readonly IPermissionsService _permissionService;
        private readonly ApplicationDBContext _dbContext;
        private readonly EngagetoDapper.Data.Interfaces.IUserInterfaces.IUserServices.IUserService _userService;

        public PermissionsController(IPermissionsService permissionService,
            ApplicationDBContext dbContext,
            EngagetoDapper.Data.Interfaces.IUserInterfaces.IUserServices.IUserService userService)
        {
            _permissionService = permissionService;
            _dbContext = dbContext;
            _userService = userService;
        }
        /*  [HttpGet("ViewAllPermissions")]
         // [AuthorizePermission("ViewAllPermissions")]
          public async Task<IActionResult> GetAllPermissions(Guid UserId, string searchKeyword = null)
          {
              try
              {
                  var permissions = await _permissionService.GetAllPermissionsAsync(UserId, searchKeyword);
                  return Ok(permissions);
              }
              catch (Exception ex)
              {
                  return StatusCode(500, $"Internal server error: {ex.Message}");
              }
          }

          [HttpPost("add-permissions")]
          //[AuthorizePermission("AddNewPermissions", RoleConstants.PlatformOwner, RoleConstants.PlatformAdmin)]
          public async Task<IActionResult> CreatePermission([FromBody] PermissionDto newPermission)
          {
              try
              {
                  var currentUserId = Guid.Parse(User.FindFirstValue(ClaimTypes.Name));
                  var permissionId = await _permissionService.CreatePermissionAsync(currentUserId, newPermission);
                  return Ok(permissionId);
              }
              catch (UnauthorizedAccessException ex)
              {
                  return Unauthorized(new { Message = ex.Message });
              }
              catch (Exception ex)
              {
                  return StatusCode(500, new { Message = "Internal Server Error" });
              }
          }

          [HttpPut("update-permissions/{permissionId}")]
         // [AuthorizePermission("UpdatePermissions", RoleConstants.PlatformOwner, RoleConstants.PlatformAdmin)]
          public async Task<IActionResult> UpdatePermission(Guid permissionId, [FromBody] PermissionDto updatedPermission)
          {
              try
              {
                  var currentUserId = Guid.Parse(User.FindFirstValue(ClaimTypes.Name));

                  var success = await _permissionService.UpdatePermissionAsync(currentUserId, permissionId, updatedPermission);
                  if (success)
                  {
                      return Ok("Permission updated successfully.");
                  }
                  else
                  {
                      return NotFound();
                  }
              }
              catch (UnauthorizedAccessException ex)
              {
                  return Unauthorized(new { Message = ex.Message });
              }
              catch (Exception ex)
              {
                  return StatusCode(500, new { Message = "Internal Server Error" });
              }
          }

          [HttpDelete("delete-permissions/{permissionId}")]
         // [AuthorizePermission("DeletePermissions", RoleConstants.PlatformOwner, RoleConstants.PlatformAdmin)]
          public async Task<IActionResult> DeletePermission(Guid permissionId)
          {
              try
              {
                  var currentUserId = Guid.Parse(User.FindFirstValue(ClaimTypes.Name));
                  var success = await _permissionService.DeletePermissionAsync(currentUserId, permissionId);
                  if (success)
                  {
                      return Ok("Permission deleted successfully!");
                  }
                  else
                  {
                      return NotFound();
                  }
              }
              catch (UnauthorizedAccessException ex)
              {
                  return Unauthorized(new { Message = ex.Message });
              }
              catch (Exception ex)
              {
                  return StatusCode(500, new { Message = "Internal Server Error" });
              }
          }
          [HttpPost("assign-permissions-to-user")]
         // [AuthorizePermission("AssignPermissionsToUsers", RoleConstants.PlatformOwner, RoleConstants.PlatformAdmin)]
          public async Task<IActionResult> CreateAssignPermission([FromBody] AssignPermissionsToRoleIdDto assignmentDto)
          {
              try
              {
                  var currentUserId = Guid.Parse(User.FindFirstValue(ClaimTypes.Name));
                  var success = await _permissionService.CreateAssignPermissionAsync(currentUserId, assignmentDto);
                  if (success)
                  {
                      return Ok("Permission assigned successfully.");
                  }
                  return BadRequest("Failed to assign permission.");
              }
              catch (UnauthorizedAccessException ex)
              {
                  return Unauthorized(new { Message = ex.Message });
              }
              catch (Exception ex)
              {
                  return StatusCode(500, new { Message = "Internal Server Error" });
              }
          }*/

        /*  [HttpPut("change-permissions/{roleId}/{companyId}")]
          [Authorize]
          public async Task<IActionResult> UpdatePermissionsAndStatus(Guid roleId,string companyId, [FromBody] List<AssignPermissionsDto> updatedAssignPermissions)
          {
              try
              {
                  var currentUserIdClaim = User.Claims.FirstOrDefault(c => c.Type == ClaimTypes.Name);
                  if (currentUserIdClaim == null || !Guid.TryParse(currentUserIdClaim.Value, out Guid currentUserId))
                  {
                      return BadRequest(new { Message = "Invalid current user." });
                  }

                  var success = await _permissionService.UpdatePermissionsAndStatusAsync(currentUserId, roleId, companyId,updatedAssignPermissions);
                  if (success)
                  {
                      return Ok(new { Message = "Permissions updated successfully." });
                  }
                  else
                  {
                      return StatusCode(500, new { Message = "Failed to update permissions and status." });
                  }
              }
              catch (UnauthorizedAccessException ex)
              {
                  return Unauthorized(new { Message = ex.Message });
              }
              catch (InvalidOperationException ex)
              {
                  return NotFound(new { Message = ex.Message });
              }
              catch (Exception ex)
              {
                  return StatusCode(500, new { Message = $"Internal Server Error: {ex.Message}" });
              }
          }*/

        /* [HttpDelete("delete-assigned-permissions/{id}")]
        // [AuthorizePermission("DeleteUserPermissions", RoleConstants.PlatformOwner, RoleConstants.PlatformAdmin)]
         public async Task<IActionResult> DeleteAssignPermission(Guid id, [FromBody] AssignPermissionsToRoleIdDto assignmentDto)
         {
             try
             {
                 var currentUserId = Guid.Parse(User.FindFirstValue(ClaimTypes.Name));
                 var success = await _permissionService.DeleteAssignPermissionAsync(currentUserId, assignmentDto);
                 if (success)
                 {
                     return Ok("Permission deleted successfully.");
                 }
                 return NotFound("Permission not found.");
             }
             catch (UnauthorizedAccessException ex)
             {
                 return Unauthorized(new { Message = ex.Message });
             }
             catch (Exception ex)
             {
                 return StatusCode(500, new { Message = "Internal Server Error" });
             }
         }*/
        /*
                [HttpGet("view-all-permissions")]
               // [AuthorizePermission("ViewAllusersPermissions", RoleConstants.PlatformOwner, RoleConstants.PlatformAdmin)]
                public async Task<IActionResult> GetAssignPermissions()
                {
                    try
                    {
                        var currentUserId = Guid.Parse(User.FindFirstValue(ClaimTypes.Name));
                        var permissions = await _permissionService.GetAssignPermissionsAsync(currentUserId);
                        return Ok(permissions);
                    }
                    catch (UnauthorizedAccessException ex)
                    {
                        return Unauthorized(new { Message = ex.Message });
                    }
                    catch (Exception ex)
                    {
                        return StatusCode(500, new { Message = "Internal Server Error" });
                    }
                }*/
        /*  [HttpGet("get-permissions-data/{roleId}/{companyId}")]
          public async Task<IActionResult> GetPermissionsAndAssignPermissions(Guid roleId,string companyId)
          {
              try
              {
                  var permissionsAndAssignPermissions = await _permissionService.GetPermissionsAndAssignPermissionsAsync(roleId, companyId);
                  return Ok(permissionsAndAssignPermissions);
              }
              catch (Exception ex)
              {
                  return StatusCode(500, new { Message = $"Internal Server Error: {ex.Message}" });
              }
          }*/
        [HttpGet("get-menu-permissions-data/{roleId}/{companyId}")]
        public async Task<IActionResult> GetMenusAndMenuPermissionsAsync(string roleId, string companyId)
        {
            try
            {
                var permissionsAndAssignPermissions = _permissionService.GetMenuHierarchy1(roleId, companyId);
                return Ok(permissionsAndAssignPermissions);
            }
            catch (Exception ex)
            {
                return StatusCode(500, new { Message = $"Internal Server Error: {ex.Message}" });
            }
        }
        [HttpGet("get-permission-menus/{roleId}/{companyId}")]
        [Authorize]
        public async Task<IActionResult> GetPermissionMenuAsync([Required] string roleId, [Required] string companyId)
        {
            try
            {
                var permissionMenu = await _userService.GetPermissionMenuAsync(companyId, roleId);
                return Ok(new ApiResponse<Dictionary<string, List<MainMenu>>>
                {
                    Success = true,
                    Data = permissionMenu,
                    Message = "Get the Permission Menu successfully."
                });
            }
            catch (Exception ex)
            {
                return StatusCode(500, new ApiResponse<string>()
                {
                    Success = false,
                    Message = ex.Message,
                    Error = ex?.StackTrace ?? ""
                });
            }
        }

        /* [HttpPut("assignPermissions-to-client/{roleId}/{companyId}")]
         [Authorize]
         public async Task<IActionResult> AssignPermissionsToClient(Guid roleId, string companyId, [FromBody] List<AssignPermissionsDto> updatedAssignPermissions)
         {
             try
             {
                 var currentUserIdClaim = User.Claims.FirstOrDefault(c => c.Type == ClaimTypes.Name);
                 if (currentUserIdClaim == null || !Guid.TryParse(currentUserIdClaim.Value, out Guid currentUserId))
                 {
                     return BadRequest(new { Message = "Invalid current user." });
                 }

                 var success = await _permissionService.AssignPermissionsToClientAsync(currentUserId, roleId, companyId, updatedAssignPermissions);
                 if (success)
                 {
                     return Ok(new { Message = "Permissions updated successfully." });
                 }
                 else
                 {
                     return StatusCode(500, new { Message = "Failed to update permissions and status." });
                 }
             }
             catch (UnauthorizedAccessException ex)
             {
                 return Unauthorized(new { Message = ex.Message });
             }
             catch (InvalidOperationException ex)
             {
                 return NotFound(new { Message = ex.Message });
             }
             catch (Exception ex)
             {
                 return StatusCode(500, new { Message = $"Internal Server Error: {ex.Message}" });
             }
         }*/

        [HttpPut("update-menu-status/{roleId}/{companyId}")]
        [Authorize]
        //  [AuthorizeMenu("statusChange")]
        public async Task<IActionResult> UpdateMenuStatus(string roleId, string companyId, string menuName, bool status)
        {
            try
            {
                var currentUserIdClaim = User.Claims.FirstOrDefault(c => c.Type == ClaimTypes.Name);
                if (currentUserIdClaim == null || !Guid.TryParse(currentUserIdClaim.Value, out Guid currentUserIdValue))
                {
                    return BadRequest(new { Message = "Invalid current user." });
                }
                // Get the current user's role ID
                var currentUserRole = await _dbContext.Ahex_CRM_Users
                    .Where(u => u.Id == currentUserIdValue && u.CompanyId == companyId)
                    .Select(r => r.RoleId)
                    .FirstOrDefaultAsync();

                if (currentUserRole == null)
                {
                    return BadRequest(new { Message = "Current user role not found." });
                }

                // Check if the passed roleId matches the current user's role ID
                if (roleId.ToLower() == currentUserRole.ToLower())
                {
                    return BadRequest(new { Message = "You don't have access to update your own permissions" });
                }
                var success = await _permissionService.UpdateMenuStatusAsync(roleId, companyId, menuName, status, currentUserIdValue);
                if (success)
                {
                    return Ok(new { Message = "Permissions updated successfully." });
                }
                else
                {
                    return StatusCode(500, new { Message = "Failed to update menu status." });
                }
            }
            catch (UnauthorizedAccessException ex)
            {
                return Unauthorized(new { Message = ex.Message });
            }
            catch (InvalidOperationException ex)
            {
                return NotFound(new { Message = ex.Message });
            }
            catch (Exception ex)
            {
                return StatusCode(500, new { Message = $"Internal Server Error: {ex.Message}" });
            }
        }


        [HttpPut("update-clientmenu-status/{roleId}/{companyId}")]
        [Authorize]
        // [Authorize("statusChange")]
        public async Task<IActionResult> UpdateClientMenuStatus(string roleId, string companyId, string menuName, bool status)
        {
            try
            {
                var currentUserIdClaim = User.Claims.FirstOrDefault(c => c.Type == ClaimTypes.Name);
                if (currentUserIdClaim == null || !Guid.TryParse(currentUserIdClaim.Value, out Guid currentUserIdValue))
                {
                    return BadRequest(new { Message = "Invalid current user." });
                }

                var success = await _permissionService.UpdateClientMenuStatusAsync(roleId, companyId, menuName, status, currentUserIdValue);
                if (success)
                {
                    return Ok(new { Message = "Permissions updated successfully." });
                }
                else
                {
                    return StatusCode(500, new { Message = "Failed to update          permission." });
                }
            }
            catch (UnauthorizedAccessException ex)
            {
                return Unauthorized(new { Message = ex.Message });
            }
            catch (InvalidOperationException ex)
            {
                return NotFound(new { Message = ex.Message });
            }
            catch (Exception ex)
            {
                return StatusCode(500, new { Message = $"Internal Server Error: {ex.Message}" });
            }
        }
        [HttpGet("get-user-menuPermissions")]
        [Authorize]
        public async Task<IActionResult> GetMenuHierarchy(string roleId, string companyId)
        {
            try
            {
                var menuHierarchy = await _userService.GetMenuDetailsAsync(companyId, roleId);
                //var menuHierarchy = _permissionService.GetMenuHierarchy(roleId, companyId);
                return Ok(menuHierarchy);
            }
            catch (Exception ex)
            {
                return StatusCode(500, $"Failed to fetch menu hierarchy: {ex.Message}");
            }
        }
    }
}
