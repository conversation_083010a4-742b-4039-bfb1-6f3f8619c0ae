﻿using System;
using System.Net;
using System.Net.Mail;
using System.Text.RegularExpressions;
using System.Threading.Tasks;
using DocumentFormat.OpenXml.Wordprocessing;
using EngagetoContracts.UserContracts;
using Microsoft.AspNetCore.Http;
using EngagetoEntities.Utilities;
using Microsoft.Extensions.Configuration;
using Microsoft.IdentityModel.Tokens;

namespace EngagetoRepository.Repository
{
    public class EmailService : IEmailService, IDisposable
    {


        private readonly IConfiguration _configuration;
        public EmailService(IConfiguration config)
        {
            _configuration = config;

        }

        public async Task<bool> SendEmailAsync(string to, string subject, string body, string logoUrl, string loginLink)
        {
            try
            {
                var senderDisplayName = "Engageto";
                var WebsiteUrl = _configuration["SmtpSettings:EmailWebsite"];
                using (var mailMessage = new MailMessage
                {
                    From = new MailAddress(_configuration["SmtpSettings:SmtpUsername"], senderDisplayName),
                    Subject = subject,
                    IsBodyHtml = true,
                })
                {
                    mailMessage.To.Add(to);

                    string htmlBody = $@"
                                       <html>
                                        <body>
                                            <p>{body}</p><br>
                                            <p> <a href='{loginLink}'>Click here  to login.</a></p>
                                            <p>Regards,<br>
                                            Engageto CRM,<br>
                                            <a href='{WebsiteUrl}'>www.engageto.com</a>
                                            </p></p>
                                            <img src='{logoUrl}' alt='Logo' style='width: 80px; height: 40px; margin-right: 10px;'>   
                    
                                        </body>
                                    </html>";

                    mailMessage.Body = htmlBody;
                    var _smtpClient = new SmtpClient
                    {
                        Host = "smtp.gmail.com",
                        Port = 587,
                        UseDefaultCredentials = false,
                        Credentials = new NetworkCredential(_configuration["SmtpSettings:SmtpUsername"], _configuration["SmtpSettings:SmtpPassword"]),
                        EnableSsl = true,
                    };

                    _smtpClient.Send(mailMessage);
                }

                return true;
            }
            catch (SmtpException ex)
            {
                Console.WriteLine($"SmtpException: {ex.Message}");
                return false;
            }
            catch (InvalidOperationException ex)
            {
                Console.WriteLine($"InvalidOperationException: {ex.Message}");
                return false;
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Exception: {ex.Message}");
                return false;
            }
        }

        public async Task<bool> SendClientEmailAsync(string to, string subject, string clientName, string email, string temporaryPassword, string logoUrl, string loginLink)
        {
            try
            {
                var senderDisplayName = "Engageto";
                var WebsiteUrl = _configuration["SmtpSettings:EmailWebsite"];
                string htmlBody = EmailTemplates.GetWelcomeEmail(clientName,email,temporaryPassword);
                using (var mailMessage = new MailMessage
                {
                    From = new MailAddress(_configuration["SmtpSettings:SmtpUsername"], senderDisplayName),
                    Subject = subject,
                    Body =  htmlBody,
                    IsBodyHtml = true,
                })
                {
                    mailMessage.To.Add(to);
                    var _smtpClient = new SmtpClient
                    {
                        Host = "smtp.gmail.com",
                        Port = 587,
                        UseDefaultCredentials = false,
                        Credentials = new NetworkCredential(_configuration["SmtpSettings:SmtpUsername"], _configuration["SmtpSettings:SmtpPassword"]),
                        EnableSsl = true,
                
                    };
                    await _smtpClient.SendMailAsync(mailMessage);
                }

                return true;
            }
            catch (SmtpException ex)
            {
                Console.WriteLine($"SmtpException: {ex.Message}");
                return false;
            }
            catch (InvalidOperationException ex)
            {
                Console.WriteLine($"InvalidOperationException: {ex.Message}");
                return false;
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Exception: {ex.Message}");
                return false;
            }
        }

        public void Dispose()
        {
            var _smtpClient = new SmtpClient
            {
                Host = "smtp.gmail.com",
                Port = 587,
                UseDefaultCredentials = false,
                Credentials = new NetworkCredential(_configuration["SmtpSettings:SmtpUsername"], _configuration["SmtpSettings:SmtpPassword"]),
                EnableSsl = true,
            };
            _smtpClient.Dispose();
        }

        public async Task<bool> SendEmailViaUtility(string subject, string body, List<IFormFile> attachment, List<string> to, List<string> cc, List<string> bcc)
        {
            try
            {

                var senderDisplayName = "Engageto";
                using (var mailMessage = new MailMessage
                {
                    From = new MailAddress(_configuration["SmtpSettings:SmtpUsername"], senderDisplayName),
                    Subject = subject,
                    Body = body,
                    IsBodyHtml = true,
                })
                {
                    if (to?.Any()==true && to[0]!=null&& to!=null)
                    {
                     
                        mailMessage.To.Add(string.Join(",", to));
                    }
                    if (cc?.Any()==true && cc[0]!=null && cc!=null)
                    {
                        mailMessage.CC.Add(string.Join(",", cc));
                    }
                    if (bcc?.Any() == true && bcc[0]!=null &&bcc!=null)
                    {
                        mailMessage.Bcc.Add(string.Join(",", bcc));
                    }
                    if (attachment?.Any() == true )
                    {
                        attachment.ForEach(attachment =>
                        {
                            var stream = attachment.OpenReadStream();
                            var fileAttachment = new Attachment(stream, attachment.FileName);
                            mailMessage.Attachments.Add(fileAttachment);
                        });
                    }

                    using (var smtpClient = new SmtpClient
                    {
                        Host = "smtp.gmail.com",
                        Port = 587,
                        UseDefaultCredentials = false,
                        Credentials = new NetworkCredential(_configuration["SmtpSettings:SmtpUsername"], _configuration["SmtpSettings:SmtpPassword"]),
                        EnableSsl = true
                    })
                    {
                        await smtpClient.SendMailAsync(mailMessage);
                    }
                }
                return true;
            }
            catch (Exception ex)
            {
                return false;
            }
        }

        public async Task<bool> SendQualityRatingEmail(List<string> to, string previous_quality_score, string new_quality_score, List<string> clientName)
        {
            var senderDisplayName = "Engageto";
            string subject = "";
            string body = "";

            try
            {
                switch (new_quality_score.ToLower().Trim())
                {
                    case "green":
                        subject = "Your WhatsApp Campaign Template Quality is Excellent!";
                        body = $@"
                <html>
                <body style='font-family: Arial, sans-serif; line-height: 1.6; color: #333;'>
                    <p>Dear {{0}},</p>
                    <p>We’re excited to inform you that your WhatsApp campaign template has been successfully updated from <strong style='color: black;'>{previous_quality_score}</strong> to <strong style='color: black;'>{new_quality_score}</strong>! 🎉</p>
                    <p>Your campaign template is now rated <strong style='color: green;'>Green</strong>, which means it meets the highest standards and is performing excellently.</p>
                    <h3>Key highlights of your updated template:</h3>
                    <ul>
                        <li><strong>High Delivery Rate:</strong> Your messages are successfully reaching your recipients.</li>
                        <li><strong>Strong Engagement:</strong> Your recipients are interacting positively with your content.</li>
                        <li><strong>Optimal Template Quality:</strong> The new template follows best practices, ensuring top-quality delivery and user engagement.</li>
                    </ul>
                    <p>Great job on updating your template! If you need any help optimizing further, feel free to reach out to us.</p>
                    <p><strong>Best regards,</strong><br>Engageto</p>
                </body>
                </html>";
                        break;

                    case "red":
                        subject = "Urgent: Your WhatsApp Campaign Template Has Been Blocked!";
                        body = $@"
                <html>
                <body style='font-family: Arial, sans-serif; line-height: 1.6; color: #333;'>
                    <p>Dear {{0}},</p>
                    <p>We regret to inform you that your updated WhatsApp campaign template, which has been changed from <strong style='color: black;'>{previous_quality_score}</strong> to <strong style='color: black;'>{new_quality_score}</strong>, has been rated <strong style='color: red;'>Red</strong> and is currently <strong style='color: black;'>Blocked</strong>. This means the template has been flagged for non-compliance or other issues preventing your campaign from running effectively.</p>
                    <h3>Here are the Key points:</h3>
                    <ul>
                        <li><strong>Blocked Status:</strong> Your campaign template has been suspended, and messages are not being delivered.</li>
                        <li><strong>Compliance Issues:</strong> The new template may not meet all necessary guidelines or best practices.</li>
                        <li><strong>Immediate Action Required:</strong> To resume your campaign, please review and modify your template to ensure it adheres to all required standards.</li>
                    </ul>
                    <p>We recommend reviewing the template guidelines and resolving any potential issues. If you need assistance, please contact our support team immediately.</p>
                    <p><strong>Best regards,</strong><br>The WhatsApp CRM Team</p>
                </body>
                </html>";
                        break;

                    case "yellow":
                        subject = "Your WhatsApp Campaign Template Quality Needs Attention!";
                        body = $@"
                <html>
                <body style='font-family: Arial, sans-serif; line-height: 1.6; color: #333;'>
                    <p>Dear {{0}},</p>
                    <p>We noticed that your WhatsApp campaign template has been recently updated from <strong style='color: black;'>{previous_quality_score}</strong> to <strong style='color: black;'>{new_quality_score}</strong>!, but it is currently rated <strong style='color: orange;'>Yellow</strong>, indicating risks that may impact your campaign.</p>
                    <h3>Here’s what we’ve observed:</h3>
                    <ul>
                        <li><strong>Delivery Issues:</strong> Some messages might not be reaching their intended recipients.</li>
                        <li><strong>Decreased Engagement:</strong> There may be a slight drop in user interactions with the new template.</li>
                        <li><strong>Potential Template Improvements:</strong> We suggest reviewing the new template for optimization to avoid further issues.</li>
                    </ul>
                    <p>Please review the new template and ensure it aligns with best practices. If you need guidance, we’re here to help!</p>
                    <p><strong>Best regards,</strong><br>Engageto</p>
                </body>
                </html>";
                        break;

                    default:
                        return false;
                }
                int count = Math.Min(to.Count, clientName.Count);

                using (var smtpClient = new SmtpClient
                {
                    Host = "smtp.gmail.com",
                    Port = 587,
                    UseDefaultCredentials = false,
                    Credentials = new NetworkCredential(_configuration["SmtpSettings:SmtpUsername"], _configuration["SmtpSettings:SmtpPassword"]),
                    EnableSsl = true
                })
                {
                    for (int i = 0; i < count; i++)
                    {
                        string bodyTemplate = string.Format(body, clientName[i]);
                        using (var mailMessage = new MailMessage
                        {
                            From = new MailAddress(_configuration["SmtpSettings:SmtpUsername"], senderDisplayName),
                            Subject = subject,
                            Body = bodyTemplate,
                            IsBodyHtml = true
                        })
                        {
                            mailMessage.To.Add(to[i]);
                            await smtpClient.SendMailAsync(mailMessage);
                        }
                    }
                }
          
                return true;

            }
            catch (Exception e)
            {
                return false;
            }

        }



    }
}
