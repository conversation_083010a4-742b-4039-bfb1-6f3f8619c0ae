﻿using EngagetoEntities.Enums;
using System.ComponentModel.DataAnnotations;


namespace EngagetoEntities.Dtos.AutomationDtos
{
    public class AutoReplyAutomationDto
    {
        public Guid? Id { get; set; }
        public Guid CompanyId { get; set; }
        public Guid? UserId { get; set; }
        [Required]
        public string Input { get; set; }
        public ResponseType AutomationResponseType { get; set; }
        public List<string>? InputVariations { get; set; }
        public string? WorkflowName { get; set; }
    }
}
