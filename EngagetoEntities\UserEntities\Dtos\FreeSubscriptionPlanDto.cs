﻿using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace EngagetoEntities.UserEntities.Dtos
{
    public class FreeSubscriptionPlanDto
    {
        [Required]
        public string CompanyId { get; set; }
        [Required]
        public int PlanId { get; set; }
        [Required]
        public string PlanType { get; set; }
        public Guid? UserId { get; set; }
    }
}
