﻿using EngagetoContracts.UserContracts;
using EngagetoEntities.Entities;
using EngagetoEntities.UserEntities.Models;
using Microsoft.EntityFrameworkCore;

namespace EngagetoRepository.UserRepository
{
    public class Authentication : IAuthentication
    {
        private readonly ApplicationDBContext _context;

        public Authentication(ApplicationDBContext context)
        {
            _context = context;
        }
        public async Task<Ahex_CRM_Users> GetUserAsync(string Email, string password)
        {
            var user = await _context.Ahex_CRM_Users
           .FirstOrDefaultAsync(u => u.EmailAddress == Email && u.Status);


            if (user != null && VerifyPassword(password, user.Password))
            {
                var company = await _context.Ahex_CRM_BusinessDetails.FirstOrDefaultAsync(m => m.Id.ToString().ToLower() == user.CompanyId && m.Status);
                if (company != null)
                {
                    return user;
                }

            }

            return null;
        }
        /*  public async Task<AccountDetails> GetUserAsync1(string Email, string password)
          {
              var user = await _context.ManageAccounts
             .FirstOrDefaultAsync(u => u.EmailAddress == Email);

              if (user != null && VerifyPassword(password, user.Password))
              {
                  return user;
              }

              return null;
          }
          public async Task<ClientsAccountDetails> GetUserAsync2(string Email, string password)
          {
              var user = await _context.ClientsAccountDetails
             .FirstOrDefaultAsync(u => u.EmailAddress == Email);

              if (user != null && VerifyPassword(password, user.Password))
              {
                  return user;
              }

              return null;
          }*/
        private bool VerifyPassword(string password, string hashedPassword)
        {
            return BCrypt.Net.BCrypt.Verify(password, hashedPassword);
        }

    }
}
