﻿using Hangfire.Server;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace EngagetoBackGroundJobs.Interfaces
{
    public interface IJobTaskService
    {
        public Task GetConversationAnalyticsCost(PerformContext? context);
        public Task CleanUnusedDataAsync(PerformContext? context);
        public Task SendNotificationAsync(PerformContext? context);
        public Task SendAnalyticsReportNotificationAsync(PerformContext? context);
    }
}
