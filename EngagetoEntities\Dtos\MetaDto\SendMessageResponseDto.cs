﻿using EngagetoEntities.Enums;
using Newtonsoft.Json;

namespace EngagetoEntities.Dtos.MetaDto
{
    public class SendMessageResponseDto
    {
        public string MessagingProduct { get; set; }

        [JsonProperty("contacts")]
        public List<Contact> Contacts { get; set; }

        [JsonProperty("messages")]
        public List<Messages> Messages { get; set; }
    }
    public class Messages
    {
        [JsonProperty("id")]
        public string WhatsAppMessageId { get; set; }

        [JsonProperty("message_status")]
        public SendStatus Status { get; set; }
    }
    public class Contact
    {
        [JsonProperty("input")]
        public string To { get; set; }
    }
}
