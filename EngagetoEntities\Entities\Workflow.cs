using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace EngagetoEntities.Entities
{
    [Table("Workflows")]
    public class Workflow : BaseEntity
    {
        [Key]
        public Guid Id { get; set; }
        public string Name { get; set; }
        public bool IsActive { get; set; }
        public string CompanyId { get; set; }
        public Guid? UserId { get; set; }
        public virtual ICollection<WorkflowNode> Nodes { get; set; } = new List<WorkflowNode>();
        public virtual ICollection<WorkflowEdge> Edges { get; set; } = new List<WorkflowEdge>();
        public virtual ICollection<WorkflowKeyword> Keywords { get; set; } = new List<WorkflowKeyword>();
    }
}