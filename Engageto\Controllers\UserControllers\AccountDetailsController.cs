﻿using EngagetoContracts.MetaContracts;
using EngagetoContracts.UserContracts;
using EngagetoEntities.Dtos.MetaDto;
using EngagetoEntities.Response;
using EngagetoEntities.UserEntities.Dtos;
using EngagetoEntities.Utilities;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using Newtonsoft.Json;
using System.Net.Http.Headers;
using System.Security.Claims;

namespace Engageto.Controllers.UserControllers
{
    [Route("api/[controller]")]
    [ApiController]
    public class AccountDetailsController : ControllerBase
    {
        private readonly IAccountDetailsService _manageAccountService;
        private readonly IMetaApiService _metaApiService;

        public AccountDetailsController(IAccountDetailsService manageAccountService,
            IMetaApiService metaApiService)
        {
            _manageAccountService = manageAccountService;
            _metaApiService = metaApiService;
        }
        [HttpGet("get-all-accounts")]
        // [AuthorizePermission("Allow to view All Users page")]
        public async Task<IActionResult> GetAllManageAccounts(
          [FromQuery] string searchQuery = null,
          [FromQuery] bool? includeInactive = true,
          [FromQuery] string sortBy = null,
          [FromQuery] bool isSortAscending = true)
        {
            try
            {
                var currentUserIdClaim = User.Claims.FirstOrDefault(c => c.Type == ClaimTypes.Name);

                if (currentUserIdClaim == null || !Guid.TryParse(currentUserIdClaim.Value, out Guid currentUserId))
                {
                    return BadRequest(new { Message = "Invalid current user." });
                }
                bool includeInactiveValue = includeInactive ?? true;
                var accounts = await _manageAccountService.GetAllManageAccountsAsync(currentUserId, searchQuery, includeInactiveValue, sortBy, isSortAscending);

                if (accounts != null)
                {
                    return Ok(accounts);
                }
                else
                {
                    return BadRequest(new { Message = "Failed to retrieve accounts." });
                }
            }
            catch (Exception ex)
            {
                return StatusCode(500, new { Message = $"Internal Server Error: {ex.Message}" });
            }
        }
        [HttpGet("{Id}")]
        // [AuthorizePermission("ViewAllAccountDetails")]
        [Authorize]
        public async Task<IActionResult> GetManageAccountById(Guid Id)
        {
            try
            {

                var currentUserIdClaim = User.Claims.FirstOrDefault(c => c.Type == ClaimTypes.Name);

                if (currentUserIdClaim == null || !Guid.TryParse(currentUserIdClaim.Value, out Guid currentUserId))
                {
                    return BadRequest(new { Message = "Invalid current user." });
                }

                var manageAccountDto = await _manageAccountService.GetManageAccountByIdAsync(currentUserId, Id);

                return Ok(manageAccountDto);
            }
            catch (InvalidOperationException ex)
            {
                return NotFound(new { Message = ex.Message });
            }
            catch (Exception ex)
            {
                return StatusCode(500, new { Message = $"Internal Server Error: {ex.Message}" });
            }
        }
        /*  [HttpPost("create-account")]
          [Authorize]
          public async Task<IActionResult> CreateManageAccount([FromForm] ManageAccountDto request,IFormFile Image)
          {
              try
              {
                  var currentUserIdClaim = User.Claims.FirstOrDefault(c => c.Type == ClaimTypes.Name);

                  if (currentUserIdClaim == null || !Guid.TryParse(currentUserIdClaim.Value, out Guid currentUserId))
                  {
                      return BadRequest(new { Message = "Invalid current user." });
                  }


                  var manageAccountId = await _manageAccountService.CreateManageAccountAsync(currentUserId, request, Image);

                  if (manageAccountId != Guid.Empty)
                  {

                      var existingAccount = await _manageAccountService.GetManageAccountByIdAsync(currentUserId, manageAccountId);
                      return Ok(new { ExistingAccount = existingAccount });
                  }
                  else
                  {

                      return Ok(new { ManageAccountId = manageAccountId });
                  }
              }
              catch (InvalidOperationException ex)
              {
                  return BadRequest(new { Message = ex.Message });
              }
              catch (Exception ex)
              {
                  return StatusCode(500, new { Message = $"Internal Server Error: {ex.Message}" });
              }
          }*/
        [HttpPut("Update-Account/{userId}")]
        [Authorize]
        // [AuthorizeMenu("editDetails")]
        // [AuthorizeMenuAccess("editDetails")]
        public async Task<IActionResult> UpdateManageAccount([FromForm] Ahex_CRM_UsersDto updateRequest)
        {
            try
            {

                var currentUserIdClaim = User.Claims.FirstOrDefault(c => c.Type == ClaimTypes.Name);

                if (currentUserIdClaim == null || !Guid.TryParse(currentUserIdClaim.Value, out Guid userId))
                {
                    return BadRequest(new { Message = "Invalid current user." });
                }


                var updatedManageAccountId = await _manageAccountService.UpdateManageAccountAsync(userId, updateRequest);

                return Ok(new { ManageAccountId = updatedManageAccountId, Message = "Account updated successfully." });
            }
            catch (InvalidOperationException ex)
            {
                return BadRequest(new { Message = ex.Message });
            }
            catch (Exception ex)
            {
                return StatusCode(500, new { Message = $"Internal Server Error: {ex.Message}" });
            }
        }
        [HttpDelete("delete-account/{Id}")]
        //[AuthorizePermission("Allow to Add/Delete Users")]
        public async Task<IActionResult> DeleteManageAccount(Guid Id)
        {
            try
            {
                var currentUserIdClaim = User.Claims.FirstOrDefault(c => c.Type == ClaimTypes.Name);

                if (currentUserIdClaim == null || !Guid.TryParse(currentUserIdClaim.Value, out Guid currentUserId))
                {
                    return BadRequest(new { Message = "Invalid current user." });
                }

                var isDeleted = await _manageAccountService.DeleteManageAccountAsync(currentUserId, Id);

                if (isDeleted)
                {
                    return Ok(new { Message = "Account deleted successfully." });
                }
                else
                {
                    return BadRequest(new { Message = "Failed to delete ManageAccount." });
                }
            }
            catch (Exception ex)
            {
                return StatusCode(500, new { Message = $"Internal Server Error: {ex.Message}" });
            }
        }
        [HttpGet("get-meta-status/businessId")]
        [Authorize]
        public async Task<IActionResult> GetMetaStatus(string businessId)
        {
            try
            {
                var currentUserIdClaim = User.Claims.FirstOrDefault(c => c.Type == ClaimTypes.Name);

                if (currentUserIdClaim == null || !Guid.TryParse(currentUserIdClaim.Value, out Guid currentUserId))
                {
                    throw new Exception("Invalid current user.");
                }
                var healtStatus = await _metaApiService.GetHealthStatusAsync(businessId);
                return Ok(new ApiResponse<HealthStatusDto>
                {
                    Success = true,
                    Message = "Health status",
                    Data = healtStatus
                });
            }
            catch (Exception ex)
            {
                return StatusCode(500, new ApiResponse<string>
                {
                    Success = false,
                    Message = ex.Message,
                    Errors = ex.StackTrace
                });
            }
        }
        [HttpPost("upload-file-in-meta")]
        [AllowAnonymous]
        public async Task<IActionResult> UploaFileinMeta(string appId, string token, string file)
        {
            try
            {

                var client = new HttpClient();
                var res = await client.GetAsync(file);
                var contentType = res.Content.Headers.ContentType?.MediaType;
                var fileSize = res.Content.Headers.ContentLength ?? 0;
                var fileName = file.Split('/').Last();
                var fileType = contentType;
                byte[] fileBytes = await res.Content.ReadAsByteArrayAsync();
                var result = await InitializeUploadSession(appId, fileName, fileBytes.Length, fileBytes, fileType, token);
                return Ok(result);
            }
            catch (Exception ex)
            {
                return BadRequest(ex.Message);
            }
        }

        #region upload profile file in meta 

        public static async Task<string> InitializeUploadSession(string appId, string fileName, long fileLength, byte[] fileBytes, string fileType, string accessToken)
        {
            // Prepare the upload session and get the upload session ID
            string uploadSessionId = await StartUploadSession(appId, fileName, fileLength, fileType, accessToken);
            if (string.IsNullOrEmpty(uploadSessionId))
            {
                Console.WriteLine("Failed to initialize upload session.");
                return null;
            }

            Console.WriteLine($"Upload session initialized. Session ID: {uploadSessionId}");

            // Upload the file data using the obtained session ID
            string handle = await UploadFileData(uploadSessionId, fileBytes, accessToken);
            if (string.IsNullOrEmpty(handle))
            {
                Console.WriteLine("Failed to upload file data.");
                return null;
            }

            return handle;
        }

        private static async Task<string> StartUploadSession(string appId, string fileName, long fileLength, string fileType, string accessToken)
        {
            HttpClient client = new HttpClient();
            string url = $"{MetaApi.GetBaseUrl()}{appId}/uploads" +
                         $"?file_name={Uri.EscapeDataString(fileName)}" +
                         $"&file_length={fileLength}" +
                         $"&file_type={Uri.EscapeDataString(fileType)}" +
                         $"&access_token={Uri.EscapeDataString(accessToken)}";

            try
            {
                HttpResponseMessage response = await client.PostAsync(url, null);
                if (!response.IsSuccessStatusCode)
                {
                    Console.WriteLine($"Error: {response.StatusCode}");
                    return null;
                }

                string responseBody = await response.Content.ReadAsStringAsync();
                var jsonResponse = JsonConvert.DeserializeObject<SendHandler>(responseBody);
                return jsonResponse?.Id;
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Exception in StartUploadSession: {ex.Message}");
                return null;
            }
        }

        private static async Task<string?> UploadFileData(string uploadSessionId, byte[] fileBytes, string accessToken)
        {
            string endpoint = $"{MetaApi.GetBaseUrl()}{uploadSessionId}";
            using (var client = new HttpClient())
            {
                client.DefaultRequestHeaders.Authorization = new AuthenticationHeaderValue("OAuth", accessToken);
                client.DefaultRequestHeaders.Add("file_offset", "0");
                string boundary = $"----------{Guid.NewGuid():N}";
                var content = new MultipartFormDataContent(boundary);
                try
                {
                    ByteArrayContent mediaFileContent = new ByteArrayContent(fileBytes);
                    HttpRequestMessage requestMessage = new HttpRequestMessage();
                    requestMessage.Method = HttpMethod.Post;
                    requestMessage.Content = mediaFileContent;
                    requestMessage.Content.Headers.ContentType = MediaTypeHeaderValue.Parse("application/x-www-form-urlencoded");
                    requestMessage.RequestUri = new Uri($"{client.BaseAddress}{endpoint}");
                    var response = await client.SendAsync(requestMessage).ConfigureAwait(false);
                    var responseContent = await response.Content.ReadAsStringAsync();
                    var result = JsonConvert.DeserializeObject<SendHandler>(responseContent);
                    var handle = result?.h;
                    return handle;
                }
                catch (Exception ex)
                {
                    Console.WriteLine(ex.Message);
                    return null;
                }
            }
        }
        #endregion
    }
}
