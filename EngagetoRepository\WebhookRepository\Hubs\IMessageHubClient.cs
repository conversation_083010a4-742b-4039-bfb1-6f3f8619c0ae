﻿using EngagetoEntities.Dtos.ConversationDtos;

namespace Engageto.Hubs
{
    public interface IMessageHubClient 
    {
        Task ReceiveMessageFromServer(List<ConversationDto> message);
        Task ReceiveErrorMessageFromServer(List<ConversationDto> messages);
        Task MessageStatus(ConversationDto message);
        Task SendContacts(Object Contacts);
        Task SendConversations(object data);
        Task RenderContacts();
    }
}
