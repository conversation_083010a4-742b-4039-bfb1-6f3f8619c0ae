﻿namespace EngagetoEntities.Dtos.SubscriptionDtos
{
    public class SubscriptionDto
    {

    }
    public class BaseSubscriptionDto
    {
        public int? Id { get; set; }
        public string? PlanType { get; set; }
        public string? PlanName { get; set; }
        public DateTime StartDate { get; set; }
        public DateTime EndDate { get; set; }
        public bool IsActive { get; set; }
        public List<PaymentDetailsDto>? PaymentDetails { get; set; }
    }

    public class PaymentDetailsDto
    {
        public int? Id { get; set; }
        public string? PlanType { get; set; }
        public string? PlanName { get; set; }
        public decimal? Amount { get; set; }
        public decimal? IgstAmount { get; set; }
        public decimal? TotalAmount { get; set; }
        public DateTime? CreatedAt { get; set; }
    }

    public class TenantSubscriptionDto : BaseSubscriptionDto
    {
        public string? TenantId { get; set; }
    }
}
