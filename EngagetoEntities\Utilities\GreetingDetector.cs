﻿using System;
using System.Text.RegularExpressions;
using System.Linq;
using System.Collections.Generic;
using iTextSharp.text;

public static class GreetingDetector
{
    // More complete emoji regex using Unicode blocks and symbols
    private static readonly Regex EmojiRegex = new(
        @"[\p{Cs}\p{So}\p{Sk}\p{Mn}\p{Me}]|[\u200d\uFE0F]",
        RegexOptions.Compiled);

    private static readonly char[] WordSeparators = { ' ', '\t', '\n', '\r' };

    public static bool ContainsGreetingAfterEmojiRemoval(string? message, string[] greetingKeywords)
    {
        if (string.IsNullOrWhiteSpace(message) || greetingKeywords == null || greetingKeywords.Length == 0)
            return false;

        if (ContainsAnyGreetingKeyword(message, greetingKeywords))
            return true;

        return false;
        // Remove emojis before further processing
        //string cleanedMessage = EmojiRegex.Replace(message, string.Empty);

        //return ProcessWordsAndCheckGreeting(cleanedMessage.AsSpan(), greetingKeywords);
    }

    public static bool ContainsGreetingAfterEmojiRemoval(string? message, IEnumerable<string> greetingKeywords)
    {
        return ContainsGreetingAfterEmojiRemoval(message, greetingKeywords?.ToArray());
    }

    private static bool ContainsAnyGreetingKeyword(string message, string[] greetingKeywords)
    {
        var lowerMessage = message.ToLowerInvariant();
        foreach (var keyword in greetingKeywords)
        {
            var pattern = $@"\b{Regex.Escape(keyword)}\b[\p{{P}}\p{{S}}\s]*";
            if (Regex.IsMatch(message, pattern, RegexOptions.IgnoreCase))
            {
                return true;
            }
            //if (!string.IsNullOrWhiteSpace(keyword) && lowerMessage.Contains(keyword.ToLowerInvariant()))
            //    return true;
        }
        return false;
    }

    private static bool ProcessWordsAndCheckGreeting(ReadOnlySpan<char> messageSpan, string[] greetingKeywords)
    {
        const int MaxWords = 20;
        Span<char> buffer = stackalloc char[messageSpan.Length];
        int bufferIndex = 0;
        int wordCount = 0;
        int wordStart = 0;
        bool inWord = false;

        for (int i = 0; i <= messageSpan.Length; i++)
        {
            char currentChar = i < messageSpan.Length ? messageSpan[i] : ' ';
            bool isSeparator = IsWordSeparator(currentChar);

            if (!inWord && !isSeparator)
            {
                wordStart = i;
                inWord = true;
            }
            else if (inWord && isSeparator)
            {
                if (wordCount >= MaxWords)
                    break;

                var word = messageSpan.Slice(wordStart, i - wordStart);
                if (ProcessWord(word, buffer.Slice(bufferIndex), out int cleanedLength))
                {
                    if (bufferIndex > 0)
                    {
                        buffer[bufferIndex++] = ' ';
                    }
                    bufferIndex += cleanedLength;
                    wordCount++;
                }
                inWord = false;
            }
        }

        if (bufferIndex == 0)
            return false;

        var cleanedText = buffer.Slice(0, bufferIndex);
        return ContainsAnyGreeting(cleanedText.ToString(), greetingKeywords);
    }

    private static bool IsWordSeparator(char c) =>
        c == ' ' || c == '\t' || c == '\n' || c == '\r';

    private static bool ProcessWord(ReadOnlySpan<char> word, Span<char> output, out int cleanedLength)
    {
        cleanedLength = 0;

        for (int i = 0; i < word.Length; i++)
        {
            char c = word[i];
            var category = char.GetUnicodeCategory(c);

            // Skip emojis only
            if (char.IsSurrogate(c) || category == System.Globalization.UnicodeCategory.OtherSymbol)
                continue;

            output[cleanedLength++] = char.ToLowerInvariant(c);
        }

        return cleanedLength > 0;
    }

    private static bool ContainsAnyGreeting(string cleanedText, string[] greetingKeywords)
    {
        foreach (var keyword in greetingKeywords)
        {
            if (cleanedText.Contains(keyword, StringComparison.OrdinalIgnoreCase))
            {
                return true;
            }
        }
        return false;
    }

}
