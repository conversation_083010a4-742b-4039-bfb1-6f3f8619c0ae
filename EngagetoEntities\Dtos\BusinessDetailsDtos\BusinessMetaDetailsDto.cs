﻿using EngagetoEntities.Enums;

namespace EngagetoEntities.Dtos.BusinessDetailsDtos
{
    public class BusinessMetaDetailsDto
    {
        public Guid Id { get; set; }
        public string BusinessId { get; set; }
        public QualityScore? QualityRating { get; set; }
        public string? BusinessStatus { get; set; }
        public long? MessageLimit { get; set; }
        public string? Tier { get; set; }
        public int? ConversationCount { get; set; }
        public decimal? Cost { get; set; }
    }
}
