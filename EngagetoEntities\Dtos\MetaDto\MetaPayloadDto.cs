﻿namespace EngagetoEntities.Dtos.MetaDto
{
    public static class MetaPayloadDto
    {
        public static string BasePayloadTemplate = @"
        {{
            ""messaging_product"": ""whatsapp"",
            ""recipient_type"": ""individual"",
            ""to"": ""{0}"",
            ""type"": ""template"",
            ""template"": {{
                ""name"": ""{1}"",
                ""language"": {{
                    ""code"": ""{2}""
                }},
                ""components"": []
            }}
        }}";

        public static string BodyComponentTemplate = @"
        {
            ""type"": ""body"",
            ""parameters"": []
        }";

        public static string BodyParameterTemplate = @"
        {{
            ""type"": ""text"",
            ""text"": ""{0}""
        }}";

        public static string HeaderComponentTemplate = @"
        {
            ""type"": ""header"",
            ""parameters"": []
        }";


        public static string TextHeaderParameterTemplate = @"
        {{
            ""type"": ""text"",
            ""text"": ""{0}""
        }}";

        public static string MediaHeaderParameterTemplate = @"
        {{
            ""type"": ""{0}"",
            ""{0}"": {{
                ""link"": ""{1}""
            }}
        }}";

        public static string ButtonComponentTemplate = @"
        {{
            ""type"": ""button"",
            ""sub_type"": ""{0}"",
            ""index"": ""{1}"",
            ""parameters"": []
        }}";

        public static string ButtonParameterTemplate = @"
        {{
            ""type"": ""payload"",
            ""payload"": ""{0}""
        }}";

        public static string CreateBasePayload(string to, string templateName, string languageCode = "en")
        {
            return string.Format(BasePayloadTemplate, to, templateName, languageCode);
        }

        public static string CreateBodyParameter(string text)
        {
            return string.Format(BodyParameterTemplate, text);
        }

        public static string CreateTextHeaderParameter(string text)
        {
            return string.Format(TextHeaderParameterTemplate, text);
        }

        public static string CreateMediaHeaderParameter(string type, string link)
        {
            return string.Format(MediaHeaderParameterTemplate, type, link);
        }

        public static string CreateButtonComponent(string subType, int index)
        {
            return string.Format(ButtonComponentTemplate, subType, index);
        }

        public static string CreateButtonParameter(string payload)
        {
            return string.Format(ButtonParameterTemplate, payload);
        }
        public static object SendAuthPayload(string mobileNumber, string otp, string templateName, string? langaugeCode = "en")
        {
            return new
            {
                messaging_product = "whatsapp",
                recipient_type = "individual",
                to = mobileNumber,
                type = "template",
                template = new
                {
                    name = templateName,
                    language = new { code = langaugeCode },
                    components = new object[]
                    {
                        new
                        {
                            type = "body",
                            parameters = new object[]
                            {
                                new { type = "text", text = otp }
                            }
                        },
                        new
                        {
                            type = "button",
                            sub_type = "url",
                            index = "0",
                            parameters = new object[]
                            {
                                new { type = "text", text = otp }
                            }
                        }
                    }
                }
            };
        }
    }
}
