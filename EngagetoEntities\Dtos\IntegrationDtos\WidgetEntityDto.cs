﻿namespace EngagetoEntities.Dtos.IntegrationDtos
{
    public class WidgetEntityDto
    {
        // Properties for WidgetEntity
        public string PhoneNumber { get; set; } 
        public string? CtaText { get; set; }
        public string? ButtonBackground { get; set; } 
        public int? MarginBottom { get; set; } 
        public int? MarginLeft { get; set; } // Nullable
        public int? MarginRight { get; set; } // Nullable
        public int? BorderRadius { get; set; } // Nullable
        public string? DefaultMessage { get; set; } // Nullable
        public string? Position { get; set; } // Nullable

        // Properties for widgetConfig
        public string BrandName { get; set; } // Not nullable
        public string BrandSubtitle { get; set; } // Not nullable
        public string? BrandColor { get; set; } // Nullable
        public string? WidgetCtaText { get; set; } // Nullable
        public string? BrandImageUrl { get; set; } // Not nullable
        public string? DefaultOnScreenMessage { get; set; } // Not nullable
        public string? OpenWidgetOnMobileScreen { get; set; } // Nullable
        public string? OpenWidgetByDefault { get; set; } // Nullable
        public string? StartChat { get; set; } // Nullable

        // List of WidgetsUrlFieldEntities (DTO)
        public List<WidgetUrlFieldEntityDto>? UrlFields { get; set; }
    }

    public class WidgetUrlFieldEntityDto
    {
        public string SourceUrl { get; set; } // Not nullable
        public string PreFilledMessage { get; set; } // Not nullable
        public string OnScreenMessage { get; set; } // Not nullable
        public bool RemoveChecked { get; set; } // Not nullable
        public bool CapitalizeChecked { get; set; } // Not nullable
        public string BrandImageUrl { get; set; } // Not nullable
    }
}
