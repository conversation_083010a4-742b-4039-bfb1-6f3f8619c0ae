﻿namespace EngagetoEntities.Utilities
{
    public static class MediaExtentionsHelper
    {
        public static readonly Dictionary<string, string> MediaTypeMap =
       new Dictionary<string, string>(StringComparer.OrdinalIgnoreCase)
       {
            { ".jpg", "image" },
            { ".jpeg", "image" },
            { ".png", "image" },
            { ".gif", "image" },
            { ".bmp", "image" },
            { ".mp4", "video" },
            { ".avi", "video" },
            { ".mov", "video" },
            { ".wmv", "video" },
            { ".flv", "video" }
       };

        public static string GetMediaType(string extension)
        {
            if (string.IsNullOrWhiteSpace(extension))
                return "unknown";
            if (!extension.StartsWith("."))
                extension = "." + extension;
            return MediaTypeMap.TryGetValue(extension, out var type) ? type : "unknown";
        }

        public static string GetMediaTypeFromUrl(string url)
        {
            if (string.IsNullOrWhiteSpace(url))
                return "unknown";
            try
            {
                var extension = System.IO.Path.GetExtension(new Uri(url).AbsolutePath);
                return GetMediaType(extension);
            }
            catch
            {
                return "unknown";
            }
        }
    }
}
