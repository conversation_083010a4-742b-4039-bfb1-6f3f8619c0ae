﻿using Microsoft.AspNetCore.Http;
using System.ComponentModel.DataAnnotations;

namespace EngagetoEntities.UserEntities.Dtos
{
    public class Ahex_CRM_UsersDto
    {
        public string? Name { get; set; }
        public string? Designation { get; set; }
        // public string? Password { get; set; }
        public string? CountryCode { get; set; }
        [RegularExpression("^[0-9]+$", ErrorMessage = "Phone number must contain only digits.")]
        public string? PhoneNumber { get; set; }
        public string? CountryName { get; set; }
        public string? FacebookBusinessManagerId { get; set; }
        public string? WhatsAppBusinessId { get; set; }
        public string? Address { get; set; }
        public string? About { get; set; }
        public IFormFile? Image { get; set; }
    }
    public class User_TeamMember
    {
        public string? FullName { get; set; }
        public string? Email { get; set; }
        public string? Role { get; set; }
        public string? CountryCode { get; set; }
        [RegularExpression("^[0-9]+$", ErrorMessage = "Phone number must contain only digits.")]
        public string? PhoneNumber { get; set; }
    }
    public class User_ClientTeamMember
    {
        public string? FullName { get; set; }
        public string? Email { get; set; }
        public string? Role { get; set; }
        public string? CountryCode { get; set; }
        [RegularExpression("^[0-9]+$", ErrorMessage = "Phone number must contain only digits.")]
        public string? PhoneNumber { get; set; }
        public string? CompanyId { get; set; }
    }
    public class User_TeamMemberUpdate
    {
        public string? FullName { get; set; }
        public string? Email { get; set; }
        public string? CountryCode { get; set; }
        public string? Role { get; set; }
        [RegularExpression("^[0-9]+$", ErrorMessage = "Phone number must contain only digits.")]
        public string? PhoneNumber { get; set; }
    }
    public class Ahex_CRM_UserDisplay
    {
        public Guid Id { get; set; }
        public string Name { get; set; }
        public string EmailAddress { get; set; }
        public string? CountryCode { get; set; }
        [RegularExpression("^[0-9]+$", ErrorMessage = "Phone number must contain only digits.")]
        public string PhoneNumber { get; set; }
        public string CountryName { get; set; }
        public string FacebookBusinessManagerId { get; set; }
        public string WhatsAppBusinessId { get; set; }
        public string Address { get; set; }
        public string About { get; set; }
        public string Designation { get; set; }
        public bool Status { get; set; }
        public string Image { get; set; }
        public DateTime CreationDate { get; set; }
        public bool? companyVerificationStatus { get; set; }
        public bool? IsMetaEnabled { get; set; } = false;
        // public Guid UserId { get; set; }
    }
}
