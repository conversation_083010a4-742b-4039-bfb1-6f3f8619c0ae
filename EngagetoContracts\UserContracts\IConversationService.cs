﻿using EngagetoContracts.Services;
using EngagetoEntities.Entities;

namespace EngagetoContracts.UserContracts
{
    public interface IConversationService
    {
        Task<List<Conversations>> SaveConversationsAsync(List<Conversations> conversations);
        Task<Conversations> SaveTextMediaConversationAsync(string? textMessage, string from, string to, string? mediaType, string? caption, string? mediaMimeType, string? mediaUrl, string waMessageId, string? replyId);
        Task<Conversations?> GetMessageByIdAsync(Guid id);
    }
}
