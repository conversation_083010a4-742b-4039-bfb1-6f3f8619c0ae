using EngagetoEntities.Dtos.AttributeNameDtos;

namespace EngagetoContracts.AttributeName
{
    public interface IAttributeNameService
    {
        Task<AttributeNameDtos> CreateAttributeNameAsync(CreateAttributeNameDtos attributeNameDto, Guid userId);
        Task<AttributeNameDtos> UpdateAttributeNameAsync(Guid id, UpdateAttributeNameDtos attributeNameDto, Guid userId);
        Task<bool> DeleteAttributeNameAsync(Guid id, Guid userId);
        Task<List<AttributeNameDtos>> GetAllAttributeNamesAsync();
        Task<AttributeNameDtos> GetAttributeNameByIdAsync(Guid id , Guid ? businessId=null);
        Task<string> GetAttributeNameByIdAsync(Guid id );
    }
} 