﻿using EngagetoContracts.WebhookContracts.Client;
using EngagetoDatabase.WhatsAppBusinessDatabase.Models;
using EngagetoEntities.DdContext;
using EngagetoEntities.Dtos.FilterDtos;
using EngagetoEntities.Dtos.MetaDto;
using EngagetoEntities.Dtos.WebhookDtos;
using EngagetoRepository.WebhookRepository.Hubs.Service;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.SignalR;
using Microsoft.AspNetCore.StaticFiles;
using System.Security.Claims;

namespace Engageto.Hubs
{
    [Authorize]
    public class MessageHub : Hub<IMessageHubClient>
    {
        private readonly IInboxService _inboxService;
        private static readonly FileExtensionContentTypeProvider _contentTypeProvider = new FileExtensionContentTypeProvider();
        private readonly ApplicationDbContext appDbContext;
        private readonly IWhatsAppBusinessClient _whatsAppBusinessClient;
        private readonly DbAa80b1WhatsappbusinessContext DbAa80b1WhatsappbusinessContext;
        public MessageHub(IInboxService inboxService,
            ApplicationDbContext appDbContext,
            IWhatsAppBusinessClient whatsAppBusiness,
            DbAa80b1WhatsappbusinessContext dbAa80B1Whatsappbusiness)
        {
            _inboxService = inboxService;
            this.appDbContext = appDbContext;
            this._whatsAppBusinessClient = whatsAppBusiness;
            this.DbAa80b1WhatsappbusinessContext = dbAa80B1Whatsappbusiness;
        }

        #region Connection Management
        public override async Task OnDisconnectedAsync(Exception exception)
        {

            await base.OnDisconnectedAsync(exception);
        }

        public override Task OnConnectedAsync()
        {
            return base.OnConnectedAsync();
        }
        #endregion
        #region User and Contact Management
        public async Task SendUserData(Guid UserId, Guid BusinessId, FilterDto operations, int? Page)
        {
            try
            {


                await Groups.AddToGroupAsync(Context.ConnectionId, BusinessId.ToString() + UserId.ToString());


                var data = await _inboxService.LatestInboxContacts(BusinessId, UserId.ToString(), operations, Page ?? 1);
                await Clients.Caller.SendContacts(data);

            }
            catch (Exception ex)
            {
                // Handle errors appropriately (e.g., log to a central system)
            }
        }


        public async Task ContactsData(Guid UserId, Guid BusinessId, FilterDto operations, int? Page)
        {
            try
            {
                var data = await _inboxService.InboxContacts(BusinessId, UserId, operations, Page);
                await Clients.Caller.SendContacts(data);

            }
            catch (Exception ex)
            {
                // Handle errors appropriately (e.g., log to a central system)
            }
        }


        public async Task ContactConversationHistory(string contact, string businessId, string userId, int? Page)
        {
            try
            {//
                if (Guid.TryParse(businessId, out Guid BusinessId))
                {

                    var data = await _inboxService.InboxContactsConversations(BusinessId, contact, Page);
                    await Clients.Caller.SendConversations(data);

                }
            }
            catch (Exception ex)
            {
                // Handle exceptions
            }
        }

        #endregion
        #region Messaging
        public async Task SendTextOrEmojiMessage(TextMessageDto data, Guid BusinessId)
        {
            var currentUserIdClaim = Context.User.Claims.FirstOrDefault(c => c.Type == ClaimTypes.Name);

            if (currentUserIdClaim == null || !Guid.TryParse(currentUserIdClaim.Value, out Guid currentUserId))
            {
                throw new Exception("Invalid current user.");
            }

            await _inboxService.SendTextOrEmojiMessage(data, BusinessId, currentUserId.ToString());

        }

        public async Task SendTemplate(SendTemplate model)
        {
            var currentUserIdClaim = Context.User.Claims.FirstOrDefault(c => c.Type == ClaimTypes.Name);

            if (currentUserIdClaim == null || !Guid.TryParse(currentUserIdClaim.Value, out Guid currentUserId))
            {
                throw new Exception("Invalid current user.");
            }

            await _inboxService.SendTemplateByUsingContact(model, currentUserId.ToString());
            var data1 = DbAa80b1WhatsappbusinessContext.Users.Where(m => m.CompanyId == model.BusinessId.ToString());
            if (data1 != null)
            {
                var UserIds = data1.Select(m => m.Id).ToList();
                foreach (var UserId in UserIds)
                {
                    await Clients.Groups(model.BusinessId.ToString().ToLower() + UserId.ToString().ToLower()).RenderContacts();
                }

            }
        }
        public async Task SendMediaMessage(List<string> Contact, string MediaFile, string? MediaFileName, string MediaCaption, Guid BusinessId, string? MessageId)
        {

            var currentUserIdClaim = Context.User.Claims.FirstOrDefault(c => c.Type == ClaimTypes.Name);

            if (currentUserIdClaim == null || !Guid.TryParse(currentUserIdClaim.Value, out Guid currentUserId))
            {
                throw new Exception("Invalid current user.");
            }


            // Create a MemoryStream from byte array

            MediaMessageDto mediaData = new MediaMessageDto();
            mediaData.Contact = Contact;
            mediaData.Caption = MediaCaption;
            mediaData.MediaFile = MediaFile;
            mediaData.MediaFileName = MediaFileName;
            mediaData.MessageId = MessageId;
            await _inboxService.SendMediaMessage(mediaData, BusinessId, currentUserId.ToString());

        }
        #endregion
        #region Message Management
        public async Task MarkAsRead(Guid MessageId, Guid BusinessId)
        {
            try
            {
                var businessDetailsMeta = DbAa80b1WhatsappbusinessContext.BusinessDetailsMetas
                              .FirstOrDefault(m => m.BusinessId == BusinessId.ToString());
                var From = appDbContext.Conversations.Where(m => m.Id == MessageId).FirstOrDefault()?.From;
                if (businessDetailsMeta != null && From != businessDetailsMeta?.PhoneNumberId)
                {
                    var WhatsappPhoneNumberId = businessDetailsMeta.PhoneNumberId;

                    var data = appDbContext.Conversations
                                .Where(m => m.From == From).OrderByDescending(m => m.CreatedAt).FirstOrDefault();

                    if (data != null)
                    {
                        var WhatsAppMessageId = data.WhatsAppMessageId;
                        MarkMessageRequest markMessageRequest = new MarkMessageRequest
                        {
                            MessageId = WhatsAppMessageId,
                            Status = "read"
                        };

                        await _whatsAppBusinessClient.MarkMessageAsReadAsync(markMessageRequest, BusinessId);
                    }
                }
            }
            catch (Exception ex)
            {

            }
        }

        #endregion
    }
}
