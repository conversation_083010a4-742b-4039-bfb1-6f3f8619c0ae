﻿using EngagetoEntities.UserEntities.Models;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Security.Permissions;
using System.Text;
using System.Threading.Tasks;

namespace EngagetoEntities.UserEntities.Dtos
{
    public class AgentPerformanceDto
    {
        public AgentDto Agent { get; set; }
        public int AssignedCount { get; set; } = 0;
        public int ReassignedCount { get; set; } = 0;
        public int RespondedCount { get; set; } = 0;
        public int? ResolvedCount { get; set; } = 0;
        public int ResolutionTime { get; set; } = 0;
        public long AvgResonseTime {get; set; } = 0;
    }
    public class AgentDto
    {
        public Guid Id { get; set; }
        public string? CompanyId { get; set; }
        public string? Name { get; set; }
        public string? RoleId { get; set; }
    }
}
