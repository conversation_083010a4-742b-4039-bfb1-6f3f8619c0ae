﻿namespace EngagetoEntities.UserEntities.Dtos
{
    public class SubscriptionDetailsDto
    {
        public int Id { get; set; }
        public string? UserId { get; set; }
        public string? CompanyId { get; set; }
        public int? PlanId { get; set; }
        public string? DurationType { get; set; }
        public string? PlanName { get; set; }
        public bool? IsActive { get; set; }
    }
}
