﻿<Project Sdk="Microsoft.NET.Sdk">

  <PropertyGroup>
    <TargetFramework>net7.0</TargetFramework>
    <ImplicitUsings>enable</ImplicitUsings>
    <Nullable>enable</Nullable>
  </PropertyGroup>

  <ItemGroup>
    <PackageReference Include="Hangfire" Version="1.8.12" />
    <PackageReference Include="Hangfire.Console" Version="1.4.3" />
    <PackageReference Include="Hangfire.Core" Version="1.8.12" />
    <PackageReference Include="Hangfire.Dashboard.BasicAuthorization" Version="1.0.2" />
    <PackageReference Include="System.IdentityModel.Tokens.Jwt" Version="7.0.0" />
    <PackageReference Include="System.Net.Http" Version="4.3.4" />
  </ItemGroup>

  <ItemGroup>
    <ProjectReference Include="..\EngagetoContracts\EngagetoContracts.csproj" />
    <ProjectReference Include="..\EngagetoDapper\EngagetoDapper.csproj" />
    <ProjectReference Include="..\EngagetoEntities\EngagetoEntities.csproj" />
  </ItemGroup>

</Project>
