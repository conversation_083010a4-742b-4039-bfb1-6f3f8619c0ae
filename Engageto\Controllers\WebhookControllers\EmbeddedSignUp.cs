﻿using EngagetoEntities.DdContext;
using EngagetoEntities.Entities;
using Microsoft.AspNetCore.Mvc;
using System.Net.Http.Headers;
using System.Text;
using System.Text.Json;
using System.Text.RegularExpressions;

namespace Engageto.Controllers.WebhookControllers
{
    [Route("api/[controller]")]
    [ApiController]
    public class EmbeddedSignUp : ControllerBase
    {
        private readonly IConfiguration _configuration;
        private ApplicationDbContext applicationDBContext;
        public EmbeddedSignUp(IConfiguration configuration, ApplicationDbContext applicationDB)
        {
            _configuration = configuration;
            applicationDBContext = applicationDB;
        }
        [HttpPost]
        public async Task<IActionResult> Embedded(Guid BusinessId, string code)
        {

            var appId = _configuration["Facebook:AppId"];
            var appSecret = _configuration["Facebook:AppSecret"];
            var accessToken = _configuration["Facebook:AccessToken"];
            var appName = _configuration["Facebook:AppName"];
            var BusinessManagerId = _configuration["Facebook:BusinessId"];

            string inputToken = null;

            using (var httpClient = new HttpClient())
            {
                // Step 1: Exchange code for access token
                var requestUrl = $"https://graph.facebook.com/v17.0/oauth/access_token?client_id={appId}&client_secret={appSecret}&code={code}";
                var response = await httpClient.GetAsync(requestUrl);
                if (response.IsSuccessStatusCode)
                {
                    var responseContent = await response.Content.ReadAsStringAsync();
                    var tokenData = JsonSerializer.Deserialize<Dictionary<string, string>>(responseContent);
                    inputToken = tokenData["access_token"];
                }
                else
                {
                    return BadRequest(new { Message = "Invalid token data received from Facebook." });
                }
            }

            if (inputToken != null)
            {
                // Step 2: Debug the token and extract page ID
                string debugTokenUrl = $"https://graph.facebook.com/v20.0/debug_token?input_token={inputToken}&access_token={accessToken}";
                string pageId = null;

                using (var httpClient = new HttpClient())
                {
                    var debugResponse = await httpClient.GetAsync(debugTokenUrl);
                    if (debugResponse.IsSuccessStatusCode)
                    {
                        var debugResponseContent = await debugResponse.Content.ReadAsStringAsync();

                        // Parse the JSON to extract the target_id from granular_scopes
                        var jsonDocument = JsonDocument.Parse(debugResponseContent);
                        foreach (var scope in jsonDocument.RootElement.GetProperty("data").GetProperty("granular_scopes").EnumerateArray())
                        {
                            if (scope.GetProperty("scope").GetString() == "whatsapp_business_messaging")
                            {
                                pageId = scope.GetProperty("target_ids")[0].GetString();
                                break;
                            }
                        }
                    }
                    else
                    {
                        var errorContent = await debugResponse.Content.ReadAsStringAsync();
                        return StatusCode((int)debugResponse.StatusCode, errorContent);
                    }
                }

                if (pageId != null)
                {
                    // Step 3: Get phone numbers using the extracted page ID
                    string phoneNumbersUrl = $"https://graph.facebook.com/v20.0/{pageId}/phone_numbers";
                    using (var httpClient = new HttpClient())
                    {
                        httpClient.DefaultRequestHeaders.Authorization = new AuthenticationHeaderValue("Bearer", accessToken);
                        var phoneNumbersResponse = await httpClient.GetAsync(phoneNumbersUrl);
                        if (phoneNumbersResponse.IsSuccessStatusCode)
                        {
                            var phoneNumbersContent = await phoneNumbersResponse.Content.ReadAsStringAsync();
                            var phoneNumbersDocument = JsonDocument.Parse(phoneNumbersContent);
                            var phoneNumberId = phoneNumbersDocument.RootElement.GetProperty("data")[0].GetProperty("id").GetString();
                            var displayPhoneNumber = phoneNumbersDocument.RootElement.GetProperty("data")[0].GetProperty("display_phone_number").GetString();

                            var registerUrl = $"https://graph.facebook.com/v20.0/{phoneNumberId}/register";
                            var registerRequestData = new
                            {
                                messaging_product = "whatsapp",
                                pin = "123456"
                            };

                            var registerContent = new StringContent(JsonSerializer.Serialize(registerRequestData), Encoding.UTF8, "application/json");
                            var registerResponse = await httpClient.PostAsync(registerUrl, registerContent);
                            if (registerResponse.IsSuccessStatusCode)
                            {
                                try
                                {
                                    var businessDetails = applicationDBContext.BusinessDetails.FirstOrDefault(m => m.Id == BusinessId);
                                    if (businessDetails == null)
                                    {
                                        return BadRequest(new { Message = "Business details not found" });
                                    }

                                    var match = Regex.Match(displayPhoneNumber, @"^(\+\d{1,3})\s*([\d\s]+)");
                                    if (match.Success)
                                    {
                                        businessDetails.CountryCode = match.Groups[1].Value;
                                        businessDetails.PhoneNumber = match.Groups[2].Value;
                                    }

                                    applicationDBContext.BusinessDetails.Update(businessDetails);
                                    var saveChangesResult = await applicationDBContext.SaveChangesAsync();

                                    var businessDetailsMeta = new BusinessDetailsMeta
                                    {
                                        AppName = appName,
                                        BusinessId = BusinessId.ToString(),
                                        AppId = appId,
                                        PhoneNumberID = phoneNumberId,
                                        WhatsAppBusinessAccountID = pageId,
                                        Token = accessToken
                                    };

                                    applicationDBContext.BusinessDetailsMetas.Add(businessDetailsMeta);
                                    await applicationDBContext.SaveChangesAsync();

                                    if (saveChangesResult > 0)
                                    {
                                        using var _httpClient = new HttpClient();
                                        var subscribeUrl = $"https://graph.facebook.com/v20.0/{pageId}/subscribed_apps";
                                        _httpClient.DefaultRequestHeaders.Authorization = new AuthenticationHeaderValue("Bearer", accessToken);
                                        await _httpClient.PostAsync(subscribeUrl, null);

                                        var registerResponseContent = await registerResponse.Content.ReadAsStringAsync();


                                        // Step 4: Retrieve system users
                                        string systemUsersUrl2 = $"https://graph.facebook.com/v20.0/{BusinessManagerId}/system_users?access_token={accessToken}";
                                        string systemUserId = null;
                                        var systemUsersResponse = await _httpClient.GetAsync(systemUsersUrl2);
                                        if (systemUsersResponse.IsSuccessStatusCode)
                                        {
                                            var systemUsersContent = await systemUsersResponse.Content.ReadAsStringAsync();
                                            var systemUsersDocument = JsonDocument.Parse(systemUsersContent);
                                            systemUserId = systemUsersDocument.RootElement.GetProperty("data")[0].GetProperty("id").GetString();
                                        }
                                        else
                                        {
                                            var errorContent = await systemUsersResponse.Content.ReadAsStringAsync();
                                            return BadRequest(new { Message = errorContent });
                                        }

                                        // Step 5: Assign user with permissions
                                        if (!string.IsNullOrEmpty(systemUserId))
                                        {
                                            string assignedUserId = systemUserId; // Use the extracted system user ID
                                            string tasks = "MANAGE, DEVELOP, MANAGE_TEMPLATES, MANAGE_PHONE, VIEW_COST, MANAGE_EXTENSIONS, VIEW_PHONE_ASSETS, MANAGE_PHONE_ASSETS, VIEW_TEMPLATES, MESSAGING";
                                            string assignedUsersUrl = $"https://graph.facebook.com/v20.0/{pageId}/assigned_users?user={assignedUserId}&tasks={tasks}&access_token={accessToken}";

                                            var assignedUsersResponse = await _httpClient.PostAsync(assignedUsersUrl, null);
                                            if (assignedUsersResponse.IsSuccessStatusCode)
                                            {
                                                var assignedUsersContent = await assignedUsersResponse.Content.ReadAsStringAsync();
                                                return Ok(new { Message = "User assigned successfully", Data = assignedUsersContent });
                                            }
                                            else
                                            {
                                                var errorContent = await assignedUsersResponse.Content.ReadAsStringAsync();
                                                return BadRequest(new { Message = errorContent });
                                            }
                                        }
                                        else
                                        {
                                            return BadRequest(new { Message = "Failed to save business details" });
                                        }
                                    }
                                }
                                catch (Exception ex)
                                {
                                    return BadRequest(new { Message = "An error occurred while processing your request", Details = ex.Message });
                                }
                            }
                            else
                            {
                                var errorContent = await registerResponse.Content.ReadAsStringAsync();
                                return BadRequest(new { Message = errorContent });
                            }
                        }
                        else
                        {
                            var errorContent = await phoneNumbersResponse.Content.ReadAsStringAsync();
                            return BadRequest(new { Message = errorContent });
                        }
                    }

                    // Step 4: Retrieve system users
                    string systemUsersUrl = $"https://graph.facebook.com/v20.0/{BusinessManagerId}/system_users?access_token={accessToken}";
                    using (var httpClient = new HttpClient())
                    {
                        string systemUserId = null;
                        var systemUsersResponse = await httpClient.GetAsync(systemUsersUrl);
                        if (systemUsersResponse.IsSuccessStatusCode)
                        {
                            var systemUsersContent = await systemUsersResponse.Content.ReadAsStringAsync();
                            var systemUsersDocument = JsonDocument.Parse(systemUsersContent);
                            systemUserId = systemUsersDocument.RootElement.GetProperty("data")[0].GetProperty("id").GetString();
                        }
                        else
                        {
                            var errorContent = await systemUsersResponse.Content.ReadAsStringAsync();
                            return BadRequest(new { Message = errorContent });
                        }

                        // Step 5: Assign user with permissions
                        if (!string.IsNullOrEmpty(systemUserId))
                        {
                            string assignedUserId = systemUserId; // Use the extracted system user ID
                            string tasks = "MANAGE, DEVELOP, MANAGE_TEMPLATES, MANAGE_PHONE, VIEW_COST, MANAGE_EXTENSIONS, VIEW_PHONE_ASSETS, MANAGE_PHONE_ASSETS, VIEW_TEMPLATES, MESSAGING"; // Replace with actual tasks/permissions
                            string assignedUsersUrl = $"https://graph.facebook.com/v20.0/{pageId}/assigned_users?user={assignedUserId}&tasks={tasks}&access_token={accessToken}";
                            var _httpClient = new HttpClient();
                            var assignedUsersResponse = await _httpClient.PostAsync(assignedUsersUrl, null);
                            if (assignedUsersResponse.IsSuccessStatusCode)
                            {
                                var assignedUsersContent = await assignedUsersResponse.Content.ReadAsStringAsync();
                                return Ok(new { Message = "Embedded signup with meta done successfully" });
                            }
                            else
                            {
                                var errorContent = await assignedUsersResponse.Content.ReadAsStringAsync();
                                return BadRequest(new { Message = errorContent });
                            }
                        }
                        else
                        {
                            return BadRequest(new { Message = "Failed to retrieve system user ID." });
                        }
                    }


                }

            }
            return BadRequest(new { Message = "Failed to obtain input token." });
        }
    }
}