﻿using EngagetoEntities.Enums;

namespace EngagetoEntities.Entities
{
    public class CampaignTracker : BaseEntity
    {
        public Guid Id { get; set; }
        public Guid CampaignId { get; set; }
        public string? ContactId { get; set; }
        public string? BusinessId { get; set; }
        public Guid UserId { get; set; }
        public string Status { get; set; } = "Pending"; // Pending, Processing, Completed, Failed
        public string? WhatsAppMessagesId { get; set; }
        public string? ErrorMessage { get; set; }
        public ConvStatus? ConvStatus { get; set; }
    }
}
