﻿using EngagetoEntities.Enums;
using System.ComponentModel.DataAnnotations;

namespace EngagetoEntities.Entities
{

    public class AutomateConversations
    {
        [Key]
        public Guid Id { get; set; }
        public Guid UserId { get; set; }
        public Guid BusinessId { get; set; }
        public string Input { get; set; }
        public string InputVariation { get; set; }
        public ResponseType ResponseType { get; set; }
        public string? CustomMessageBody { get; set; }
        public string? CustomMessageButton1 { get; set; }
        public string? CustomMessageButton2 { get; set; }
        public string? CustomMessageButton3 { get; set; }
        public string? CollectionListHeader { get; set; }
        public string? CollectionListBody { get; set; }
        public string? CollectionListFooter { get; set; }
        public string? CollectionListButton { get; set; }
        public string? CollectionListTitle { get; set; }
        public string? CollectionListDescription { get; set; }
        public string? CollectionListSectionsTitle { get; set; }
    }







}
