﻿using EngagetoEntities.Enums;

namespace EngagetoEntities.Dtos.TemplateDtos
{
    public class EditTemplateDto
    {
        public string BusinessId { get; set; }
        public Guid UserId { get; set; }
        public Guid TemplateId { get; set; }
        public string? SubCategory { get; set; }
        public MediaType MediaType { get; set; }
        public string? Header { get; set; }
        public string Body { get; set; }
        public string? Footer { get; set; }
        public string? CallButtonName { get; set; }
        public string? CountryCode { get; set; }
        public string? PhoneNumber { get; set; }
        public List<string>? UrlButtonName { get; set; }
        public List<string>? RedirectUrl { get; set; }
        public List<string>? QuickReply { get; set; }
        public string? MediaFile { get; set; }

    }
}
