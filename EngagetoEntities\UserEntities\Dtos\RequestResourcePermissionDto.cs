﻿using EngagetoEntities.Enums;

namespace EngagetoEntities.UserEntities.Dtos
{
    public class RequestResourcePermissionDto
    {
        public int PlanId { get; set; }
        public PlanType PlanType { get; set; }
        public int Year { get; set; }
        public int AgentLimit { get; set; } = -1;
        public string? CompanyId { get; set; }
        public List<FreeConversationCountDetail> FreeAnalyticsConversationCounts { get; set; } = new();
    }
    public class FreeConversationCountDetail
    {
        public ConversationCategoriesEnum AnalyticsCategoriesEnum { get; set; }
        public int Count { get; set; }
    }
}
