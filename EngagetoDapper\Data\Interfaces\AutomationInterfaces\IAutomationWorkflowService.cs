﻿using EngagetoEntities.Dtos.AutomationDtos;

namespace EngagetoDapper.Data.Interfaces.AutomationInterfaces
{
    public interface IAutomationWorkflowService
    {
        Task<Guid> SaveAutomationWorkFlowAsync(AutomationWorkflowDto automationWorkflowDto,bool isNew);
        Task<Dictionary<string, List<AutomationWorkflowResponseDto>>> GetWorkflowAsync(Guid companyId, Guid? userId = null, Guid? workflowId = null, string? workflowName = null);
        Task<bool> UpdateWorkflowAsync(AutomationWorkflowDto automationWorkflowResponse);
        Task<bool> DeleteWorkflowAsync(Guid companyId, Guid userId, Guid? workflowId, string? workflowName);
        Task<List<WorkflowNamesDto>> GetWorkflowNamesAsync(Guid companyId);
        Task<bool> UpdateWorkflowNameAsync(WorkflowUpdatePayloadDto workflowUpdatePayload, Guid userId);
    }
}
