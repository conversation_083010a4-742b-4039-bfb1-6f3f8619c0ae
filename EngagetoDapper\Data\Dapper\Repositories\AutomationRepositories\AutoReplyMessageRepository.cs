﻿
using Dapper;
using EngagetoDapper.Data.Connections;
using EngagetoDapper.Data.Interfaces.AutomationInterfaces;
using EngagetoEntities.Entities;
using System.Data;


namespace EngagetoDapper.Data.Dapper.Repositories.AutomationRepositories
{
    public class AutoReplyMessageRepository : IAutoReplyMessageRepository
    {
        private readonly IUnitOfWork _unitOfWork;
        public AutoReplyMessageRepository(IUnitOfWork unitOfWork)
        {
            _unitOfWork = unitOfWork;
        }

        public async Task<IEnumerable<T>> GetAutoReplyMessageAsync<T>(Guid companyId, Guid? autoReplyAutomationId)
        {
            return await _unitOfWork.Connection.QueryAsync<T>("AutoReplyMessageEntity_Get",
                 new
                 {
                     companyId,
                     @AutoReplyId = autoReplyAutomationId
                 },
                 _unitOfWork.Transaction,
                 commandType: CommandType.StoredProcedure);
        }
        public async Task<T?> SaveAutoReplyMessageAsync<T>(AutoReplyCustomMessageEntity autoCustomMessage, Guid userId)
        {
            return await _unitOfWork.Connection.ExecuteScalarAsync<T>(
                               "AutoReplyMessageEntity_Save",
                               new
                               {
                                   autoCustomMessage.Id,
                                   userId,
                                   autoCustomMessage.AutoReplyId,
                                   autoCustomMessage.BodyMessage,
                                   autoCustomMessage.Buttons,
                                   autoCustomMessage.IsDeleted
                               },
                               _unitOfWork.Transaction,
                               commandType: CommandType.StoredProcedure);
        }

        public async Task<T?> SaveAutoReplyVeriablesAsync<T>(string veriablesXml)
        {
            return await _unitOfWork.Connection.ExecuteScalarAsync<T>(
                            "AutoReplyVeriableEntity_SaveBulk",
                            new 
                            { 
                                xmlData = veriablesXml 
                            },
                            _unitOfWork.Transaction,
                            commandType: CommandType.StoredProcedure);
        }
    }

}
