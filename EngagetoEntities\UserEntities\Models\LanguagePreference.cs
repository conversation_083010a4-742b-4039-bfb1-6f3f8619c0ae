﻿using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations.Schema;
using System.ComponentModel.DataAnnotations;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using EngagetoEntities.Entities;

namespace EngagetoEntities.UserEntities.Models
{
    [Table("LanguagePreference")]
    public class LanguagePreference
    {
        [Key]
        public Guid Id { get; set; }
        [ForeignKey("Ahex_CRM_Users")]
        public Guid UserId { get; set; }

        [ForeignKey("Language")]
        public Guid LanguageId { get; set; }


        public Ahex_CRM_Users Ahex_CRM_Users { get; set; }
        public Language Language { get; set; }
    }
}
