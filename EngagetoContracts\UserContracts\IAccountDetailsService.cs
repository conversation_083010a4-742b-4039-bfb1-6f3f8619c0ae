﻿using EngagetoEntities.Entities;
using EngagetoEntities.UserEntities.Dtos;

namespace EngagetoContracts.UserContracts
{
    public interface IAccountDetailsService
    {
        //public Task<Guid> CreateManageAccountAsync(Guid userId, ManageAccountDto request, IFormFile image);
        //  Task<Guid> UpdateManageAccountAsync(Guid currentUserIdccountId, ManageAccountUpdateDto updateRequest, IFormFile logoFile);
        Task<Guid> UpdateManageAccountAsync(Guid currentUserId, Ahex_CRM_UsersDto updateRequest);
        Task<bool> DeleteManageAccountAsync(Guid currentUserId, Guid Id);
        Task<Ahex_CRM_UserDisplay> GetManageAccountByIdAsync(Guid currentUserId, Guid Id);
        Task<IEnumerable<Ahex_CRM_Users>> GetAllManageAccountsAsync(Guid currentUserId, string searchQuery, bool includeInactive, string sortBy, bool isSortAscending);
        Task<Ahex_CRM_Users> GetAccountDetails(Guid currentUserId);
    }
}
