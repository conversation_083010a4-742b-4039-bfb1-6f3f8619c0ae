﻿using EngagetoEntities.Enums;
using Newtonsoft.Json;

namespace EngagetoEntities.Dtos.MetaDto
{
    public class AuthTemplatePreviewRequestDto
    {
        public string CompanyId { get; set; }
        public List<Language> Languages { get; set; } = new();
        public WATemplateCategory Category { get; set; } = WATemplateCategory.AUTHENTICATION;
        public bool AddSecurityRecommendation { get; set; } = true;
        public int CodeExpirationMinutes { get; set; }
        public string? ButtonTypes { get; set; } = "OTP";
        public int MessageSendTtlSeconds { get; set; } = 0;
    }
    public class BaseAuthTemplatePreviewDto
    {
        [JsonProperty("language")]
        public Language Language { get; set; }
        public string Body { get; set; }
        public string? Footer { get; set; }
        public List<AuthButtonDto>? Buttons { get; set; }

    }
    public class AuthButtonDto
    {
        public string? Text { get; set; }
        [JsonProperty("autofill_text")]
        public string? AutofillText { get; set; }
    }
    public class ViewAuthTempaltePreviewDto
    {
        public List<BaseAuthTemplatePreviewDto>? Data { get; set; }
    }
}
