﻿using EngagetoEntities.Dtos.AutomationDtos;


namespace EngagetoDapper.Data.Interfaces.AutomationInterfaces
{
    public interface IAutoReplyMessageService
    {


        Task<bool> SaveAutoReplyMessageAsync(AutoReplyMessageDto customMessageDto, Guid userId,bool isAutomation = false);
        Task<IEnumerable<AutoReplyMessageDto>> GetAutoReplyMessageAsync(Guid companyId, Guid? autoReplyAutomationId);
        Task<bool> DeleteAutoReplyMessageAsync(Guid companyId, Guid userId, Guid autoReplyAutomationId);
        Task<bool> SetAutoReplyAutomationCampaignAsync(string companyId, Guid userId, CampaignAutomationDto campaignAutomationDto);
    }
}
