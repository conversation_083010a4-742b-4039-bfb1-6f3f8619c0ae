﻿using EngagetoEntities.Dtos.MetaDto;

namespace EngagetoContracts.WebhookContracts.DownloadMedia
{
    public interface IDownloadMedia
    {
        /// <summary>
        /// To retrieve your media’s URL, make a GET call to /{{Media-ID}}?phone_number_id=<PHONE_NUMBER_ID>. Later, you can use this URL to download the media file.
        /// </summary>
        /// <param name="mediaId">ID for the media to send a media message or media template message to your customers.</param>
        /// <returns>MediaUrlResponse</returns>
        Task<MediaUrlResponse> GetMediaUrlAsync(string mediaId,string PhoneNumberId);

        /// <summary>
        /// To download media uploaded from whatsapp
        /// </summary>
        /// <returns>byte[]</returns>
        Task<byte[]> DownloadMediaAsync(string mediaUrl,string PhoneNumberId);
    }
}
