﻿using EngagetoEntities.Dtos.UserDtos;
using EngagetoEntities.Enums;

namespace EngagetoEntities.Dtos.ContactDtos
{
    public class ContactImportTrackerDto
    {
        public Guid Id { get; set; }
        public int UploadedFilesId { get; set; }
        public int TotalCount { get; set; }
        public int DistinctCount { get; set; }
        public int TotalUploadedCount { get; set; }
        public int DuplicateCount { get; set; }
        public int InvalidCount { get; set; }
        public string? FileName { get; set; }
        public UploadStatus status { get; set; }
        public string? Message { get; set; }
        public DateTime? CreatedAt { get; set; }
        public DateTime? UpdatedAt { get; set; }
        public UserInfoDto? CreateUser { get; set; }
        public UserInfoDto? UpdateUser { get; set; }
    }
}
