﻿using EngagetoContracts.Repostiories;
using EngagetoContracts.Services;
using EngagetoEntities.DdContext;
using EngagetoEntities.Entities;
using Microsoft.EntityFrameworkCore;


namespace EngagetoRepository.Repostiories
{
    public class AutomationSettingRepo : IAutomationSettingRepo
    {
        private readonly ApplicationDbContext _context;
        private readonly IUserIdentityService _userIdentityService;

        public AutomationSettingRepo(ApplicationDbContext context, IUserIdentityService userIdentityService)
        {
            _context = context;
            _userIdentityService = userIdentityService;
        }

        public async Task<AutomationSetting> GetAutomationSettingAsync(string businessId)
        {
            var result = await _context.AutomationSettings
                .Where(auto => auto.BusinessId == businessId && auto.IsDeleted == false)
                .FirstOrDefaultAsync();
            return result;
        }
        public async Task<int> SaveAutomationSettingAsync(AutomationSetting autoSetting)
        {
            await _context.AddAsync(autoSetting);
            await _context.SaveChangesAsync();
            return autoSetting.Id;
        }

        public async Task<AutomationSetting> UpdateAutomationSettingAsync(AutomationSetting settingDto)
        {
            if (_context.AutomationSettings.Any(x => x.Id == settingDto.Id && x.IsDeleted == false))
            {
                _context.Update(settingDto);
                await _context.SaveChangesAsync();
                return settingDto;
            }
            throw new Exception("Not found record");
        }
    }
}
