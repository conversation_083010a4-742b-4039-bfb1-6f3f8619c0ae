﻿using Microsoft.AspNetCore.Http;
using Microsoft.EntityFrameworkCore;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using EngagetoContracts.UserContracts;
using EngagetoEntities.UserEntities.Models;
using EngagetoEntities.Entities;

namespace EngagetoRepository.UserRepository
{

    public class NotificationPreferenceService : INotificationPreferenceService
    {
        private readonly IHttpContextAccessor _httpContextAccessor;
        private readonly ApplicationDBContext _dbContext;
        public NotificationPreferenceService(ApplicationDBContext dbContext, IHttpContextAccessor httpContextAccessor)
        {
            _dbContext = dbContext;
            _httpContextAccessor = httpContextAccessor;
        }

        /* public async Task<NotificationDetails> GetNotificationPreferencesAsync(Guid userId)
         {
             return await _dbContext.NotificationPreferences.FirstOrDefaultAsync(np => np.UserId == userId);
         }


         public async Task<NotificationDetails> SetNotificationPreferences(Guid userId, NotificationDetailsDto preferences)
         {
             var existingNotification = await _dbContext.NotificationPreferences.FirstOrDefaultAsync(np => np.UserId == userId);

             if (existingNotification == null)
             {
                 var newNotificationPreference = new NotificationDetails
                 {
                     UserId = userId,
                     AccountDetailsChanges = preferences.AccountDetailsChanges ?? new Notification(),
                     SubscriptionChanges = preferences.SubscriptionChanges ?? new Notification(),
                     WalletUsage = preferences.WalletUsage ?? new Notification(),
                     ContactTagConversion = preferences.ContactTagConversion ?? new Notification(),
                     ContactAssignment = preferences.ContactAssignment ?? new Notification(),
                     PlanUpdates = preferences.PlanUpdates ?? new Notification(),
                     NewUserOptIn = preferences.NewUserOptIn ?? new Notification(),
                     EmailQueries = preferences.EmailQueries ?? new Notification(),
                     ChatNotifications = preferences.ChatNotifications ?? new Notification(),
                     AssignedChatMessages = preferences.AssignedChatMessages ?? new Notification(),
                 };



                 _dbContext.NotificationPreferences.Add(newNotificationPreference);
                 await _dbContext.SaveChangesAsync();

                 return newNotificationPreference;
             }
             else
             {

                 existingNotification.AccountDetailsChanges = preferences.AccountDetailsChanges ?? existingNotification.AccountDetailsChanges;
                 existingNotification.SubscriptionChanges = preferences.SubscriptionChanges ?? existingNotification.SubscriptionChanges;
                 existingNotification.WalletUsage = preferences.WalletUsage ?? existingNotification.WalletUsage;
                 existingNotification.ContactTagConversion = preferences.ContactTagConversion ?? existingNotification.ContactTagConversion;
                 existingNotification.ContactAssignment = preferences.ContactAssignment ?? existingNotification.ContactAssignment;
                 existingNotification.PlanUpdates = preferences.PlanUpdates ?? existingNotification.PlanUpdates;
                 existingNotification.NewUserOptIn = preferences.NewUserOptIn ?? existingNotification.NewUserOptIn;
                 existingNotification.EmailQueries = preferences.EmailQueries ?? existingNotification.EmailQueries;
                 existingNotification.ChatNotifications = preferences.ChatNotifications ?? existingNotification.ChatNotifications;
                 existingNotification.AssignedChatMessages = preferences.AssignedChatMessages ?? existingNotification.AssignedChatMessages;

                 await _dbContext.SaveChangesAsync();

                 return existingNotification;
             }


         }*/
        public async Task<IEnumerable<NotificationDto>> GetNotificationHierarchyAsync(string userId, string companyId)
        {
            // Fetch user notifications
            var userNotifications = await _dbContext.UserNotificationEntities
                .Where(un => un.UserId == userId && un.CompanyId == companyId)
                .ToListAsync();

            var notificationIds = userNotifications.Select(un => un.NotificationId).ToList();

            // Fetch notifications based on user notifications
            var notifications = await _dbContext.NotificationEntities
                .Where(n => notificationIds.Contains(n.Id))
                .ToListAsync();

            // Create a dictionary for easy lookup of user notification status
            var userNotificationStatus = userNotifications.ToDictionary(un => un.NotificationId, un => un.isActive.GetValueOrDefault());

            var result = notifications
                .Where(n => n.ParentNotificationId == "0")
                .Select(n => new NotificationDto
                {
                    MainMenu = n.Name,
                    Status = userNotificationStatus.ContainsKey(n.Id) && userNotificationStatus[n.Id],
                    SubMenus = notifications
                        .Where(sub => sub.ParentNotificationId == n.Id.ToString())
                        .Select(sub => new SubMenuDto
                        {
                            Name = sub.Name,
                            Status = userNotificationStatus.ContainsKey(sub.Id) && userNotificationStatus[sub.Id]
                        })
                        .ToList()
                })
                .ToList();

            return result;
        }

        public async Task<bool> UpdateNotificationStatusAsync(string userId, string companyId, string notificationName, bool isActive)
        {
            // Get the notification entity based on the menu name
            var notification = await _dbContext.NotificationEntities
                .FirstOrDefaultAsync(n => n.Name == notificationName);

            if (notification == null)
            {
                return false;
            }

            // Get the user notification entity
            var userNotification = await _dbContext.UserNotificationEntities
                .FirstOrDefaultAsync(un => un.UserId == userId && un.CompanyId == companyId && un.NotificationId == notification.Id);

            if (userNotification == null)
            {
                return false;
            }

            // Update the isActive status
            userNotification.isActive = isActive;

            await _dbContext.SaveChangesAsync();
            return true;
        }

    }
}
