﻿using EngagetoEntities.Dtos.AutomationDtos;
using EngagetoEntities.Entities;
using EngagetoEntities.Enums;

namespace EngagetoContracts.AutomationContracts
{
    public interface IRepositoryBase
    {
        // Working Hours methods
        Task<WorkingHours?> GetWorkingHours(Guid UserId, Guid BusinessId);
        Task<int> UpdateWorkingHours(WorkingHours data);
        Task AddWorkingHours(WorkingHours data);

        // Inbox Settings methods
        Task InboxSettingsMessage(Guid UserId, Guid BusinessId, InboxSettingsMessageDto inboxSettingsMessageDto);
        Task<object> GetInboxSettingsMessage(Guid UserId, Guid BusinessId, Feature feature);

        // Select Response methods
        Task UpdateSelectResponseEntity(AutomationSelectResponseEntity selectResponseEntity);
        Task<List<AutomationSelectResponseEntity>> GetSelectResponseEntity();
        Task AddSelectResponse(AutomationSelectResponseEntity selectResponse);
        Task DeleteSelectResponseAsync(Guid Id, Guid UserId);

        // Workflow Response methods
        Task UpdateWorkflowResponseEntity(WorkflowResponseHistoryEntity selectResponseEntity);
        Task<List<WorkflowResponseHistoryEntity>> GetWorkflowResponseEntity();
        Task AddWorkflowResponse(WorkflowResponseHistoryEntity selectResponse);
        Task DeleteWorkflowResponseAsync(Guid Id, Guid UserId);

        // Validation methods
        bool IsValidTimeZone(string? timeZoneId);
    }

}
