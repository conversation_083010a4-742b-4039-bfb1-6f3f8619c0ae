﻿using EngagetoContracts.UserContracts;
using EngagetoDapper.Data.Interfaces.CommonInterfaces;
using EngagetoDapper.Data.Interfaces.IUserInterfaces.IUserServices;
using EngagetoEntities.Entities;
using EngagetoEntities.Enums;
using EngagetoEntities.Response;
using EngagetoEntities.UserEntities.Dtos;
using EngagetoEntities.UserEntities.Models;
using EngagetoEntities.Utilities;
using Mapster;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using System.ComponentModel.DataAnnotations;
using System.Security.Claims;

namespace Engageto.Controllers.UserControllers
{
    [Route("api/[controller]")]
    [ApiController]
    public class PlansController : ControllerBase
    {
        private readonly IPlanEntitiesService _subscriptionPlanService;
        private readonly ICommonService _commonService;
        private readonly EngagetoDapper.Data.Interfaces.IConversationAnalyticsInterfaces.IConversationAnalyticsService _conversationAnalyticsService;
        private readonly IWalletService _walletService;
        private readonly IResourcePermissionService _resourcePermissionService;
        private readonly EngagetoDapper.Data.Interfaces.IUserInterfaces.IUserServices.IPlanService _planService;
        private readonly IRazorPayService _razorPayService;
        private readonly EngagetoDapper.Data.Interfaces.AutomationInterfaces.IAutoReplyAutomationService _autoReplyAutomationService;
        private readonly IConfiguration _configuration;
        public PlansController(IPlanEntitiesService subscriptionPlanService,
            ICommonService commonService,
            EngagetoDapper.Data.Interfaces.IConversationAnalyticsInterfaces.IConversationAnalyticsService conversationAnalyticsService,
            IWalletService walletService,
            IResourcePermissionService resourcePermissionService,
            EngagetoDapper.Data.Interfaces.IUserInterfaces.IUserServices.IPlanService planService,
            IRazorPayService razorPayService,
            EngagetoDapper.Data.Interfaces.AutomationInterfaces.IAutoReplyAutomationService autoReplyAutomationService,
            IConfiguration configuration
           )
        {
            _subscriptionPlanService = subscriptionPlanService;
            _commonService = commonService;
            _conversationAnalyticsService = conversationAnalyticsService;
            _walletService = walletService;
            _resourcePermissionService = resourcePermissionService;
            _planService = planService;
            _razorPayService = razorPayService;
            _autoReplyAutomationService = autoReplyAutomationService;
            _configuration = configuration;
        }

        [HttpGet("get-all-plans")]
        [AllowAnonymous]
        public async Task<IActionResult> GetAllPlans([Required] PlanType planType, string? companyId)
        {
            try
            {
                var subscriptionPlans = await _subscriptionPlanService.GetAllPlansAsync();
                List<PlanDetailsDto> planDetailsDtos = new List<PlanDetailsDto>();
                var planDiscounts = await _planService.GetPlanDiscountsAsync();
                var priceEntity = await _conversationAnalyticsService.GetDefaultConversationAnalyticsPrice();

                WalletAndSubscription? walletAndSubscription = null;
                var resourcePermissions = await _resourcePermissionService.GetResourcePermissionsAsync();
                if (!string.IsNullOrEmpty(companyId))
                    walletAndSubscription = await _walletService.GetWalletAndSubscriptionPlanAsync(companyId);

                switch (planType)
                {
                    case PlanType.Monthly:
                        foreach (var plan in subscriptionPlans)
                        {
                            if (walletAndSubscription == null || (walletAndSubscription != null && (string.IsNullOrEmpty(walletAndSubscription.subscriptionPlan.PlanName) || plan.Price > 0)))
                            {
                                var prices = priceEntity.Where(x => x.PlanId == plan.Id)?.ToList();
                                var planDiscountValue = GetPlanDiscountValue(plan, planDiscounts, planType);
                                plan.Price = Math.Round(Math.Round((plan.Price - planDiscountValue) ?? 0));
                                planDetailsDtos.Add(new PlanDetailsDto()
                                {
                                    Type = PlanType.Monthly.ToString(),
                                    PlanDetails = plan,
                                    PlanDiscription = plan.PlanName == "Intro" ? "250 free Service Conversations " : "1000 free service conversations per calendar month",
                                    AnalyticsPrice = prices?.OrderBy(x => x.ConversationCategory).Adapt<List<ConversationAnalyticsPriceDto>>(),
                                    ResourcePermission = resourcePermissions.FirstOrDefault(x => x.PlanId == plan.Id)
                                });
                            }
                        }
                        break;
                    case PlanType.Quarterly:
                        foreach (var plan in subscriptionPlans)
                        {
                            var prices = priceEntity.Where(x => x.PlanId == plan.Id)?.ToList();
                            if (plan.Price > 0)
                            {
                                var planDiscountValue = GetPlanDiscountValue(plan, planDiscounts, planType);
                                plan.Price = Math.Round(((plan.Price * 3) - planDiscountValue) ?? 0);
                                var newPlan = plan.Clone() as PlanEntities;
                                planDetailsDtos.Add(new PlanDetailsDto()
                                {
                                    Type = PlanType.Quarterly.ToString(),
                                    PlanDetails = newPlan,
                                    PlanDiscription = plan.PlanName == "Intro" ? "250 free Service Conversations " : "1000 free service conversations per calendar month",
                                    AnalyticsPrice = prices?.OrderBy(x => x.ConversationCategory).Adapt<List<ConversationAnalyticsPriceDto>>(),
                                    ResourcePermission = resourcePermissions.FirstOrDefault(x => x.PlanId == plan.Id)
                                });
                            }
                        }
                        break;
                    case PlanType.Annually:
                        foreach (var plan in subscriptionPlans)
                        {
                            var prices = priceEntity.Where(x => x.PlanId == plan.Id)?.ToList();
                            if (plan.Price > 0)
                            {
                                var planDiscountValue = GetPlanDiscountValue(plan, planDiscounts, planType);
                                plan.Price = Math.Round(((plan.Price * 12) - planDiscountValue) ?? 0);
                                var newPlan = plan.Clone() as PlanEntities;
                                planDetailsDtos.Add(new PlanDetailsDto()
                                {
                                    Type = PlanType.Annually.ToString(),
                                    PlanDetails = newPlan,
                                    PlanDiscription = plan.PlanName == "Intro" ? "250 free Service Conversations " : "1000 free service conversations per calendar month",
                                    AnalyticsPrice = prices?.OrderBy(x => x.ConversationCategory).Adapt<List<ConversationAnalyticsPriceDto>>(),
                                    ResourcePermission = resourcePermissions.FirstOrDefault(x => x.PlanId == plan.Id)
                                });
                            }
                        }
                        break;
                }
                return Ok(new ApiResponse<List<PlanDetailsDto>>()
                {
                    Success = true,
                    Data = planDetailsDtos,
                    Message = "Plans list."
                });

            }
            catch (Exception ex)
            {
                return StatusCode(500, new ApiResponse<string>()
                {
                    Success = false,
                    Data = null,
                    Message = $"Internal Server Error: {ex.Message}"
                });
            }
        }


        [HttpGet("GetCurrentPlan")]
        [Authorize]
        public async Task<IActionResult> GetCurrentPlanAsync([Required] string companyId)
        {
            try
            {
                var currentUserIdClaim = User.Claims.FirstOrDefault(c => c.Type == ClaimTypes.Name);

                if (currentUserIdClaim == null || !Guid.TryParse(currentUserIdClaim.Value, out Guid currentUserId))
                {
                    throw new Exception("Invalid current user.");
                }

                bool isPlatform = RoleConstants.CompanyId == companyId;
                PlanDetailsDto? planDetailsDto = null;
                var planDiscounts = await _commonService.GetPlanDiscountsAsync();
                var currentSubscriptions = await _subscriptionPlanService.GetSubscriptionsAsync(companyId);
                if (isPlatform)
                {
                    if (currentSubscriptions is null)
                    {
                        currentSubscriptions = await UpdatePlatformSubscriptionAsync(companyId, currentUserId);
                    }
                    else if ((currentSubscriptions.RenewEndDate?.Date ?? currentSubscriptions.PlanEndDate.Date) <= DateTime.Now.Date)
                        currentSubscriptions = await UpdatePlatformSubscriptionAsync(companyId, currentUserId);
                }
                if (currentSubscriptions is not null)
                {
                    var plan = await _subscriptionPlanService.GetPlanById(currentSubscriptions.PlanId);
                    if ((plan?.Status ?? false) == true)
                    {
                        planDetailsDto = new PlanDetailsDto
                        {
                            Type = currentSubscriptions?.DurationType ?? string.Empty,
                            PlanDetails = plan,
                            PlanDiscription = plan.PlanName == "Intro"
                                ? "250 free Service Conversations"
                                : "1000 free service conversations per calendar month"
                        };
                        decimal planDiscountValue = 0;
                        switch (currentSubscriptions?.DurationType ?? "Monthly")
                        {
                            case nameof(PlanType.Monthly):
                                planDiscountValue = GetPlanDiscountValue(plan, planDiscounts, PlanType.Monthly);
                                plan.Price = Math.Round((plan.Price - planDiscountValue) ?? 0);
                                break;
                            case nameof(PlanType.Quarterly):
                                planDiscountValue = GetPlanDiscountValue(plan, planDiscounts, PlanType.Quarterly);
                                plan.Price = Math.Round(((plan.Price * 3) - planDiscountValue) ?? 0);
                                break;
                            case nameof(PlanType.Annually):
                                planDiscountValue = GetPlanDiscountValue(plan, planDiscounts, PlanType.Annually);  // Changed from Quarterly to Annually
                                plan.Price = Math.Round(((plan.Price * 12) - planDiscountValue) ?? 0);
                                break;
                        }
                    }
                }

                return Ok(new ApiResponse<PlanDetailsDto>
                {
                    Success = true,
                    Data = planDetailsDto,
                    Message = planDetailsDto is not null ? "Get Current plan successfully." : "No Current plan is available."
                });
            }
            catch (Exception ex)
            {
                return StatusCode(500, new ApiResponse<string>
                {
                    Success = false,
                    Data = null,
                    Message = $"Internal Server Error: {ex.Message}"
                });
            }
        }

        [HttpGet("get-planDtails-by-planId")]
        public async Task<IActionResult> GetPlanDetasilByPlanId(int planId,
                PlanType planType, string? companyId = null, bool isUpgrade = false)
        {
            try
            {
                var subscription = await _subscriptionPlanService.GetSubscriptionsAsync(companyId);
                var IGSTValue = _configuration["GstDetails:IGSTPercentage"];
                int IGSTPercentage = 18;
                int.TryParse(IGSTValue, out IGSTPercentage);

                var plans = await _subscriptionPlanService.GetAllPlansAsync();
                var selectedPlan = plans.FirstOrDefault(x => x.Id == planId);
                if (selectedPlan == null)
                {
                    throw new Exception("Plan is not found.");
                }

                var planDetails = await GetPlanDetailsAsync(selectedPlan, planType);
                planDetails.IGST = $"{IGSTValue}%";
                planDetails.Id = planId;

                if (isUpgrade && companyId != null && subscription is not null)
                {
                    var existingPlan = await _subscriptionPlanService.GetPlanById(subscription.PlanId);
                    if ((plans.FirstOrDefault(x => x.Id == subscription?.PlanId)?.Price ?? 1) < (existingPlan?.Price ?? 1))
                        throw new Exception("Can't choose the lower tier plan");
                    PlanType existingPlanType;
                    if (!Enum.TryParse(subscription.DurationType, true, out existingPlanType))
                        existingPlanType = planType;

                    var existingPlanDetails = await GetPlanDetailsAsync(existingPlan, existingPlanType);
                    var startEndDate = subscription?.RenewStartDate ?? subscription?.PlanStartDate ?? StringHelper.ConvertIntoIndianDateTime(DateTime.UtcNow);
                    var differenceDays = (StringHelper.ConvertIntoIndianDateTime(DateTime.UtcNow) - startEndDate).Days;

                    var dayAmount = Math.Round((existingPlan.Price ?? 1) / 30);
                    var totalDayAmount = differenceDays * dayAmount;

                    planDetails.RemainingUpgradePlanAmount = planDetails.PlanAmount - (existingPlanDetails.PlanAmount - totalDayAmount);
                    planDetails.IGSTAmount = (planDetails.RemainingUpgradePlanAmount * IGSTPercentage) / 100;
                    planDetails.TotalAmount = Math.Round(planDetails.RemainingUpgradePlanAmount + planDetails.IGSTAmount);
                    planDetails.ExpiryDate = CalculateEndDate(planType.ToString(), StringHelper.ConvertIntoIndianDateTime(DateTime.UtcNow));
                }
                else
                {
                    planDetails.IGSTAmount = (planDetails.PlanAmount * IGSTPercentage) / 100;
                    planDetails.TotalAmount = planDetails.PlanAmount + planDetails.IGSTAmount;

                    var startDate = subscription != null
                        ? (subscription.RenewEndDate?.AddDays(1) ?? subscription.PlanEndDate.AddDays(1))
                        : StringHelper.ConvertIntoIndianDateTime(DateTime.UtcNow);

                    planDetails.ExpiryDate = CalculateEndDate(planType.ToString(), startDate);
                }

                return Ok(new ApiResponse<PlanDto>()
                {
                    Success = true,
                    Data = planDetails,
                    Message = "Get plan details successfully."
                });
            }
            catch (Exception ex)
            {
                return BadRequest(new ApiResponse<string>
                {
                    Success = false,
                    Message = ex.Message,
                    Errors = ex.StackTrace
                });
            }
        }

        [HttpGet("{planId}")]
        [Authorize]
        public async Task<IActionResult> GetPlanById(int planId)
        {
            var subscriptionPlan = await _subscriptionPlanService.GetPlanById(planId);

            if (subscriptionPlan == null)
            {
                return NotFound("Plan not found");
            }
            else if (!subscriptionPlan.Status)
                return NotFound("Plan is not active.");

            return Ok(subscriptionPlan);
        }


        [HttpPost("add-new-plan")]
        public async Task<IActionResult> AddNewPlan([FromBody] PlanEntitiesDto planEntities)
        {
            try
            {
                var currentUserIdClaim = User.Claims.FirstOrDefault(c => c.Type == ClaimTypes.Name);

                if (currentUserIdClaim == null || !Guid.TryParse(currentUserIdClaim.Value, out Guid currentUserId))
                {
                    return BadRequest(new { Message = "Invalid current user." });
                }

                var result = await _subscriptionPlanService.CreatePlan(currentUserId, planEntities);

                if (result)
                {
                    return Ok(new { Message = "Plan added successfully." });
                }
                else
                {
                    return BadRequest(new { Message = "Failed to add plan." });
                }
            }
            catch (Exception ex)
            {
                return StatusCode(500, new { Message = "Internal Server Error" });
            }
        }

        [HttpPut("update-Plan/{planId}")]
        public async Task<IActionResult> Updateplan(int planId, [FromBody] UpdatePlanEntitiesDto updatePlanEntitiesDto)
        {
            try
            {
                var currentUserIdClaim = User.Claims.FirstOrDefault(c => c.Type == ClaimTypes.Name);

                if (currentUserIdClaim == null || !Guid.TryParse(currentUserIdClaim.Value, out Guid currentUserId))
                {
                    return BadRequest(new { Message = "Invalid current user." });
                }

                var result = await _subscriptionPlanService.UpdatePlan(currentUserId, planId, updatePlanEntitiesDto);

                if (result)
                {
                    return Ok(new { Message = "Plan updated successfully." });
                }
                else
                {
                    return BadRequest(new { Message = "Failed to update plan." });
                }
            }
            catch (Exception ex)
            {
                return StatusCode(500, new { Message = "Internal Server Error" });
            }
        }

        [HttpDelete("delete-plan/{planId}")]
        public async Task<IActionResult> DeleteSubscriptionPlan(int planId)
        {
            try
            {
                var currentUserIdClaim = User.Claims.FirstOrDefault(c => c.Type == ClaimTypes.Name);

                if (currentUserIdClaim == null || !Guid.TryParse(currentUserIdClaim.Value, out Guid currentUserId))
                {
                    return BadRequest(new { Message = "Invalid current user." });
                }

                var result = await _subscriptionPlanService.DeletePlan(currentUserId, planId);

                if (result)
                {
                    return Ok(new { Message = "Plan deleted successfully." });
                }
                else
                {
                    return BadRequest(new { Message = "Failed to delete plan." });
                }
            }
            catch (Exception ex)
            {
                return StatusCode(500, new { Message = "Internal Server Error" });
            }
        }

        [HttpGet]
        public async Task<ActionResult<IEnumerable<MenuWithPlanDto>>> GetActiveMenusByPlan([FromQuery] int? planId, [FromQuery] string? planName)
        {
            if (!planId.HasValue && string.IsNullOrEmpty(planName))
            {
                return BadRequest("Either planId or planName must be provided.");
            }

            var menus = await _subscriptionPlanService.GetActiveMenusByPlanAsync(planId, planName);

            if (menus == null || !menus.Any())
            {
                return NotFound();
            }

            return Ok(menus);
        }
        [HttpPost("AddPlanDiscount")]
        // [AuthorizeRoles(RoleConstants.Admin, RoleConstants.Owner)]
        [Authorize]
        public async Task<IActionResult> AddPlanDiscount(PlanDiscountDto planDiscount)
        {
            try
            {
                var result = await _planService.AddPlanDiscountAsync(planDiscount);
                return Ok(new ApiResponse<string>()
                {
                    Success = true,
                    Message = "Plan discount has been saved successfully."
                });

            }
            catch (Exception ex)
            {
                return StatusCode(500, new ApiResponse<string>()
                {
                    Success = true,
                    Message = ex.Message,
                    Errors = ex.StackTrace
                });
            }
        }
        private async Task<Subscriptions> UpdatePlatformSubscriptionAsync(string companyId, Guid currentUserId)
        {
            var plan = (await _subscriptionPlanService.GetAllPlansAsync())?
                .OrderByDescending(x => x.Price)
                .FirstOrDefault();

            var subscription = await _subscriptionPlanService.GetSubscriptionsAsync(companyId);
            bool isUpdate = false;
            if (subscription == null)
            {
                subscription = new Subscriptions()
                {
                    CompanyId = companyId,
                    DurationType = PlanType.Annually.ToString(),
                    PlanId = plan.Id,
                    PlanStartDate = StringHelper.ConvertIntoIndianDateTime(DateTime.UtcNow),
                    PlanEndDate = StringHelper.ConvertIntoIndianDateTime(DateTime.UtcNow).AddYears(1),
                    Status = "Paid",
                    UserId = currentUserId.ToString(),
                    CreatedAt = StringHelper.ConvertIntoIndianDateTime(DateTime.UtcNow),
                    CreatedBy = currentUserId.ToString()
                };
            }
            else
            {
                isUpdate = true;
                subscription.PlanId = plan.Id;
                subscription.RenewStartDate = StringHelper.ConvertIntoIndianDateTime(DateTime.UtcNow);
                subscription.RenewEndDate = StringHelper.ConvertIntoIndianDateTime(DateTime.UtcNow).AddYears(1);
                subscription.UpdatedAt = StringHelper.ConvertIntoIndianDateTime(DateTime.UtcNow);
                subscription.UpdatedBy = currentUserId.ToString();
            }
            var subscriptionResult = await _razorPayService.SaveSubscriptionAsync(subscription, isUpdate);

            var paymentWalletDetails = new PaymentWalletDetail()
            {
                CompanyId = companyId,
                UserId = currentUserId,
                PlanId = plan.Id,
                DurationType = PlanType.Annually.ToString(),
                OrderId = null,
                OrderAmount = 0,
                Currency = "INR",
                Status = "Paid",
                CreatedBy = currentUserId,
                CreatedAt = StringHelper.ConvertIntoIndianDateTime(DateTime.UtcNow),
                PlanStartDate = StringHelper.ConvertIntoIndianDateTime(DateTime.UtcNow),
                PlanEndDate = StringHelper.ConvertIntoIndianDateTime(DateTime.UtcNow).AddYears(1),
                SubscriptionId = subscriptionResult.Id
            };
            await _razorPayService.SavePaymentWalletAsync(paymentWalletDetails);
            return subscription;
        }
        private decimal GetPlanDiscountValue(PlanEntities plan, List<PlanDiscountEntity>? planDiscounts, PlanType planType)
        {
            var discount = planDiscounts?
                .FirstOrDefault(x => x.PlanId == plan.Id);

            if (planDiscounts == null || !planDiscounts.Any())
                return 0;

            if (discount == null)
                return 0;

            decimal discountValue = 0;
            decimal planPrice = plan.Price ?? 0;
            if (discount.DiscountType == DiscountType.Amount.ToString())
            {
                discountValue = planType switch
                {
                    PlanType.Weekly => discount.WeeklyDiscount ?? 0,
                    PlanType.Monthly => discount.MonthlyDiscount ?? 0,
                    PlanType.Quarterly => discount.QuarterlyDiscount ?? 0,
                    PlanType.Annually => discount.AnnuallyDiscount ?? 0,
                    _ => 0,
                };
            }
            else
            {
                discountValue = planType switch
                {
                    PlanType.Weekly => (discount.WeeklyDiscount ?? 0) != 0 ?
                        (planPrice / 4) * (discount.WeeklyDiscount ?? 0) / 100 : 0,
                    PlanType.Monthly => (discount.MonthlyDiscount ?? 0) != 0 ?
                        planPrice * (discount.MonthlyDiscount ?? 0) / 100 : 0,
                    PlanType.Quarterly => (discount.QuarterlyDiscount ?? 0) != 0 ?
                        (planPrice * 3) * (discount.QuarterlyDiscount ?? 0) / 100 : 0,
                    PlanType.Annually => (discount.AnnuallyDiscount ?? 0) != 0 ?
                        planPrice * 12 * (discount.AnnuallyDiscount ?? 0) / 100 : 0,
                    _ => 0,
                };
            }
            return discountValue;
        }
        private async Task<PlanDto> GetPlanDetailsAsync(PlanEntities plan, PlanType planType)
        {
            PlanDto planDetailsDto = new();
            var planDiscounts = await _planService.GetPlanDiscountsAsync();
            decimal planDiscountValue = 0;
            planDetailsDto.PlanName = plan.PlanName;
            decimal price = plan.Price ?? 0;
            switch (planType)
            {
                case PlanType.Monthly:

                    planDiscountValue = GetPlanDiscountValue(plan, planDiscounts, planType);
                    planDetailsDto.PlanAmount = Math.Round(Math.Round((plan.Price - planDiscountValue) ?? 0));
                    planDetailsDto.Type = PlanType.Monthly.ToString();
                    break;

                case PlanType.Quarterly:

                    planDiscountValue = GetPlanDiscountValue(plan, planDiscounts, planType);
                    planDetailsDto.PlanAmount = Math.Round(Math.Round((price * 3) - planDiscountValue));
                    planDetailsDto.Type = PlanType.Quarterly.ToString();

                    break;

                case PlanType.Annually:

                    planDiscountValue = GetPlanDiscountValue(plan, planDiscounts, planType);
                    planDetailsDto.PlanAmount = Math.Round(Math.Round(price * 12) - planDiscountValue);
                    planDetailsDto.Type = PlanType.Annually.ToString();

                    break;
            }
            return planDetailsDto;
        }
        private static DateTime CalculateEndDate(string? durationType, DateTime startDate)
        {
            return (durationType ?? "Monthly") switch
            {
                nameof(PlanType.Weekly) => startDate.AddDays(7),
                nameof(PlanType.Monthly) => startDate.AddMonths(1),
                nameof(PlanType.Quarterly) => startDate.AddMonths(3),
                nameof(PlanType.Annually) => startDate.AddYears(1),
                _ => startDate.AddMonths(1)
            };
        }
    }
}
