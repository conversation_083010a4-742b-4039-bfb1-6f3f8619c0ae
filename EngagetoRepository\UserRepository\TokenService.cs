﻿using Amazon.Runtime.Internal.Transform;
using EngagetoContracts.UserContracts;
using EngagetoDapper.Data.Interfaces.GenericInterfaces;
using EngagetoEntities.Entities;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Configuration;
using Microsoft.Identity.Client;
using Microsoft.IdentityModel.Tokens;
using System.IdentityModel.Tokens.Jwt;
using System.Security.Claims;
using System.Text;

namespace EngagetoRepository.UserRepository
{
    public class TokenService : ITokenService
    {
        private readonly IConfiguration _configuration;
        private readonly IGenericRepository _genericRepository;

        public TokenService(IConfiguration configuration, IGenericRepository genericRepository)
        {
            _configuration = configuration;
            _genericRepository = genericRepository;
        }

        public string GenerateToken(Ahex_CRM_Users user)
        {
            var claims = new[]
            {
                new Claim(ClaimTypes.Name, user.Id.ToString()),
                new Claim(JwtRegisteredClaimNames.Jti, Guid.NewGuid().ToString()),
                new Claim("UserName",user.Name),
                new Claim("BusinessId",user.CompanyId),
                new Claim("Email",user.EmailAddress ?? string.Empty),
            };

            var key = new SymmetricSecurityKey(Encoding.UTF8.GetBytes("Im0BochrmocQD992BKCFQvfcUsVdaJZBcTDb9TQ8wt-o9qIAzGH69rubwf5RAGGV"));
            var creds = new SigningCredentials(key, SecurityAlgorithms.HmacSha256);

            var token = new JwtSecurityToken(
                /* issuer: _configuration["Jwt:Issuer"],
                 audience: _configuration["Jwt:Audience"],*/
                issuer: "https://dev-jvgzpy2caz4fvqso.us.auth0.com/",
                audience: "https://loginAPI/api",
                claims: claims,
                expires: DateTime.UtcNow.AddHours(2),
                signingCredentials: creds
            );

            return new JwtSecurityTokenHandler().WriteToken(token);
        }
        public async Task<string> GenerateTokenForTenant(string tenantId)
        {
            try
            {
                string roleId = "55aa1ba5-1507-47e2-888b-89d80cd41906";
                var businessDetail = (await _genericRepository.GetByObjectAsync<Ahex_CRM_BusinessDetails>(new Dictionary<string, object> { { "TenantId", tenantId } }))?.FirstOrDefault();
                if (businessDetail != null)
                {
                    var userDetail = (await _genericRepository.GetByObjectAsync<Ahex_CRM_Users>(new Dictionary<string, object> { { "CompanyId", businessDetail.Id.ToString() }, { "RoleId", roleId } }))?.FirstOrDefault();
                    if (userDetail != null)
                    {
                        var claims = new[]
                        {
                            new Claim(ClaimTypes.Name, userDetail.Id.ToString()),
                            new Claim(JwtRegisteredClaimNames.Jti, Guid.NewGuid().ToString()),
                            new Claim("UserName",userDetail.Name),
                            new Claim("BusinessId",userDetail.CompanyId),
                            new Claim("Email",userDetail.EmailAddress ?? string.Empty),
                        };

                        var key = new SymmetricSecurityKey(Encoding.UTF8.GetBytes("Im0BochrmocQD992BKCFQvfcUsVdaJZBcTDb9TQ8wt-o9qIAzGH69rubwf5RAGGV"));
                        var creds = new SigningCredentials(key, SecurityAlgorithms.HmacSha256);

                        var token = new JwtSecurityToken(
                            /* issuer: _configuration["Jwt:Issuer"],
                                audience: _configuration["Jwt:Audience"],*/
                            issuer: "https://dev-jvgzpy2caz4fvqso.us.auth0.com/",
                            audience: "https://loginAPI/api",
                            claims: claims,
                            expires: DateTime.UtcNow.AddHours(2),
                            signingCredentials: creds
                        );
                        return new JwtSecurityTokenHandler().WriteToken(token);
                    }
                }
                throw new Exception("Not valid tenant");
            }
            catch (Exception ex)
            {
                throw;
            }
        }
        /* public string GenerateToken1(AccountDetails user)
         {
             var claims = new[]
             {
             new Claim(ClaimTypes.Name, user.Id.ToString()),
             new Claim(JwtRegisteredClaimNames.Jti, Guid.NewGuid().ToString()),

         };

             var key = new SymmetricSecurityKey(Encoding.UTF8.GetBytes("Im0BochrmocQD992BKCFQvfcUsVdaJZBcTDb9TQ8wt-o9qIAzGH69rubwf5RAGGV"));
             var creds = new SigningCredentials(key, SecurityAlgorithms.HmacSha256);

             var token = new JwtSecurityToken(
                *//* issuer: _configuration["Jwt:Issuer"],
                 audience: _configuration["Jwt:Audience"],*//*
                issuer: "https://dev-jvgzpy2caz4fvqso.us.auth0.com/",
                 audience: "https://loginAPI/api",
                 claims: claims,
                 expires: DateTime.UtcNow.AddHours(1),
                 signingCredentials: creds
             );

             return new JwtSecurityTokenHandler().WriteToken(token);
         }
         public string GenerateToken2(ClientsAccountDetails user)
         {
             var claims = new[]
             {
             new Claim(ClaimTypes.Name, user.Id.ToString()),
             new Claim(JwtRegisteredClaimNames.Jti, Guid.NewGuid().ToString()),

         };

             var key = new SymmetricSecurityKey(Encoding.UTF8.GetBytes("Im0BochrmocQD992BKCFQvfcUsVdaJZBcTDb9TQ8wt-o9qIAzGH69rubwf5RAGGV"));
             var creds = new SigningCredentials(key, SecurityAlgorithms.HmacSha256);

             var token = new JwtSecurityToken(
                *//* issuer: _configuration["Jwt:Issuer"],
                 audience: _configuration["Jwt:Audience"],*//*
                issuer: "https://dev-jvgzpy2caz4fvqso.us.auth0.com/",
                 audience: "https://loginAPI/api",
                 claims: claims,
                 expires: DateTime.UtcNow.AddHours(1),
                 signingCredentials: creds
             );

             return new JwtSecurityTokenHandler().WriteToken(token);
         }*/
      
        public async Task<object?> RefreshTokenAsync(string token, string refreshToken)
        {
            string secretKey = _configuration["JWT:Secret"] ?? string.Empty;
            var key = Encoding.UTF8.GetBytes(secretKey);
            var tokenHandler = new JwtSecurityTokenHandler();

            ClaimsPrincipal? principal = null;
            SecurityToken? validatedAccessToken = null;
            SecurityToken? validatedRefreshToken = null;

            try
            {
                principal = tokenHandler.ValidateToken(token, new TokenValidationParameters
                {
                    ValidateIssuerSigningKey = true,
                    IssuerSigningKey = new SymmetricSecurityKey(key),
                    ValidateIssuer = true,
                    ValidIssuer = _configuration["JWT:ValidIssuer"],
                    ValidateAudience = true,
                    ValidAudience = _configuration["JWT:ValidAudience"],
                    ValidateLifetime = false 
                }, out validatedAccessToken);

                var refreshTokenPrincipal = tokenHandler.ValidateToken(refreshToken, new TokenValidationParameters
                {
                    ValidateIssuerSigningKey = true,
                    IssuerSigningKey = new SymmetricSecurityKey(key),
                    ValidateIssuer = true,
                    ValidIssuer = _configuration["JWT:ValidIssuer"],
                    ValidateAudience = true,
                    ValidAudience = _configuration["JWT:ValidAudience"],
                    ValidateLifetime = true 
                }, out validatedRefreshToken);

                var userIdClaim = principal.FindFirst(ClaimTypes.Name);
                if (userIdClaim == null || !Guid.TryParse(userIdClaim.Value, out Guid userId))
                    throw new SecurityTokenException("Invalid token claims.");

                var users = await _genericRepository.GetByObjectAsync<Ahex_CRM_Users>(
                    new Dictionary<string, object> { { "Id", userId } });
                var user = users?.FirstOrDefault();
                if (user == null)
                    throw new SecurityTokenException("User not found.");

                if (user.RefreshToken != refreshToken)
                    throw new SecurityTokenException("Refresh token does not match.");

                var accessTokenExpiry = user.RefreshToken != null ? GetExpiryFromToken(user.RefreshToken) : new();
                var refreshTokenExpiry = user.RefreshToken != null ? GetExpiryFromToken(user.RefreshToken) : new();
                var newToken = GenerateToken(user);
                var newRefreshToken = "";
               
                if (refreshTokenExpiry == null || refreshTokenExpiry <= DateTime.UtcNow)
                {
                    newRefreshToken = GenerateRefreshToken(user, secretKey);
                    user.RefreshToken = newRefreshToken;
                    await _genericRepository.UpdateRecordAsync("Users", new List<string> { "RefreshToken" }, user, new Dictionary<string, object> { { "Id", user.Id } });
                }
                return new
                {
                    Token = newToken,
                    RefreshToken = !string.IsNullOrWhiteSpace(newRefreshToken) ? newRefreshToken : user.RefreshToken
            };
            }
            catch (SecurityTokenExpiredException ex)
            {
                throw new SecurityTokenException("Refresh token expired, please login again.");
            }
            catch (Exception ex)
            {
                throw new SecurityTokenException("Token validation failed: " + ex.Message);
            }
        }

        public string GenerateRefreshToken(Ahex_CRM_Users user, string secretKey)
        {
            var key = new SymmetricSecurityKey(Encoding.UTF8.GetBytes(secretKey));
            var creds = new SigningCredentials(key, SecurityAlgorithms.HmacSha256);

            var claims = new List<Claim> { new Claim(ClaimTypes.Name, user.Id.ToString()) };

            var token = new JwtSecurityToken(
                issuer: _configuration["JWT:ValidIssuer"],
                audience: _configuration["JWT:ValidAudience"],
                claims: claims,
                expires: DateTime.UtcNow.AddDays(7),  
                signingCredentials: creds
            );
            return new JwtSecurityTokenHandler().WriteToken(token);
        }


        public  DateTime? GetExpiryFromToken(string token)
        {
            var handler = new JwtSecurityTokenHandler();
            if (!handler.CanReadToken(token))
                return null;

            var jwtToken = handler.ReadJwtToken(token);
            return jwtToken.ValidTo;
        }
    }
}
