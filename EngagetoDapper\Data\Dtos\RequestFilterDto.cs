﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace EngagetoDapper.Data.Dtos
{
    public class RequestFilterDto
    {
        public string Key { get; set; }
        public object Value { get; set; }
        public string? LogicalOperator { get; set; } = "AND";
        public string Operator { get; set; } = "=";
        public RequestFilterDto() { }
        public RequestFilterDto(string key, object value,string oper)
        {
            Key = key;
            Value = value;
            Operator = oper;
        }
    }
}
