﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using EngagetoEntities.Entities;

namespace EngagetoContracts.UserContracts
{
    public interface IPermissionsService
    {
        Task<IEnumerable<ViewPermissionDto>> GetAllPermissionsAsync(Guid currentUserId, string searchKeyword = null);
        Task<Guid> CreatePermissionAsync(Guid currentUserId, PermissionDto newPermission);
        Task<bool> UpdatePermissionAsync(Guid currentUserId, Guid permissionId, PermissionDto updatedPermission);
        Task<bool> DeletePermissionAsync(Guid currentUserId, Guid permissionId);
        Task<bool> CreateAssignPermissionAsync(Guid currentUserId, AssignPermissionsToRoleIdDto assignmentDto);
        Task<bool> UpdatePermissionsAndStatusAsync(Guid currentUserId, Guid roleId, string companyId, List<AssignPermissionsDto> updatedAssignPermissions);
        Task<bool> DeleteAssignPermissionAsync(Guid currentUserId, AssignPermissionsToRoleIdDto assignmentDto);
        Task<List<AssignPermissionsToRoleIdDto>> GetAssignPermissionsAsync(Guid currentUserId);

        Task<object> GetPermissionsAndAssignPermissionsAsync(Guid roleId, string companyId);
        Task<bool> AssignPermissionsToClientAsync(Guid currentUserId, Guid roleId, string companyId, List<AssignPermissionsDto> updatedAssignPermissions);

        public Dictionary<string, List<Dictionary<string, List<string>>>> GetMenuHierarchy(string roleId, string companyId);

        Task<bool> UpdateMenuStatusAsync(string roleId, string companyId, string menuName, bool status, Guid currentUserId);
        Task<object> GetMenuAndRoleRelationshipAsync(Guid roleId, string companyId);
        Dictionary<string, List<MainMenu>> GetMenuHierarchy1(string roleId, string companyId);
        Task<bool> UpdateClientMenuStatusAsync(string roleId, string companyId, string menuName, bool status, Guid currentUserId);
    }
}
