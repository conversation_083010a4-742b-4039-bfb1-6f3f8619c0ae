﻿
using EngagetoEntities.Dtos.ConversationDtos;

namespace EngagetoContracts.WebhookContracts.SentMessage
{
    public interface IWhatsAppBusinessNotificarion
    {
        /// <summary>
        /// the status of send message change into database
        /// </summary>
        Task UpdateSentMessageStatus(dynamic Status);
        Task RenderMessagesBySignalR(string companyId, List<ConversationDto> objects);
        Task RenderErrorMessagesBySignalR(string companyId, List<ConversationDto> objects);
    }
}
