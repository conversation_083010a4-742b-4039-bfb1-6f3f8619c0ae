﻿using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace EngagetoEntities.Entities
{
    [Table("EnvironmentUrlEntities")]
    public class EnvironmentUrlEntity
    {
        [Key]
        public int Id { get; set; }
        public string Environment { get; set; }
        public string Url { get; set; }
        public bool IsActive { get; set; }
        public string Description { get; set; }
    }
}
