﻿namespace EngagetoEntities.Dtos.WalletDtos
{
    public class TenantWalletDto
    {
        public string TenantId { get; set; }
        public decimal Balance { get; set; }
        public decimal ExpectedBalance { get; set; }
        public WalletTransactionHistoryDto? Marketing { get; set; }
        public WalletTransactionHistoryDto? Utility { get; set; }
        public WalletTransactionHistoryDto? Authentication { get; set; }
    }
    public class WalletTransactionHistoryDto
    {
        public string? Category { get; set; }
        public decimal? Cost { get; set; }
        public decimal? Count { get; set; }
    }

}
