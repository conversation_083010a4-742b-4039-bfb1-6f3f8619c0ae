﻿using EngagetoContracts.MetaContracts;
using EngagetoEntities.Dtos.MetaDto;
using Newtonsoft.Json.Linq;
namespace EngagetoRepository.MetaServices
{
    public class MetaAuthenticationService : IMetaAuthenticationService
    {

        public void AddBodyComponent(ref JArray components, bool? add_security_recommendation)
        {
            var bodyPayload = CreateAuthenticationMetaPayloadJsonDto.CreateBodyPayload(add_security_recommendation);
            components.Add(JObject.Parse(bodyPayload));
        }

        public void AddFooterComponent(ref JArray components, string? code_expiration_minutes)
        {
            var footerPayload = CreateAuthenticationMetaPayloadJsonDto.CreateFooterPayload(code_expiration_minutes);
            components.Add(JObject.Parse(footerPayload));
        }

        public void AddButtonsComponent(ref JArray components, string? otpType, string package_name, string signature_hash)
        {
            var supportedApps = new JArray();

            var supportedAppJson = CreateAuthenticationMetaPayloadJsonDto.CreateSupportedAppPayload(package_name, signature_hash);
            supportedApps.Add(JObject.Parse(supportedAppJson));

            var otpButtonPayload = CreateAuthenticationMetaPayloadJsonDto.CreateOtpButtonPayload_One_Tap(otpType, supportedApps);

            var buttonsArray = new JArray();
            CreateAuthenticationMetaPayloadJsonDto.AddButtonToButtonsArray(otpButtonPayload, ref buttonsArray);

            var buttonsPayload = CreateAuthenticationMetaPayloadJsonDto.CreateButtonPayload(buttonsArray);
            components.Add(JObject.Parse(buttonsPayload));
        }

        public JObject BuildComponents(bool? add_security_recommendation, string? code_expiration_minutes, string? otpType, string package_name, string signature_hash)
        {
            var components = new JArray();
            AddBodyComponent(ref components, add_security_recommendation);
            AddFooterComponent(ref components, code_expiration_minutes);
            AddButtonsComponent(ref components, otpType, package_name, signature_hash);
            return new JObject { ["components"] = components };
        }
    }
}

