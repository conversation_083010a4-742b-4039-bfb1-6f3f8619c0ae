﻿using System;
using System.Collections.Generic;

namespace EngagetoDatabase.AutomationDataBase.Models;

public partial class User
{
    public Guid Id { get; set; }

    public string? CompanyId { get; set; }

    public string? Name { get; set; }

    public string? Designation { get; set; }

    public string? EmailAddress { get; set; }

    public string? Password { get; set; }

    public string? RoleId { get; set; }

    public string? CountryCode { get; set; }

    public string? PhoneNumber { get; set; }

    public string? CountryName { get; set; }

    public string? FacebookBusinessManagerId { get; set; }

    public string? WhatsAppBusinessId { get; set; }

    public string? Address { get; set; }

    public string? About { get; set; }

    public string? Image { get; set; }

    public string? CreatedBy { get; set; }

    public DateTime? LastOnlineTime { get; set; }

    public DateTime? CreationDate { get; set; }

    public bool Status { get; set; }
}
