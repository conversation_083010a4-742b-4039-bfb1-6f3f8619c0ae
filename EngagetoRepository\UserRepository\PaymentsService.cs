﻿using Microsoft.EntityFrameworkCore;
using Razorpay.Api;
using System;
using System.Linq;
using System.Threading.Tasks;
using EngagetoContracts.UserContracts;
using EngagetoEntities.UserEntities.Models;
using static EngagetoEntities.UserEntities.Models.ExpiryDateAttribute;

namespace EngagetoRepository.Repository
{
    public class PaymentsService : IRazorpayPaymentService
    {
        private readonly ApplicationDBContext _context;
        private readonly IRazorPayService _razorpayService;

        public PaymentsService(ApplicationDBContext context, IRazorPayService razorpayService)
        {
            _context = context;
            _razorpayService = razorpayService;
        }

        /*  public async Task<string> CreatePaymentAsync(Guid currentUserId, PaymentCreateRequest paymentCreateRequest)
          {
              try
              {

                  var subscriptionPlan = await _context.PlanEntities
                  .FirstOrDefaultAsync(sp => sp.Id == paymentCreateRequest.SubscriptionPlanId);

                  if (subscriptionPlan == null)
                  {
                      throw new InvalidOperationException("Invalid SubscriptionPlanId.");
                  }

                  // Create a new payment record in the database
                  var payment = new PaymentsDetails
                  {
                      RazorpayPaymentId = paymentCreateRequest.RazorpayPaymentId,
                      Amount = subscriptionPlan.Price,
                      PaymentDate = DateTime.UtcNow,
                      PaymentMode = paymentCreateRequest.PaymentMode,
                      PhoneNumber = paymentCreateRequest.PhoneNumber,
                      SubscriptionPlanId = subscriptionPlan.SubscriptionPlanId,
                      UserId = currentUserId.ToString()  
                  };

                  _context.RazorpayPayments.Add(payment);


                  var orderId = await _razorpayService.CreateOrder(subscriptionPlan.Price, paymentCreateRequest.Currency, paymentCreateRequest.RazorpayPaymentId);
                  await _context.SaveChangesAsync();

                  return orderId;
              }
              catch (Exception ex)
              {

                  Console.WriteLine($"Error in CreatePaymentAsync: {ex.Message}");
                  throw;
              }
          }*/

        /* public async Task<bool> VerifyPaymentAsync(PaymentVerificationRequest paymentVerificationRequest)
         {
             try
             {
                 // Retrieve the payment record in the database
                 var payment = await _context.RazorpayPayments
                     .FirstOrDefaultAsync(p => p.PaymentId.ToString() == paymentVerificationRequest.PaymentId);

                 if (payment != null)
                 {

                     var isSignatureValid = await _razorpayService.VerifyPaymentSignature(
                         paymentVerificationRequest.OrderId,
                         paymentVerificationRequest.PaymentId,
                         paymentVerificationRequest.Signature);

                     if (!isSignatureValid)
                     {

                         return false;
                     }


                     payment.PaymentDate = DateTime.UtcNow;
                     await _context.SaveChangesAsync();

                     return true;
                 }
                 else
                 {
                     return false; 
                 }
             }
             catch (Exception ex)
             {

                 Console.WriteLine($"Error in VerifyPaymentAsync: {ex.Message}");
                 return false;
             }
         }*/
        public async Task<Guid> AddPaymentCardAsync(Guid userId, PaymentCardDetailsDto paymentCardDto)
        {
            var paymentCard = new PaymentCardDetails
            {
                UserId = userId,
                CardNumber = paymentCardDto.CardNumber,
                ExpiryDate = paymentCardDto.ExpiryDate,
                CVV = paymentCardDto.CVV,
                CardholderName = paymentCardDto.CardholderName
            };

            _context.PaymentCardDetails.Add(paymentCard);
            await _context.SaveChangesAsync();

            return paymentCard.CardId;
        }
        public async Task<IEnumerable<PaymentCardDetails>> GetAllPaymentCardsAsync(Guid currentUserId, string searchQuery = null)
        {
            try
            {

                var query = _context.PaymentCardDetails
                    .Where(card => card.UserId == currentUserId);

                if (!string.IsNullOrEmpty(searchQuery))
                {

                    if (Guid.TryParse(searchQuery, out Guid cardId))
                    {
                        query = query.Where(card => card.CardId == cardId);
                    }
                    else if (Guid.TryParse(searchQuery, out Guid userId))
                    {
                        query = query.Where(card => card.UserId == userId);
                    }
                    else
                    {
                        query = query.Where(card => card.CardholderName.Contains(searchQuery));
                    }
                }

                var paymentCards = await query.ToListAsync();
                return paymentCards;
            }
            catch (Exception)
            {

                return null;
            }
        }
        public async Task<bool> DeletePaymentCardAsync(Guid cardId)
        {
            var card = await _context.PaymentCardDetails.FindAsync(cardId);
            if (card == null)
                return false;

            _context.PaymentCardDetails.Remove(card);
            await _context.SaveChangesAsync();
            return true;
        }
    }
}
