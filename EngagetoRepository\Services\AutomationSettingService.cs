﻿using EngagetoContracts.MetaContracts;
using EngagetoContracts.Repostiories;
using EngagetoContracts.Services;
using EngagetoDapper.Data.Interfaces.GenericInterfaces;
using EngagetoDapper.Data.Interfaces.IConversationAnalyticsInterfaces;
using EngagetoDapper.Data.Interfaces.ILogInterfaces;
using EngagetoEntities.Dtos.AutomationDtos;
using EngagetoEntities.Dtos.MetaDto;
using EngagetoEntities.Entities;
using EngagetoEntities.Enums;
using EngagetoEntities.Utilities;
using Mapster;
using Newtonsoft.Json;
using System.Text.RegularExpressions;

namespace EngagetoRepository.Services
{
    public class AutomationSettingService : IAutomationSettingService
    {
        private readonly IAutomationSettingRepo _settingRepo;
        private readonly IUserIdentityService _userIdentityService;
        private readonly IMetaApiService _metaApiService;
        private readonly IGenericRepository _genericRepository;
        private readonly IConversationAnalyticsRepository _analyticsRepository;
        private readonly ILogHistoryService _logHistoryService;

        public AutomationSettingService(IAutomationSettingRepo settingRepo,
            IUserIdentityService userIdentityService,
            IMetaApiService metaApiService,
            IGenericRepository genericRepository,
            IConversationAnalyticsRepository analyticsRepository,
            ILogHistoryService logHistoryService)
        {
            _settingRepo = settingRepo;
            _userIdentityService = userIdentityService;
            _metaApiService = metaApiService;
            _genericRepository = genericRepository;
            _analyticsRepository = analyticsRepository;
            _logHistoryService = logHistoryService;
        }

        public async Task<ViewAutomationSettingDto> GetAutomationSettingAsync(string businessId)
        {
            try
            {
                return (await _settingRepo.GetAutomationSettingAsync(businessId)).Adapt<ViewAutomationSettingDto>();
            }
            catch (Exception ex)
            {
                throw;
            }
        }

        public async Task<int> SaveAutomationSettingAsync(BaseAutomationSettingDto settingDto)
        {
            try
            {
                var autoSetting = settingDto.Adapt<AutomationSetting>();
                autoSetting.BusinessId = _userIdentityService.BusinessId;
                autoSetting.UserId = _userIdentityService.UserId;
                autoSetting.CreatedAt = DateTime.UtcNow;
                autoSetting.UpdatedAt = DateTime.UtcNow;
                autoSetting.CreatedBy = _userIdentityService.UserId;
                autoSetting.UpdatedBy = _userIdentityService.UserId;
                return await _settingRepo.SaveAutomationSettingAsync(autoSetting);
            }
            catch (Exception ex)
            {
                throw;
            }
        }

        public async Task<ViewAutomationSettingDto> UpdateAutomationSettingAsync(EditAutomationSettingDto settingDto)
        {
            try
            {
                var autoSetting = settingDto.Adapt<AutomationSetting>();
                autoSetting.BusinessId = _userIdentityService.BusinessId;
                autoSetting.UserId = _userIdentityService.UserId;
                autoSetting.UpdatedAt = DateTime.UtcNow;

                autoSetting.UpdatedBy = _userIdentityService.UserId;
                return (await _settingRepo.UpdateAutomationSettingAsync(autoSetting)).Adapt<ViewAutomationSettingDto>();
            }
            catch (Exception ex)
            {
                throw;
            }
        }
        public async Task SendAutomationMessageAsync(string businessId, Contacts contact, Conversations conv, Guid? userId)
        {
            try
            {
                string phoneNumber = ($"{contact.CountryCode}{contact.Contact}").Replace("+", "");
                var setting = await GetAutomationSettingAsync(businessId);
                if (setting != null)
                {
                    switch (setting.AutomationType)
                    {
                        case AutomationType.Welcome:
                            var isSentWelcomeMessage = await _analyticsRepository.IsSentWelcomeMessageAsync(businessId, phoneNumber);
                            await _logHistoryService.SaveInformationLogHistoryAsyn("SendAutomationMessageAsync", conv, setting, $"isSentWelcomeMessage:{isSentWelcomeMessage}");
                            if (isSentWelcomeMessage)
                            {
                                var welcomeMessageSetting = setting.WelcomeSetting;
                                var keyWordsSettings = welcomeMessageSetting?.Where(x => x.KeywordSettings != null).ToList();
                                var withoutKeyWordsSettingMessage = welcomeMessageSetting?.FirstOrDefault(x => x.KeywordSettings == null);

                                var matchedMessage = keyWordsSettings?.FirstOrDefault(message =>
                                {
                                    if (message.KeywordSettings == null) return false;

                                    switch (message.KeywordSettings.SendType)
                                    {
                                        case MessageSendType.ByText:
                                            if (conv == null || string.IsNullOrWhiteSpace(conv.TextMessage)) return false;

                                            var convMessage = Regex.Replace(conv.TextMessage, @"[^a-zA-Z0-9]+", "").ToLowerInvariant();
                                            var normalizedKeywords = message.KeywordSettings.Keywords?
                                                .Where(k => !string.IsNullOrWhiteSpace(k))
                                                .Select(k => Regex.Replace(k, @"[^a-zA-Z0-9]+", "").ToLowerInvariant());

                                            return normalizedKeywords?.Any(k => message.KeywordSettings.Condition switch
                                            {
                                                Condition.Contains => convMessage.Contains(k),
                                                Condition.ExactMatch => convMessage == k,
                                                _ => false
                                            }) ?? false;

                                        case MessageSendType.ByCountryCode:
                                            if (string.IsNullOrWhiteSpace(conv?.From)) return false;

                                            var countryCode = PhoneNumberHelper.GetCountryCodeAndPhoneNumber(conv.From).CountryCode;
                                            return message.KeywordSettings.Keywords?
                                                .Where(k => !string.IsNullOrWhiteSpace(k))
                                                .Any(k => message.KeywordSettings.Condition switch
                                                {
                                                    Condition.Contains => k.Contains(countryCode),
                                                    Condition.ExactMatch => k == countryCode,
                                                    _ => false
                                                }) ?? false;

                                        default:
                                            return false;
                                    }
                                });
                                await _logHistoryService.SaveInformationLogHistoryAsyn("SendAutomationMessageAsync", matchedMessage, setting, $"before:{isSentWelcomeMessage}");
                                matchedMessage ??= withoutKeyWordsSettingMessage;

                                if (matchedMessage?.Message != null)
                                {
                                    await SendTextMessageAsync(businessId, phoneNumber, contact, userId, matchedMessage.Message);
                                }
                                else if (matchedMessage?.Templates != null)
                                {
                                    await SendTemplateAsync(businessId, phoneNumber, contact, userId, matchedMessage.Templates);
                                }
                            }
                            break;

                        case AutomationType.Delay:
                            break;

                        case AutomationType.OutOffice:
                            break;
                    }
                }
            }
            catch (Exception ex) 
            { 
            
            }
        }

        private async Task SendTextMessageAsync(string businessId, string phoneNumber, Contacts contact, Guid? userId, AutomationMessage message)
        {
            var conversation = new Conversations()
            {
                From = businessId,
                To = phoneNumber.Replace("+", ""),
                BusinessId = Guid.TryParse(businessId, out var tempGuid) ? tempGuid : Guid.Empty,
                ContactId = contact.ContactId,
                CreatedAt = DateTime.UtcNow,
                Status = ConvStatus.sent,
                Id = Guid.NewGuid(),
                UserId = userId.ToString(),
            };
            (string mimeType, string mediaType) = string.IsNullOrEmpty(message.MediaUrl) ? ("text", string.Empty) : (await GetMediaTypeAsync(message.MediaUrl));
            string? bodyMessage = contact != null ? MapContact(contact, message.Text, message.BodyVariable ?? new()).message : message.Text;
            conversation.TextMessage = string.IsNullOrEmpty(message.MediaUrl) ? bodyMessage : string.Empty;
            conversation.MediaCaption = string.IsNullOrEmpty(message.MediaUrl) ? string.Empty : bodyMessage;
            conversation.MediaUrl = message.MediaUrl;
            var result = await _metaApiService.SendTextWithMediaMessageAsync(businessId, phoneNumber, bodyMessage, mediaType, conversation.MediaUrl, null);
            if (result.IsSuccess)
            {
                var response = result.Result.ToObject<WhatsAppResponse>();
                conversation.WhatsAppMessageId = response?.Messages?.FirstOrDefault()?.Id ?? string.Empty;
            }
            else
            {
                conversation.Status = ConvStatus.failed;
                conversation.WhatsAppMessageId = string.Empty;
            }
            await _genericRepository.SaveAsync(conversation);
        }
        private async Task SendTemplateAsync(string businessId, string phoneNumber, Contacts contact, Guid? userId, AutomatonTemplate message)
        {
            var templates = await _genericRepository.GetByObjectAsync<Template>(new Dictionary<string, object> { { "TemplateId", message.Id } }, "Templates");
            if (templates.Any())
            {
                var template = templates.First();
                var buttons = await _genericRepository.GetByObjectAsync<Button>(new Dictionary<string, object> { { "TemplateId", message.Id } }, "ButtonDetails");

                var bodyValues = contact != null && !string.IsNullOrEmpty(template.Body) ? MapContact(contact, template.Body, message.BodyVariable ?? new()).values.ToList() : message.BodyVariable?.Select(x => x.FallbackValue)?.ToList();
                var headerValue = contact != null && !string.IsNullOrEmpty(template.Header) ? MapContact(contact, template.Header ?? string.Empty, message.HeaderVariable ?? new()).values.FirstOrDefault() : message.HeaderVariable?.FirstOrDefault()?.FallbackValue;

                var bodyMessage = StringHelper.ReplacePlaceholders(StringHelper.ReplaceAndExtractVariables(template.Body).UpdatedMessage, bodyValues ?? new());
                var headerMessage = template.MediaType == MediaType.TEXT
                    ? StringHelper.ReplaceAndExtractVariables(template.Header ?? string.Empty).UpdatedMessage.Replace("{{1}}", headerValue)
                    : template.Header;

                var phoneNumberButtons = buttons?.FirstOrDefault(b => b.ButtonType == "PHONE_NUMBER");
                var urlButtons = buttons?.Where(b => b.ButtonType == "URL").ToList();
                var quickReplyButtons = buttons?.Where(b => b.ButtonType == "QUICK_REPLY").ToList();

                var conv = new Conversations
                {
                    Id = Guid.NewGuid(),
                    From = template.BusinessId,
                    To = phoneNumber,
                    BusinessId = Guid.TryParse(template.BusinessId, out var tempGuid) ? tempGuid : Guid.Empty,
                    ContactId = contact?.ContactId ?? Guid.Empty,
                    TemplateBody = bodyMessage,
                    TemplateHeader = headerMessage,
                    TemplateMediaUrl = template.MediaAwsUrl,
                    TemplateFooter = template.Footer,
                    TemplateMediaType = template.MediaType,
                    MessageType = MessageType.Template,
                    CreatedAt = DateTime.UtcNow,
                    ReferenceId = template.TemplateId.ToString(),
                    CallButtonName = phoneNumberButtons?.ButtonName ?? "",
                    PhoneNumber = phoneNumberButtons?.ButtonValue ?? "",
                    UrlButtonNames = urlButtons?.Any() == true ? string.Join(",", urlButtons?.Select(m => m.ButtonName)) : string.Empty,
                    RedirectUrls = urlButtons?.Any() == true ? string.Join(",", urlButtons?.Select(x => x.ButtonValue)) : string.Empty,
                    QuickReplies = quickReplyButtons?.Any() == true ? string.Join(",", quickReplyButtons?.Select(m => m.ButtonValue)) : string.Empty
                };

                if (template.MediaType != MediaType.TEXT && template.MediaType != MediaType.NONE)
                {
                    headerValue = template.MediaAwsUrl;
                }
                var response = await _metaApiService.SendTemplateAsync(businessId, template.LanguageCode.ToString(),
                                   phoneNumber, template.TemplateName, bodyValues, template.MediaType,
                                   headerValue, null);
                if (response.IsSuccess)
                {
                    var waId = StringHelper.GetValueFromResponse(response.Result, "messages[0].id") ?? string.Empty;
                    conv.WhatsAppMessageId = waId;
                    conv.Status = ConvStatus.sent;
                }
                else
                {
                    conv.WhatsAppMessageId = string.Empty;
                    conv.Status = ConvStatus.failed;
                    conv.ErrorMessage = StringHelper.GetValueFromResponse(response.Result, "error.message") ?? "Unknown error";
                    conv.ErrorDetails = JsonConvert.SerializeObject(response.Result);
                }
                await _logHistoryService.SaveSuccessLogHistoryAsyn("SendAutomationMessageAsync", message, response.Result, $"complete");
                await _genericRepository.SaveAsync(conv);
            }
        }
        private (string message, List<string> values) MapContact(Contacts contact, string messaage, List<BaseVariableDto> variables)
        {
            if (contact == null || !variables.Any())
                return (messaage, new());

            List<string> values = new List<string>();
            var dictContact = ObjectHelper.ConvertToDictionary(contact);
            var bodyValue = variables.Select(x =>
            {
                if (x.Value != null && dictContact.TryGetValue(x.Value, out var value))
                {
                    return !string.IsNullOrEmpty(value) ? value : x.FallbackValue;
                }
                return x.FallbackValue;
            }).ToList();

            var updateMessage = StringHelper.ReplacePlaceholders(messaage, bodyValue);

            return (updateMessage, bodyValue);
        }
        private async Task<(string MimeType, string MediaType)> GetMediaTypeAsync(string url)
        {
            try
            {
                var client = new HttpClient();
                var response = await client.GetAsync(url);

                if (!response.IsSuccessStatusCode)
                    throw new InvalidOperationException("Failed to retrieve content from the provided URL.");

                var mimeType = response.Content.Headers.ContentType?.MediaType;
                if (string.IsNullOrEmpty(mimeType))
                    throw new InvalidOperationException("Content type not found.");

                var mediaType = GetMediaCategory(mimeType);
                return (mimeType, mediaType);
            }
            catch (Exception ex)
            {
                await _logHistoryService.SaveErrorLogHistoryAsyn("SendAutomationMessageAsync", url,"", ex.Message,ex.StackTrace);
                throw new InvalidOperationException("The provided URL is not valid or accessible.", ex);
            }
        }
        private string GetMediaCategory(string mimeType)
        {
            if (mimeType.StartsWith("application/"))
            {
                var documentTypes = new HashSet<string>
                {
                    "application/pdf",
                    "application/msword",
                    "application/vnd.openxmlformats-officedocument.wordprocessingml.document",
                    "application/vnd.ms-excel",
                    "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet",
                    "text/plain"
                };

                return documentTypes.Contains(mimeType) ? "document" : "Others";
            }
            var mediaType = mimeType.Split('/')[0]; // Example: "image/jpeg" -> "image"
            return mediaType switch
            {
                "image" => "image",
                "video" => "video",
                "audio" => "audio",
                _ => "Others"
            };
        }
    }
}
